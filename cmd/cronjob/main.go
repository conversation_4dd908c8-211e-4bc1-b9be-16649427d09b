// -----------------------------------------------------------------------------
// JOB SCHEDULE NOTES
//
// cleanup-orphan-videos  —  30 3 * * *
// other-job-name         —  <min> <hour> <dom> <mon> <dow>
// -----------------------------------------------------------------------------

package main

import (
	"context"
	"flag"
	"os"
	"vibico-education-api/internal/cronjobs"
	"vibico-education-api/internal/repository"

	config "github.com/BehemothLtd/behemoth-pkg/golang/config"
	"github.com/BehemothLtd/behemoth-pkg/golang/databases"
	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/rs/zerolog/log"
)

func main() {
	os.Setenv("TZ", "UTC")
	config.InitEnvironment(utils.AppName())

	// Define command-line flags
	var job string
	flag.StringVar(&job, "job", "", "Job to run (e.g. 'cleanup-orphan-videos')")
	flag.Parse()

	// Validate job parameter
	if job == "" {
		log.Error().Msg("Job parameter is required. Use -job=<job_name>")
		os.Exit(1)
	}

	// Connect to database
	db := databases.ConnectDatabase()
	defer func() {
		sqlDB, err := db.DB()
		if err != nil {
			log.Error().Err(err).Msg("Error getting database instance")
			return
		}
		if err := sqlDB.Close(); err != nil {
			log.Error().Err(err).Msg("Error closing database connection")
		}
	}()

	repos := repository.NewRepositoryRegister(db)
	context := context.Background()

	// Execute the specified job
	log.Info().Str("job", job).Msg("Starting job")
	switch job {
	case "cleanup-orphan-videos":
		cronjobs.CleanupOrphanVideos(context, repos)
	default:
		log.Error().Str("job", job).Msg("Unknown job")
		os.Exit(1)
	}

	log.Info().Str("job", job).Msg("Job completed")
}
