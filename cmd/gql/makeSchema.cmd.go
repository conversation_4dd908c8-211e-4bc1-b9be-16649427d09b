package main

import (
	"log"

	gqlSetting "github.com/BehemothLtd/behemoth-pkg/golang/gql"
)

const (
	baseSchemaPath    = "./setup/gqls/definitions"
	sharedSchemaPath  = "./setup/gqls/definitions/shared"
	outputSchemaPath  = "./setup/gqls/schemas"
	userSchemaFile    = "userRootSchema.graphql"
	teacherSchemaFile = "teacherRootSchema.graphql"
	adminSchemaFile   = "adminRootSchema.graphql"
	publicSchemaFile  = "publicRootSchema.graphql"
)

// Schema definitions
var schemaDefinitions = map[string]string{
	"users":    userSchemaFile,
	"teachers": teacherSchemaFile,
	"admins":   adminSchemaFile,
	"publics":  publicSchemaFile,
}

func main() {
	for schemaDir, outputFile := range schemaDefinitions {
		if err := gqlSetting.CreateRootSchema(schemaDir, baseSchemaPath, sharedSchemaPath, outputSchemaPath, outputFile); err != nil {
			log.Printf("❌ Error generating schema for %s: %v", schemaDir, err)
		} else {
			log.Printf("✅ Successfully generated %s", outputFile)
		}
	}
}
