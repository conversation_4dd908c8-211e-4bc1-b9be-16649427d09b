package main

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/spf13/cobra"

	"vibico-education-api/internal/services/video"
)

var (
	ffmpegPath string
	videoSvc   *video.VideoService
)

func main() {
	if err := execute(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}

func execute() error {
	rootCmd := newRootCmd()
	return rootCmd.Execute()
}

func newRootCmd() *cobra.Command {
	rootCmd := &cobra.Command{
		Use:   "video-cli",
		Short: "Video CLI tool for processing videos using ffmpeg",
		Long:  `A command-line tool that provides various video processing operations using ffmpeg.`,
		PersistentPreRunE: func(cmd *cobra.Command, args []string) error {
			var err error
			// Initialize the video service with the provided ffmpeg path
			// If ffmpegPath is empty, the service will try to find ffmpeg in system PATH
			videoSvc, err = video.NewVideoService(ffmpegPath)
			if err != nil {
				return fmt.Errorf("failed to initialize video service: %v", err)
			}
			return nil
		},
	}

	// Add global flags
	rootCmd.PersistentFlags().StringVar(&ffmpegPath, "ffmpeg-path", "", "Path to ffmpeg binary (if not specified, will try to find in system PATH)")

	// Add subcommands
	rootCmd.AddCommand(newWatermarkCmd())
	rootCmd.AddCommand(newThumbnailCmd())

	return rootCmd
}

func newWatermarkCmd() *cobra.Command {
	var (
		input     string
		watermark string
		position  string
		output    string
	)

	cmd := &cobra.Command{
		Use:   "watermark",
		Short: "Add watermark to a video",
		Long:  `Add a watermark image to a video at a specified position.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			// Validate required flags
			if input == "" {
				return fmt.Errorf("input file is required")
			}
			if watermark == "" {
				return fmt.Errorf("watermark file is required")
			}
			if output == "" {
				return fmt.Errorf("output file is required")
			}

			// Check if input file exists
			if _, err := os.Stat(input); err != nil {
				return fmt.Errorf("input file not found: %v", err)
			}

			// Check if watermark file exists
			if _, err := os.Stat(watermark); err != nil {
				return fmt.Errorf("watermark file not found: %v", err)
			}

			// Create output directory if it doesn't exist
			outputDir := filepath.Dir(output)
			if err := os.MkdirAll(outputDir, 0755); err != nil {
				return fmt.Errorf("failed to create output directory: %v", err)
			}

			// Add watermark to video
			fmt.Printf("Adding watermark to %s...\n", input)
			err := videoSvc.AddWatermark(input, watermark, output, position)
			if err != nil {
				return fmt.Errorf("failed to add watermark: %v", err)
			}

			fmt.Printf("Watermark added successfully! Output saved to: %s\n", output)
			return nil
		},
	}

	// Add flags
	cmd.Flags().StringVar(&input, "input", "", "Input video file path (required)")
	cmd.Flags().StringVar(&watermark, "watermark", "", "Watermark image file path (required)")
	cmd.Flags().StringVar(&position, "position", "bottom-right", "Watermark position (top-left, top-right, bottom-left, bottom-right, center)")
	cmd.Flags().StringVar(&output, "output", "", "Output video file path (required)")

	// Mark required flags
	cmd.MarkFlagRequired("input")
	cmd.MarkFlagRequired("watermark")
	cmd.MarkFlagRequired("output")

	return cmd
}

func newThumbnailCmd() *cobra.Command {
	var (
		input  string
		timeAt string
		output string
	)

	cmd := &cobra.Command{
		Use:   "thumbnail",
		Short: "Generate a thumbnail from a video",
		Long:  `Generate a thumbnail image from a video at a specified time position. If no time is provided, the middle of the video will be used.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			// Validate required flags
			if input == "" {
				return fmt.Errorf("input file is required")
			}
			if output == "" {
				return fmt.Errorf("output file is required")
			}

			// Check if input file exists
			if _, err := os.Stat(input); err != nil {
				return fmt.Errorf("input file not found: %v", err)
			}

			// Create output directory if it doesn't exist
			outputDir := filepath.Dir(output)
			if err := os.MkdirAll(outputDir, 0755); err != nil {
				return fmt.Errorf("failed to create output directory: %v", err)
			}

			var duration *time.Duration

			// Only parse time if provided
			if timeAt != "" {
				parsedDuration, err := parseTimeFlag(timeAt)
				if err != nil {
					return fmt.Errorf("invalid time format: %v", err)
				}
				duration = &parsedDuration
				fmt.Printf("Generating thumbnail from %s at %s...\n", input, timeAt)
			} else {
				fmt.Printf("Generating thumbnail from %s at the middle point...\n", input)
			}

			// Generate thumbnail
			err := videoSvc.GenerateThumbnail(input, output, duration)
			if err != nil {
				return fmt.Errorf("failed to generate thumbnail: %v", err)
			}

			fmt.Printf("Thumbnail generated successfully! Output saved to: %s\n", output)

			// Remind user if output doesn't have image extension
			outputExt := strings.ToLower(filepath.Ext(output))
			if outputExt != ".jpg" && outputExt != ".jpeg" && outputExt != ".png" {
				fmt.Printf("Note: The output file doesn't have a common image extension. Consider using .jpg or .png for thumbnails.\n")
			}

			return nil
		},
	}

	// Add flags
	cmd.Flags().StringVar(&input, "input", "", "Input video file path (required)")
	cmd.Flags().StringVar(&timeAt, "time", "", "Time position in format HH:MM:SS (optional, defaults to middle of video)")
	cmd.Flags().StringVar(&output, "output", "", "Output image file path (required)")

	// Mark required flags
	cmd.MarkFlagRequired("input")
	// Time is no longer required
	cmd.MarkFlagRequired("output")

	return cmd
}

// parseTimeFlag parses a time string in format "HH:MM:SS" into time.Duration
func parseTimeFlag(timeStr string) (time.Duration, error) {
	parts := strings.Split(timeStr, ":")
	if len(parts) != 3 {
		return 0, fmt.Errorf("time must be in format HH:MM:SS")
	}

	// Parse hours
	hours, err := time.ParseDuration(parts[0] + "h")
	if err != nil {
		return 0, err
	}

	// Parse minutes
	minutes, err := time.ParseDuration(parts[1] + "m")
	if err != nil {
		return 0, err
	}

	// Parse seconds
	seconds, err := time.ParseDuration(parts[2] + "s")
	if err != nil {
		return 0, err
	}

	return hours + minutes + seconds, nil
}
