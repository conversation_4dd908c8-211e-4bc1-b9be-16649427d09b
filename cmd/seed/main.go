package main

import (
	config "github.com/BehemothLtd/behemoth-pkg/golang/config"
	databases "github.com/BehemothLtd/behemoth-pkg/golang/databases"
	utils "github.com/BehemothLtd/behemoth-pkg/golang/utils"

	"vibico-education-api/setup/seeds"

	"gorm.io/gorm"
)

func main() {
	config.InitEnvironment("vibico-education-api")
	db := databases.ConnectDatabase()

	SeedCore(db)

	if utils.IsLocalEnv() || utils.IsDevelopmentEnv() {
		SeedDev(db)
	}
}

func SeedCore(db *gorm.DB) {
	seeds.SkillSeed(db)
}

func SeedDev(db *gorm.DB) {
	seeds.AdminSeed(db)
	seeds.UserSeed(db)
	seeds.TeacherSeed(db)

	seeds.PackageDealSeed(db)
	seeds.CourseSeed(db)
	seeds.CourseSectionSeed(db)
	seeds.DrillSeed(db)
	// seeds.TemplateDiagramSeed(db)
	// seeds.DiagramSeed(db)
	// seeds.CourseUserSeed(db)
}
