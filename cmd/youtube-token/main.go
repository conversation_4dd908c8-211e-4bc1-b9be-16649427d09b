package main

import (
	"context"
	"flag"
	"fmt"
	"net/url"
	"os"
	"strings"

	"github.com/BehemothLtd/behemoth-pkg/golang/config"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"

	"vibico-education-api/internal/services/youtube"
)

type youtubeConfig struct {
	credFile  string
	tokenFile string
	verbose   bool
}

func main() {
	// Parse command line flags
	config.LoadEnv()
	cfg := parseFlags()

	// Configure logger
	zerolog.SetGlobalLevel(zerolog.InfoLevel)
	if cfg.verbose {
		zerolog.SetGlobalLevel(zerolog.DebugLevel)
	}
	log.Logger = log.Output(zerolog.ConsoleWriter{Out: os.Stderr})

	// Validate flags
	if cfg.credFile == "" {
		log.Fatal().Msg("Credentials file path is required")
	}
	if cfg.tokenFile == "" {
		log.Fatal().Msg("Token file path is required")
	}

	// Check if credentials file exists
	if _, err := os.Stat(cfg.credFile); os.IsNotExist(err) {
		log.Fatal().Str("file", cfg.credFile).Msg("Credentials file does not exist")
	}

	// Create context
	ctx := context.Background()

	// Create token generator
	fmt.Println("Generating YouTube API token...")
	generator, err := youtube.NewTokenGenerator(cfg.credFile, cfg.tokenFile)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to create token generator")
	}

	// Generate token
	authURL, err := generator.GetAuthURL()
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to get auth URL")
	}

	fmt.Printf("1. Go to this URL in your browser:\n\n%s\n\n", authURL)
	fmt.Println("2. Authenticate with your Google account")
	fmt.Println("3. Grant the requested permissions")
	fmt.Println("4. You will be redirected to http://localhost:8080/ with the code in the URL")
	fmt.Print("5. Enter the complete redirect URL or just the authorization code: ")

	var input string
	if _, err := fmt.Scan(&input); err != nil {
		log.Fatal().Err(err).Msg("Failed to read input")
	}

	// Extract authorization code from input
	authCode := extractAuthCode(input)
	if authCode == "" {
		log.Fatal().Msg("Failed to extract authorization code from input")
	}

	// Exchange auth code for token
	token, err := generator.ExchangeToken(ctx, authCode)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to exchange authorization code for token")
	}

	// Save token
	if err := generator.SaveToken(token); err != nil {
		log.Fatal().Err(err).Msg("Failed to save token")
	}

	fmt.Println("Token generated and saved successfully!")
}

// extractAuthCode extracts the authorization code from the input, which can be either
// the complete redirect URL or just the authorization code
func extractAuthCode(input string) string {
	// Check if input is a URL
	if strings.HasPrefix(input, "http://") || strings.HasPrefix(input, "https://") {
		// Parse URL
		parsedURL, err := url.Parse(input)
		if err != nil {
			log.Error().Err(err).Msg("Failed to parse URL")
			return ""
		}

		// Extract code parameter
		code := parsedURL.Query().Get("code")
		if code != "" {
			return code
		}
	}

	// If not a URL or code parameter not found, return the input as is
	return input
}

func parseFlags() youtubeConfig {
	cfg := youtubeConfig{}

	flag.StringVar(&cfg.credFile, "cred", os.Getenv("YOUTUBE_API_CREDENTIALS_FILE"), "Path to the credentials file")
	flag.StringVar(&cfg.tokenFile, "token", os.Getenv("YOUTUBE_API_TOKEN_FILE"), "Path to save the token file")
	flag.BoolVar(&cfg.verbose, "verbose", false, "Enable verbose logging")

	// Parse command line arguments
	flag.Parse()

	return cfg
}
