package main

import (
	"fmt"
	"os"
	rakeTasks "vibico-education-api/setup/rakes"

	"github.com/BehemothLtd/behemoth-pkg/golang/config"
	"github.com/BehemothLtd/behemoth-pkg/golang/databases"
)

func main() {
	if len(os.Args) < 2 {
		fmt.Println("Please provide a rake task name.")
		os.Exit(1)
	}
	task := os.Args[1]

	config.InitEnvironment("vibico-education-api")
	db := databases.ConnectDatabase()

	switch task {
	case "courseSectionItemDrillCloneAndUpdateRelatedData":
		if err := rakeTasks.CourseSectionItemDrillCloneAndUpdateRelatedData(db); err != nil {
			fmt.Println("Error:", err)
			os.Exit(1)
		}
		fmt.Println("Migration completed successfully.")
	case "teacherApprovedCourseAndStudentCount":
		if err := rakeTasks.TeacherApprovedCourseAndStudentCount(db); err != nil {
			fmt.Println("Error:", err)
			os.Exit(1)
		}
		fmt.Println("Migration completed successfully.")
	case "migrateCourseCensorHistoryData":
		if err := rakeTasks.MigrateCourseCensorHistoryData(db); err != nil {
			fmt.Println("Error:", err)
			os.Exit(1)
		}
		fmt.Println("Migration completed successfully.")
	case "coursePackageInitData":
		if err := rakeTasks.CoursePackageInitData(db); err != nil {
			fmt.Println("Error:", err)
			os.Exit(1)
		}
		fmt.Println("Migration completed successfully.")
	default:
		fmt.Printf("Unknown rake task: %s\n", task)
		os.Exit(1)
	}
}
