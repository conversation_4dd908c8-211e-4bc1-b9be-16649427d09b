package main

import (
	"os"

	"vibico-education-api/internal/asyncq"
	"vibico-education-api/internal/repository"
	"vibico-education-api/internal/routes"
	"vibico-education-api/pkg/helpers"
	"vibico-education-api/setup/grpc_clients"

	config "github.com/BehemothLtd/behemoth-pkg/golang/config"
	"github.com/BehemothLtd/behemoth-pkg/golang/databases"
	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
)

func main() {
	os.Setenv("TZ", "UTC")
	config.InitEnvironment(utils.AppName())

	db := databases.ConnectDatabase()
	repos := repository.NewRepositoryRegister(db)
	grpc_clients.InitAuthGrpcClient()
	defer grpc_clients.Conn.Close()
	helpers.InitRedisClient()

	workerManager := asyncq.GetManager()
	workerManager.InitAsynqClient()
	defer workerManager.CloseAsynqClient()

	server := routes.InitServer(repos)
	go config.StartServer(server, utils.AppName())
	config.WaitForShutdown(server, db)
}
