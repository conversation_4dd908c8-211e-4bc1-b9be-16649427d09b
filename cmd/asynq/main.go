package main

import (
	"os"

	"vibico-education-api/internal/asyncq"
	generalTasks "vibico-education-api/internal/asyncq/tasks/general"
	videoTranscoderTasks "vibico-education-api/internal/asyncq/tasks/videoTranscoder"
	"vibico-education-api/internal/middlewares"
	"vibico-education-api/internal/repository"
	"vibico-education-api/pkg/helpers"

	"github.com/BehemothLtd/behemoth-pkg/golang/config"
	"github.com/BehemothLtd/behemoth-pkg/golang/databases"
	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/hibiken/asynq"
	"github.com/rs/zerolog/log"
	"github.com/spf13/cobra"
)

var queues []string

func main() {
	var rootCmd = &cobra.Command{
		Use:   "worker",
		Short: "Run async worker",
		Run: func(cmd *cobra.Command, args []string) {
			runWorker(queues)
		},
	}

	rootCmd.Flags().StringArrayVarP(&queues, "queue", "q", []string{}, "Queue(s) to subscribe (repeatable)")

	if err := rootCmd.Execute(); err != nil {
		log.Fatal().Err(err).Msg("Could not run cmd")
	}
}

func runWorker(queues []string) {
	queuesConfig := asyncq.ParseQueues(queues)

	os.Setenv("TZ", "UTC")
	config.InitEnvironment(utils.AppName())
	helpers.InitRedisClient()

	db := databases.ConnectDatabase()
	repos := repository.NewRepositoryRegister(db)

	mux := asynq.NewServeMux()
	mux.Use(middlewares.WithTaskLogger())

	queueHandlers := map[string]func(*asynq.ServeMux, repository.IRepositories){
		asyncq.QueueVideoTranscoder: videoTranscoderTasks.RegisterHandlers,
	}

	registered := make(map[string]bool)

	for queue := range queuesConfig {
		if register, ok := queueHandlers[queue]; ok {
			if !registered[queue] {
				register(mux, repos)
				registered[queue] = true
			}
		} else {
			if !registered["generalTask"] {
				generalTasks.RegisterHandlers(mux, repos)
				registered["generalTask"] = true
			}
		}
	}

	manager := asyncq.GetManager()
	defer manager.CloseAsynqClient()

	srv := manager.NewServer(10, queuesConfig)

	log.Info().Msgf("Starting worker with queues: %v", queuesConfig)
	log.Info().Msg("Starting Asynq server")

	if err := srv.Run(mux); err != nil {
		log.Fatal().Err(err).Msg("Could not run Asynq server")
	}
}
