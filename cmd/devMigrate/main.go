package main

import (
	"encoding/json"
	"flag"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"strings"

	"github.com/BehemothLtd/behemoth-pkg/golang/config"
	"github.com/BehemothLtd/behemoth-pkg/golang/databases"
	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

const stateFile = "./tmp/dev_migrations_ran.json"

func main() {
	config.InitEnvironment(utils.AppName())

	if utils.IsProductionEnv() {
		log.Fatal().Msg("Refusing to run dev migrations in production")
	}

	db := databases.ConnectDatabase()
	defer databases.Close(db)

	state := loadRanMigrations()

	rollback := flag.Bool("rollback", false, "Rollback instead of migrate")
	flag.Parse()

	if *rollback {
		rollbackDevMigrations(db, state)
	} else {
		runDevMigrations(db, state)
	}
}

func runDevMigrations(db *gorm.DB, state map[string]bool) {
	files, _ := filepath.Glob("setup/migrations/devs/*.up.sql")
	sort.Strings(files)

	for _, f := range files {
		if strings.HasSuffix(f, ".down.sql") {
			continue // skip down files during migrate
		}
		if state[f] {
			continue
		}

		runSQLFile(db, f)
		state[f] = true
		saveRanMigrations(state)
	}

	log.Info().Msg(" Dev migrations complete.")
}

func rollbackDevMigrations(db *gorm.DB, state map[string]bool) {
	var ranFiles []string
	for file := range state {
		ranFiles = append(ranFiles, file)
	}
	sort.Sort(sort.Reverse(sort.StringSlice(ranFiles)))

	for _, f := range ranFiles {
		down := strings.TrimSuffix(f, ".up.sql") + ".down.sql"

		if _, err := os.Stat(down); err == nil {
			fmt.Printf("==> Rolling back %s\n", f)
			runSQLFile(db, down)
		} else {
			fmt.Printf("No rollback found for %s\n", f)
			continue
		}

		delete(state, f)
		saveRanMigrations(state)
	}

	log.Info().Msg(" Dev rollback complete.")
}

func runSQLFile(db *gorm.DB, path string) {
	content, err := os.ReadFile(path)
	if err != nil {
		log.Fatal().Msgf("Read failed %s: %v", path, err)
	}

	fmt.Printf("==> Running %s\n", path)

	if err := db.Exec(string(content)).Error; err != nil {
		log.Fatal().Msgf("Execution failed %s: %v", path, err)
	}
}

func loadRanMigrations() map[string]bool {
	state := make(map[string]bool)
	if data, err := os.ReadFile(stateFile); err == nil {
		json.Unmarshal(data, &state)
	}
	return state
}

func saveRanMigrations(state map[string]bool) {
	data, _ := json.MarshalIndent(state, "", "  ")
	err := os.MkdirAll(filepath.Dir(stateFile), 0750)
	if err != nil {
		log.Fatal().Msgf("saveRanMigrations - os.MkdirAll Execution failed: %v", err)
	}
	if err := os.WriteFile(stateFile, data, 0644); err != nil {
		log.Fatal().Msgf("saveRanMigrations - os.WriteFile Execution failed: %v", err)
	}
}
