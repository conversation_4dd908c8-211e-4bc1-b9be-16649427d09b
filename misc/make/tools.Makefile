# This makefile should be used to hold functions/variables

ifeq ($(ARCH),x86_64)
	ARCH := amd64
else ifeq ($(ARCH),aarch64)
	ARCH := arm64
endif

define github_url
    https://github.com/$(GITHUB)/releases/download/v$(VERSION)/$(ARCHIVE)
endef

# creates a directory bin.
bin:
	@ mkdir -p $@

# ~~~ Tools ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

# ~~ [migrate] ~~~ https://github.com/golang-migrate/migrate ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
# ~~ Link release ARM: https://github.com/golang-migrate/migrate/releases/download/v4.18.2/migrate.darwin-arm64.tar.gz

MIGRATE := $(shell command -v migrate || echo "bin/migrate")
migrate: bin/migrate ## Install migrate (database migration)

bin/migrate: VERSION := 4.18.2
bin/migrate: GITHUB  := golang-migrate/migrate
bin/migrate: ARCHIVE := migrate.$(OSTYPE)-$(ARCH).tar.gz
bin/migrate: bin
	@ printf "Install migrate...\n"
	@ printf "Download URL: $(call github_url)\n"
	@ curl -Ls $(shell echo $(call github_url) | tr A-Z a-z) | tar -zOxf - migrate > $@ && chmod +x $@
	@ printf "done.\n"

# ~~ [ air ] ~~~ https://github.com/cosmtrek/air ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
# ~~ Link release ARM: https://github.com/air-verse/air/releases/download/v1.61.7/air_1.61.7_darwin_amd64.tar.gz

AIR := $(shell command -v air || echo "bin/air")
air: bin/air ## Installs air (go file watcher)

bin/air: VERSION := 1.61.7
bin/air: GITHUB  := cosmtrek/air
bin/air: ARCHIVE := air_$(VERSION)_$(OSTYPE)_$(ARCH).tar.gz
bin/air: bin
	@ printf "Install air...\n"
	@ printf "Download URL: $(call github_url)\n"
	@ curl -Ls $(shell echo $(call github_url) | tr A-Z a-z) | tar -zOxf - air > $@ && chmod +x $@
	@ printf "done.\n"


# ~~ [ go-enum ] ~~~ https://github.com/abice/go-enum ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
# ~~ Link release ARM: https://github.com/abice/go-enum/releases/download/v0.6.0/go-enum_Darwin_arm64

GO_ENUM := $(shell command -v go-enum || echo "bin/go-enum")
go-enum: bin/go-enum ## Installs go-enum (go file watcher)

bin/go-enum: VERSION := 0.6.0
bin/go-enum: GITHUB  := abice/go-enum
bin/go-enum: ARCHIVE := go-enum_$(shell uname -s)_$(shell uname -m)
bin/go-enum: bin
	@ printf "Install go-enum...\n"
	@ printf "Download URL: $(call github_url)\n"
	@ curl -L -o ./bin/go-enum $(call github_url) && chmod +x $@
	@ printf "done.\n"

# ~~ [ ffmpeg ] ~~~ https://ffmpeg.org/ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.PHONY: install-ffmpeg
install-ffmpeg: ## Install ffmpeg and ffprobe
ifeq ($(OSTYPE),darwin)
	@if command -v brew >/dev/null 2>&1; then \
		echo "Installing ffmpeg using Homebrew..."; \
		brew update; \
		brew install ffmpeg; \
	else \
		echo "Homebrew is not installed. Please install ffmpeg manually."; \
		exit 1; \
	fi
else ifeq ($(OSTYPE),linux)
	@if command -v apt-get >/dev/null 2>&1; then \
		echo "Installing ffmpeg using apt..."; \
		sudo apt-get update && sudo apt-get install -y ffmpeg; \
	elif command -v yum >/dev/null 2>&1; then \
		echo "Installing ffmpeg using yum..."; \
		sudo yum install -y ffmpeg; \
	else \
		echo "Could not determine package manager. Please install ffmpeg manually."; \
		exit 1; \
	fi
else
	@echo "Unsupported OS: $(OSTYPE). Please install ffmpeg manually."
	@exit 1
endif
	@if command -v ffmpeg >/dev/null 2>&1; then \
		echo "ffmpeg installed successfully"; \
		ffmpeg -version | head -n 1; \
	else \
		echo "ffmpeg installation failed"; \
		exit 1; \
	fi
	@if command -v ffprobe >/dev/null 2>&1; then \
		echo "ffprobe installed successfully"; \
		ffprobe -version | head -n 1; \
	else \
		echo "ffprobe installation failed"; \
		exit 1; \
	fi
