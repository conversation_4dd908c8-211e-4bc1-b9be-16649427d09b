package constants

const (
	// App Generals
	AuthorizationHeader = "Education-Authorization"
	BearerKey           = "Bearer"

	GraphQLEndpoint = "/graphql"
	RestEndpoint    = "/rest"

	CurrentUserID   = "currentUserId"
	CurrentUserRole = "currentUserRole"
	RoleUser        = "user"

	PhoneNumberFormatter = `^(?:\+84|0084|0)[1235789][0-9]{1,2}[0-9]{7}$`

	MaxTextLength = 65535
)

// UPLOAD
const (
	CourseUploadPath    = "courses"
	CourseThumbnailPath = "thumbnails.webp"

	// GCS PATH
	CourseVideoPath          = "courses/%s/video/%s"                  // fileid
	CourseVideoThumbnailPath = "courses/%s/thumbnail/thumbnails.webp" // fileid

	// LOCAL PATH
	WatermarkImagePath = "assets/watermark.png"

	GCSMaxComposePerBatch = 32
)

// Course
const (
	CourseBasicPackage   = "Basic"
	CourseAdvancePackage = "Advance"
	CourseOfflinePackage = "Offline"
)
