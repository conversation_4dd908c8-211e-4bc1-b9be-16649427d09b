package gcs

import (
	"context"
	"fmt"
	"io"
	"os"
	"sync"
	"time"
	"vibico-education-api/constants"

	"cloud.google.com/go/storage"
	"github.com/rs/zerolog/log"
)

const (
	// Chunk size for parallel downloads - 8MB chunks
	chunkSize = 8 * 1024 * 1024
	// Number of concurrent download workers
	maxConcurrentDownloads = 4
	// Buffer size for copying data
	bufferSize = 256 * 1024 // 256KB buffer
)

// DownloadConfig holds configuration for optimized downloads
type DownloadConfig struct {
	ChunkSize           int64
	MaxConcurrentChunks int
	BufferSize          int
	EnableParallel      bool
}

// DefaultDownloadConfig returns optimized default configuration
func DefaultDownloadConfig() *DownloadConfig {
	return &DownloadConfig{
		ChunkSize:           chunkSize,
		MaxConcurrentChunks: maxConcurrentDownloads,
		BufferSize:          bufferSize,
		EnableParallel:      true,
	}
}

// OptimizedDownloadVideoFromGCS downloads video with multiple optimization techniques
func DownloadVideoFromGCS(ctx context.Context, fileId string, config *DownloadConfig) (*os.File, error) {
	// Input validation
	if fileId == "" {
		return nil, fmt.Errorf("fileId cannot be empty")
	}

	if config == nil {
		config = DefaultDownloadConfig()
	}

	// Validate config
	if config.ChunkSize <= 0 {
		config.ChunkSize = chunkSize
	}
	if config.MaxConcurrentChunks <= 0 {
		config.MaxConcurrentChunks = maxConcurrentDownloads
	}
	if config.BufferSize <= 0 {
		config.BufferSize = bufferSize
	}

	// Check environment variables
	bucketName := os.Getenv("GCS_BUCKET_NAME")
	if bucketName == "" {
		return nil, fmt.Errorf("GCS_BUCKET_NAME environment variable is not set")
	}

	client, err := storage.NewClient(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to create GCS client: %w", err)
	}
	defer func() {
		if closeErr := client.Close(); closeErr != nil {
			log.Ctx(ctx).Warn().Err(closeErr).Msg("Failed to close GCS client")
		}
	}()

	videoPath := fmt.Sprintf(constants.CourseVideoPath, fileId, fileId)
	if videoPath == "" {
		return nil, fmt.Errorf("failed to generate video path for fileId: %s", fileId)
	}

	bucket := client.Bucket(bucketName)
	obj := bucket.Object(videoPath)

	// Get object attributes to determine size and validate existence
	attrs, err := obj.Attrs(ctx)
	if err != nil {
		if err == storage.ErrObjectNotExist {
			return nil, fmt.Errorf("video file not found in GCS: %s", videoPath)
		}
		return nil, fmt.Errorf("failed to get object attributes: %w", err)
	}

	// Validate file size
	if attrs.Size <= 0 {
		return nil, fmt.Errorf("invalid file size: %d bytes", attrs.Size)
	}

	// Check if file is too large (e.g., > 50GB)
	maxFileSize := int64(50 * 1024 * 1024 * 1024) // 50GB
	if attrs.Size > maxFileSize {
		return nil, fmt.Errorf("file too large: %d bytes (max: %d bytes)", attrs.Size, maxFileSize)
	}

	log.Ctx(ctx).Info().
		Int64("fileSize", attrs.Size).
		Str("contentType", attrs.ContentType).
		Str("videoPath", videoPath).
		Msg("Starting optimized download")

	// Create temporary file with proper cleanup
	tmpFile, err := os.CreateTemp("", "youtube-upload-*.mp4")
	if err != nil {
		return nil, fmt.Errorf("failed to create temp file: %w", err)
	}

	// Ensure cleanup on any error
	var downloadErr error
	defer func() {
		if downloadErr != nil {
			tmpFile.Close()
			if removeErr := os.Remove(tmpFile.Name()); removeErr != nil {
				log.Ctx(ctx).Warn().
					Err(removeErr).
					Str("tempFile", tmpFile.Name()).
					Msg("Failed to cleanup temp file after error")
			}
		}
	}()

	// Choose download strategy based on file size
	if config.EnableParallel && attrs.Size > config.ChunkSize*2 {
		log.Ctx(ctx).Info().
			Int64("fileSize", attrs.Size).
			Int64("chunkSize", config.ChunkSize).
			Msg("Using parallel chunked download")
		downloadErr = downloadParallel(ctx, obj, tmpFile, attrs.Size, config)
	} else {
		log.Ctx(ctx).Info().
			Int64("fileSize", attrs.Size).
			Msg("Using optimized single-stream download")
		downloadErr = downloadOptimizedSingle(ctx, obj, tmpFile, config)
	}

	if downloadErr != nil {
		return nil, fmt.Errorf("failed to download video: %w", downloadErr)
	}

	// Verify file was written correctly
	fileInfo, err := tmpFile.Stat()
	if err != nil {
		downloadErr = err
		return nil, fmt.Errorf("failed to stat downloaded file: %w", err)
	}

	if fileInfo.Size() != attrs.Size {
		downloadErr = fmt.Errorf("file size mismatch: expected %d, got %d", attrs.Size, fileInfo.Size())
		return nil, downloadErr
	}

	// Reset file pointer to beginning
	if _, err := tmpFile.Seek(0, 0); err != nil {
		downloadErr = err
		return nil, fmt.Errorf("failed to seek file: %w", err)
	}

	log.Ctx(ctx).Info().
		Str("tempFile", tmpFile.Name()).
		Int64("expectedSize", attrs.Size).
		Int64("actualSize", fileInfo.Size()).
		Msg("Video downloaded successfully")

	return tmpFile, nil
}

// downloadOptimizedSingle performs single-stream download with optimizations
func downloadOptimizedSingle(ctx context.Context, obj *storage.ObjectHandle, tmpFile *os.File, config *DownloadConfig) error {
	// Validate inputs
	if obj == nil {
		return fmt.Errorf("object handle is nil")
	}
	if tmpFile == nil {
		return fmt.Errorf("temp file is nil")
	}
	if config == nil {
		return fmt.Errorf("config is nil")
	}

	// Create reader with optimized settings
	reader, err := obj.NewRangeReader(ctx, 0, -1)
	if err != nil {
		return fmt.Errorf("failed to create object reader: %w", err)
	}
	defer func() {
		if closeErr := reader.Close(); closeErr != nil {
			log.Warn().Err(closeErr).Msg("Failed to close GCS reader")
		}
	}()

	// Validate buffer size
	if config.BufferSize <= 0 {
		config.BufferSize = bufferSize
	}

	// Use larger buffer for better performance
	buffer := make([]byte, config.BufferSize)

	// Track progress
	var totalBytes int64
	startTime := time.Now()
	lastProgressLog := time.Now()
	progressInterval := 10 * 1024 * 1024 // 10MB

	for {
		// Check context cancellation more frequently
		select {
		case <-ctx.Done():
			return fmt.Errorf("download cancelled: %w", ctx.Err())
		default:
		}

		n, err := reader.Read(buffer)
		if n > 0 {
			// Write with retry mechanism
			written := 0
			for written < n {
				w, writeErr := tmpFile.Write(buffer[written:n])
				if writeErr != nil {
					return fmt.Errorf("failed to write to temp file at offset %d: %w", totalBytes+int64(written), writeErr)
				}
				written += w
			}

			totalBytes += int64(n)

			// Log progress with rate limiting to avoid spam
			if totalBytes-int64(totalBytes/int64(progressInterval)*int64(progressInterval)) < int64(n) &&
				time.Since(lastProgressLog) > 5*time.Second {
				elapsed := time.Since(startTime)
				if elapsed.Seconds() > 0 {
					speed := float64(totalBytes) / elapsed.Seconds() / (1024 * 1024) // MB/s
					log.Info().
						Int64("downloadedMB", totalBytes/(1024*1024)).
						Float64("speedMBps", speed).
						Msg("Download progress")
					lastProgressLog = time.Now()
				}
			}
		}

		if err == io.EOF {
			break
		}
		if err != nil {
			return fmt.Errorf("failed to read from GCS after %d bytes: %w", totalBytes, err)
		}
	}

	// Ensure data is flushed to disk
	if err := tmpFile.Sync(); err != nil {
		return fmt.Errorf("failed to sync file to disk: %w", err)
	}

	log.Info().
		Int64("totalBytes", totalBytes).
		Float64("totalTimeSec", time.Since(startTime).Seconds()).
		Msg("Single-stream download completed")

	return nil
}

// downloadParallel performs parallel chunked download
func downloadParallel(ctx context.Context, obj *storage.ObjectHandle, tmpFile *os.File, fileSize int64, config *DownloadConfig) error {
	// Validate inputs
	if obj == nil {
		return fmt.Errorf("object handle is nil")
	}
	if tmpFile == nil {
		return fmt.Errorf("temp file is nil")
	}
	if config == nil {
		return fmt.Errorf("config is nil")
	}
	if fileSize <= 0 {
		return fmt.Errorf("invalid file size: %d", fileSize)
	}

	// Validate and adjust config values
	if config.ChunkSize <= 0 {
		config.ChunkSize = chunkSize
	}
	if config.MaxConcurrentChunks <= 0 {
		config.MaxConcurrentChunks = maxConcurrentDownloads
	}

	// Calculate chunks
	numChunks := (fileSize + config.ChunkSize - 1) / config.ChunkSize

	// Prevent too many small chunks or too few large chunks
	if numChunks > int64(config.MaxConcurrentChunks) {
		// If too many chunks, increase chunk size
		config.ChunkSize = (fileSize + int64(config.MaxConcurrentChunks) - 1) / int64(config.MaxConcurrentChunks)
		numChunks = int64(config.MaxConcurrentChunks)
	}

	// Ensure minimum chunk size (1MB)
	minChunkSize := int64(1024 * 1024)
	if config.ChunkSize < minChunkSize {
		config.ChunkSize = minChunkSize
		numChunks = (fileSize + config.ChunkSize - 1) / config.ChunkSize
	}

	log.Info().
		Int64("numChunks", numChunks).
		Int64("chunkSize", config.ChunkSize).
		Int64("fileSize", fileSize).
		Msg("Starting parallel download")

	// Create a channel for chunks with buffer
	type chunk struct {
		index int64
		data  []byte
		err   error
		start int64
		end   int64
	}

	chunkChan := make(chan chunk, numChunks)
	var wg sync.WaitGroup

	// Worker pool for downloading chunks
	semaphore := make(chan struct{}, config.MaxConcurrentChunks)

	// Create context with timeout for individual chunks
	chunkCtx, cancel := context.WithTimeout(ctx, 10*time.Minute)
	defer cancel()

	// Start download workers
	for i := int64(0); i < numChunks; i++ {
		wg.Add(1)
		go func(chunkIndex int64) {
			defer wg.Done()

			// Acquire semaphore with timeout
			select {
			case semaphore <- struct{}{}:
				defer func() { <-semaphore }()
			case <-chunkCtx.Done():
				chunkChan <- chunk{index: chunkIndex, err: fmt.Errorf("failed to acquire semaphore: %w", chunkCtx.Err())}
				return
			}

			start := chunkIndex * config.ChunkSize
			end := start + config.ChunkSize - 1
			if end >= fileSize {
				end = fileSize - 1
			}

			// Validate range
			if start > end || start >= fileSize {
				chunkChan <- chunk{
					index: chunkIndex,
					err:   fmt.Errorf("invalid chunk range: start=%d, end=%d, fileSize=%d", start, end, fileSize),
					start: start,
					end:   end,
				}
				return
			}

			// Download chunk with retry
			var data []byte
			var err error
			maxRetries := 3

			for retry := 0; retry < maxRetries; retry++ {
				// Check context before each retry
				select {
				case <-chunkCtx.Done():
					err = fmt.Errorf("chunk download cancelled: %w", chunkCtx.Err())
					break
				default:
				}

				reader, readerErr := obj.NewRangeReader(chunkCtx, start, end-start+1)
				if readerErr != nil {
					err = fmt.Errorf("failed to create range reader for chunk %d (attempt %d): %w", chunkIndex, retry+1, readerErr)
					time.Sleep(time.Duration(retry+1) * time.Second) // Exponential backoff
					continue
				}

				data, err = io.ReadAll(reader)
				reader.Close()

				if err == nil {
					// Verify chunk size
					expectedSize := end - start + 1
					if int64(len(data)) != expectedSize {
						err = fmt.Errorf("chunk %d size mismatch: expected %d, got %d", chunkIndex, expectedSize, len(data))
						time.Sleep(time.Duration(retry+1) * time.Second)
						continue
					}
					break // Success
				}

				log.Warn().
					Err(err).
					Int64("chunkIndex", chunkIndex).
					Int("attempt", retry+1).
					Int("maxRetries", maxRetries).
					Msg("Chunk download failed, retrying")

				time.Sleep(time.Duration(retry+1) * time.Second) // Exponential backoff
			}

			chunkChan <- chunk{
				index: chunkIndex,
				data:  data,
				err:   err,
				start: start,
				end:   end,
			}
		}(i)
	}

	// Close channel when all workers complete
	go func() {
		wg.Wait()
		close(chunkChan)
	}()

	// Collect chunks and write in order
	chunks := make(map[int64][]byte)
	var nextChunk int64 = 0
	var totalWritten int64 = 0

	for chunkData := range chunkChan {
		if chunkData.err != nil {
			// Cancel remaining operations
			cancel()
			return fmt.Errorf("failed to download chunk %d (range %d-%d): %w",
				chunkData.index, chunkData.start, chunkData.end, chunkData.err)
		}

		chunks[chunkData.index] = chunkData.data

		// Write consecutive chunks to maintain file order
		for {
			if data, exists := chunks[nextChunk]; exists {
				// Calculate expected file position
				expectedPos := nextChunk * config.ChunkSize

				// Seek to correct position
				currentPos, seekErr := tmpFile.Seek(expectedPos, 0)
				if seekErr != nil {
					cancel()
					return fmt.Errorf("failed to seek to position %d for chunk %d: %w", expectedPos, nextChunk, seekErr)
				}

				if currentPos != expectedPos {
					cancel()
					return fmt.Errorf("seek position mismatch: expected %d, got %d", expectedPos, currentPos)
				}

				// Write chunk data
				written, writeErr := tmpFile.Write(data)
				if writeErr != nil {
					cancel()
					return fmt.Errorf("failed to write chunk %d at position %d: %w", nextChunk, expectedPos, writeErr)
				}

				if written != len(data) {
					cancel()
					return fmt.Errorf("incomplete write for chunk %d: expected %d bytes, wrote %d bytes",
						nextChunk, len(data), written)
				}

				totalWritten += int64(written)
				delete(chunks, nextChunk)
				nextChunk++

				// Log progress
				if nextChunk%10 == 0 || nextChunk == numChunks {
					log.Info().
						Int64("completedChunks", nextChunk).
						Int64("totalChunks", numChunks).
						Int64("writtenMB", totalWritten/(1024*1024)).
						Float64("progress", float64(nextChunk)/float64(numChunks)*100).
						Msg("Parallel download progress")
				}
			} else {
				break
			}
		}
	}

	// Verify all chunks were written
	if nextChunk != numChunks {
		return fmt.Errorf("incomplete parallel download: wrote %d chunks out of %d", nextChunk, numChunks)
	}

	// Ensure data is flushed to disk
	if err := tmpFile.Sync(); err != nil {
		return fmt.Errorf("failed to sync file to disk after parallel download: %w", err)
	}

	log.Info().
		Int64("totalChunks", numChunks).
		Int64("totalWritten", totalWritten).
		Msg("Parallel download completed successfully")

	return nil
}
