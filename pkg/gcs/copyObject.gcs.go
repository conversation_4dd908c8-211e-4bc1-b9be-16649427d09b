package gcs

import (
	"context"
	"fmt"
	"net/url"
	"strings"
	"time"

	"cloud.google.com/go/storage"
	"github.com/google/uuid"
)

func CopyFileGCS(dstBucket, srcBucket, srcObject string) (*string, error) {
	ctx := context.Background()
	client, err := storage.NewClient(ctx)
	if err != nil {
		return nil, fmt.Errorf("storage.NewClient: %w", err)
	}
	defer client.Close()

	ctx, cancel := context.WithTimeout(ctx, time.Second*10)
	defer cancel()

	dstObject := fmt.Sprintf("%d-%s", time.Now().Unix(), uuid.New().String())
	src := client.Bucket(srcBucket).Object(srcObject)
	dst := client.Bucket(dstBucket).Object(dstObject)

	dst = dst.If(storage.Conditions{DoesNotExist: true})

	if _, err := dst.CopierFrom(src).Run(ctx); err != nil {
		return nil, fmt.Errorf("Object(%q).CopierFrom(%q).Run: %w", dstObject, srcObject, err)
	}

	maxDstUrlLen := 255
	baseUrl := fmt.Sprintf("https://storage.googleapis.com/%s/", dstBucket)

	maxObjectLen := maxDstUrlLen - len(baseUrl)
	if len(dstObject) > maxObjectLen {
		dstObject = dstObject[:maxObjectLen]
	}
	dstUrl := baseUrl + dstObject

	return &dstUrl, nil
}

func ParseGCSUrl(gcsUrl string) (bucket string, object string) {
	u, err := url.Parse(gcsUrl)
	if err != nil {
		return "", ""
	}

	parts := strings.SplitN(u.Path, "/", 3)
	if len(parts) >= 3 {
		bucket = parts[1]
		object = parts[2]
	}
	return
}
