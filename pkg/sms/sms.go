package sms

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"vibico-education-api/pkg/helpers"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/redis/go-redis/v9"
	"github.com/rs/zerolog/log"
	"github.com/twilio/twilio-go"
	verify "github.com/twilio/twilio-go/rest/verify/v2"
)

const (
	defaultTimeDuration = time.Minute

	// Twilio verification status
	TwilioStatusPending     = "pending"
	TwilioStatusApproved    = "approved"
	TwilioStatusCanceled    = "canceled"
	TwilioStatusMaxAttempts = "max-attempts-reached"
	TwilioStatusDeleted     = "deleted"
	TwilioStatusFailed      = "failed"
	TwilioStatusExpired     = "expired"
)

type SMSService struct {
	ctx          context.Context
	redisClient  *redis.Client
	twilioClient *twilio.RestClient
	serviceSID   string
}

func NewSMSService(ctx context.Context, serviceSID string) *SMSService {
	s := &SMSService{
		ctx:         ctx,
		redisClient: helpers.GetRedisClient(),
		serviceSID:  serviceSID,
	}

	if s.IsTwilioEnabled() {
		s.twilioClient = twilio.NewRestClient()
	}

	return s
}

func (s *SMSService) SendVerificationCode(to string, limitDuration *time.Duration, message *string) error {
	to = helpers.ConvertPhoneNumber(to)
	if !s.canSendSms(to) {
		errMessage := translator.Translate(nil, "errValidationMsg_tooManyRequest_invalid")
		log.Error().Ctx(s.ctx).Msg(errMessage)
		return errors.New(errMessage)
	}

	if s.IsTwilioEnabled() {
		params := &verify.CreateVerificationParams{}
		params.SetChannel("sms")
		params.SetTo(to)

		if message != nil && *message != "" {
			params.SetCustomMessage(*message)
		}

		resp, err := s.twilioClient.VerifyV2.CreateVerification(s.serviceSID, params)
		if err != nil {
			return err
		}

		if resp != nil && *resp.Status != TwilioStatusPending {
			errMessage := "invalid status"
			log.Error().Ctx(s.ctx).Str("status", *resp.Status).Msg(errMessage)
			return errors.New(errMessage)
		}
	}

	go s.setSmsSent(to, limitDuration)

	return nil
}

func (s *SMSService) VerifyCode(to string, code string) (string, error) {
	if s.IsBypassVerifyCode(code) {
		return TwilioStatusApproved, nil
	}

	if !s.IsTwilioEnabled() {
		return TwilioStatusFailed, nil
	}

	params := &verify.CreateVerificationCheckParams{}
	params.SetTo(helpers.ConvertPhoneNumber(to))
	params.SetCode(code)

	resp, err := s.twilioClient.VerifyV2.CreateVerificationCheck(s.serviceSID, params)
	if err != nil {
		return "", err
	}

	return *resp.Status, nil
}

func (s *SMSService) canSendSms(to string) bool {
	count, err := s.redisClient.Get(s.ctx, s.smsSentKey(to)).Int()
	if err == redis.Nil {
		return true
	} else if err != nil {
		log.Error().Err(err).Ctx(s.ctx).Msg("Redis err")
		return false
	}

	return count == 0
}

func (s *SMSService) setSmsSent(to string, limitDuration *time.Duration) {
	duration := defaultTimeDuration
	if limitDuration != nil {
		duration = *limitDuration
	}

	if err := s.redisClient.Set(s.ctx, s.smsSentKey(to), 1, duration).Err(); err != nil {
		log.Error().Err(err).Ctx(s.ctx).Msg("Redis err")
	}
}

func (s *SMSService) smsSentKey(to string) string {
	return fmt.Sprintf("smsSent:%s", to)
}

func (s *SMSService) IsBypassVerifyCode(code string) bool {
	return code == utils.GetEnv("USER_SMS_VERIFY_CODE", "")
}

func (s *SMSService) IsTwilioEnabled() bool {
	return strings.ToLower(utils.GetEnv("TWILIO_SMS_ENABLED", "false")) == "true"
}
