package uploaders

import (
	"fmt"
)

func (u *Uploader) Upload() ([]*UploadedBlob, error) {
	err := u.Ctx.Request.ParseMultipartForm(10 << 20)
	if err != nil {
		return nil, err
	}

	var uploadedBlobs []*UploadedBlob

	form := u.Ctx.Request.MultipartForm
	files := form.File["files[]"]

	for _, file := range files {
		if err := u.validate(file); err != nil {
			return nil, err
		}

		var uploadedBlob *UploadedBlob
		var uploadErr error

		switch u.StorageService {
		case "local":
			uploadedBlob, uploadErr = u.uploadLocally(file, nil)
		case "google":
			uploadedBlob, uploadErr = u.uploadToGCS(file, nil)
		default:
			return nil, fmt.Errorf("unsupported storage service: %s", u.StorageService)
		}

		if uploadErr != nil {
			return nil, uploadErr
		}

		uploadedBlobs = append(uploadedBlobs, uploadedBlob)
	}

	return uploadedBlobs, nil
}
