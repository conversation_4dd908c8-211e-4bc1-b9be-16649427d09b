package uploaders

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"mime/multipart"
	"os"
	"time"

	"vibico-education-api/constants"
	"vibico-education-api/internal/asyncq"
	videoTranscoderTasks "vibico-education-api/internal/asyncq/tasks/videoTranscoder"
	"vibico-education-api/internal/enums"
	"vibico-education-api/internal/models"
	"vibico-education-api/internal/repository"
	"vibico-education-api/internal/repository/repositories"

	"cloud.google.com/go/storage"
	"github.com/gin-gonic/gin"
	"github.com/hibiken/asynq"
	"github.com/redis/go-redis/v9"
)

// Configuration constants
const (
	uploadTimeout    = 50 * time.Second
	progressKeyTpl   = "upload:%s"
	countKeyTpl      = "upload:%s:count"
	uploadIDKeyTpl   = "upload:%s:id"
	videoIDKeyTpl    = "upload:%s:video_id"
	defaultChunkPath = "chunks"
	maxComposeBatch  = 32
	maxRetries       = 3
	chunkSizeMB      = 10
)

// ChunkMetadata carries chunk info from client.
type ChunkMetadata struct {
	ChunkIndex      int     `json:"chunkIndex"`
	TotalChunks     int     `json:"totalChunks"`
	FileId          string  `json:"fileId"`
	ContentType     string  `json:"contentType"`
	FileName        string  `json:"fileName"`
	FileDescription *string `json:"fileDescription"`
}

// UploadProgress tracks overall upload state.
type UploadProgress struct {
	FileId      string `json:"fileId"`
	TotalChunks int    `json:"totalChunks"`
	ContentType string `json:"contentType"`
	UploadID    string `json:"uploadId,omitempty"`
}

// ChunkUploader handles chunked uploads to GCS, tracks progress in Redis, and updates DB status.
type ChunkUploader struct {
	client          *storage.Client
	bucket          string
	uploadPath      string
	chunkPath       string
	ctx             context.Context
	ginCtx          *gin.Context
	redis           *redis.Client
	videoUploadRepo *repositories.VideoUploadRepository
	videoRepo       *repositories.VideoRepository
}

// NewChunkUploader creates a new uploader for chunked uploads.
func NewChunkUploader(c *gin.Context, redisClient *redis.Client, repos repository.IRepositories) (*ChunkUploader, error) {
	ctx := c.Request.Context()
	client, err := storage.NewClient(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to init GCS client: %w", err)
	}

	bucket := os.Getenv("GCS_BUCKET_NAME")
	if bucket == "" {
		return nil, errors.New("GCS_BUCKET_NAME is required")
	}

	return &ChunkUploader{
		client:          client,
		bucket:          bucket,
		uploadPath:      constants.CourseUploadPath,
		chunkPath:       defaultChunkPath,
		ctx:             ctx,
		ginCtx:          c,
		redis:           redisClient,
		videoUploadRepo: repos.VideoUploadRepo(),
		videoRepo:       repos.VideoRepo(),
	}, nil
}

// UploadToGCS uploads one chunk, updates progress, and creates a VideoUpload record on the first chunk.
func (cu *ChunkUploader) UploadToGCS(file *multipart.FileHeader) (*UploadedBlob, error) {
	defer cu.Close()

	// Parse and validate metadata
	meta, err := cu.parseChunkMetadata()
	if err != nil {
		return nil, fmt.Errorf("metadata error: %w", err)
	}

	// Get or initialize progress tracking
	progress, err := cu.getOrInitProgress(meta)
	if err != nil {
		return nil, fmt.Errorf("init progress: %w", err)
	}

	// Upload the chunk to GCS
	if err := cu.uploadChunk(file, meta, progress); err != nil {
		return nil, err
	}

	// Handle first chunk special case (create DB record)
	if err := cu.handleChunkCounter(meta); err != nil {
		return nil, err
	}

	return &UploadedBlob{}, nil
}

// FinishUpload enqueues an async task to compose all chunks into the final file
func (cu *ChunkUploader) FinishUpload(fileId string, parentType string, parentId int) (*uint32, error) {
	// Fetch progress data
	key := fmt.Sprintf(progressKeyTpl, fileId)
	prog, err := cu.getProgressFromRedis(key)
	if err != nil {
		return nil, fmt.Errorf("get progress: %w", err)
	}
	if prog == nil {
		return nil, fmt.Errorf("no progress for file %s", fileId)
	}

	meta := &ChunkMetadata{FileId: prog.FileId, TotalChunks: prog.TotalChunks}

	// Get upload ID
	uploadID, err := cu.getUploadID(meta.FileId)
	if err != nil {
		return nil, err
	}

	// Update status to processing
	if err := cu.videoUploadRepo.UpdateStatus(cu.ginCtx, uploadID, enums.VideoUploadStatusProcessing, 0); err != nil {
		return nil, fmt.Errorf("failed to update status to processing: %w", err)
	}

	// Get content type from first chunk
	contentType, err := cu.detectContentType(meta.FileId)
	if err != nil {
		return nil, err
	}

	// Generate paths for all chunks
	paths := make([]string, meta.TotalChunks)
	for i := 0; i < meta.TotalChunks; i++ {
		paths[i] = fmt.Sprintf("%s/%s/chunk_%d", cu.chunkPath, meta.FileId, i)
	}

	// Create task payload
	payload := videoTranscoderTasks.GCSChunksComposeTaskPayload{
		FileID:      meta.FileId,
		TotalChunks: meta.TotalChunks,
		Paths:       paths,
		ContentType: contentType,
		UploadID:    uploadID,
		BucketName:  cu.bucket,
		ChunkPath:   cu.chunkPath,
		UploadPath:  cu.uploadPath,
		ParentType:  parentType,
		ParentID:    uint32(parentId),
	}

	// Create the task
	task, err := videoTranscoderTasks.NewGCSChunksComposeTask(payload)
	if err != nil {
		cu.videoUploadRepo.UpdateStatus(cu.ginCtx, uploadID, enums.VideoUploadStatusFailed, 0)
		return nil, fmt.Errorf("failed to create compose task: %w", err)
	}

	// Enqueue the task
	if _, err := asyncq.GetManager().EnqueueTask(context.Background(), task); err != nil {
		cu.videoUploadRepo.UpdateStatus(cu.ginCtx, uploadID, enums.VideoUploadStatusFailed, 0)
		return nil, fmt.Errorf("failed to enqueue compose task: %w", err)
	}

	// Get video ID
	videoID, err := cu.getVideoID(meta.FileId)
	if err != nil {
		return nil, err
	}

	return &videoID, nil
}

// uploadChunk handles uploading a single chunk file to storage
func (cu *ChunkUploader) uploadChunk(file *multipart.FileHeader, meta *ChunkMetadata, progress *UploadProgress) error {
	fileReader, err := file.Open()
	if err != nil {
		return fmt.Errorf("open chunk: %w", err)
	}
	defer fileReader.Close()

	chunkObj := fmt.Sprintf("%s/%s/chunk_%d", cu.chunkPath, meta.FileId, meta.ChunkIndex)
	if err := cu.uploadSingleChunk(fileReader, chunkObj, progress); err != nil {
		return fmt.Errorf("upload chunk: %w", err)
	}

	return nil
}

// handleChunkCounter increments the chunk counter and handles first chunk special case
func (cu *ChunkUploader) handleChunkCounter(meta *ChunkMetadata) error {
	countKey := fmt.Sprintf(countKeyTpl, meta.FileId)
	cnt, err := cu.redis.Incr(cu.ctx, countKey).Result()
	if err != nil {
		return fmt.Errorf("count increment: %w", err)
	}

	// First chunk: create DB record and store upload ID
	if cnt == 1 {
		// Get user info from context with proper type assertion
		userID, exists := cu.ginCtx.Get(constants.CurrentUserID)
		if !exists {
			return fmt.Errorf("currentUserId not found in context")
		}
		userType, exists := cu.ginCtx.Get(constants.CurrentUserRole)
		if !exists {
			return fmt.Errorf("currentUserType not found in context")
		}

		//create video record
		video := &models.Video{
			Title:        meta.FileName,
			Description:  meta.FileDescription,
			Status:       enums.VideoStatusDraft,
			UploaderID:   userID.(uint32),
			UploaderType: userType.(string),
			CreatedAt:    time.Now(),
			UpdatedAt:    time.Now(),
			VideoUpload: &models.VideoUpload{
				Filename: meta.FileId,
				Status:   enums.VideoUploadStatusUploading,
			},
		}
		if err = cu.videoRepo.Create(cu.ginCtx, video); err != nil {
			return fmt.Errorf("create video record: %w", err)
		}

		if err := cu.redis.Set(cu.ctx, fmt.Sprintf(uploadIDKeyTpl, meta.FileId), video.VideoUpload.ID, 24*time.Hour).Err(); err != nil {
			return fmt.Errorf("store upload ID: %w", err)
		}

		if err := cu.redis.Set(cu.ctx, fmt.Sprintf(videoIDKeyTpl, meta.FileId), video.ID, 24*time.Hour).Err(); err != nil {
			return fmt.Errorf("store video ID: %w", err)
		}

		// Schedule cleanup task
		cleanupPayload := videoTranscoderTasks.CleanUpOrphanChunksTaskPayload{
			FileID:    meta.FileId,
			ChunkPath: cu.chunkPath,
			Bucket:    cu.bucket,
			VideoID:   video.ID,
		}
		cleanupTask, err := videoTranscoderTasks.NewCleanUpOrphanChunksTask(cleanupPayload)
		if err != nil {
			log.Printf("Failed to create cleanup task: %v", err)
		} else {
			opts := []asynq.Option{asynq.ProcessIn(23 * time.Hour)}
			if _, err := asyncq.GetManager().EnqueueTask(context.Background(), cleanupTask, opts...); err != nil {
				log.Printf("Failed to schedule cleanup task: %v", err)
			}
		}
	}

	return nil
}

// uploadSingleChunk writes a chunk to GCS with timeout and retry logic
func (cu *ChunkUploader) uploadSingleChunk(r io.Reader, object string, progress *UploadProgress) error {
	var lastErr error

	for attempt := 0; attempt < maxRetries; attempt++ {
		if attempt > 0 {
			time.Sleep(time.Duration(attempt) * time.Second)
			log.Printf("Retrying chunk upload attempt %d for %s", attempt+1, object)
		}

		ctx, cancel := context.WithTimeout(cu.ctx, uploadTimeout)
		defer cancel()

		w := cu.client.Bucket(cu.bucket).Object(object).NewWriter(ctx)
		w.ChunkSize = chunkSizeMB * 1024 * 1024
		w.ContentType = progress.ContentType

		if _, err := io.Copy(w, r); err != nil {
			lastErr = fmt.Errorf("copy chunk: %w", err)
			w.Close()
			continue
		}

		if err := w.Close(); err != nil {
			lastErr = fmt.Errorf("close writer: %w", err)
			continue
		}

		return nil
	}

	return fmt.Errorf("failed after %d attempts, last error: %v", maxRetries, lastErr)
}

// getUploadID retrieves the upload ID from Redis
func (cu *ChunkUploader) getUploadID(fileId string) (uint32, error) {
	uidVal, err := cu.redis.Get(cu.ctx, fmt.Sprintf(uploadIDKeyTpl, fileId)).Uint64()
	if err != nil {
		return 0, fmt.Errorf("get upload ID: %w", err)
	}
	return uint32(uidVal), nil
}

// getVideoID retrieves the video ID from Redis
func (cu *ChunkUploader) getVideoID(fileId string) (uint32, error) {
	vidVal, err := cu.redis.Get(cu.ctx, fmt.Sprintf(videoIDKeyTpl, fileId)).Uint64()
	if err != nil {
		return 0, fmt.Errorf("get video ID: %w", err)
	}
	return uint32(vidVal), nil
}

// detectContentType determines the content type from the first chunk
func (cu *ChunkUploader) detectContentType(fileId string) (string, error) {
	key := fmt.Sprintf(progressKeyTpl, fileId)
	prog, err := cu.getProgressFromRedis(key)
	if err != nil {
		return "", fmt.Errorf("get progress: %w", err)
	}
	if prog == nil {
		return "", fmt.Errorf("no progress found for file %s", fileId)
	}

	return prog.ContentType, nil
}

// parseChunkMetadata reads and validates metadata JSON.
func (cu *ChunkUploader) parseChunkMetadata() (*ChunkMetadata, error) {
	jsonStr := cu.ginCtx.PostForm("metadata")
	if jsonStr == "" {
		return nil, errors.New("metadata required")
	}

	var m ChunkMetadata
	if err := json.Unmarshal([]byte(jsonStr), &m); err != nil {
		return nil, fmt.Errorf("invalid metadata format: %w", err)
	}

	// Validate metadata
	if m.ChunkIndex < 0 || m.TotalChunks <= 0 || m.ChunkIndex >= m.TotalChunks {
		return nil, errors.New("invalid chunk indices")
	}
	if m.FileId == "" {
		return nil, errors.New("fileId required")
	}
	if m.ContentType == "" {
		return nil, errors.New("ContentType required")
	}

	if m.FileName == "" {
		return nil, errors.New("fileName required")
	}

	return &m, nil
}

// getOrInitProgress fetches or initializes upload progress in Redis.
func (cu *ChunkUploader) getOrInitProgress(m *ChunkMetadata) (*UploadProgress, error) {
	key := fmt.Sprintf(progressKeyTpl, m.FileId)
	prog, err := cu.getProgressFromRedis(key)
	if err != nil {
		return nil, err
	}

	if prog == nil {
		// Use content type from metadata, with fallback
		contentType := "application/octet-stream"
		if m.ContentType != "" {
			contentType = m.ContentType
		}

		prog = &UploadProgress{
			FileId:      m.FileId,
			TotalChunks: m.TotalChunks,
			ContentType: contentType,
		}
		if err := cu.storeProgressInRedis(key, prog); err != nil {
			return nil, err
		}
	}

	return prog, nil
}

// getProgressFromRedis retrieves UploadProgress or nil if not found.
func (cu *ChunkUploader) getProgressFromRedis(key string) (*UploadProgress, error) {
	val, err := cu.redis.Get(cu.ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil
		}
		return nil, err
	}

	var prog UploadProgress
	if err := json.Unmarshal([]byte(val), &prog); err != nil {
		return nil, fmt.Errorf("failed to parse progress data: %w", err)
	}

	return &prog, nil
}

// storeProgressInRedis saves UploadProgress with 24h TTL.
func (cu *ChunkUploader) storeProgressInRedis(key string, prog *UploadProgress) error {
	b, err := json.Marshal(prog)
	if err != nil {
		return fmt.Errorf("failed to serialize progress: %w", err)
	}
	return cu.redis.Set(cu.ctx, key, b, 24*time.Hour).Err()
}

// Close releases GCS client resources.
func (cu *ChunkUploader) Close() error {
	return cu.client.Close()
}
