package uploaders

import (
	"context"
	"errors"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"
	"vibico-education-api/internal/repository"
	"vibico-education-api/pkg/helpers"
	translator "vibico-education-api/pkg/translators"

	"cloud.google.com/go/storage"
	"github.com/BehemothLtd/behemoth-pkg/golang/constants"
	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

type Uploader struct {
	Ctx            *gin.Context
	StorageService string
	UploadPath     string
}

type ClientUploader struct {
	cl         *storage.Client
	bucketName string
	uploadPath string
}

type UploadedBlob struct {
	Url string `json:"url"`
	Key string `json:"key"`
}

func (u *Uploader) validate(fileHeader *multipart.FileHeader) error {
	if err := u.validateFileSize(fileHeader); err != nil {
		return err
	}

	if err := u.validateFileType(fileHeader); err != nil {
		return err
	}

	return nil
}

func (u *Uploader) validateFileSize(fileHeader *multipart.FileHeader) error {
	if fileHeader.Size > constants.FileMaxSize {
		message := translator.Translate(nil, "errValidation_maxSizeImg", constants.FileMaxSize/1024/1024)
		return exceptions.NewBadRequestError(&message)
	}
	return nil
}

func (u *Uploader) validateFileType(fileHeader *multipart.FileHeader) error {
	file, err := fileHeader.Open()
	if err != nil {
		return err
	}
	defer file.Close()

	buffer := make([]byte, 512)

	_, err = file.Read(buffer)
	if err != nil && err != io.EOF {
		return err
	}
	file.Seek(0, io.SeekStart)

	filetype := http.DetectContentType(buffer)
	allowedTypes := map[string]bool{
		"image/jpeg": true,
		"image/png":  true,
		"image/gif":  true,
		"image/webp": true,
	}

	if !allowedTypes[filetype] {
		message := translator.Translate(nil, "ValidateFileFormat")
		return exceptions.NewBadRequestError(&message)
	}

	return nil
}

func (u *Uploader) uploadLocally(file *multipart.FileHeader, blobKey *string) (*UploadedBlob, error) {
	if blobKey != nil && *blobKey != "" {
		existingPath := filepath.Join(".", "tmp", "uploads", *blobKey)
		if _, err := os.Stat(existingPath); err == nil {
			if err := os.Remove(existingPath); err != nil {
				return nil, fmt.Errorf("failed to delete existing file: %v", err)
			}
		}
	} else {
		newKey := uuid.New().String()
		blobKey = &newKey
	}

	uploadDst := filepath.Join(".", "tmp", "uploads", *blobKey)
	err := u.Ctx.SaveUploadedFile(file, uploadDst)

	if err != nil {
		return nil, err
	}
	return &UploadedBlob{
		Url: "http://localhost:" + utils.GetEnv("APP_PORT", "3000") + "/uploads/" + *blobKey,
		Key: *blobKey,
	}, nil
}

func (u *Uploader) uploadToGCS(file *multipart.FileHeader, blobKey *string) (*UploadedBlob, error) {
	bucketName := os.Getenv("GCS_BUCKET_NAME")
	projectId := os.Getenv("GCS_PROJECT_ID")
	gcsAccountService := os.Getenv("GOOGLE_APPLICATION_CREDENTIALS")

	if bucketName == "" || projectId == "" || gcsAccountService == "" {
		return nil, errors.New("invalid Setting for Upload")
	}

	client, err := storage.NewClient(context.Background())
	if err != nil {
		return nil, err
	}

	uploadClient := &ClientUploader{
		cl:         client,
		bucketName: bucketName,
		uploadPath: u.UploadPath,
	}

	blobFile, err := file.Open()
	if err != nil {
		return nil, err
	}
	defer blobFile.Close()

	if blobKey == nil || strings.TrimSpace(*blobKey) == "" {
		newKey := uuid.New().String()
		blobKey = &newKey
	}

	err = uploadClient.UploadFile(blobFile, *blobKey)
	if err != nil {
		return nil, err
	}

	return &UploadedBlob{
		Url: fmt.Sprintf("https://storage.googleapis.com/%s/%s", bucketName, *blobKey),
		Key: *blobKey,
	}, nil
}

func (c *ClientUploader) UploadFile(file multipart.File, object string) error {
	ctx, cancel := context.WithTimeout(context.Background(), 50*time.Second)
	defer cancel()

	wc := c.cl.Bucket(c.bucketName).Object(c.uploadPath + object).NewWriter(ctx)
	defer wc.Close()

	if _, err := io.Copy(wc, file); err != nil {
		return fmt.Errorf("io.Copy: %v", err)
	}
	return nil
}

func (c *ClientUploader) DeleteFile(key string) {
	ctx := context.Background()
	o := c.cl.Bucket(c.bucketName).Object(key)
	ctx, cancel := context.WithTimeout(ctx, time.Second*10)
	defer cancel()

	o.Delete(ctx)
}

func (u *Uploader) UploadChunkFile(file *multipart.FileHeader, repos repository.IRepositories) (*UploadedBlob, error) {
	switch u.StorageService {
	case "google":
		redisClient := helpers.GetRedisClient()
		chunkUploader, err := NewChunkUploader(u.Ctx, redisClient, repos)
		if err != nil {
			return nil, err
		}
		return chunkUploader.UploadToGCS(file)
	default:
		return nil, fmt.Errorf("unsupported storage service: %s", u.StorageService)
	}
}
