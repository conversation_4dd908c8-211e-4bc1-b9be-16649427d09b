package uploaders

import (
	"errors"
	"fmt"
)

func (u *Uploader) DrillUpload() (*UploadedBlob, error) {
	err := u.Ctx.Request.ParseMultipartForm(10 << 20)
	if err != nil {
		return nil, err
	}

	form := u.Ctx.Request.MultipartForm
	files := form.File["files[]"]
	blobKeys := form.Value["key"]

	if len(files) == 0 || len(blobKeys) == 0 {
		return nil, errors.New("invalid drill upload form data")
	}

	file := files[0]
	blobKey := blobKeys[0]
	var uploadedBlob *UploadedBlob
	var uploadErr error

	if err := u.validate(file); err != nil {
		return nil, err
	}
	switch u.StorageService {
	case "local":
		uploadedBlob, uploadErr = u.uploadLocally(file, &blobKey)
	case "google":
		uploadedBlob, uploadErr = u.uploadToGCS(file, &blobKey)
	default:
		return nil, fmt.Errorf("unsupported storage service: %s", u.StorageService)
	}

	if uploadErr != nil {
		return nil, uploadErr
	}

	return uploadedBlob, nil
}
