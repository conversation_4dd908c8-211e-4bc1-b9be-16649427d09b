package translator

import (
	"fmt"
	"reflect"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
)

var ClientLanguage string

// TODO: fix language on request (ctx gin.context)
func Translate(language *string, constantName string, args ...interface{}) string {
	if ClientLanguage == "" {
		ClientLanguage = utils.GetEnv("CLIENT_LANGUAGE", "vi")
	}

	lang := ClientLanguage

	if language != nil {
		lang = *language
	}

	translations, found := supportedLanguages[lang]
	if !found {
		return "unsupported language"
	}

	// Using reflection to dynamically access the fields of the struct
	value := reflect.ValueOf(translations).FieldByName(constantName)
	if !value.IsValid() {
		return constantName
	}

	message := value.String()
	return fmt.Sprintf(message, args...)
}
