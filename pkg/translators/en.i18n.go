package translator

var (
	EnglishTranslations = Translations{
		general_invalidId:          "Invalid ID",
		general_createSuccess:      "Created",
		general_updateSuccess:      "Updated",
		general_changeStateSuccess: "State Changed",
		general_deleteSuccess:      "Deleted",
		general_error:              "An error has occurred",
		general_pleaseInputCorrect: "Please enter the correct input",
		general_success:            "Success",

		// INFO MESSAGES
		infoMsg_signInSuccess:                   "Sign In Successfully",
		infoMsg_signOutSuccess:                  "Sign Out Successfully",
		infoMsg_signUpSuccess:                   "Sign Up Successfully",
		infoMsg_createSuccess:                   "Create Successfully",
		infoMsg_updateSuccess:                   "Update Successfully",
		infoMsg_deleteSuccess:                   "Delete Successfully",
		infoMsg_joinCourseSuccess:               "Join Course Successfully",
		infoMsg_completeSuccess:                 "Completed",
		infoMsg_submitSuccess:                   "Submit Successfully",
		infoMsg_resentVerificationSmsSuccess:    "Resent Verification Sms Success",
		infoMsg_registrationVerificationSuccess: "Registration Verification Success",

		// Exception ERROR MESSAGES
		errExceptionMsg_badRequest:           "Bad Request",
		errExceptionMsg_unauthorized:         "Unauthorized",
		errExceptionMsg_forbidden:            "Forbidden",
		errExceptionMsg_unprocessableContent: "Unprocessable Content",
		errExceptionMsg_internal:             "Internal Server Error",

		// VALIDATION MESSAGES
		errValidationMsg_general:              "Errors Happened, Please check your input",
		errValidationMsg_required:             "is required",
		errValidationMsg_invalid:              "has invalid value",
		errValidation_wrongFormat:             "is wrong format",
		errValidation_minLength:               "is too short %d",
		errValidation_maxLength:               "is too long %d",
		errValidation_uniq:                    "already exists",
		errValidation_notExist:                "is not exist",
		errValidation_maxSizeImg:              "The maximum image size that can be uploaded is %dMB",
		errValidation_greaterThanInt:          "must be greater than %d",
		errValidation_greaterThanOrEqualInt:   "must be greater than or equal to %d",
		errValidation_lessThanOrEqualInt:      "must be less than or equal to %d",
		errValidation_lessThanOrEqualString:   "must be less than or equal to %s",
		errValidation_lessThanInt:             "must be less than %d",
		errValidation_greaterThanTime:         "must be after %s",
		errValidation_lessThanTime:            "must be before %s",
		errValidation_invalidJson:             "invalid json",
		errValidation_reachMaximum:            "Reached maximum",
		errValidation_lessThanOrEqualFloat:    "must be less than or equal to %.2f",
		errValidation_notDivisibleBy:          "must be divisible by %s",
		errValidation_validationIsPowerOf:     "must be power of %d",
		errValidation_notEqual:                "must be equal %d",
		errValidationMsg_passwordConfirmation: "Password Confirmation does not match",
		errValidationMsg_passwordFormat:       "Password must be at least 8 characters, including uppercase, lowercase and numbers.",

		errValidationMsg_status_invalid:              "Status is invalid",
		errValidationMsg_censor_invalid:              "Censor action is invalid",
		errValidationMsg_invalid_censoring_condition: "Does not satisfy censorship conditions",

		errValidationMsg_verifyCode_invalid:         "Verify code is invalid",
		errValidationMsg_tooManyRequest_invalid:     "Too many requests",
		errValidationMsg_phoneNumberHasBeenVerified: "Phone number has been verified",

		errValidation_existReviewInCourse: "You have rated teacher in this course before",
		// DB Error Messages
		errDbMsg_notFound:          "Not Found",
		errDbMsg_unexpected:        "Unexpected DB Error",
		errDbMsg_wrongPassword:     "Wrong Username Or Password",
		errDbMsg_IncorrectPassword: "Password is incorrect",

		// Course
		errDbMsg_AlreadyJoinCourse:        "You've already joined this course",
		errDbMsg_AlreadyCompleted:         "You've already completed this lesson",
		errDbMsg_UserAlreadyJoinCourse:    "This user has already joined this course",
		errDbMsg_UnverifiedCourse:         "Unverified course",
		errDbMsg_CanNotJoinYourOwnCourse:  "Can't join your own course",
		errDbMsg_CourseContentInvalid:     "Course content is incomplete, please check again",
		errDbMsg_CourseSectionInvalid:     "Course section can not be empty",
		errDbMsg_CourseSectionItemInvalid: "Course section item content is invalid",
		errDbMsg_CourseNotEnrolled:        "You haven't enrolled the course yet",
		errDbMsg_CourseNotCompleted:       "You haven't completed the course yet",
		//  Drill
		errDbMsg_duplicated:            "Duplicated drill",
		errMsg_InvalidDrill:            "Invalid drill",
		errDbMsg_CanNotBuyYourOwnDrill: "Can not buy your own drill",
		errDbMsg_AlreadyBoughtDrill:    "You've already bought this drill",
		errDbMsg_DrillInUser:           "This drill has been purchased and cannot be deleted.",

		// Template
		Name: "Name",

		// Diagram
		ImageUrl: "Image Url",

		// User
		PhoneNumber: "Phone Number",
		BirthDate:   "Date Of Birth",

		Password:        "Password",
		NewPassword:     "New password",
		ConfirmPassword: "Confirm password",

		// Skills
		SkillPositionPlay:   "Position Play",
		SkillShotmaking:     "Shotmaking",
		SkillKicking:        "Kicking",
		SkillBanking:        "Banking",
		SkillJumping:        "Jumping",
		SkillSafetyPlay:     "Safety Play",
		SkillMasseShots:     "Masse Shots",
		SkillBreaking:       "Breaking",
		SkillStraightStroke: "Straight Stroke",

		// DrillLevel
		DrillLevelBeginner:     "Beginner",
		DrillLevelIntermediate: "Intermediate",
		DrillLevelAdvanced:     "Advanced",
		DrillLevelExpert:       "Expert",

		// Drill Censor
		DrillCensorDraft:     "Draft",
		DrillCensorSubmitted: "Submitted",
		DrillCensorApproved:  "Approved",
		DrillCensorRejected:  "Rejected",

		// Drill Status
		DrillStatusPrivate: "Private",
		DrillStatusPublic:  "Public",

		// Course fields
		Title:             "Title",
		Description:       "Description",
		Price:             "Price",
		SalePrice:         "Sale Price",
		BonusPoint:        "Bonus Point",
		BonusPointPercent: "Bonus Point Percentage",
		SectionCount:      "Section Count",
		SectionItemCount:  "Lesson Count",
		Feedback:          "Feedback",

		ValidatePrice: "original price",

		// Course section item fields
		Content: "Content",

		// Course status
		CourseStatusDraft:     "Draft",
		CourseStatusSubmitted: "Submitted",
		CourseStatusApproved:  "Approved",
		CourseStatusRejected:  "Rejected",

		// Censor history status
		CensorStatusFeedback:  "Feedback",
		CensorStatusSubmitted: "Submitted",

		// Practice submission status
		PracticeSubmissionStatusNotSubmitted: "Not Submitted",
		PracticeSubmissionStatusSubmitted:    "Submitted",
		PracticeSubmissionStatusApproved:     "Approved",
		PracticeSubmissionStatusRejected:     "Rejected",

		VerifyTeacherInviteUser_Msg: "[Vibico Academy] You have been invited to join the course %s by instructor %s. \nIf you are interested, please visit the link below to join the course: \n%s \nYour verification code is: %s",

		// Course Instructional Level
		CourseInstructionalLevelBeginner:     "Beginner",
		CourseInstructionalLevelIntermediate: "Intermediate",
		CourseInstructionalLevelAdvanced:     "Advanced",
		CourseInstructionalLevelAllLevels:    "All Levels",
	}
)
