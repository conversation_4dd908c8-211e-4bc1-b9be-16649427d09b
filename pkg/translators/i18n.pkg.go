package translator

type Translations struct {
	general_invalidId          string
	general_createSuccess      string
	general_deleteSuccess      string
	general_updateSuccess      string
	general_changeStateSuccess string
	general_error              string
	general_pleaseInputCorrect string
	general_success            string

	// INFO MESSAGES
	infoMsg_signInSuccess                   string
	infoMsg_signOutSuccess                  string
	infoMsg_signUpSuccess                   string
	infoMsg_createSuccess                   string
	infoMsg_updateSuccess                   string
	infoMsg_deleteSuccess                   string
	infoMsg_joinCourseSuccess               string
	infoMsg_completeSuccess                 string
	infoMsg_submitSuccess                   string
	infoMsg_resentVerificationSmsSuccess    string
	infoMsg_registrationVerificationSuccess string

	// EXCEPTION ERROR MESSAGES
	errExceptionMsg_badRequest           string
	errExceptionMsg_unauthorized         string
	errExceptionMsg_forbidden            string
	errExceptionMsg_unprocessableContent string
	errExceptionMsg_internal             string

	// Validation MESSAGES
	errValidationMsg_general            string
	errValidationMsg_invalidEmailFormat string
	errValidationMsg_required           string
	errValidationMsg_invalid            string
	errValidation_wrongFormat           string
	errValidation_minLength             string
	errValidation_maxLength             string
	errValidation_uniq                  string
	errValidation_notExist              string
	errValidation_maxSizeImg            string
	errValidation_existReviewInCourse   string

	errValidation_greaterThanInt          string
	errValidation_greaterThanOrEqualInt   string
	errValidation_lessThanOrEqualInt      string
	errValidation_lessThanOrEqualString   string
	errValidation_lessThanInt             string
	errValidation_greaterThanTime         string
	errValidation_lessThanTime            string
	errValidation_invalidJson             string
	errValidation_reachMaximum            string
	errValidation_lessThanOrEqualFloat    string
	errValidation_notDivisibleBy          string
	errValidation_validationIsPowerOf     string
	errValidation_notEqual                string
	errValidationMsg_passwordConfirmation string
	errValidationMsg_passwordFormat       string

	errValidationMsg_status_invalid              string
	errValidationMsg_censor_invalid              string
	errValidationMsg_invalid_censoring_condition string

	errValidationMsg_verifyCode_invalid         string
	errValidationMsg_tooManyRequest_invalid     string
	errValidationMsg_phoneNumberHasBeenVerified string

	// DB Error Messages
	errDbMsg_notFound          string
	errDbMsg_unexpected        string
	errDbMsg_wrongPassword     string
	errDbMsg_IncorrectPassword string

	// Course error
	errDbMsg_AlreadyJoinCourse        string
	errDbMsg_AlreadyCompleted         string
	errDbMsg_UserAlreadyJoinCourse    string
	errDbMsg_UnverifiedCourse         string
	errDbMsg_CanNotJoinYourOwnCourse  string
	errDbMsg_CourseContentInvalid     string
	errDbMsg_CourseSectionInvalid     string
	errDbMsg_CourseSectionItemInvalid string
	errDbMsg_CourseNotEnrolled        string
	errDbMsg_CourseNotCompleted       string
	//  Drill error
	errDbMsg_duplicated            string
	errMsg_InvalidDrill            string
	errDbMsg_CanNotBuyYourOwnDrill string
	errDbMsg_AlreadyBoughtDrill    string
	errDbMsg_DrillInUser           string

	// Template
	Name string

	// Diagram
	ImageUrl string

	// User
	PhoneNumber string
	BirthDate   string

	// Password
	Password        string
	NewPassword     string
	ConfirmPassword string

	// Skills
	SkillPositionPlay   string
	SkillShotmaking     string
	SkillKicking        string
	SkillBanking        string
	SkillJumping        string
	SkillSafetyPlay     string
	SkillMasseShots     string
	SkillBreaking       string
	SkillStraightStroke string

	// DrillLevel
	DrillLevelBeginner     string
	DrillLevelIntermediate string
	DrillLevelAdvanced     string
	DrillLevelExpert       string

	// Drill Censor
	DrillCensorDraft     string
	DrillCensorSubmitted string
	DrillCensorApproved  string
	DrillCensorRejected  string

	// Drill Status
	DrillStatusPrivate string
	DrillStatusPublic  string

	// Course fields
	Title             string
	Description       string
	Price             string
	SalePrice         string
	BonusPoint        string
	BonusPointPercent string
	SectionCount      string
	SectionItemCount  string
	Feedback          string

	ValidatePrice string

	// Course section item fields
	Content string

	// Course status
	CourseStatusDraft     string
	CourseStatusSubmitted string
	CourseStatusApproved  string
	CourseStatusRejected  string

	// Censor history status
	CensorStatusFeedback  string
	CensorStatusSubmitted string

	// Practice submission status
	PracticeSubmissionStatusNotSubmitted string
	PracticeSubmissionStatusSubmitted    string
	PracticeSubmissionStatusApproved     string
	PracticeSubmissionStatusRejected     string

	// SMS
	VerifyTeacherInviteUser_Msg string

	// Course Instructional Level
	CourseInstructionalLevelBeginner     string
	CourseInstructionalLevelIntermediate string
	CourseInstructionalLevelAdvanced     string
	CourseInstructionalLevelAllLevels    string
}

var supportedLanguages = map[string]Translations{
	"en": EnglishTranslations,
	"vi": VietnameseTranslations,
}
