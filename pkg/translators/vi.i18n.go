package translator

var (
	VietnameseTranslations = Translations{
		general_invalidId:          "ID không hợp lệ",
		general_createSuccess:      "Đ<PERSON> tạo xong",
		general_updateSuccess:      "Đ<PERSON> cập nhật",
		general_changeStateSuccess: "Trạng thái đã thay đổi",
		general_deleteSuccess:      "Đã xóa",
		general_error:              "Đã xảy ra lỗi",
		general_pleaseInputCorrect: "Vui lòng nhập thông tin chính xác",
		general_success:            "Thành công",

		// INFO MESSAGES
		infoMsg_signInSuccess:                   "Đăng Nhập Thành Công",
		infoMsg_signOutSuccess:                  "Đăng Xuất Thành Công",
		infoMsg_signUpSuccess:                   "Đăng Ký Thành Công",
		infoMsg_createSuccess:                   "Tạo thành công",
		infoMsg_updateSuccess:                   "Cập nhật thành công",
		infoMsg_deleteSuccess:                   "<PERSON><PERSON><PERSON> thành công",
		infoMsg_joinCourseSuccess:               "Tham gia kho<PERSON> học",
		infoMsg_completeSuccess:                 "Hoàn thành",
		infoMsg_submitSuccess:                   "Đã nộp thành công",
		infoMsg_resentVerificationSmsSuccess:    "Đã gửi lại mã xác nhận sms thành công",
		infoMsg_registrationVerificationSuccess: "Đăng ký Xác minh Thành công",

		// ERROR MESSAGES
		errExceptionMsg_badRequest:           "Yêu Cầu Không Hợp Lệ",
		errExceptionMsg_unauthorized:         "Không Được Phép",
		errExceptionMsg_forbidden:            "Không Có Quyền",
		errExceptionMsg_unprocessableContent: "Nội Dung Không Thể Xử Lý",
		errExceptionMsg_internal:             "Lỗi Máy Chủ",

		// VALIDATION MESSAGES
		errValidationMsg_general:              "Đã có lỗi xảy ra, vui lòng kiểm tra lại dữ liệu bạn đã nhập",
		errValidationMsg_invalidEmailFormat:   "Email không hợp lệ",
		errValidationMsg_required:             "bắt buộc",
		errValidationMsg_invalid:              "có giá trị ko hợp lệ",
		errValidation_wrongFormat:             "không hợp lệ",
		errValidation_minLength:               "có độ dài tối thiểu là %d",
		errValidation_maxLength:               "có độ dài tối đa là %d",
		errValidation_uniq:                    "Đã tồn tại",
		errValidation_notExist:                "Không tồn tại",
		errValidation_maxSizeImg:              "Kích thước hình ảnh tối đa có thể được tải lên là %dMB",
		errValidation_greaterThanInt:          "phải lớn hơn %d",
		errValidation_greaterThanOrEqualInt:   "phải lớn hơn hoặc bằng %d",
		errValidation_lessThanOrEqualInt:      "phải nhỏ hơn hoặc bằng %d",
		errValidation_lessThanOrEqualString:   "phải nhỏ hơn hoặc bằng %s",
		errValidation_lessThanInt:             "phải nhỏ hơn %d",
		errValidation_greaterThanTime:         "phải sau %s",
		errValidation_lessThanTime:            "phải trước %s",
		errValidation_invalidJson:             "json không hợp lệ",
		errValidation_reachMaximum:            "Đã đạt đến giới hạn",
		errValidation_lessThanOrEqualFloat:    "phải nhỏ hơn hoặc bằng %.2f",
		errValidation_notDivisibleBy:          "phải chia hết cho %s",
		errValidation_validationIsPowerOf:     "phải là luỹ thừa của %d",
		errValidation_notEqual:                "phải bằng %d",
		errValidationMsg_passwordConfirmation: "không khớp",
		errValidationMsg_passwordFormat:       "phải có ít nhất 8 ký tự, bao gồm chữ hoa, chữ thường và số.",

		errValidationMsg_status_invalid:              "Trạng thái không hợp lệ",
		errValidationMsg_censor_invalid:              "Thao tác kiểm duyệt không hợp lệ",
		errValidationMsg_invalid_censoring_condition: "Không thoả mãn điều kiện kiểm duyệt",

		errValidationMsg_verifyCode_invalid:         "Mã xác thực không hợp lệ",
		errValidationMsg_tooManyRequest_invalid:     "Yêu cầu gửi quá nhiều",
		errValidationMsg_phoneNumberHasBeenVerified: "Số điện thoại đã được xác minh",

		errValidation_existReviewInCourse: "Bạn đã đánh giá giáo viên trong khoá học này trước đó",
		// DB Error Messages
		errDbMsg_notFound:          "Không tìm được",
		errDbMsg_unexpected:        "Lỗi DB không mong đợi",
		errDbMsg_wrongPassword:     "Tên người dùng hoặc mật khẩu không chính xác",
		errDbMsg_IncorrectPassword: "Mật khẩu không chính xác",

		// Course
		errDbMsg_AlreadyJoinCourse:        "Bạn đã tham gia khóa học này",
		errDbMsg_AlreadyCompleted:         "Bạn đã hoàn thành bài học này trước đó",
		errDbMsg_UserAlreadyJoinCourse:    "Người dùng này đã tham gia hoá học từ trước",
		errDbMsg_UnverifiedCourse:         "Khoá học chưa được kiểm duyệt",
		errDbMsg_CanNotJoinYourOwnCourse:  "Không thể tham gia khóa học của chính bạn",
		errDbMsg_CourseContentInvalid:     "Nội dung học phần không đầy đủ, vui lòng kiểm tra lại",
		errDbMsg_CourseSectionInvalid:     "Nội dung học phần không được để trống",
		errDbMsg_CourseSectionItemInvalid: "Nội dung bài học không được để trống ",
		errDbMsg_CourseNotEnrolled:        "Bạn chưa tham gia khoá học",
		errDbMsg_CourseNotCompleted:       "Bạn chưa hoàn thành khoá học",
		// Drill
		errDbMsg_duplicated:            "Hình bi này đã tồn tại",
		errMsg_InvalidDrill:            "Hình bi Không Hợp Lệ",
		errDbMsg_CanNotBuyYourOwnDrill: "Không thể mua hình bi của chính bạn",
		errDbMsg_AlreadyBoughtDrill:    "Bạn đã mua hình bi này",
		errDbMsg_DrillInUser:           "Đã có người mua, hình bi này không thể xoá",

		// Template
		Name: "Tên",

		// Diagram
		ImageUrl: "URL hình ảnh",

		// User
		PhoneNumber: "Số điện thoại",
		BirthDate:   "Ngày sinh",

		Password:        "Mật khẩu",
		NewPassword:     "Mật khẩu mới",
		ConfirmPassword: "Mật khẩu xác nhận",

		// Skills
		SkillPositionPlay:   "Điều Bóng",
		SkillShotmaking:     "Kỹ Năng Thực Hiện Cú Đánh",
		SkillKicking:        "Gián Bi",
		SkillBanking:        "Băng Bi",
		SkillJumping:        "Nhảy Bi",
		SkillSafetyPlay:     "Đánh Phòng Thủ",
		SkillMasseShots:     "Đánh Xoáy Masse",
		SkillBreaking:       "Phá Bóng",
		SkillStraightStroke: "Đánh Thẳng",

		// DrillLevel
		DrillLevelBeginner:     "Bắt Đầu",
		DrillLevelIntermediate: "Trung Bình",
		DrillLevelAdvanced:     "Nâng Cao",
		DrillLevelExpert:       "Chuyên Gia",

		// Drill Censor
		DrillCensorDraft:     "Nháp",
		DrillCensorSubmitted: "Đã gửi",
		DrillCensorApproved:  "Đã phê duyệt",
		DrillCensorRejected:  "Đã từ chối",

		// Drill Status
		DrillStatusPrivate: "Riêng tư",
		DrillStatusPublic:  "Công khai",

		// Course fields
		Title:             "Tiêu đề",
		Description:       "Mô tả",
		Price:             "Giá",
		SalePrice:         "Giá khuyến mãi",
		BonusPoint:        "Điểm thưởng",
		BonusPointPercent: "Phần trăm điểm thưởng",
		SectionCount:      "Số học phần",
		SectionItemCount:  "Số bài học",
		Feedback:          "Phản hồi",

		ValidatePrice: "giá gốc",

		// Course section item fields
		Content: "Nội dung",

		// Course status
		CourseStatusDraft:     "Nháp",
		CourseStatusSubmitted: "Đã gửi",
		CourseStatusApproved:  "Đã phê duyệt",
		CourseStatusRejected:  "Đã từ chối",

		// Censor history status
		CensorStatusFeedback:  "Phản hồi",
		CensorStatusSubmitted: "Gửi duyệt",

		// Practice submission status
		PracticeSubmissionStatusNotSubmitted: "Chưa nộp",
		PracticeSubmissionStatusSubmitted:    "Đã nộp",
		PracticeSubmissionStatusApproved:     "Đã duyệt",
		PracticeSubmissionStatusRejected:     "Đã từ chối",

		// SMS
		VerifyTeacherInviteUser_Msg: "[Vibico Academy] Bạn đã được mời tham gia khoá học %s của giảng viên %s. \nNếu quan tâm, truy cập đường link sau đây để tham gian khoá học: \n%s \nMã xác thực của bạn là: %s",

		// Course Instructional Level
		CourseInstructionalLevelBeginner:     "Cơ bản",
		CourseInstructionalLevelIntermediate: "Trung cấp",
		CourseInstructionalLevelAdvanced:     "Nâng cao",
		CourseInstructionalLevelAllLevels:    "Mọi cấp độ",
	}
)
