package helpers

import (
	"bytes"
	"encoding/json"
	"io"
	"net/url"
	"regexp"
	"strings"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/gin-gonic/gin"
)

func GetRequestBody(ctx *gin.Context) (map[string]any, error) {
	var requestBody map[string]any

	bodyString, err := ExtractStringBody(ctx)
	if err != nil {
		return requestBody, err
	}

	// bodyString = BlurSensitiveData(bodyString)

	if err := json.Unmarshal([]byte(bodyString), &requestBody); err != nil {
		return requestBody, err
	}

	return requestBody, nil
}

func ExtractStringBody(c *gin.Context) (string, error) {
	bodyBytes, err := io.ReadAll(c.Request.Body)
	if err != nil {
		return "", err
	}

	c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
	return string(bodyBytes), nil
}

func BlurSensitiveData(body string) string {
	sensitiveFields := []string{
		"password", "confirmPassword", "oldPassword", "newPassword", "passwordConfirmation",
		"securityAnswer", "cvv", "cvc", "creditCardNumber", "token",
		"accessToken", "refreshToken", "apiKey", "secretKey",
		"socialSecurityNumber", "nationalId", "passportNumber",
		"driverLicenseNumber", "bankAccountNumber", "iban",
		"routingNumber", "medicalRecordNumber", "insurancePolicyNumber",
	}

	var regexPattern string
	for _, field := range sensitiveFields {
		if regexPattern != "" {
			regexPattern += "|"
		}
		regexPattern += `("` + field + `":")(.*?)(\"|$)`
	}
	re := regexp.MustCompile(regexPattern)

	return re.ReplaceAllString(body, `$1******$3`)
}

func BlurSensitiveDataMap(data map[string]any) map[string]any {
	sensitiveFields := map[string]struct{}{
		"password": {}, "confirmPassword": {}, "oldPassword": {}, "newPassword": {}, "passwordConfirmation": {},
		"securityAnswer": {}, "cvv": {}, "cvc": {}, "creditCardNumber": {}, "token": {},
		"accessToken": {}, "refreshToken": {}, "apiKey": {}, "secretKey": {},
		"socialSecurityNumber": {}, "nationalId": {}, "passportNumber": {},
		"driverLicenseNumber": {}, "bankAccountNumber": {}, "iban": {},
		"routingNumber": {}, "medicalRecordNumber": {}, "insurancePolicyNumber": {},
	}
	for k, v := range data {
		if _, ok := sensitiveFields[k]; ok {
			data[k] = "******"
			continue
		}

		if subMap, ok := v.(map[string]any); ok {
			data[k] = BlurSensitiveDataMap(subMap)
			continue
		}

		if arr, ok := v.([]any); ok {
			for i, item := range arr {
				if subMap, ok := item.(map[string]any); ok {
					arr[i] = BlurSensitiveDataMap(subMap)
				}
			}
			data[k] = arr
		}
	}
	return data
}

func GqlQueryCamelToPascalCase(s string) string {
	parts := strings.Split(s, ".")
	for i, part := range parts {
		if utils.IsCamelCase(part) {
			parts[i] = strings.ToUpper(string(part[0])) + part[1:]
		}
	}
	return strings.Join(parts, ".")
}

func MakeUrlWithParams(baseUrl string, params map[string]string) string {
	url, err := url.Parse(baseUrl)
	if err != nil {
		return ""
	}

	query := url.Query()
	for key, value := range params {
		query.Add(key, value)
	}

	url.RawQuery = query.Encode()

	return url.String()
}
