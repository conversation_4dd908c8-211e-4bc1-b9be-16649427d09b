package helpers

// `^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[a-zA-Z]).{8,}$`
// The regexp package in Go, which utilizes the RE2 regex engine,
// does not directly support lookahead or lookbehind assertions
// (e.g., (?=...), (?!...), (?<=...), (?<!...))
func IsValidPassword(password string) bool {
	var hasMinLen, hasUpper, hasLower, hasNumber bool
	if len(password) >= 8 {
		hasMinLen = true
	}
	for _, c := range password {
		switch {
		case 'A' <= c && c <= 'Z':
			hasUpper = true
		case 'a' <= c && c <= 'z':
			hasLower = true
		case '0' <= c && c <= '9':
			hasNumber = true
		}
	}
	return hasMinLen && hasUpper && hasLower && hasNumber
}
