package helpers

import (
	"regexp"

	"github.com/BehemothLtd/behemoth-pkg/golang/constants"
)

func ConvertPhoneNumber(phoneNumber string) string {
	if phoneNumber == "" {
		return ""
	}

	inputRegex := regexp.MustCompile(constants.PhoneNumberFormat)

	if inputRegex.MatchString(phoneNumber) {
		if phoneNumber[0] == '0' {
			return "+84" + phoneNumber[1:]
		} else {
			return phoneNumber
		}
	} else {
		return ""
	}
}
