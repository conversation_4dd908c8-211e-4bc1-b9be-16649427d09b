package helpers

import (
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"strconv"
	"time"

	"math/rand"

	"github.com/BehemothLtd/behemoth-pkg/golang/constants"
	"github.com/BehemothLtd/behemoth-pkg/golang/utils/slug"
)

func GenerateSlug(title string) string {
	baseSlug := slug.Make(title)

	// Create a hash based on title + timestamp
	timestamp := time.Now().UnixNano()
	hashInput := fmt.Sprintf("%s%d", title, timestamp)
	hasher := sha256.New()
	hasher.Write([]byte(hashInput))
	hash := hex.EncodeToString(hasher.Sum(nil))[:12]

	// Ensure slug doesn't exceed maximum length
	if len(baseSlug) > (constants.MaxStringLength - len(hash) - 1) {
		baseSlug = baseSlug[:constants.MaxStringLength-len(hash)-2]
	}

	slug := fmt.Sprintf("%s-%s", baseSlug, hash)
	return slug
}

func RandomDigits(length int) string {
	seed := time.Now().UnixNano()
	seededRand := rand.New(rand.NewSource(seed))

	result := ""
	for i := 0; i < length; i++ {
		result += strconv.Itoa(seededRand.Intn(10))
	}
	return result
}
