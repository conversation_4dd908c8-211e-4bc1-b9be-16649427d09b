package helpers

import (
	"encoding/base64"
	"fmt"
	"strings"
)

func DecodeBase64(s string) ([]byte, error) {
	if s == "" {
		return nil, fmt.Erro<PERSON>("base64 string is empty")
	}

	s = strings.Trim(s, " \n\r\t\"'")

	s = strings.TrimRight(s, "=")

	switch len(s) % 4 {
	case 2:
		s += "=="
	case 3:
		s += "="
	}

	decoded, err := base64.StdEncoding.DecodeString(s)
	if err != nil {
		decoded, err = base64.URLEncoding.DecodeString(s)
		if err != nil {
			return nil, fmt.Errorf("failed to decode base64 string: %w", err)
		}
	}

	if len(decoded) == 0 {
		return nil, fmt.<PERSON><PERSON><PERSON>("decoded base64 string is empty")
	}

	return decoded, nil
}
