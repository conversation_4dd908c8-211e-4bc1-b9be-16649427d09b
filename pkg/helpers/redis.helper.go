package helpers

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"strconv"
	"time"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/redis/go-redis/v9"
)

type RedisCfg struct {
	Addr     string
	Password string
	DB       int
}

var (
	redisConfig   *RedisCfg
	redisInstance *redis.Client
	ctx           = context.Background()
)

func initRedisConfig() *RedisCfg {
	if redisConfig != nil {
		return redisConfig
	}

	addr := utils.GetEnv("REDIS_ADDR", "localhost:6379")
	password := utils.GetEnv("REDIS_PASSWORD", "")
	dbStr := utils.GetEnv("REDIS_DB_ASYNC", "0")
	db, err := strconv.Atoi(dbStr)
	if err != nil {
		db = 0
	}

	redisConfig = &RedisCfg{
		Addr:     addr,
		Password: password,
		DB:       db,
	}

	return redisConfig
}

func GetRedisConfig() *RedisCfg {
	if redisConfig == nil {
		return initRedisConfig()
	}
	return redisConfig
}

func InitRedisClient() *redis.Client {
	if redisInstance != nil {
		return redisInstance
	}

	cfg := GetRedisConfig()

	redisInstance = redis.NewClient(&redis.Options{
		Addr:     cfg.Addr,
		Password: cfg.Password,
		DB:       cfg.DB,
	})

	if err := redisInstance.Ping(ctx).Err(); err != nil {
		log.Fatalf("Failed to connect to Redis: %v", err)
	}

	return redisInstance
}

func GetRedisClient() *redis.Client {
	if redisInstance == nil {
		return InitRedisClient()
	}
	return redisInstance
}

func GetFromRedis[T any](key string) (*T, error) {
	if redisInstance == nil {
		return nil, errors.New("redis connection not initialized")
	}

	val, err := redisInstance.Get(ctx, key).Result()
	if err == redis.Nil {
		return nil, nil
	} else if err != nil {
		return nil, err
	}

	var result T
	if err := json.Unmarshal([]byte(val), &result); err != nil {
		return nil, fmt.Errorf("failed to unmarshal data from Redis: %w", err)
	}

	return &result, nil
}

func StoreInRedis[T any](key string, data T, TTL time.Duration) error {
	if redisInstance == nil {
		return errors.New("redis connection not initialized")
	}
	dataJSON, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("failed to marshal data: %v", err)
	}

	if err := redisInstance.Set(ctx, key, dataJSON, TTL).Err(); err != nil {
		return fmt.Errorf("failed to set data in Redis: %v", err)
	}

	return nil
}
