# Billiard Community API

This is the backend API for the Billiard Community application, built with Go and GraphQL.

## Getting Started

### Prerequisites

- Go 1.18+
- PostgreSQL 12+
- Add env GOPRIVATE to your .zshrc or .bashrc

```bash
export GOPRIVATE="github.com/kero-chan/*,github.com/BehemothLtd/*"
```

- Config use git ssh instead of https

```bash
git config --global url."**************:".insteadOf "https://github.com/"
```

### Installation

```sh
make deps # Install dependencies
cp .env.example .env # Set up environment variables. Update the values in .env as needed.
```

### Database migrations

```sh
# DB
make db.create
make db.migrate

# Schema
make schema.create
make schema.migrate
```

### Dev command

```sh
make dev-air # Start the development server
make update-dependencies # To update dependencies
make gql.make # Generate the GraphQL schema
```

The API server will be running at `http://localhost:3000` by default. You can change the port by setting the `APP_PORT` environment variable in your `.env` file.

### Core Commands

- `make deps`: Install required tools and dependencies
- `make install-deps`: Install migrate, air, and go-enum tools
- `make db.migrate`: Run database migrations
- `make db.rollback`: Rollback the last database migration
- `make db.reset`: Drop and recreate the database, run migrations
- `make db.seed`: Seed the database with initial data
- `make schema.create`: Create database schema
- `make schema.drop`: Drop database schema
- `make schema.migrate`: Run database schema migrations
- `make gql.make`: Generate the GraphQL schema
- `make up`: Start the development server with hot reload
- `make go-generate`: Run Go generate for the project
- `make enum-generate`: Generate Go enum file (requires filepath input)
- `make enum-int-generate`: Generate Go enum file with SQL int support
- `make docker-build`: Build Docker image
- `make docker-push`: Push Docker image to registry
- `make k8s-release`: Update deployment image in Kubernetes
- `make builder-deploy`: Deploy builder pod to Kubernetes cluster

### Dev Database Migration Workflow

To support development, when you need to **add columns or modify existing tables**, please follow these steps:

1. **Add new tables or columns as usual** in the main migration files (in `setup/migrations/`).
2. **Create an additional SQL file** for your changes in the `setup/migrations/devs/` folder.
   - Name the file clearly, for example: `1_add_column_to_table.sql`.
   - Only include the necessary SQL statements for your change (typically `ALTER TABLE` to add/remove columns, or `UPDATE` data in existing tables).  
     If you need to add a new table, do it in the main migration files as usual.

**Reason:**  
When other team members pull the code, they just need to run the files in `setup/migrations/devs/` to quickly update their local dev database, avoiding errors due to missing columns or fields before the main migration is applied.

**How to run dev migrations:**

- Normally, you **just need to run `make up`**. The dev-only migrations will be applied automatically when starting the server.
- If you want to run manually, use:
  ```sh
  make schema.migrate-dev
  ```
- To rollback dev migrations, use:
  ```sh
  make schema.rollback-dev
  ```

> **Note:**
>
> - Once the main migration has been merged and applied to production, please delete the corresponding files in `devs` to avoid duplication.
> - This convention is for development environments only.

---
