FROM golang:1.23.6-bullseye as base

# ARG SSH_PRIVATE_KEY=""

WORKDIR /app
COPY go.mod go.sum ./
COPY id_rsa /root/.ssh/id_rsa
RUN mkdir -p ~/.ssh && \
    chmod 700 ~/.ssh && \
    # echo "$SSH_PRIVATE_KEY" > ~/.ssh/id_rsa && \
    chmod 400 ~/.ssh/id_rsa && \
    ssh-keyscan github.com >> ~/.ssh/known_hosts && \
    git config --global url."ssh://**************/".insteadOf "https://github.com/" && \
    go mod download && \
    rm -f ~/.ssh/id_rsa

FROM base as builder

WORKDIR /app

RUN apt-get update && \
    apt-get install -y \
    redis \
    redis-tools \
    postgresql \
    postgresql-client \
    make \
    tar \
    gzip \
    nano \
    vim \
    netcat \
    net-tools \
    telnet \
    iputils-ping \
    ffmpeg \
    upx && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

COPY Makefile ./
COPY misc/make/tools.Makefile ./misc/make/
COPY misc/make/help.Makefile ./misc/make/

COPY . .

RUN make gql.make

RUN CGO_ENABLED=0 GOOS=linux GO111MODULE=on go build -ldflags "-s -w" -a -trimpath -o apiserver ./cmd/apiserver && \
    upx -9 apiserver

RUN CGO_ENABLED=0 GOOS=linux GO111MODULE=on go build -ldflags "-s -w" -a -trimpath -o makeSchema ./cmd/gql && \
    upx -9 makeSchema

RUN CGO_ENABLED=0 GOOS=linux GO111MODULE=on go build -ldflags "-s -w" -a -trimpath -o seed ./cmd/seed && \
    upx -9 seed

RUN CGO_ENABLED=0 GOOS=linux GO111MODULE=on go build -ldflags "-s -w" -a -trimpath -o asynq ./cmd/asynq && \
    upx -9 asynq

RUN CGO_ENABLED=0 GOOS=linux GO111MODULE=on go build -ldflags "-s -w" -a -trimpath -o cronjob ./cmd/cronjob && \
    upx -9 cronjob

FROM debian:12-slim

ENV GIN_MODE=release

WORKDIR /app

RUN apt-get update && \
    apt-get install -y ffmpeg && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

COPY --from=builder /app/apiserver ./apiserver
COPY --from=builder /app/makeSchema ./makeSchema
COPY --from=builder /app/seed ./seed
COPY --from=builder /app/asynq ./asynq
COPY --from=builder /app/cronjob ./cronjob
COPY assets/watermark.png ./assets/watermark.png

CMD ["/app/apiserver"]
