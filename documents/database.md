# Cấu Trúc Database Vibico Education

## Tổng Quan
The database is designed to serve the online education system, including functions for managing courses, teachers, students and practice exercises.

## Sơ Đồ Quan Hệ

```mermaid
erDiagram
    Users ||--o{ UserPlans : has
    Users ||--o{ UserDrills : practices
    Users ||--o{ CourseUsers : enrolls
    Users ||--o{ UserCourseSections : tracks
    Users ||--o{ Comments : makes

    Teachers ||--o{ Courses : creates

    Courses ||--o{ CourseSections : contains
    Courses ||--o{ CourseUsers : has

    CourseSections ||--o{ CourseSectionItems : contains
    CourseSections ||--o{ UserCourseSections : tracked_by

    CourseSectionItems ||--o{ CourseSectionItemDrills : has

    Drills ||--o{ UserDrills : practiced_by
    Drills ||--o{ DrillSkills : requires
    Drills ||--o{ DrillVideos : includes
    Drills ||--o{ CourseSectionItemDrills : used_in

    Skills ||--o{ DrillSkills : used_in

    Plans ||--o{ UserPlans : subscribed_by
    UserPlans ||--o{ UserSubscriptions : has

    Tags ||--o{ Taggings : used_in

    TemplateDiagrams ||--o{ Diagrams : based_on

    Users {
        bigserial id PK
        varchar auth_id UK
        varchar name
        date birth_date
        varchar gender
        varchar image_url
    }

    Teachers {
        bigserial id PK
        varchar auth_id UK
        varchar name
        varchar phone_number
        varchar contact_email
        varchar description
        varchar award
        varchar address
        varchar image_url
        boolean basic_entered
    }

    Courses {
        bigserial id PK
        bigint teacher_id FK
        varchar title
        varchar slug UK
        text description
        smallint status
        integer sale_price
        integer price
        integer bonus_point
        integer bonus_point_percent
        integer section_count
        varchar banner
    }

    CourseSections {
        bigserial id PK
        bigint course_id FK
        varchar slug
        varchar title
        integer position
    }

    CourseSectionItems {
        bigserial id PK
        bigint course_section_id FK
        varchar title
        varchar slug
        smallint type
        integer position
        text content
    }

    Drills {
        bigserial id PK
        varchar title
        text description
        smallint level
        smallint status
        jsonb tags
        jsonb step
        integer sale_price
        integer price
        bigint owner_id
        varchar owner_type
    }

    Skills {
        bigserial id PK
        varchar name
        text description
    }

    Plans {
        bigserial id PK
        varchar name
        decimal price
        jsonb options
    }

    UserPlans {
        bigserial id PK
        bigint plan_id FK
        bigint user_id FK
        integer price
        timestamptz start_time
        timestamptz end_time
        jsonb options
    }

    UserSubscriptions {
        bigserial id PK
        bigint user_plan_id FK
        integer circle
        timestamptz scheduled_to_be_renew
        smallint status
        jsonb options
    }

    Tags {
        bigserial id PK
        varchar name
    }

    Taggings {
        bigserial id PK
        bigint tag_id FK
        bigint parent_id
        varchar parent_type
    }

    Comments {
        bigserial id PK
        bigint user_id FK
        bigint parent_id
        varchar parent_type
        text content
    }

    Diagrams {
        bigserial id PK
        bigint parent_id
        varchar parent_type
        varchar image_url
        integer position
        jsonb setting
    }

    TemplateDiagrams {
        bigserial id PK
        varchar name
    }

    DrillSkills {
        bigserial id PK
        bigint drill_id FK
        bigint skill_id FK
    }

    UserDrills {
        bigserial id PK
        bigint drill_id FK
        bigint user_id FK
    }

    DrillVideos {
        bigserial id PK
        bigint drill_id FK
        varchar video_url
    }

    CourseUsers {
        bigserial id PK
        bigint user_id FK
        bigint course_id FK
        jsonb course_user_metadata
    }

    UserCourseSections {
        bigserial id PK
        bigint user_id FK
        bigint course_section_id FK
        jsonb data
    }

    CourseSectionItemDrills {
        bigserial id PK
        bigint course_section_item_id FK
        bigint drill_id FK
    }
```
