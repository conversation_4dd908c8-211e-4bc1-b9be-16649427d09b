-- Vibico Education Database Schema V1\n
-- Generated at: Fri Jun 27 15:17:01 +07 2025\n
-- File: setup/migrations/000001_create_users.up.sql\n
CREATE TABLE users (
  id BIGSERIAL PRIMARY KEY,
  auth_id VARCHAR(255) NOT NULL,
  name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
  phone_number VARCHAR(255),
  birth_date DATE,
  gender VARCHAR(255),
  image_url VARCHAR(255),
  active BOOLEAN NOT NULL DEFAULT TRUE,
  created_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE UNIQUE INDEX idx_users_auth_id ON users (auth_id);

CREATE UNIQUE INDEX idx_users_phone_number ON users (phone_number);

-- File: setup/migrations/000002_create_plans.up.sql\n
CREATE TABLE IF NOT EXISTS plans (
  id BIGSERIAL PRIMARY KEY,
  name <PERSON><PERSON>HA<PERSON>(255) NOT NULL,
  price DECIMAL(12, 2),
  options JSONB,
  created_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- File: setup/migrations/000003_create_user_subscriptions.up.sql\n
CREATE TABLE IF NOT EXISTS user_subscriptions (
  id BIGSERIAL PRIMARY KEY,
  user_plan_id BIGINT NOT NULL,
  circle INTEGER,
  scheduled_to_be_renew TIMESTAMPTZ (6),
  status SMALLINT NOT NULL,
  options JSONB,
  created_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- File: setup/migrations/000004_create_user_plans.up.sql\n
CREATE TABLE IF NOT EXISTS user_plans (
  id BIGSERIAL PRIMARY KEY,
  plan_id BIGINT NOT NULL,
  user_id BIGINT NOT NULL,
  price INTEGER,
  start_time TIMESTAMPTZ (6),
  end_time TIMESTAMPTZ (6),
  options JSONB,
  created_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- File: setup/migrations/000005_create_authens.up.sql\n
CREATE TABLE IF NOT EXISTS authens (
  id BIGSERIAL PRIMARY KEY,
  username VARCHAR(255) NOT NULL,
  phone VARCHAR(20),
  email VARCHAR(255),
  encrypted_password VARCHAR(255) NOT NULL,
  created_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE UNIQUE INDEX idx_authens_username ON authens (username);

CREATE UNIQUE INDEX idx_authens_email ON authens (email)
WHERE
  email IS NOT NULL;

CREATE UNIQUE INDEX idx_authens_phone ON authens (phone)
WHERE
  phone IS NOT NULL;

-- File: setup/migrations/000006_create_teachers.up.sql\n
CREATE TABLE IF NOT EXISTS teachers (
  id BIGSERIAL PRIMARY KEY,
  auth_id VARCHAR(255) NOT NULL,
  name VARCHAR(255) NOT NULL,
  slug VARCHAR(255) NOT NULL,
  phone_number VARCHAR(255),
  contact_email VARCHAR(255),
  description VARCHAR(255),
  award VARCHAR(255),
  address VARCHAR(255),
  image_url VARCHAR(255),
  can_invite_students BOOLEAN NOT NULL DEFAULT FALSE,
  basic_entered BOOLEAN NOT NULL DEFAULT FALSE,
  active BOOLEAN NOT NULL DEFAULT TRUE,
  approved_course_count INTEGER NOT NULL DEFAULT 0,
  student_count INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE UNIQUE INDEX idx_teachers_auth_id ON teachers (auth_id);

CREATE UNIQUE INDEX idx_teachers_phone_number ON teachers (phone_number);

CREATE UNIQUE INDEX idx_teachers_contact_email ON teachers (contact_email);

CREATE UNIQUE INDEX idx_teachers_slug ON teachers (slug);

-- File: setup/migrations/000007_create_tags.up.sql\n
CREATE TABLE IF NOT EXISTS tags (
  id BIGSERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  created_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- File: setup/migrations/000008_create_taggings.up.sql\n
CREATE TABLE IF NOT EXISTS taggings (
  id BIGSERIAL PRIMARY KEY,
  tag_id BIGINT NOT NULL,
  parent_id BIGINT NOT NULL,
  parent_type VARCHAR(50) NOT NULL,
  created_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- File: setup/migrations/000009_create_comments.up.sql\n
CREATE TABLE IF NOT EXISTS comments (
  id BIGSERIAL PRIMARY KEY,
  parent_id BIGINT REFERENCES comments (id) ON DELETE CASCADE,
  author_id BIGINT NOT NULL,
  author_type VARCHAR(50) NOT NULL,
  target_id BIGINT NOT NULL,
  target_type VARCHAR(50) NOT NULL,
  content TEXT NOT NULL,
  rating DECIMAL(2, 1),
  created_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_comments_author ON comments (author_type, author_id);

CREATE INDEX idx_comments_target ON comments (target_type, target_id);

CREATE INDEX idx_comments_parent ON comments (parent_id);

-- File: setup/migrations/000010_create_diagrams.up.sql\n
CREATE TABLE IF NOT EXISTS diagrams (
  id BIGSERIAL PRIMARY KEY,
  parent_id BIGINT NOT NULL,
  parent_type VARCHAR(50) NOT NULL,
  image_url VARCHAR(255),
  position INTEGER NOT NULL DEFAULT 1,
  setting JSONB,
  created_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_diagrams_parent ON diagrams (parent_type, parent_id);

-- File: setup/migrations/000011_create_template_diagrams.up.sql\n
CREATE TABLE IF NOT EXISTS template_diagrams (
  id BIGSERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  created_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- File: setup/migrations/000012_create_drills.up.sql\n
CREATE TABLE IF NOT EXISTS drills (
  id BIGSERIAL PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  slug VARCHAR(255) NOT NULL,
  description TEXT,
  level SMALLINT NOT NULL DEFAULT 1,
  status SMALLINT NOT NULL DEFAULT 0,
  censor SMALLINT NOT NULL DEFAULT 0,
  tags JSONB,
  step JSONB,
  sale_price INTEGER,
  price INTEGER,
  is_master BOOLEAN NOT NULL,
  owner_id BIGINT NOT NULL,
  owner_type VARCHAR(50) NOT NULL,
  created_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_drills_owner ON drills (owner_id, owner_type);

CREATE INDEX idx_drills_slug ON drills (slug);

CREATE INDEX idx_drills_created_at ON drills (created_at);

-- File: setup/migrations/000013_create_skills.up.sql\n
CREATE TABLE IF NOT EXISTS skills (
  id BIGSERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  created_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- File: setup/migrations/000014_create_drill_skills.up.sql\n
CREATE TABLE IF NOT EXISTS drill_skills (
  id BIGSERIAL PRIMARY KEY,
  drill_id BIGINT NOT NULL,
  skill_id BIGINT NOT NULL,
  created_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE UNIQUE INDEX idx_drill_skills_drill_skill ON drill_skills (drill_id, skill_id);

-- File: setup/migrations/000015_create_user_drills.up.sql\n
CREATE TABLE IF NOT EXISTS user_drills (
  id BIGSERIAL PRIMARY KEY,
  drill_id BIGINT NOT NULL,
  user_id BIGINT NOT NULL,
  created_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_user_drills_drill_id ON user_drills (drill_id);

CREATE INDEX idx_user_drills_user_id ON user_drills (user_id);

CREATE UNIQUE INDEX idx_user_drills_user_drill ON user_drills (user_id, drill_id);

-- File: setup/migrations/000016_create_drill_videos.up.sql\n
CREATE TABLE IF NOT EXISTS drill_videos (
  id BIGSERIAL PRIMARY KEY,
  drill_id BIGINT,
  video_url VARCHAR(255) NOT NULL,
  created_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- File: setup/migrations/000017_create_courses.up.sql\n
CREATE TABLE IF NOT EXISTS courses (
  id BIGSERIAL PRIMARY KEY,
  teacher_id BIGINT NOT NULL,
  title VARCHAR(255) NOT NULL,
  slug VARCHAR(255) NOT NULL,
  description TEXT,
  status SMALLINT NOT NULL DEFAULT 0,
  instructional_level SMALLINT NOT NULL DEFAULT 0,
  is_public BOOLEAN DEFAULT FALSE,
  is_setting_package BOOLEAN DEFAULT FALSE,
  sale_price INTEGER,
  price INTEGER,
  bonus_point INTEGER,
  bonus_point_percent INTEGER,
  section_count INTEGER NOT NULL DEFAULT 0,
  section_item_count INTEGER NOT NULL DEFAULT 0,
  joined_user_count INTEGER NOT NULL DEFAULT 0,
  banner VARCHAR(255),
  created_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_courses_teacher_id ON courses (teacher_id);

CREATE INDEX idx_courses_slug ON courses (slug);

-- File: setup/migrations/000018_create_course_sections.up.sql\n
CREATE TABLE IF NOT EXISTS course_sections (
  id BIGSERIAL PRIMARY KEY,
  course_id BIGINT NOT NULL,
  slug VARCHAR(255) NOT NULL,
  title VARCHAR(255) NOT NULL,
  position INTEGER DEFAULT 1 NOT NULL,
  created_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_course_sections_course_id ON course_sections (course_id);

CREATE INDEX idx_course_sections_slug ON course_sections (slug);

-- File: setup/migrations/000019_create_course_section_items.up.sql\n
CREATE TABLE IF NOT EXISTS course_section_items (
  id BIGSERIAL PRIMARY KEY,
  course_id BIGINT NOT NULL,
  course_section_id BIGINT NOT NULL,
  title VARCHAR(255) NOT NULL,
  slug VARCHAR(255) NOT NULL,
  type SMALLINT NOT NULL DEFAULT 0,
  position INTEGER DEFAULT 1 NOT NULL,
  content TEXT,
  created_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_course_section_items_section_id ON course_section_items (course_section_id);

CREATE INDEX idx_course_section_items_course_id ON course_section_items (course_id);

CREATE INDEX idx_course_section_items_slug ON course_section_items (slug);

-- File: setup/migrations/000020_create_course_users.up.sql\n
CREATE TABLE IF NOT EXISTS course_users (
  id BIGSERIAL PRIMARY KEY,
  user_id BIGINT NOT NULL,
  course_id BIGINT NOT NULL,
  course_package_id BIGINT,
  status SMALLINT NOT NULL DEFAULT 1,
  course_user_metadata JSONB,
  joined_at TIMESTAMPTZ (6),
  created_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_course_users_user_id ON course_users (user_id);

CREATE INDEX idx_course_users_course_id ON course_users (course_id);

CREATE INDEX idx_course_users_course_package_id ON course_users (course_package_id);

CREATE UNIQUE INDEX idx_course_users_user_course ON course_users (user_id, course_id);

-- File: setup/migrations/000021_admins.up.sql\n
CREATE TABLE admins (
  id BIGSERIAL PRIMARY KEY,
  auth_id VARCHAR(255) NOT NULL,
  name VARCHAR(255) NOT NULL,
  created_at TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE UNIQUE INDEX idx_admins_auth_id ON admins(auth_id);

-- File: setup/migrations/000022_create_user_course_sections.up.sql\n
CREATE TABLE IF NOT EXISTS user_course_sections (
  id BIGSERIAL PRIMARY KEY,
  user_id BIGINT NOT NULL,
  course_section_id BIGINT NOT NULL,
  data JSONB,
  created_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_ucs_user_id ON user_course_sections (user_id);

CREATE INDEX idx_ucs_course_section_id ON user_course_sections (course_section_id);

CREATE UNIQUE INDEX idx_ucs_user_course ON user_course_sections (user_id, course_section_id);

-- File: setup/migrations/000023_create_course_section_item_drills.up.sql\n
CREATE TABLE IF NOT EXISTS course_section_item_drills (
  id BIGSERIAL PRIMARY KEY,
  course_section_item_id BIGINT NOT NULL,
  drill_id BIGINT NOT NULL,
  created_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_csids_drill_id ON course_section_item_drills (drill_id);

CREATE INDEX idx_csids_course_section_item_id ON course_section_item_drills (course_section_item_id);

CREATE UNIQUE INDEX idx_csids_course_section_item_id_drill_id ON course_section_item_drills (course_section_item_id, drill_id);

-- File: setup/migrations/000024_create_videos.up.sql\n
CREATE TABLE IF NOT EXISTS videos (
    id BIGSERIAL PRIMARY KEY,
    parent_id BIGINT NOT NULL,
    parent_type VARCHAR(50) NOT NULL,
    uploader_type VARCHAR(50) NOT NULL,
    uploader_id BIGINT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    duration INTEGER,
    thumbnail_url VARCHAR(255),
    is_free BOOLEAN DEFAULT FALSE,
    status INTEGER DEFAULT 1, -- 1: draft, 2: pending, 3: approved, 4: rejected
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    options JSONB
);

CREATE INDEX videos_status_idx ON videos (status);
CREATE INDEX videos_is_free_idx ON videos (is_free);
CREATE INDEX idx_videos_parent ON videos (parent_type, parent_id);
CREATE INDEX idx_videos_uploader ON videos (uploader_type, uploader_id);

-- File: setup/migrations/000025_create_video_uploads.up.sql\n
CREATE TABLE IF NOT EXISTS video_uploads (
    id BIGSERIAL PRIMARY KEY,
    video_id BIGINT NOT NULL REFERENCES videos(id) ON DELETE CASCADE,
    filename VARCHAR(255) NOT NULL,
    status INTEGER DEFAULT 1, -- 1: uploading, 2: processing, 3: completed, 4: failed
    progress FLOAT DEFAULT 0, -- 0-100%
    error_message TEXT,
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX video_uploads_video_id_idx ON video_uploads (video_id);
CREATE INDEX video_uploads_status_idx ON video_uploads (status);

-- File: setup/migrations/000026_create_video_versions.up.sql\n
CREATE TABLE IF NOT EXISTS video_versions (
    id BIGSERIAL PRIMARY KEY,
    video_id BIGINT NOT NULL REFERENCES videos(id) ON DELETE CASCADE,
    quality VARCHAR(50), -- SD, HD, Full HD, 4K
    resolution VARCHAR(50), -- 720p, 1080p, etc.
    file_size BIGINT,
    file_format VARCHAR(10), -- MP4, MKV
    s3_path VARCHAR(255),
    streaming_profile VARCHAR(20), -- basic, hd, 4k
    manifest_url VARCHAR(255), -- HLS/DASH manifest URL
    drm_key_id UUID,
    cache_status INTEGER DEFAULT 1, -- 1: not cached, 2: cached
    storage_class VARCHAR(20), -- STANDARD, NEARLINE
    access_tier INTEGER DEFAULT 1, -- 1: free, 2: premium, 3: ultimate
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX video_versions_video_id_idx ON video_versions (video_id);
CREATE INDEX video_versions_access_tier_idx ON video_versions (access_tier);

-- File: setup/migrations/000027_create_video_moderations.up.sql\n
CREATE TABLE IF NOT EXISTS video_moderations (
    id BIGSERIAL PRIMARY KEY,
    video_id BIGINT NOT NULL REFERENCES videos(id) ON DELETE CASCADE,
    moderator_id BIGINT NOT NULL, -- ID of moderator
    status INTEGER DEFAULT 1, -- 1: pending, 2: approved, 3: rejected
    comment TEXT,
    moderated_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX video_moderations_video_id_idx ON video_moderations (video_id);
CREATE INDEX video_moderations_moderator_id_idx ON video_moderations (moderator_id);
CREATE INDEX video_moderations_status_idx ON video_moderations (status);

-- File: setup/migrations/000028_create_video_platforms.up.sql\n
CREATE TABLE IF NOT EXISTS video_platforms (
    id BIGSERIAL PRIMARY KEY,
    video_id BIGINT NOT NULL REFERENCES videos(id) ON DELETE CASCADE,
    platform INTEGER NOT NULL DEFAULT 1,
    -- Mux, YouTube
    platform_video_id VARCHAR(100),
    -- ID của video trên nền tảng (YouTube ID)
    url VARCHAR(255),
    -- URL đến video trên nền tảng
    status INTEGER DEFAULT 1,
    -- 1: syncing, 2: available, 3: error
    sync_at TIMESTAMP,
    token_id BIGINT,
    -- ID của token dùng để sync
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX video_platforms_video_id_idx ON video_platforms (video_id);

CREATE INDEX video_platforms_platform_idx ON video_platforms (platform);

CREATE INDEX video_platforms_status_idx ON video_platforms (status);

-- File: setup/migrations/000029_create_video_access_controls.up.sql\n
CREATE TABLE IF NOT EXISTS video_access_controls (
    id BIGSERIAL PRIMARY KEY,
    video_id BIGINT NOT NULL REFERENCES videos(id) ON DELETE CASCADE,
    plan_id BIGINT, -- reference to plans (optional)
    access_type VARCHAR(50) NOT NULL, -- free, premium, specific_plan
    streaming_only BOOLEAN DEFAULT FALSE, -- must stream
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX video_access_controls_video_id_idx ON video_access_controls (video_id);
CREATE INDEX video_access_controls_plan_id_idx ON video_access_controls (plan_id);
CREATE INDEX video_access_controls_access_type_idx ON video_access_controls (access_type);

-- File: setup/migrations/000030_create_video_streaming_sessions.up.sql\n
CREATE TABLE IF NOT EXISTS video_streaming_sessions (
    id BIGSERIAL PRIMARY KEY,
    video_id BIGINT NOT NULL REFERENCES videos(id) ON DELETE CASCADE,
    user_id BIGINT NOT NULL, -- reference to students
    session_token VARCHAR(255) NOT NULL, -- authentication token
    ip_address VARCHAR(50),
    user_agent VARCHAR(255),
    started_at TIMESTAMP,
    ended_at TIMESTAMP,
    status INTEGER DEFAULT 1, -- 1: active, 2: ended, 3: expired
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX video_streaming_sessions_video_id_idx ON video_streaming_sessions (video_id);
CREATE INDEX video_streaming_sessions_user_id_idx ON video_streaming_sessions (user_id);
CREATE INDEX video_streaming_sessions_session_token_idx ON video_streaming_sessions (session_token);
CREATE INDEX video_streaming_sessions_status_idx ON video_streaming_sessions (status);

-- File: setup/migrations/000032_create_video_progress.up.sql\n
CREATE TABLE IF NOT EXISTS video_progresses (
    id BIGSERIAL PRIMARY KEY,
    video_id BIGINT NOT NULL REFERENCES videos(id) ON DELETE CASCADE,
    user_id BIGINT NOT NULL,
    -- reference to students
    last_position INTEGER DEFAULT 0,
    -- last position (seconds)
    completed BOOLEAN DEFAULT FALSE,
    -- completed
    completed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX video_progresses_video_id_idx ON video_progresses (video_id);

CREATE INDEX video_progresses_user_id_idx ON video_progresses (user_id);

CREATE UNIQUE INDEX video_progresses_video_id_user_id_idx ON video_progresses (video_id, user_id);

-- File: setup/migrations/000033_create_video_views.up.sql\n
CREATE TABLE IF NOT EXISTS video_views (
    id BIGSERIAL PRIMARY KEY,
    video_id BIGINT NOT NULL REFERENCES videos(id) ON DELETE CASCADE,
    user_id BIGINT, -- reference to students, can be NULL for anonymous views
    platform VARCHAR(50) NOT NULL, -- YouTube, Mux
    view_duration INTEGER DEFAULT 0, -- view duration (seconds)
    watched_percentage FLOAT DEFAULT 0, -- watched percentage
    ip_address VARCHAR(50),
    viewed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX video_views_video_id_idx ON video_views (video_id);
CREATE INDEX video_views_user_id_idx ON video_views (user_id) WHERE user_id IS NOT NULL;
CREATE INDEX video_views_platform_idx ON video_views (platform);
CREATE INDEX video_views_viewed_at_idx ON video_views (viewed_at);

-- File: setup/migrations/000034_create_user_course_section_items.up.sql\n
CREATE TABLE IF NOT EXISTS user_course_section_items (
  id BIGSERIAL PRIMARY KEY,
  user_id BIGINT NOT NULL,
  course_section_item_id BIGINT NOT NULL,
  status SMALLINT NOT NULL DEFAULT 0,
  data JSONB,
  created_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_ucsi_user_id ON user_course_section_items (user_id);

CREATE INDEX idx_ucsi_course_section_item_id ON user_course_section_items (course_section_item_id);

CREATE UNIQUE INDEX idx_ucsi_user_course ON user_course_section_items (user_id, course_section_item_id);

-- File: setup/migrations/000035_create_practice_submissions.up.sql\n
CREATE TABLE IF NOT EXISTS practice_submissions (
  id BIGSERIAL PRIMARY KEY,
  practice_id BIGINT NOT NULL,
  practice_type VARCHAR(50) NOT NULL,
  teacher_id BIGINT NOT NULL,
  user_id BIGINT NOT NULL,
  content TEXT,
  status SMALLINT NOT NULL DEFAULT 0,
  created_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_practice_submissions_practice ON practice_submissions (practice_id, practice_type);

CREATE INDEX idx_practice_submissions_user_id ON practice_submissions (user_id);

CREATE INDEX idx_practice_submissions_teacher_id ON practice_submissions (teacher_id);

CREATE INDEX idx_practice_submissions_status ON practice_submissions (status);

-- File: setup/migrations/000036_create_course_censor_histories.up.sql\n
CREATE TABLE IF NOT EXISTS course_censor_histories (
  id BIGSERIAL PRIMARY KEY,
  course_id BIGINT NOT NULL,
  created_by BIGINT NOT NULL,
  feedback TEXT NOT NULL,
  status SMALLINT NOT NULL DEFAULT 0,
  created_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_course_censor_histories_course_id ON course_censor_histories (course_id);

CREATE INDEX idx_course_censor_histories_created_by ON course_censor_histories (created_by);

-- File: setup/migrations/000037_create_censor_histories.up.sql\n
CREATE TABLE IF NOT EXISTS censor_histories (
  id BIGSERIAL PRIMARY KEY,
  parent_id BIGINT NOT NULL,
  parent_type VARCHAR(255) NOT NULL,
  created_by BIGINT NOT NULL,
  feedback TEXT NOT NULL,
  status SMALLINT NOT NULL DEFAULT 0,
  created_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_censor_histories_created_by ON censor_histories (created_by);

CREATE INDEX idx_censor_histories_parent ON censor_histories (parent_type, parent_id);

-- File: setup/migrations/000038_create_youtube_tokens.up.sql\n
CREATE TABLE IF NOT EXISTS youtube_tokens (
  id BIGSERIAL PRIMARY KEY,
  owner_name VARCHAR(255) NOT NULL,
  token TEXT NOT NULL,
  created_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- File: setup/migrations/000039_create_package_deals.up.sql\n
CREATE TABLE IF NOT EXISTS package_deals (
  id BIGSERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  approval_submission_required BOOLEAN NOT NULL DEFAULT FALSE,
  active BOOLEAN NOT NULL DEFAULT TRUE,
  created_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- File: setup/migrations/000040_create_course_packages.up.sql\n
CREATE TABLE IF NOT EXISTS course_packages (
  id BIGSERIAL PRIMARY KEY,
  course_id BIGINT,
  package_deal_id BIGINT,
  sale_price INTEGER,
  price INTEGER,
  metadata JSONB,
  created_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_course_packages_course_id ON course_packages (course_id);
CREATE INDEX idx_course_packages_package_deal_id ON course_packages (package_deal_id);
CREATE UNIQUE INDEX idx_course_packages_course_id_package_deal_id ON course_packages (course_id, package_deal_id);

