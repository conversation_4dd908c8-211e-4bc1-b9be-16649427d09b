package gqls

import (
	_ "embed"
	"net/http"
	teacherResolvers "vibico-education-api/internal/gqls/resolvers/teachers"
	"vibico-education-api/internal/middlewares"
	setup "vibico-education-api/setup/gqls"

	"vibico-education-api/internal/repository"

	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
	gql "github.com/BehemothLtd/behemoth-pkg/golang/gql"
	"github.com/gin-gonic/gin"
	"github.com/graph-gophers/graphql-go"
)

func TeacherGqlHandler(repos repository.IRepositories) gin.HandlerFunc {
	opts := []graphql.SchemaOpt{graphql.UseStringDescriptions(), graphql.UseFieldResolvers()}

	return func(c *gin.Context) {
		resolver := teacherResolvers.NewResolver(repos, c)

		middlewares.GraphqlAuthHandle(resolver)(c)
		if c.Is<PERSON>borted() {
			return
		}

		teacher := resolver.GetCurrentTeacher()
		if teacher != nil && !teacher.Active {
			exception := exceptions.NewForbiddenError(nil)
			extensions := exception.Extensions()
			extensions["errorCode"] = "ACCOUNT_DEACTIVATED"

			c.AbortWithStatusJSON(http.StatusOK, gin.H{
				"errors": []gin.H{
					{
						"message":    exception.Error(),
						"path":       []string{"permission"},
						"extensions": extensions,
					},
				},
				"data": nil,
			})
			return
		}

		gqlSchema := graphql.MustParseSchema(setup.TeacherSchema, resolver, opts...)

		gql.GinSchemaHandler(gqlSchema)(c)
	}
}
