package gqls

import (
	publicResolvers "vibico-education-api/internal/gqls/resolvers/publics"
	"vibico-education-api/internal/middlewares"
	setup "vibico-education-api/setup/gqls"

	"vibico-education-api/internal/repository"

	gql "github.com/BehemothLtd/behemoth-pkg/golang/gql"
	"github.com/gin-gonic/gin"
	"github.com/graph-gophers/graphql-go"
)

func PublicGqlHandler(repos repository.IRepositories) gin.HandlerFunc {
	opts := []graphql.SchemaOpt{graphql.UseStringDescriptions(), graphql.UseFieldResolvers()}

	return func(c *gin.Context) {
		resolver := publicResolvers.NewResolver(repos, c)
		middlewares.GraphqlAuthHandle(resolver)(c)
		if c.IsAborted() {
			return
		}
		gqlSchema := graphql.MustParseSchema(setup.PublicSchema, resolver, opts...)

		gql.GinSchemaHandler(gqlSchema)(c)
	}
}
