package gqls

import (
	adminResolvers "vibico-education-api/internal/gqls/resolvers/admins"
	"vibico-education-api/internal/middlewares"
	"vibico-education-api/internal/repository"
	setup "vibico-education-api/setup/gqls"

	gql "github.com/BehemothLtd/behemoth-pkg/golang/gql"
	"github.com/gin-gonic/gin"
	"github.com/graph-gophers/graphql-go"
)

func AdminGqlHandler(repos repository.IRepositories) gin.HandlerFunc {
	opts := []graphql.SchemaOpt{graphql.UseStringDescriptions(), graphql.UseFieldResolvers()}

	return func(c *gin.Context) {
		resolver := adminResolvers.NewResolver(repos, c)

		middlewares.GraphqlAuthHandle(resolver)(c)
		if c.IsAborted() {
			return
		}

		gqlSchema := graphql.MustParseSchema(setup.AdminSchema, resolver, opts...)

		gql.GinSchemaHandler(gqlSchema)(c)
	}
}
