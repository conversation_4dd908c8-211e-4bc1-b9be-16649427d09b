package publicInputs

import (
	"strings"
	globalInputs "vibico-education-api/internal/gqls/inputs/global"

	"github.com/BehemothLtd/behemoth-pkg/golang/pagination"
)

type TeachersInput struct {
	Input   *globalInputs.PagyInput
	Query   *TeachersQueryInput
	OrderBy *string
}

type TeachersQueryInput struct {
	NameCont *string
}

func (input *TeachersInput) ToPaginationDataAndQuery() (TeachersQueryInput, pagination.PaginationData, string) {
	query := input.Query.formatQueryInput()
	paginationData := input.Input.ToPaginationInput()

	orderBy := "teachers.student_count DESC, teachers.id ASC"
	if input.OrderBy != nil {
		switch *input.OrderBy {
		case "id DESC":
			orderBy = "teachers.id DESC"
		case "id ASC":
			orderBy = "teachers.id ASC"
		case "approved_course_count DESC":
			orderBy = "teachers.approved_course_count DESC, teachers.id ASC"
		case "approved_course_count ASC":
			orderBy = "teachers.approved_course_count ASC, teachers.id ASC"
		case "student_count DESC":
			orderBy = "teachers.student_count DESC, teachers.id ASC"
		case "student_count ASC":
			orderBy = "teachers.student_count ASC, teachers.id ASC"
		case "average_rating DESC":
			orderBy = "teachers.average_rating DESC NULLS LAST, teachers.id ASC"
		case "average_rating ASC":
			orderBy = "teachers.average_rating ASC NULLS LAST, teachers.id ASC"

		}
	}

	return query, paginationData, orderBy
}

func (queryInput *TeachersQueryInput) formatQueryInput() TeachersQueryInput {
	query := TeachersQueryInput{}

	if queryInput == nil {
		return query
	}

	if queryInput.NameCont != nil && strings.TrimSpace(*queryInput.NameCont) != "" {
		query.NameCont = queryInput.NameCont
	}

	return query
}
