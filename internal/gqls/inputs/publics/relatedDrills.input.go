package publicInputs

import (
	"slices"
	globalInputs "vibico-education-api/internal/gqls/inputs/global"

	"github.com/BehemothLtd/behemoth-pkg/golang/pagination"
)

type RelatedDrillInput struct {
	Input     *globalInputs.PagyInput
	Query     *drillsQueryModelInput
	OrderBy   *string
	DrillSlug string
}

type drillsQueryModelInput struct {
	LevelIn   *[]string
	SkillIDIn *[]int32
}

func (input *RelatedDrillInput) ToPaginationDataAndQuery() (
	drillsQueryModelInput, pagination.PaginationData, string,
) {
	query := input.Query.formatQueryInput()
	paginationData := input.Input.ToPaginationInput()

	var orderBy string = "drills.created_at desc"
	if input.OrderBy != nil && *input.OrderBy != "" {
		ableToOrderBy := []string{"drills.id desc", "drills.id asc", "drills.created_at desc", "drills.created_at asc"}

		if slices.Contains(ableToOrderBy, *input.OrderBy) {
			orderBy = *input.OrderBy
		}
	}

	return query, paginationData, orderBy
}

func (queryInput *drillsQueryModelInput) formatQueryInput() drillsQueryModelInput {
	query := drillsQueryModelInput{}

	if queryInput == nil {
		return query
	}
	if queryInput.LevelIn != nil {
		query.LevelIn = queryInput.LevelIn
	}
	if queryInput.SkillIDIn != nil {
		query.SkillIDIn = queryInput.SkillIDIn
	}

	return *queryInput
}
