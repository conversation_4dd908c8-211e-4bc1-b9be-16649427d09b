package publicInputs

import (
	"slices"
	globalInputs "vibico-education-api/internal/gqls/inputs/global"

	"github.com/BehemothLtd/behemoth-pkg/golang/pagination"
)

type DrillInput struct {
	Input   *globalInputs.PagyInput
	Query   *drillsQueryInput
	OrderBy *string
}

type drillsQueryInput struct {
	TitleCont *string
	LevelIn   *[]int32
	SkillIDIn *[]int32
	OrderBy   *string
}

func (input *DrillInput) ToPaginationDataAndQuery() (
	drillsQueryInput, pagination.PaginationData, string,
) {
	query := input.Query.formatQueryInput()
	paginationData := input.Input.ToPaginationInput()

	var orderBy string = "drills.created_at desc"
	if input.OrderBy != nil && *input.OrderBy != "" {
		ableToOrderBy := []string{"drills.id desc", "drills.id asc", "drills.created_at desc", "drills.created_at asc"}

		if slices.Contains(ableToOrderBy, *input.OrderBy) {
			orderBy = *input.OrderBy
		}
	}

	return query, paginationData, orderBy
}

func (queryInput *drillsQueryInput) formatQueryInput() drillsQueryInput {
	query := drillsQueryInput{}

	if queryInput == nil {
		return query
	}
	if queryInput.TitleCont != nil {
		query.TitleCont = queryInput.TitleCont
	}
	if queryInput.LevelIn != nil {
		query.LevelIn = queryInput.LevelIn
	}
	if queryInput.SkillIDIn != nil {
		query.SkillIDIn = queryInput.SkillIDIn
	}

	return *queryInput
}
