package publicInputs

import (
	globalInputs "vibico-education-api/internal/gqls/inputs/global"

	"github.com/BehemothLtd/behemoth-pkg/golang/pagination"
)

type CoursesInput struct {
	Input *globalInputs.PagyInput
	Query *coursesQueryInput
}

type coursesQueryInput struct {
	TitleCont       *string
	DescriptionCont *string
	CategoryCont    *string
	TeacherIdEq     *string
	TeacherNameCont *string
	SalePriceRange  *[]int32
}

func (input *CoursesInput) ToPaginationDataAndQuery() (coursesQueryInput, pagination.PaginationData) {
	query := input.Query.formatQueryInput()
	paginationData := input.Input.ToPaginationInput()

	return query, paginationData
}

func (queryInput *coursesQueryInput) formatQueryInput() coursesQueryInput {
	query := coursesQueryInput{}

	if queryInput == nil {
		return query
	}

	return *queryInput
}

type RelatedCoursesInput struct {
	Input      *globalInputs.PagyInput
	CourseSlug string
}
