package adminInputs

import (
	"strings"
	globalInputs "vibico-education-api/internal/gqls/inputs/global"

	"github.com/BehemothLtd/behemoth-pkg/golang/pagination"
)

type teachersQueryInput struct {
	NameCont        *string
	PhoneNumberCont *string
}

type TeachersInput struct {
	Input *globalInputs.PagyInput
	Query *teachersQueryInput
}

func (input *TeachersInput) ToPaginationDataAndQuery() (teachersQueryInput, pagination.PaginationData) {
	query := input.Query.formatQueryInput()
	paginationData := input.Input.ToPaginationInput()

	return query, paginationData
}

func (queryInput *teachersQueryInput) formatQueryInput() teachersQueryInput {
	query := teachersQueryInput{}

	if queryInput == nil {
		return query
	}

	if queryInput.NameCont != nil && strings.TrimSpace(*queryInput.NameCont) != "" {
		query.NameCont = queryInput.NameCont
	}

	if queryInput.PhoneNumberCont != nil && strings.TrimSpace(*queryInput.PhoneNumberCont) != "" {
		query.PhoneNumberCont = queryInput.PhoneNumberCont
	}

	return query
}
