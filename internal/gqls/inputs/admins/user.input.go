package adminInputs

import (
	globalInputs "vibico-education-api/internal/gqls/inputs/global"

	"github.com/BehemothLtd/behemoth-pkg/golang/pagination"
)

type UsersInput struct {
	Input *globalInputs.PagyInput
	Query *usersQueryInput
}

type usersQueryInput struct {
	NameCont        *string
	PhoneNumberCont *string
}

func (input *UsersInput) ToPaginationDataAndQuery() (usersQueryInput, pagination.PaginationData) {
	query := input.Query.formatQueryInput()
	paginationData := input.Input.ToPaginationInput()

	return query, paginationData
}

func (queryInput *usersQueryInput) formatQueryInput() usersQueryInput {
	query := usersQueryInput{}

	if queryInput == nil {
		return query
	}

	return *queryInput
}
