package adminInputs

import (
	globalInputs "vibico-education-api/internal/gqls/inputs/global"

	"github.com/BehemothLtd/behemoth-pkg/golang/pagination"
)

type DrillInput struct {
	Input *globalInputs.PagyInput
	Query *drillsQueryInput
}

type drillsQueryInput struct {
	TitleCont *string
	LevelIn   *[]int32
	SkillIDIn *[]int32
}

func (input *DrillInput) ToPaginationDataAndQuery() (
	drillsQueryInput, pagination.PaginationData,
) {
	query := input.Query.formatQueryInput()
	paginationData := input.Input.ToPaginationInput()

	return query, paginationData
}

func (queryInput *drillsQueryInput) formatQueryInput() drillsQueryInput {
	query := drillsQueryInput{}

	if queryInput == nil {
		return query
	}
	if queryInput.TitleCont != nil {
		query.TitleCont = queryInput.TitleCont
	}
	if queryInput.LevelIn != nil {
		query.LevelIn = queryInput.LevelIn
	}
	if queryInput.SkillIDIn != nil {
		query.SkillIDIn = queryInput.SkillIDIn
	}

	return *queryInput
}
