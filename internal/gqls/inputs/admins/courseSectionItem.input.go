package adminInputs

import (
	"github.com/graph-gophers/graphql-go"
)

type CourseSectionItemInput struct {
	Title   *string
	Content *string
	Type    *string
}

type CourseSectionItemCreateInput struct {
	CourseId        graphql.ID
	CourseSectionId graphql.ID
	Input           *CourseSectionItemInput
}

type CourseSectionItemUpdateInput struct {
	Id              graphql.ID
	CourseId        graphql.ID
	CourseSectionId graphql.ID
	Input           *CourseSectionItemInput
}
