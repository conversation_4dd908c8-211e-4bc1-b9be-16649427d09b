package adminInputs

import (
	globalInputs "vibico-education-api/internal/gqls/inputs/global"

	"github.com/BehemothLtd/behemoth-pkg/golang/pagination"
	"github.com/graph-gophers/graphql-go"
)

type CourseCommonInput struct {
	Title             *string
	TeacherId         *int32
	Description       *string
	SalePrice         *int32
	Price             *int32
	BonusPoint        *int32
	BonusPointPercent *int32
}

type CourseCreateInput struct {
	CourseCommonInput
}

type CourseUpdateInput struct {
	ID    graphql.ID
	Input CourseUpdateFormInput
}

type CourseUpdateFormInput struct {
	CourseCommonInput
}

type CoursesInput struct {
	Input *globalInputs.PagyInput
	Query *coursesQueryInput
}

type coursesQueryInput struct {
	TitleCont   *string
	TeacherIdEq *string
	StatusIn    *[]string
}

func (input *CoursesInput) ToPaginationDataAndQuery() (coursesQueryInput, pagination.PaginationData) {
	query := input.Query.formatQueryInput()
	paginationData := input.Input.ToPaginationInput()

	return query, paginationData
}

func (queryInput *coursesQueryInput) formatQueryInput() coursesQueryInput {
	query := coursesQueryInput{}

	if queryInput == nil {
		return query
	}

	return *queryInput
}
