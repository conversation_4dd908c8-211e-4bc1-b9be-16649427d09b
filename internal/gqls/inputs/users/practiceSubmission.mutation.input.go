package userInputs

import (
	"github.com/graph-gophers/graphql-go"
)

type PracticeSubmissionCommonInput struct {
	Content *string
}

type PracticeSubmissionCreateInput struct {
	PracticeID   *graphql.ID
	PracticeType *string
	VideoIDs     *[]graphql.ID
	PracticeSubmissionCommonInput
}

type PracticeSubmissionUpdateInput struct {
	ID    graphql.ID
	Input PracticeSubmissionUpdateFormInput
}

type PracticeSubmissionUpdateFormInput struct {
	PracticeSubmissionCommonInput
}
