package userInputs

import (
	"strings"
	globalInputs "vibico-education-api/internal/gqls/inputs/global"

	"github.com/BehemothLtd/behemoth-pkg/golang/pagination"
)

type TeachersInput struct {
	Input *globalInputs.PagyInput
	Query *TeachersQueryInput
}

type TeachersQueryInput struct {
	NameCont *string
}

func (input *TeachersInput) ToPaginationDataAndQuery() (TeachersQueryInput, pagination.PaginationData) {
	query := input.Query.formatQueryInput()
	paginationData := input.Input.ToPaginationInput()

	return query, paginationData
}

func (queryInput *TeachersQueryInput) formatQueryInput() TeachersQueryInput {
	query := TeachersQueryInput{}

	if queryInput == nil {
		return query
	}

	if queryInput.NameCont != nil && strings.TrimSpace(*queryInput.NameCont) != "" {
		query.NameCont = queryInput.NameCont
	}

	return query
}
