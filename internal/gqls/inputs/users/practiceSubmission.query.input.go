package userInputs

import (
	globalInputs "vibico-education-api/internal/gqls/inputs/global"

	"github.com/BehemothLtd/behemoth-pkg/golang/pagination"
)

type PracticeSubmissionsInput struct {
	Input *globalInputs.PagyInput
	Query *practiceSubmissionsQueryInput
}

type practiceSubmissionsQueryInput struct {
	StatusEq       *string
	PracticeIDEq   *int32
	PracticeTypeEq *string
}

func (input *PracticeSubmissionsInput) ToPaginationDataAndQuery() (practiceSubmissionsQueryInput, pagination.PaginationData) {
	query := input.Query.formatQueryInput()
	paginationData := input.Input.ToPaginationInput()

	return query, paginationData
}

func (queryInput *practiceSubmissionsQueryInput) formatQueryInput() practiceSubmissionsQueryInput {
	query := practiceSubmissionsQueryInput{}

	if queryInput == nil {
		return query
	}

	return *queryInput
}
