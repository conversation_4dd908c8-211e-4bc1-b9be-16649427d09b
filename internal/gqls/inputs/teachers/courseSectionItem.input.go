package teacherInputs

import (
	"github.com/graph-gophers/graphql-go"
)

type CourseSectionItemInput struct {
	Title   *string
	Content *string
	Type    *string
	// DrillIds *[]int32
}

type CourseSectionItemCreateInput struct {
	CourseId        graphql.ID
	CourseSectionId graphql.ID
	Input           *CourseSectionItemInput
}

type CourseSectionItemUpdateInput struct {
	Id              graphql.ID
	CourseId        graphql.ID
	CourseSectionId graphql.ID
	Input           *CourseSectionItemInput
}

type CourseSectionItemDrillMutateInput struct {
	ItemId          graphql.ID
	CourseId        graphql.ID
	CourseSectionId graphql.ID
	DrillId         graphql.ID
}
