package teacherInputs

import (
	globalInputs "vibico-education-api/internal/gqls/inputs/global"

	"github.com/BehemothLtd/behemoth-pkg/golang/pagination"
	"github.com/graph-gophers/graphql-go"
)

type CourseUsersInput struct {
	CourseId graphql.ID
	Input    *globalInputs.PagyInput
	Query    *CourseUserQuery
}

type CourseUserQuery struct {
	CreatedAtGteq *string
}

func (input *CourseUsersInput) ToPaginationDataAndQuery() (CourseUserQuery, pagination.PaginationData) {
	query := input.Query.formatQueryInput()
	paginationData := input.Input.ToPaginationInput()

	return query, paginationData
}

func (queryInput *CourseUserQuery) formatQueryInput() CourseUserQuery {
	query := CourseUserQuery{}

	if queryInput == nil {
		return query
	}

	if queryInput.CreatedAtGteq != nil {
		query.CreatedAtGteq = queryInput.CreatedAtGteq
	}

	return query
}
