package teacherInputs

import (
	"strings"
	"vibico-education-api/internal/enums"
	globalInputs "vibico-education-api/internal/gqls/inputs/global"

	"github.com/BehemothLtd/behemoth-pkg/golang/pagination"
	"github.com/graph-gophers/graphql-go"
)

type CourseCommonInput struct {
	Title              *string
	Description        *string
	SalePrice          *int32
	Price              *int32
	BonusPoint         *int32
	BonusPointPercent  *int32
	Banner             *string
	IsPublic           *bool
	InstructionalLevel *string
}

type CourseCreateInput struct {
	CourseCommonInput
}

type CourseUpdateInput struct {
	ID    graphql.ID
	Input CourseUpdateFormInput
}

type CourseUpdateFormInput struct {
	CourseCommonInput
}

type CoursesInput struct {
	Input *globalInputs.PagyInput
	Query *coursesQueryInput
}

type coursesQueryInput struct {
	TitleCont       *string
	DescriptionCont *string
	StatusEq        *string
}

func (input *CoursesInput) ToPaginationDataAndQuery() (coursesQueryInput, pagination.PaginationData) {
	query := input.Query.formatQueryInput()
	paginationData := input.Input.ToPaginationInput()

	return query, paginationData
}

func (queryInput *coursesQueryInput) formatQueryInput() coursesQueryInput {
	query := coursesQueryInput{}

	if queryInput == nil {
		return query
	}

	if queryInput.TitleCont != nil && strings.TrimSpace(*queryInput.TitleCont) != "" {
		query.TitleCont = queryInput.TitleCont
	}

	if queryInput.DescriptionCont != nil && strings.TrimSpace(*queryInput.DescriptionCont) != "" {
		query.DescriptionCont = queryInput.DescriptionCont
	}

	if queryInput.StatusEq != nil && strings.TrimSpace(*queryInput.StatusEq) != "" {
		_, err := enums.ParseCourseStatus(*queryInput.StatusEq)
		if err == nil {
			query.StatusEq = queryInput.StatusEq
		}
	}

	return query
}
