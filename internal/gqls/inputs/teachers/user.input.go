package teacherInputs

import (
	globalInputs "vibico-education-api/internal/gqls/inputs/global"

	"github.com/BehemothLtd/behemoth-pkg/golang/pagination"
)

type SectionItemUsersWithPracticesInput struct {
	CourseSlug      string
	SectionItemSlug string
	Input           *globalInputs.PagyInput
	Query           *sectionItemUsersWithPracticesQueryInput
}

type sectionItemUsersWithPracticesQueryInput struct {
	NameCont *string
	StatusEq *string
}

func (input *SectionItemUsersWithPracticesInput) ToPaginationDataAndQuery() (sectionItemUsersWithPracticesQueryInput, pagination.PaginationData) {
	query := input.Query.formatQueryInput()
	paginationData := input.Input.ToPaginationInput()

	return query, paginationData
}

func (queryInput *sectionItemUsersWithPracticesQueryInput) formatQueryInput() sectionItemUsersWithPracticesQueryInput {
	query := sectionItemUsersWithPracticesQueryInput{}

	if queryInput == nil {
		return query
	}

	return *queryInput
}
