package teacherInputs

import (
	"strings"
	globalInputs "vibico-education-api/internal/gqls/inputs/global"

	"github.com/BehemothLtd/behemoth-pkg/golang/pagination"
)

type TemplateDiagramFormInput struct {
	Name              *string
	DiagramAttributes *[]*DiagramFormInput
}

type TemplateDiagramQueryInput struct {
	NameCont *string
}

type TemplateDiagramListInput struct {
	Input *globalInputs.PagyInput
	Query *TemplateDiagramQueryInput
}

func (input *TemplateDiagramListInput) ToPaginationDataAndQuery() (
	TemplateDiagramQueryInput, pagination.PaginationData,
) {
	query := input.Query.formatQueryInput()
	paginationData := input.Input.ToPaginationInput()

	return query, paginationData
}

func (queryInput *TemplateDiagramQueryInput) formatQueryInput() TemplateDiagramQueryInput {
	query := TemplateDiagramQueryInput{}

	if queryInput == nil {
		return query
	}

	if queryInput.NameCont != nil && strings.TrimSpace(*queryInput.NameCont) != "" {
		query.NameCont = queryInput.NameCont
	}

	return query
}
