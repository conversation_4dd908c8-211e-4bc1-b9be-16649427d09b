package userResolvers

import (
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	userPayloads "vibico-education-api/internal/gqls/payloads/users"
	"vibico-education-api/internal/gqls/resolvers"

	"github.com/rs/zerolog/log"
)

func (r *Resolver) SelfInfo() (*userPayloads.UserPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("UserResolver.SelfInfo")

	// TODO: Maybe remove this logic after implement notification logic flow
	invitedCourses, err := r.Repos.CourseUserRepo().ListInvitedCourseByUser(
		r.Ctx,
		r.currentUser.ID,
		r.DetectAssociations("selfInfo", []resolvers.RequestAssociation{
			{RequestKey: "invitedCourseUsers.course", Key: "Course"},
			{RequestKey: "course.teacher", Key: "Course.Teacher"},
		})...,
	)
	if err != nil {
		return nil, err
	}

	r.currentUser.CourseUsers = &invitedCourses

	return &userPayloads.UserPayload{
		UserPayload: &globalPayloads.UserPayload{
			User: r.currentUser,
		},
	}, nil
}
