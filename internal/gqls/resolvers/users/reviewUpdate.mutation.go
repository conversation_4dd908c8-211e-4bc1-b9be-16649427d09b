package userResolvers

import (
	"errors"
	"vibico-education-api/internal/forms/users/commentForms"
	globalInputs "vibico-education-api/internal/gqls/inputs/global"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	userPayloads "vibico-education-api/internal/gqls/payloads/users"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
	"github.com/rs/zerolog/log"
)

func (r *Resolver) ReviewUpdate(args struct {
	ReviewID *graphql.ID
	Input    globalInputs.ReviewUpdateInput
}) (*userPayloads.CommentCreatePayload, error) {
	log.Info().Ctx(r.Ctx).Msg("UserResolver.ReviewUpdate")

	if args.ReviewID == nil {
		return nil, errors.New("ReviewID: " + translator.Translate(nil, "errValidationMsg_required"))
	}

	reviewID, err := utils.ParseGraphqlID[uint32](*args.ReviewID)
	if err != nil {
		return nil, err
	}
	review, err := r.Repos.CommentRepo().FindByIdAndUserAuthor(r.Ctx, reviewID, r.currentUser.ID)
	if err != nil {
		return nil, err
	}

	form := commentForms.NewReviewUpdateForm(r.Ctx, args.Input, r.currentUser, review, r.Repos)
	if err := form.Save(); err != nil {
		return nil, err
	}
	return &userPayloads.CommentCreatePayload{
		Comment: &userPayloads.CommentPayload{CommentPayload: &globalPayloads.CommentPayload{Comment: form.Comment}},
		Message: translator.Translate(nil, "infoMsg_updateSuccess"),
	}, nil
}
