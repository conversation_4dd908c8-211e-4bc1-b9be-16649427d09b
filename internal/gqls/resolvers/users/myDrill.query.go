package userResolvers

import (
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	userPayloads "vibico-education-api/internal/gqls/payloads/users"
	"vibico-education-api/internal/gqls/resolvers"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

func (r *Resolver) MyDrill(args struct {
	CourseId graphql.ID
	ItemId   graphql.ID
	DrillId  graphql.ID
}) (*userPayloads.DrillPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("UserResolver.MyDrill")

	courseId, err := utils.ParseGraphqlID[uint32](args.CourseId)
	if err != nil {
		return nil, err
	}

	itemId, err := utils.ParseGraphqlID[uint32](args.ItemId)
	if err != nil {
		return nil, err
	}

	drillId, err := utils.ParseGraphqlID[uint32](args.DrillId)
	if err != nil {
		return nil, err
	}

	_, err = r.Repos.CourseRepo().FindByIDAndUser(r.Ctx, courseId, r.currentUser.ID)
	if err != nil {
		return nil, err
	}
	_, err = r.Repos.CourseSectionItemRepo().FindByIdAndCourseId(r.Ctx, itemId, courseId)
	if err != nil {
		return nil, err
	}

	drill, err := r.Repos.DrillRepo().FindByIdAndItemId(
		r.Ctx,
		drillId,
		itemId,
		r.DetectAssociations("myDrill", []resolvers.RequestAssociation{
			{RequestKey: "skills", Key: "DrillSkills.Skill"},
			{RequestKey: "videos", Args: []any{func(db *gorm.DB) *gorm.DB {
				return db.Order("videos.id ASC")
			}}},
			{RequestKey: "videos", Key: "Videos.VideoPlatforms"},
			{RequestKey: "diagrams"},
		})...,
	)

	if err != nil {
		return nil, err
	}

	return &userPayloads.DrillPayload{
		DrillPayload: &globalPayloads.DrillPayload{
			Drill: drill,
		},
		IsVisible: true,
	}, nil
}
