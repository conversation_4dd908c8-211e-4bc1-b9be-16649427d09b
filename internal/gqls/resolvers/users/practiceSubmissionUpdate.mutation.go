package userResolvers

import (
	"vibico-education-api/internal/forms/users/practiceSubmissionForms"
	userInputs "vibico-education-api/internal/gqls/inputs/users"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	userPayloads "vibico-education-api/internal/gqls/payloads/users"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/rs/zerolog/log"
)

func (r *Resolver) PracticeSubmissionUpdate(args userInputs.PracticeSubmissionUpdateInput) (*userPayloads.PracticeSubmissionUpdatePayload, error) {
	log.Info().Ctx(r.Ctx).Msg("UserResolver.PracticeSubmissionUpdate")

	practiceSubmissionID, err := utils.ParseGraphqlID[uint32](args.ID)
	if err != nil {
		return nil, err
	}

	practiceSubmissionRepo := r.Repos.PracticeSubmissionRepo()
	practiceSubmission, err := practiceSubmissionRepo.FindByIDAndUserID(r.Ctx, practiceSubmissionID, r.currentUser.ID)
	if err != nil {
		return nil, err
	}

	form := practiceSubmissionForms.NewUpdateForm(r.Ctx, args.Input, practiceSubmission, r.Repos.PracticeSubmissionRepo())
	if err := form.Save(); err != nil {
		return nil, err
	}

	return &userPayloads.PracticeSubmissionUpdatePayload{
		PracticeSubmissionMutatePayload: userPayloads.PracticeSubmissionMutatePayload{
			PracticeSubmission: &userPayloads.PracticeSubmissionPayload{
				PracticeSubmissionPayload: &globalPayloads.PracticeSubmissionPayload{PracticeSubmission: form.PracticeSubmission},
			},
			Message: translator.Translate(nil, "infoMsg_updateSuccess"),
		},
	}, nil
}
