package userResolvers

import (
	userInputs "vibico-education-api/internal/gqls/inputs/users"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	userPayloads "vibico-education-api/internal/gqls/payloads/users"
	"vibico-education-api/internal/gqls/resolvers"
	"vibico-education-api/internal/models"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

func (r *Resolver) MyCourses(args userInputs.CoursesInput) (*userPayloads.CoursesCollectionPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("UserResolver.MyCourses")

	paginationData, err := r.Repos.CourseRepo().ListByUser(
		r.Ctx,
		args,
		r.currentUser.ID,
		r.DetectAssociations("myCourses", r.buildCoursesAssociations())...,
	)
	if err != nil {
		return nil, err
	}

	courses, ok := paginationData.Collection.([]*models.Course)
	resolvers := r.sliceCourseToTypes(courses, ok)

	return &userPayloads.CoursesCollectionPayload{
		Collection: &resolvers,
		Metadata: &globalPayloads.MetadataPayload{
			Metadata: paginationData.Metadata,
		},
	}, nil
}

func (r *Resolver) sliceCourseToTypes(courses []*models.Course, ok bool) []*userPayloads.CoursePayload {
	resolvers := make([]*userPayloads.CoursePayload, len(courses))
	if ok {
		for i, e := range courses {
			var joinedUser *models.CourseUser
			if len(e.CourseUsers) > 0 {
				joinedUser = e.CourseUsers[0]
			}
			resolvers[i] = &userPayloads.CoursePayload{
				CoursePayload: &globalPayloads.CoursePayload{Course: e},
				JoinedUser:    joinedUser,
			}
		}
	}
	return resolvers
}

func (r *Resolver) buildCoursesAssociations() []resolvers.RequestAssociation {
	userFilter := func(db *gorm.DB) *gorm.DB {
		return db.Where("course_users.user_id = ?", r.currentUser.ID)
	}

	return []resolvers.RequestAssociation{
		{RequestKey: "collection.teacher", Key: "Teacher"},
		{RequestKey: "collection.joined", Key: "CourseUsers", Args: []any{userFilter}},
		{RequestKey: "collection.processPercent", Key: "CourseUsers", Args: []any{userFilter}},
		{RequestKey: "collection.courseUserMetadata", Key: "CourseUsers", Args: []any{userFilter}},
	}
}
