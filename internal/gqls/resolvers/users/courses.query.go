package userResolvers

import (
	userInputs "vibico-education-api/internal/gqls/inputs/users"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	userPayloads "vibico-education-api/internal/gqls/payloads/users"
	"vibico-education-api/internal/gqls/resolvers"
	"vibico-education-api/internal/models"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

func (r *Resolver) UserCourses(args userInputs.CoursesInput) (*userPayloads.CoursesCollectionPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("UserResolver.UserCourses")

	userFilter := func(db *gorm.DB) *gorm.DB {
		return db.Where("course_users.user_id = ?", r.currentUser.ID)
	}

	associations := []resolvers.RequestAssociation{
		{RequestKey: "collection.teacher", Key: "Teacher"},
		{RequestKey: "collection.joined", Key: "CourseUsers", Args: []any{userFilter}},
		{RequestKey: "collection.processPercent", Key: "CourseUsers", Args: []any{userFilter}},
		{RequestKey: "collection.courseUserMetadata", Key: "CourseUsers", Args: []any{userFilter}},
	}

	paginationData, err := r.Repos.CourseRepo().ListWithUser(
		r.Ctx,
		args,
		r.currentUser.ID,
		r.DetectAssociations("userCourses", associations)...,
	)
	if err != nil {
		return nil, err
	}

	courses, ok := paginationData.Collection.([]*models.Course)
	resolvers := r.sliceCourseToTypes(courses, ok)

	return &userPayloads.CoursesCollectionPayload{
		Collection: &resolvers,
		Metadata: &globalPayloads.MetadataPayload{
			Metadata: paginationData.Metadata,
		},
	}, nil
}
