package userResolvers

import (
	userInputs "vibico-education-api/internal/gqls/inputs/users"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	userPayloads "vibico-education-api/internal/gqls/payloads/users"
	"vibico-education-api/internal/repository/repositories"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

func (r *Resolver) Drills(args userInputs.DrillInput) (*userPayloads.DrillsCollectionPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("Resolver.Drills")

	paginationData, err := r.Repos.DrillRepo().ListWithUser(
		r.Ctx,
		args,
		r.currentUser.ID,
		repositories.CustomPreload{Key: "DrillSkills.Skill"},
		repositories.CustomPreload{
			Key: "Diagrams",
			Args: []any{
				func(db *gorm.DB) *gorm.DB {
					return db.Order("diagrams.position ASC")
				},
			}},
	)

	if err != nil {
		return nil, err
	}

	return &userPayloads.DrillsCollectionPayload{
		Collection: userPayloads.DrillsSliceToTypes(paginationData.Collection),
		Metadata: &globalPayloads.MetadataPayload{
			Metadata: paginationData.Metadata,
		},
	}, nil
}
