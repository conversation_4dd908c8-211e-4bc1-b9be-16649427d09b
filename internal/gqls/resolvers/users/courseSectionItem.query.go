package userResolvers

import (
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	userPayloads "vibico-education-api/internal/gqls/payloads/users"
	"vibico-education-api/internal/gqls/resolvers"

	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

func (r *Resolver) UserCourseSectionItem(
	args struct {
		ItemSlug   string
		CourseSlug string
	},
) (*userPayloads.CourseSectionItemPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("UserResolver.CourseSectionItem")

	course, err := r.Repos.CourseRepo().FindBySlugAndUser(r.Ctx, args.CourseSlug, r.currentUser.ID)
	if err != nil {
		return nil, err
	}

	sectionItem, err := r.Repos.CourseSectionItemRepo().
		FindBySlugAndCourseId(
			r.Ctx,
			args.ItemSlug,
			course.ID,
			r.DetectAssociations("userCourseSectionItem", []resolvers.RequestAssociation{
				{RequestKey: "drills", Args: []any{func(db *gorm.DB) *gorm.DB {
					return db.Order("drills.id ASC")
				}}},
				{RequestKey: "drills.diagrams"},
				{RequestKey: "drills.skills", Key: "Drills.DrillSkills.Skill"},
			})...,
		)

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, exceptions.NewRecordNotFoundError()
		}
		return nil, err
	}
	return &userPayloads.CourseSectionItemPayload{
		CourseSectionItemPayload: &globalPayloads.CourseSectionItemPayload{
			SectionItem: sectionItem,
		},
	}, nil
}
