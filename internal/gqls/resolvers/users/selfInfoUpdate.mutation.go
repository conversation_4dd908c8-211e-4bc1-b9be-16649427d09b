package userResolvers

import (
	"vibico-education-api/internal/forms/users/infoForms"
	userInputs "vibico-education-api/internal/gqls/inputs/users"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	translator "vibico-education-api/pkg/translators"

	"github.com/rs/zerolog/log"
)

func (r *Resolver) UpdateSelfInfo(args struct {
	Input userInputs.SelfInfoInput
}) (*globalPayloads.MessageInfoPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("UserResolver.UpdateSelfInfo")

	form := infoForms.NewUpdateForm(r.Ctx, args.Input, r.currentUser, r.Repos.UserRepo())
	if err := form.Save(); err != nil {
		return nil, err
	}

	return &globalPayloads.MessageInfoPayload{
		Message: translator.Translate(nil, "general_updateSuccess"),
	}, nil
}
