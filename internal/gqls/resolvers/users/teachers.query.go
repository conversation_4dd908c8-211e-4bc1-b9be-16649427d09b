package userResolvers

import (
	"vibico-education-api/internal/enums"
	userInputs "vibico-education-api/internal/gqls/inputs/users"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	userPayloads "vibico-education-api/internal/gqls/payloads/users"
	"vibico-education-api/internal/gqls/resolvers"
	"vibico-education-api/internal/models"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

func (r *Resolver) Teachers(args userInputs.TeachersInput) (*userPayloads.TeachersCollectionPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("UserResolver.Teachers")

	paginationData, err := r.Repos.TeacherRepo().UserTeacherList(
		r.Ctx,
		args,
		r.DetectAssociations("teachers", []resolvers.RequestAssociation{
			{RequestKey: "collection.courses", Key: "Courses", Args: []any{
				func(db *gorm.DB) *gorm.DB {
					return db.Joins("LEFT JOIN course_users ON course_users.course_id = courses.id").
						Where("(course_users.user_id = ?) OR (courses.is_public = true AND courses.status = ?)",
							r.currentUser.ID, enums.CourseStatusApproved)
				},
			}},
			{RequestKey: "courses.currentUserJoining", Key: "Courses.CourseUsers", Args: []any{
				func(db *gorm.DB) *gorm.DB {
					return db.Where("course_users.user_id = ?", r.currentUser.ID)
				},
			}},
		})...,
	)

	if err != nil {
		return nil, err
	}

	teachers, ok := paginationData.Collection.([]*models.Teacher)
	resolvers := make([]*userPayloads.TeacherPayload, len(teachers))
	if ok {
		for i, e := range teachers {
			resolvers[i] = &userPayloads.TeacherPayload{TeacherPayload: &globalPayloads.TeacherPayload{Teacher: e}}
		}
	}

	return &userPayloads.TeachersCollectionPayload{
		Collection: &resolvers,
		Metadata: &globalPayloads.MetadataPayload{
			Metadata: paginationData.Metadata,
		},
	}, nil
}
