package userResolvers

import (
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	userPayloads "vibico-education-api/internal/gqls/payloads/users"
	"vibico-education-api/internal/gqls/resolvers"
	"vibico-education-api/internal/repository/repositories"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

func (r *Resolver) MyCourse(args struct{ Slug string }) (*userPayloads.CoursePayload, error) {
	log.Info().Ctx(r.Ctx).Msg("UserResolver.MyCourse")

	course, err := r.Repos.CourseRepo().FindBySlugAndUser(
		r.Ctx,
		args.Slug,
		r.currentUser.ID,
		r.DetectAssociations("myCourse", r.buildCourseAssociations())...,
	)

	if err != nil {
		return nil, err
	}

	courseUser := course.GetCourseUser(r.currentUser.ID)
	courseUser.User = r.currentUser

	return &userPayloads.CoursePayload{
		CoursePayload: &globalPayloads.CoursePayload{Course: course},
		JoinedUser:    courseUser,
	}, nil
}

func (r *Resolver) buildCourseAssociations() []resolvers.RequestAssociation {
	userFilter := func(db *gorm.DB) *gorm.DB {
		return db.Where("course_users.user_id = ?", r.currentUser.ID)
	}

	return []resolvers.RequestAssociation{
		{RequestKey: "teacher"},
		{RequestKey: "courseSections", Args: []any{
			func(db *gorm.DB) *gorm.DB {
				return db.Order("course_sections.position ASC")
			},
		}},
		{RequestKey: "courseSections.courseSectionItems", Args: []any{
			func(db *gorm.DB) *gorm.DB {
				return db.Order(repositories.CSIOrderPositionAscSQL)
			},
		}},
		{RequestKey: "joined", Key: "CourseUsers", Args: []any{userFilter}},
		{RequestKey: "processPercent", Key: "CourseUsers", Args: []any{userFilter}},
		{RequestKey: "courseUserMetadata", Key: "CourseUsers", Args: []any{userFilter}},
		{
			RequestKey: "courseSections.isCompleted",
			Key:        "CourseSections.UserCourseSection",
			Args: []any{func(db *gorm.DB) *gorm.DB {
				return db.Where("user_course_sections.user_id = ?", r.currentUser.ID)
			}},
		},
		{
			RequestKey: "courseSectionItems.isCompleted",
			Key:        "CourseSections.CourseSectionItems.UserCourseSectionItems",
			Args: []any{func(db *gorm.DB) *gorm.DB {
				return db.Where("user_course_section_items.user_id = ?", r.currentUser.ID)
			}},
		},
		{
			RequestKey: "courseSectionItems.currentUserSectionItem",
			Key:        "CourseSections.CourseSectionItems.UserCourseSectionItems",
			Args: []any{func(db *gorm.DB) *gorm.DB {
				return db.Where("user_course_section_items.user_id = ?", r.currentUser.ID)
			}},
		},
		{
			RequestKey: "myReview",
			Key:        "Comments",
			Args: []any{func(db *gorm.DB) *gorm.DB {
				return db.Where("comments.author_id = ? AND comments.author_type = 'User'", r.currentUser.ID)
			}},
		},
		{RequestKey: "courseSectionItems.videos",
			Key: "CourseSections.CourseSectionItems.Videos",
			Args: []any{func(db *gorm.DB) *gorm.DB {
				return db.Order("videos.id ASC")
			}}},
		{RequestKey: "courseSectionItems.videos", Key: "CourseSections.CourseSectionItems.Videos.VideoProgress", Args: []any{func(db *gorm.DB) *gorm.DB {
			return db.Where("video_progresses.user_id = ?", r.currentUser.ID)
		}}},
	}
}
