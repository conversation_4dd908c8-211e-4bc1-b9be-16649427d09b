package userResolvers

import (
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
	"github.com/rs/zerolog/log"
)

func (r *Resolver) MarkVideoProgress(
	args struct {
		VideoId  graphql.ID
		Progress int32
	},
) (*globalPayloads.MessageInfoPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("UserResolver.MarkVideoProgress")

	videoId, err := utils.ParseGraphqlID[uint32](args.VideoId)
	if err != nil {
		return nil, err
	}

	videoProgress, err := r.Repos.VideoProgressRepo().FindOrCreateByUserAndVideo(r.Ctx, r.currentUser.ID, videoId)
	if err != nil {
		return nil, err
	}

	err = r.Repos.VideoProgressRepo().UpdateProgress(r.Ctx, videoProgress.ID, args.Progress, false)
	if err != nil {
		return nil, err
	}

	return &globalPayloads.MessageInfoPayload{
		Message: translator.Translate(nil, "general_updateSuccess"),
	}, nil
}
