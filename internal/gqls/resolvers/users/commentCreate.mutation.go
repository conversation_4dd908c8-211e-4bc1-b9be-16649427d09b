package userResolvers

import (
	"vibico-education-api/internal/forms/users/commentForms"
	globalInputs "vibico-education-api/internal/gqls/inputs/global"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	userPayloads "vibico-education-api/internal/gqls/payloads/users"
	translator "vibico-education-api/pkg/translators"

	"github.com/rs/zerolog/log"
)

func (r *Resolver) CommentCreate(args struct {
	Input globalInputs.CommentCreateInput
}) (*userPayloads.CommentCreatePayload, error) {
	log.Info().Ctx(r.Ctx).Msg("UserResolver.CommentCreate")

	form := commentForms.NewCreateForm(r.Ctx, args.Input, r.currentUser, r.Repos)
	if err := form.Save(); err != nil {
		return nil, err
	}

	return &userPayloads.CommentCreatePayload{
		Comment: &userPayloads.CommentPayload{CommentPayload: &globalPayloads.CommentPayload{Comment: form.Comment}},
		Message: translator.Translate(nil, "infoMsg_createSuccess"),
	}, nil
}
