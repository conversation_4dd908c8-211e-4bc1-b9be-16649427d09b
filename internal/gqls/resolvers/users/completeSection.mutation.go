package userResolvers

import (
	"errors"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
	"github.com/rs/zerolog/log"
)

func (r *Resolver) CompleteSection(args struct {
	ID       graphql.ID
	CourseID graphql.ID
}) (*globalPayloads.MessageInfoPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("UserResolver.CompleteSection")

	sectionID, err := utils.ParseGraphqlID[uint32](args.ID)
	if err != nil {
		return nil, err
	}
	courseID, err := utils.ParseGraphqlID[uint32](args.CourseID)
	if err != nil {
		return nil, err
	}

	_, err = r.Repos.CourseUserRepo().FindByUserAndCourse(r.Ctx, courseID, r.currentUser.ID)
	if err != nil {
		return nil, err
	}

	userSectionRepo := r.Repos.UserCourseSectionRepo()
	userSection := userSectionRepo.FindOrInit(r.Ctx, r.currentUser.ID, sectionID)
	if userSection.ID != 0 {
		return nil, errors.New(translator.Translate(nil, "errDbMsg_AlreadyCompleted"))
	}

	if err := userSectionRepo.Create(r.Ctx, userSection, r.currentUser.ID); err != nil {
		return nil, err
	}

	return &globalPayloads.MessageInfoPayload{
		Message: translator.Translate(nil, "infoMsg_completeSuccess"),
	}, nil
}
