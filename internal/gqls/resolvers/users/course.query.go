package userResolvers

import (
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	userPayloads "vibico-education-api/internal/gqls/payloads/users"
	"vibico-education-api/internal/gqls/resolvers"
	"vibico-education-api/internal/models"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

func (r *Resolver) UserCourse(args struct{ Slug string }) (*userPayloads.CoursePayload, error) {
	log.Info().Ctx(r.Ctx).Msg("UserResolver.UserCourse")

	userFilter := func(db *gorm.DB) *gorm.DB {
		return db.Where("course_users.user_id = ?", r.currentUser.ID)
	}

	course, err := r.Repos.CourseRepo().FindBySlugWithUser(
		r.Ctx,
		args.Slug,
		r.DetectAssociations("userCourse", []resolvers.RequestAssociation{
			{RequestKey: "teacher"},
			{RequestKey: "courseSections"},
			{RequestKey: "joined", Key: "CourseUsers", Args: []any{userFilter}},
			{RequestKey: "processPercent", Key: "CourseUsers", Args: []any{userFilter}},
			{RequestKey: "courseUserMetadata", Key: "CourseUsers", Args: []any{userFilter}},
			{
				RequestKey: "courseSections.isCompleted",
				Key:        "CourseSections.UserCourseSection",
				Args: []any{func(db *gorm.DB) *gorm.DB {
					return db.Where("user_course_sections.user_id = ?", r.currentUser.ID)
				}},
			},
			{
				RequestKey: "courseSections.courseSectionItems",
				Key:        "CourseSections.CourseSectionItems",
				Args: []any{func(db *gorm.DB) *gorm.DB {
					return db.Order("course_section_items.position ASC")
				}},
			},
		})...,
	)
	if err != nil {
		return nil, err
	}

	var joinedUser *models.CourseUser
	if len(course.CourseUsers) > 0 {
		joinedUser = course.CourseUsers[0]
	}

	return &userPayloads.CoursePayload{
		CoursePayload: &globalPayloads.CoursePayload{Course: course},
		JoinedUser:    joinedUser,
	}, nil
}
