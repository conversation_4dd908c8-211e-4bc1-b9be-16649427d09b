package userResolvers

import (
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	userPayloads "vibico-education-api/internal/gqls/payloads/users"
	"vibico-education-api/internal/gqls/resolvers"
	"vibico-education-api/internal/models"
	"vibico-education-api/internal/repository/repositories"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

func (r *Resolver) UserCourseSection(args struct {
	SectionSlug *string
	CourseSlug  string
}) (*userPayloads.CourseSectionPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("UserResolver.UserCourseSection")

	course, err := r.Repos.CourseRepo().FindBySlugAndUser(r.Ctx, args.CourseSlug, r.currentUser.ID)
	if err != nil {
		return nil, err
	}

	var section *models.CourseSection
	if args.SectionSlug == nil || *args.SectionSlug == "" {
		section, err = r.Repos.CourseSectionRepo().FindUserCurrentSection(
			r.Ctx,
			course.ID,
			r.currentUser.ID,
			r.GetAssociations()...,
		)
	} else {
		section, err = r.Repos.CourseSectionRepo().FindBySlugAndCourseId(
			r.Ctx,
			*args.SectionSlug,
			course.ID,
			r.GetAssociations()...,
		)
	}

	if err != nil {
		return nil, err
	}

	return &userPayloads.CourseSectionPayload{
		CourseSectionPayload: &globalPayloads.CourseSectionPayload{
			CourseSection: section,
		},
	}, nil
}

func (r *Resolver) GetAssociations() []repositories.CustomPreload {
	return r.DetectAssociations("userCourseSection", []resolvers.RequestAssociation{
		// {RequestKey: "courseSectionItems"},
		// {RequestKey: "courseSectionItems.drills", Key: "CourseSectionItems.Drills"},
		{RequestKey: "isCompleted", Key: "UserCourseSections", Args: []any{
			func(db *gorm.DB) *gorm.DB {
				return db.Where("user_course_sections.user_id = ?", r.currentUser.ID)
			},
		}},
	})
}
