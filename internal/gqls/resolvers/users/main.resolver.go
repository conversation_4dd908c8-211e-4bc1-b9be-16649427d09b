package userResolvers

import (
	"context"
	"vibico-education-api/internal/gqls/resolvers"
	"vibico-education-api/internal/middlewares"
	"vibico-education-api/internal/models"
	"vibico-education-api/internal/repository"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/davecgh/go-spew/spew"
	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
)

type Resolver struct {
	resolvers.Resolver

	currentUser *models.User
}

func NewResolver(repos repository.IRepositories, ginCtx *gin.Context) *Resolver {
	return &Resolver{
		Resolver: *resolvers.NewResolver(repos, ginCtx),
	}
}

func (r *Resolver) Test(ctx context.Context) (*string, error) {
	log.Info().Msg("Resolver.Test")
	spew.Dump("Resolver.Test")

	return utils.PointerString("test"), nil
}

func (r *Resolver) Auth() error {
	log.Debug().Ctx(r.Ctx).Msg("userResolvers.Auth")

	user, err := middlewares.UserAuth(r.Ctx, r.Repos)
	if err != nil {
		return err
	}

	r.currentUser = user

	return nil
}

func (r *Resolver) GetCurrentUser() *models.User {
	return r.currentUser
}

func (r *Resolver) GetPublicOperations() map[string]struct{} {
	publicOperations := r.Resolver.GetPublicOperations()

	return publicOperations
}
