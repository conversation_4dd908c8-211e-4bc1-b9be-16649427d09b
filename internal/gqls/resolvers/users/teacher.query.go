package userResolvers

import (
	"vibico-education-api/internal/enums"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	userPayloads "vibico-education-api/internal/gqls/payloads/users"
	"vibico-education-api/internal/gqls/resolvers"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

func (r *Resolver) Teacher(args struct{ Slug string }) (*userPayloads.TeacherPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("UserResolver.Teacher")

	resultTeacher, err := r.Repos.TeacherRepo().FindBySlug(r.Ctx, args.Slug, r.DetectAssociations("teacher", []resolvers.RequestAssociation{
		{RequestKey: "courses", Key: "Courses", Args: []any{
			func(db *gorm.DB) *gorm.DB {
				return db.Joins("LEFT JOIN course_users ON course_users.course_id = courses.id").
					Where("(course_users.user_id = ?) OR (courses.is_public = true AND courses.status = ?)",
						r.currentUser.ID, enums.CourseStatusApproved)
			},
		}},
		{RequestKey: "courses.currentUserJoining", Key: "Courses.CourseUsers", Args: []any{
			func(db *gorm.DB) *gorm.DB {
				return db.Where("course_users.user_id = ?", r.currentUser.ID)
			},
		}},
	})...)
	if err != nil {
		return nil, err
	}

	return &userPayloads.TeacherPayload{
		TeacherPayload: &globalPayloads.TeacherPayload{Teacher: resultTeacher},
	}, nil
}
