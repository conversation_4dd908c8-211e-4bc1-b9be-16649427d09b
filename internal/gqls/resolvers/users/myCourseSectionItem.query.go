package userResolvers

import (
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	userPayloads "vibico-education-api/internal/gqls/payloads/users"
	"vibico-education-api/internal/gqls/resolvers"
	"vibico-education-api/internal/models"

	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

func (r *Resolver) MyCourseSectionItem(
	args struct {
		ItemSlug   *string
		CourseSlug string
	},
) (*userPayloads.CourseSectionItemPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("UserResolver.MyCourseSectionItem")

	course, err := r.Repos.CourseRepo().FindBySlugAndUser(r.Ctx, args.CourseSlug, r.currentUser.ID)
	if err != nil {
		return nil, err
	}

	var sectionItem *models.CourseSectionItem
	repo := r.Repos.CourseSectionItemRepo()

	associations := r.DetectAssociations("myCourseSectionItem", []resolvers.RequestAssociation{
		{RequestKey: "drills", Args: []any{func(db *gorm.DB) *gorm.DB {
			return db.Order("drills.id ASC")
		}}},
		{RequestKey: "drills.diagrams"},
		{RequestKey: "drills.skills", Key: "Drills.DrillSkills.Skill"},
		{RequestKey: "isCompleted", Key: "UserCourseSectionItems", Args: []any{
			func(db *gorm.DB) *gorm.DB {
				return db.Where("user_course_section_items.user_id = ?", r.currentUser.ID)
			},
		}},
		{RequestKey: "currentUserSectionItem", Key: "UserCourseSectionItems", Args: []any{
			func(db *gorm.DB) *gorm.DB {
				return db.Where("user_course_section_items.user_id = ?", r.currentUser.ID)
			},
		}},
		{RequestKey: "videos", Args: []any{func(db *gorm.DB) *gorm.DB {
			return db.Order("videos.id ASC")
		}}},
		{RequestKey: "videos", Key: "videos.VideoPlatforms"},
		{RequestKey: "videos", Key: "videos.VideoProgress", Args: []any{func(db *gorm.DB) *gorm.DB {
			return db.Where("video_progresses.user_id = ?", r.currentUser.ID)
		}}},
	})

	if args.ItemSlug == nil || *args.ItemSlug == "" {
		sectionItem, err = repo.FindUserCurrentSectionItem(
			r.Ctx,
			course.ID,
			r.currentUser.ID,
			associations...,
		)
	} else {
		sectionItem, err = repo.FindBySlugAndCourseId(
			r.Ctx,
			*args.ItemSlug,
			course.ID,
			associations...,
		)
	}

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, exceptions.NewRecordNotFoundError()
		}
		return nil, err
	}

	return &userPayloads.CourseSectionItemPayload{
		CourseSectionItemPayload: &globalPayloads.CourseSectionItemPayload{
			SectionItem: sectionItem,
		},
		CurrentUser: r.currentUser,
	}, nil
}
