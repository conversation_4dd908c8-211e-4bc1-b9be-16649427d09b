package userResolvers

import (
	"vibico-education-api/internal/forms/users/userAuthForms"
	userInputs "vibico-education-api/internal/gqls/inputs/users"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	translator "vibico-education-api/pkg/translators"

	"github.com/rs/zerolog/log"
)

func (r *Resolver) ChangePassword(args struct {
	Input userInputs.ChangePasswordInput
}) (*globalPayloads.MessageInfoPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("UserResolver.ChangePassword")

	form := userAuthForms.NewUserChangePasswordForm(r.Ctx, r.currentUser, args.Input)

	if err := form.Save(); err != nil {
		return nil, err
	}

	return &globalPayloads.MessageInfoPayload{
		Message: translator.Translate(nil, "general_success"),
	}, nil
}
