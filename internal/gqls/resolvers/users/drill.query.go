package userResolvers

import (
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	userPayloads "vibico-education-api/internal/gqls/payloads/users"
	"vibico-education-api/internal/gqls/resolvers"
	"vibico-education-api/internal/repository/repositories"

	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

func (r *Resolver) Drill(args struct{ Slug string }) (
	*userPayloads.DrillPayload, error,
) {
	log.Info().Ctx(r.Ctx).Msg("UserResolver.Drill")

	relations := r.DetectAssociations("drill", []resolvers.RequestAssociation{
		{RequestKey: "skills", Key: "DrillSkills.Skill"},
		{RequestKey: "diagrams"},
		{RequestKey: "videos", Args: []any{func(db *gorm.DB) *gorm.DB {
			return db.Order("videos.id ASC")
		}}},
		{RequestKey: "videos.videoPlatforms"},
	})

	relations = append(relations, repositories.CustomPreload{
		Key: "UserDrills",
		Args: []any{func(db *gorm.DB) *gorm.DB {
			return db.Where("user_drills.user_id = ?", r.currentUser.ID).Limit(1)
		}}})

	drill, err := r.Repos.DrillRepo().FindApprovedBySlugWithUser(
		r.Ctx,
		args.Slug,
		r.currentUser.ID,
		relations...,
	)

	isOwner := false
	if drill.OwnerType == "Teacher" {
		owner, err := r.Repos.TeacherRepo().FindById(r.Ctx, drill.OwnerID)
		if err != nil {
			return nil, err
		}
		isOwner = owner.AuthId == r.currentUser.AuthId

		if !isOwner && !owner.Active {
			return nil, exceptions.NewRecordNotFoundError()
		}
	}

	isBought := drill.IsBoughtByUser(r.currentUser.ID)
	isVisible := isOwner || drill.IsFree() || isBought

	if err != nil {
		return nil, err
	}

	return &userPayloads.DrillPayload{
		DrillPayload: &globalPayloads.DrillPayload{
			Drill: drill,
		},
		IsVisible: isVisible,
	}, nil
}
