package userResolvers

import (
	"errors"
	"vibico-education-api/internal/enums"
	userInputs "vibico-education-api/internal/gqls/inputs/users"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	userPayloads "vibico-education-api/internal/gqls/payloads/users"
	"vibico-education-api/internal/gqls/resolvers"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

func (r *Resolver) Review(args userInputs.ReviewQueryInput) (*userPayloads.CommentPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("userResolvers.Review")

	if args.TargetID == nil {
		return nil, errors.New("TargetID: " + translator.Translate(nil, "errValidationMsg_required"))
	}

	if args.TargetType == nil {
		return nil, errors.New("TargetType: " + translator.Translate(nil, "errValidationMsg_required"))

	}
	targetType, err := enums.ParseCommentTargetType(*args.TargetType)
	if err != nil {
		return nil, err
	}

	targetID, err := utils.ParseGraphqlID[uint32](*args.TargetID)
	if err != nil {
		return nil, err
	}

	comment, err := r.Repos.CommentRepo().FindByTargetAndAuthor(r.Ctx, targetID, r.currentUser.ID, string(targetType), "User", r.DetectAssociations("comments", []resolvers.RequestAssociation{
		{RequestKey: "authorUser", Key: "AuthorUser"},
	})...)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil

		}
		return nil, err
	}

	return &userPayloads.CommentPayload{
		CommentPayload: &globalPayloads.CommentPayload{
			Comment: comment,
		},
	}, nil
}
