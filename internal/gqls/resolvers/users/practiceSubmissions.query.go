package userResolvers

import (
	userInputs "vibico-education-api/internal/gqls/inputs/users"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	userPayloads "vibico-education-api/internal/gqls/payloads/users"
	"vibico-education-api/internal/models"
	"vibico-education-api/internal/repository/repositories"

	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
	"github.com/rs/zerolog/log"
)

func (r *Resolver) PracticeSubmissions(args userInputs.PracticeSubmissionsInput) (*userPayloads.PracticeSubmissionsCollectionPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("UserResolver.PracticeSubmissions")

	paginationData, err := r.Repos.PracticeSubmissionRepo().ListByUser(
		r.Ctx,
		args,
		r.currentUser.ID,
		repositories.CustomPreload{Key: "Comments.AuthorTeacher"},
		repositories.CustomPreload{Key: "Comments.AuthorUser"},
		repositories.CustomPreload{Key: "Videos.VideoPlatforms"},
	)
	if err != nil {
		return nil, err
	}

	practiceSubmissions, ok := paginationData.Collection.([]*models.PracticeSubmission)
	if !ok {
		return nil, exceptions.NewUnprocessableContentError("invalid collection type", nil)
	}

	resolvers := make([]*userPayloads.PracticeSubmissionPayload, len(practiceSubmissions))
	for i, ps := range practiceSubmissions {
		resolvers[i] = &userPayloads.PracticeSubmissionPayload{
			PracticeSubmissionPayload: &globalPayloads.PracticeSubmissionPayload{
				PracticeSubmission: ps,
			},
		}
	}

	return &userPayloads.PracticeSubmissionsCollectionPayload{
		Collection: &resolvers,
		Metadata: &globalPayloads.MetadataPayload{
			Metadata: paginationData.Metadata,
		},
	}, nil
}
