package userResolvers

import (
	userPayloads "vibico-education-api/internal/gqls/payloads/users"
	"vibico-education-api/internal/gqls/resolvers"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

func (r *Resolver) MyCourseSectionItems(
	args struct {
		CourseSlug        string
		CourseSectionSlug string
	},
) (*[]*userPayloads.CourseSectionItemPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("UserResolver.MyCourseSectionItems")

	course, err := r.Repos.CourseRepo().FindBySlugAndUser(r.Ctx, args.CourseSlug, r.currentUser.ID)
	if err != nil {
		return nil, err
	}

	sectionItems, err := r.Repos.CourseSectionItemRepo().
		ListByCourseSectionSlug(
			r.Ctx,
			course.ID,
			args.CourseSectionSlug,
			r.DetectAssociations("myCourseSectionItems", []resolvers.RequestAssociation{
				{RequestKey: "drills"},
				{RequestKey: "drills.diagrams"},
				{RequestKey: "drills.skills", Key: "Drills.DrillSkills.Skill"},
				{RequestKey: "isCompleted", Key: "UserCourseSectionItems", Args: []any{
					func(db *gorm.DB) *gorm.DB {
						return db.Where("user_course_section_items.user_id = ?", r.currentUser.ID)
					},
				}},
			})...,
		)
	if err != nil {
		return nil, err
	}

	return sectionItemSliceToType(*sectionItems), nil
}
