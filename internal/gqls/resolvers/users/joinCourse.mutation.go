package userResolvers

import (
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	userServices "vibico-education-api/internal/services/users"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
	"github.com/rs/zerolog/log"
)

func (r *Resolver) JoinCourse(
	args struct {
		ID              graphql.ID
		VerifyCode      *string
		CoursePackageID *graphql.ID
	},
) (*globalPayloads.MessageInfoPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("UserResolver.JoinCourse")

	courseID, err := utils.ParseGraphqlID[uint32](args.ID)
	if err != nil {
		return nil, err
	}

	var coursePackageId uint32
	if args.CoursePackageID != nil {
		coursePackageId, err = utils.ParseGraphqlID[uint32](*args.CoursePackageID)
		if err != nil {
			return nil, err
		}
	}

	service := userServices.NewJoinCourseService(
		r.Ctx,
		r.Repos.CourseRepo(),
		r.Repos.CourseUserRepo(),
		r.Repos.CoursePackageRepo(),
		r.currentUser,
		courseID,
		args.VerifyCode,
		coursePackageId,
	)

	return service.Execute()
}
