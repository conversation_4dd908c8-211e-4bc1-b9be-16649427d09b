package userResolvers

import (
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	userPayloads "vibico-education-api/internal/gqls/payloads/users"
	"vibico-education-api/internal/gqls/resolvers"
	"vibico-education-api/internal/models"

	"github.com/rs/zerolog/log"
)

func (r *Resolver) UserCourseSectionItems(
	args struct {
		CourseSlug        string
		CourseSectionSlug string
	},
) (*[]*userPayloads.CourseSectionItemPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("UserResolver.CourseSectionItems")

	course, err := r.Repos.CourseRepo().FindBySlugWithUser(r.Ctx, args.CourseSlug)
	if err != nil {
		return nil, err
	}

	sectionItems, err := r.Repos.CourseSectionItemRepo().
		ListByCourseSectionSlug(
			r.Ctx,
			course.ID,
			args.CourseSectionSlug,
			r.DetectAssociations("userCourseSectionItems", []resolvers.RequestAssociation{
				{RequestKey: "drills"},
				{RequestKey: "drills.diagrams"},
				{RequestKey: "drills.skills", Key: "Drills.DrillSkills.Skill"},
			})...,
		)
	if err != nil {
		return nil, err
	}

	return sectionItemSliceToType(*sectionItems), nil
}

func sectionItemSliceToType(
	sectionItems []*models.CourseSectionItem,
) *[]*userPayloads.CourseSectionItemPayload {
	var sectionItemPayloads []*userPayloads.CourseSectionItemPayload
	for _, item := range sectionItems {
		sectionItemPayloads = append(sectionItemPayloads, &userPayloads.CourseSectionItemPayload{
			CourseSectionItemPayload: &globalPayloads.CourseSectionItemPayload{
				SectionItem: item,
			},
		})
	}

	return &sectionItemPayloads
}
