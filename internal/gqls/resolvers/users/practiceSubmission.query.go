package userResolvers

import (
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	userPayloads "vibico-education-api/internal/gqls/payloads/users"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
	"github.com/rs/zerolog/log"
)

func (r *Resolver) PracticeSubmission(args struct{ ID graphql.ID }) (*userPayloads.PracticeSubmissionPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("UserResolver.PracticeSubmission")

	practiceSubmissionID, err := utils.ParseGraphqlID[uint32](args.ID)
	if err != nil {
		return nil, err
	}

	practiceSubmission, err := r.Repos.PracticeSubmissionRepo().FindByIDAndUserID(r.Ctx, practiceSubmissionID, r.currentUser.ID)
	if err != nil {
		return nil, err
	}

	return &userPayloads.PracticeSubmissionPayload{
		PracticeSubmissionPayload: &globalPayloads.PracticeSubmissionPayload{PracticeSubmission: practiceSubmission},
	}, nil
}
