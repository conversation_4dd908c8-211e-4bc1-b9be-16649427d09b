package userResolvers

import (
	"context"
	"fmt"
	"time"
	"vibico-education-api/internal/forms/users/practiceSubmissionForms"
	userInputs "vibico-education-api/internal/gqls/inputs/users"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	userPayloads "vibico-education-api/internal/gqls/payloads/users"
	"vibico-education-api/internal/models"
	globalServices "vibico-education-api/internal/services/globals"
	"vibico-education-api/pkg/helpers"
	translator "vibico-education-api/pkg/translators"

	"github.com/redis/go-redis/v9"
	"github.com/rs/zerolog/log"
)

func (r *Resolver) PracticeSubmissionCreate(args struct {
	Input userInputs.PracticeSubmissionCreateInput
}) (*userPayloads.PracticeSubmissionCreatePayload, error) {
	log.Info().Ctx(r.Ctx).Msg("UserResolver.PracticeSubmissionCreate")

	form := practiceSubmissionForms.NewCreateForm(r.Ctx, args.Input, r.currentUser, r.Repos.PracticeSubmissionRepo(), r.<PERSON>)
	if err := form.Save(); err != nil {
		return nil, err
	}

	if form.PracticeSubmission.Videos != nil && len(*form.PracticeSubmission.Videos) > 0 {
		ctx, cancel := context.WithCancel(r.Ctx)
		go func(ctx context.Context, vid *models.Video) {
			defer cancel()
			red := helpers.GetRedisClient()
			lastID := "0"

			for {
				res, err := red.XRead(ctx, &redis.XReadArgs{
					Streams: []string{"video_events", lastID},
					Count:   1,
					Block:   5 * time.Minute,
				}).Result()

				if err != nil {
					log.Error().Err(err).Ctx(r.Ctx).Msg("XRead error")
					select {
					case <-time.After(10 * time.Second):
						continue
					case <-ctx.Done():
						return
					}
				}

				for _, sr := range res {
					for _, msg := range sr.Messages {
						if fmt.Sprint(msg.Values["video_id"]) == fmt.Sprint(vid.ID) &&
							msg.Values["event"] == "video_uploaded" {
							service := globalServices.NewYouTubeUploadService(r.Ctx, []*models.Video{vid})
							service.Execute()
							return
						}
						lastID = msg.ID
					}
				}
			}
		}(ctx, (*form.PracticeSubmission.Videos)[0])
	}

	return &userPayloads.PracticeSubmissionCreatePayload{
		PracticeSubmissionMutatePayload: userPayloads.PracticeSubmissionMutatePayload{
			PracticeSubmission: &userPayloads.PracticeSubmissionPayload{
				PracticeSubmissionPayload: &globalPayloads.PracticeSubmissionPayload{PracticeSubmission: form.PracticeSubmission},
			},
			Message: translator.Translate(nil, "infoMsg_createSuccess"),
		},
	}, nil
}
