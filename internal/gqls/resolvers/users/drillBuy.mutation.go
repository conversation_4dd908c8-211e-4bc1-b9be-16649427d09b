package userResolvers

import (
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	userServices "vibico-education-api/internal/services/users"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"

	"github.com/rs/zerolog/log"
)

func (r *Resolver) DrillBuy(args struct{ ID graphql.ID }) (*globalPayloads.MessageInfoPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("UserResolver.DrillBuy")

	drillId, err := utils.ParseGraphqlID[uint32](args.ID)
	if err != nil {
		return nil, err
	}

	service := userServices.NewBuyDrillService(
		r.Ctx,
		r.<PERSON>,
		r.currentUser,
		drillId,
	)

	if err := service.Execute(); err != nil {
		return nil, err
	}

	return &globalPayloads.MessageInfoPayload{
		Message: translator.Translate(nil, "general_success"),
	}, nil
}
