package userResolvers

import (
	userInputs "vibico-education-api/internal/gqls/inputs/users"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	userServices "vibico-education-api/internal/services/users"
	translator "vibico-education-api/pkg/translators"

	"github.com/rs/zerolog/log"
)

func (r *Resolver) SetSectionItemStatus(args userInputs.SetSectionItemStatusInput) (*globalPayloads.MessageInfoPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("UserResolver.SetSectionItemStatus")

	service := userServices.NewSetSectionItemStatusService(r.Ctx, r.<PERSON>os, r.currentUser, args)

	if err := service.Execute(); err != nil {
		return nil, err
	}

	return &globalPayloads.MessageInfoPayload{
		Message: translator.Translate(nil, "infoMsg_updateSuccess"),
	}, nil
}
