package publicResolvers

import (
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	translator "vibico-education-api/pkg/translators"
	"vibico-education-api/setup/grpc_clients"

	pb "github.com/BehemothLtd/vibico-auth/proto/auth"
	"github.com/rs/zerolog/log"
)

func (r *Resolver) RefreshToken(args struct {
	RefreshToken string
}) (*globalPayloads.TokenPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("publicResolvers.RefreshToken")

	grpcResponse, err := grpc_clients.AuthClient().UserRefreshToken(
		grpc_clients.NewCtx(r.Ctx),
		&pb.UserRefreshTokenRequest{
			IdentityPoolId: grpc_clients.PoolId(),
			RefreshToken:   args.RefreshToken,
		},
	)
	if err != nil {
		return nil, err
	}

	return &globalPayloads.TokenPayload{
		AccessToken:  grpcResponse.AccessToken,
		RefreshToken: grpcResponse.RefreshToken,
		Message:      translator.Translate(nil, "RefreshTokenSuccess"),
	}, nil
}
