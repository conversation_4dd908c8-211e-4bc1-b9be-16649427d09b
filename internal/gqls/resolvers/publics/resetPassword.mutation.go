package publicResolvers

import (
	"vibico-education-api/internal/forms/public/authForms"
	publicInputs "vibico-education-api/internal/gqls/inputs/publics"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	translator "vibico-education-api/pkg/translators"

	"github.com/rs/zerolog/log"
)

func (r *Resolver) ResetPassword(args struct {
	Input publicInputs.ResetPasswordInput
}) (*globalPayloads.MessageInfoPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("publicResolvers.ResetPassword")

	form := authForms.NewResetPasswordForm(r.Ctx, args.Input)

	if err := form.Save(); err != nil {
		return nil, err
	}

	return &globalPayloads.MessageInfoPayload{
		Message: translator.Translate(nil, "general_success"),
	}, nil
}
