package publicResolvers

import (
	"vibico-education-api/internal/enums"
	publicPayloads "vibico-education-api/internal/gqls/payloads/publics"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
	"github.com/rs/zerolog/log"
)

func (r *Resolver) VideoPlayback(args struct {
	VideoID graphql.ID
}) (*publicPayloads.VideoPlaybackPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("publicResolvers.VideoPlayback")

	videoID, err := utils.ParseGraphqlID[uint32](args.VideoID)
	if err != nil {
		return nil, err
	}

	video, err := r.Repos.VideoRepo().FindByID(r.Ctx, videoID)
	if err != nil {
		return nil, err
	}

	// For free/YouTube videos
	if video.IsFree {
		videoPlatform, err := r.Repos.VideoPlatformRepo().FindByVideoIDAndPlatform(r.Ctx, videoID, enums.VideoPlatformYoutube)
		if err != nil {
			return nil, err
		}

		return &publicPayloads.VideoPlaybackPayload{
			IsFree:     true,
			PlaybackID: videoPlatform.PlatformVideoID,
		}, nil
	}

	videoPlatform, err := r.Repos.VideoPlatformRepo().FindByVideoIDAndPlatform(r.Ctx, videoID, enums.VideoPlatformMux)
	if err != nil {
		return nil, err
	}

	return &publicPayloads.VideoPlaybackPayload{
		IsFree:     false,
		PlaybackID: videoPlatform.PlatformVideoID,
	}, nil
}
