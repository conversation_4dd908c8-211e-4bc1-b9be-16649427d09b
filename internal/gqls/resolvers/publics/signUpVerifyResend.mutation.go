package publicResolvers

import (
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	publicServices "vibico-education-api/internal/services/publics"
	translator "vibico-education-api/pkg/translators"

	"github.com/rs/zerolog/log"
)

func (r *Resolver) SignUpVerifyResend(args struct{ PhoneNumber *string }) (*globalPayloads.MessageInfoPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("publicResolvers.SignUpVerifyResend")

	service := publicServices.NewSignUpVerifyResendService(r.Ctx, args.PhoneNumber)
	if err := service.Execute(); err != nil {
		return nil, err
	}

	return &globalPayloads.MessageInfoPayload{Message: translator.Translate(nil, "infoMsg_resentVerificationSmsSuccess")}, nil
}
