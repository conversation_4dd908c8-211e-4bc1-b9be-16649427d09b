package publicResolvers

import (
	publicInputs "vibico-education-api/internal/gqls/inputs/publics"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	publicPayloads "vibico-education-api/internal/gqls/payloads/publics"
	"vibico-education-api/internal/gqls/resolvers"

	"github.com/rs/zerolog/log"
)

func (r *Resolver) Drills(args publicInputs.DrillInput) (*publicPayloads.DrillsCollectionPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("publicResolvers.Drills")

	paginationData, err := r.Repos.DrillRepo().PublicList(
		r.Ctx,
		args,
		r.DetectAssociations("drills", []resolvers.RequestAssociation{
			{RequestKey: "collection.skills", Key: "DrillSkills.Skill"},
			{RequestKey: "collection.diagrams", Key: "Diagrams"},
		})...,
	)

	if err != nil {
		return nil, err
	}

	return &publicPayloads.DrillsCollectionPayload{
		Collection: publicPayloads.DrillsSliceToTypes(paginationData.Collection),
		Metadata: &globalPayloads.MetadataPayload{
			Metadata: paginationData.Metadata,
		},
	}, nil
}
