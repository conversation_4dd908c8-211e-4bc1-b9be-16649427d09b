package publicResolvers

import (
	"vibico-education-api/internal/enums"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	publicPayloads "vibico-education-api/internal/gqls/payloads/publics"
	"vibico-education-api/internal/gqls/resolvers"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

func (r *Resolver) Teacher(args struct{ Slug string }) (*publicPayloads.TeacherPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("publicResolvers.Teacher")

	teacher, err := r.Repos.TeacherRepo().FindBySlug(
		r.Ctx,
		args.Slug,
		r.Detect<PERSON>sociations("teacher", []resolvers.RequestAssociation{
			{RequestKey: "rating", Key: "Ratings"},
			{RequestKey: "courses", Key: "Courses", Args: []any{
				func(db *gorm.DB) *gorm.DB {
					return db.Where("courses.is_public = true AND courses.status = ?", enums.CourseStatusApproved)
				},
			}},
			{RequestKey: "joinedStudentCount", Key: "Courses", Args: []any{
				func(db *gorm.DB) *gorm.DB {
					return db.Where("courses.is_public = true AND courses.status = ?", enums.CourseStatusApproved)
				},
			}},
		})...,
	)
	if err != nil {
		return nil, err
	}

	return &publicPayloads.TeacherPayload{
		TeacherPayload: &globalPayloads.TeacherPayload{Teacher: teacher},
	}, nil
}
