package publicResolvers

import (
	"vibico-education-api/internal/forms/public/authForms"
	globalInputs "vibico-education-api/internal/gqls/inputs/global"
	publicPayloads "vibico-education-api/internal/gqls/payloads/publics"
	translator "vibico-education-api/pkg/translators"
	"vibico-education-api/setup/grpc_clients"

	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
	pb "github.com/BehemothLtd/vibico-auth/proto/auth"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

func (r *Resolver) SignIn(
	args struct {
		Input globalInputs.SignInInput
	}) (*publicPayloads.SignInPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("publicResolvers.SignIn")

	form := authForms.NewSignInForm(r.Ctx, &args.Input)
	if err := form.Validate(); err != nil {
		return nil, err
	}

	exception := exceptions.NewUnprocessableContentError(translator.Translate(nil, "errDbMsg_wrongPassword"), nil)
	grpcResponse, err := grpc_clients.AuthClient().UserSignIn(
		grpc_clients.NewCtx(r.Ctx),
		&pb.UserSignInRequest{
			IdentityPoolId: grpc_clients.PoolId(),
			IdentifierType: "phone",
			Identifier:     args.Input.Identifier,
			Password:       args.Input.Password,
		},
	)
	if err != nil {
		exception.AddError("auth_rpc", []any{err.Error()})
		return nil, exception
	}

	teacher, err := r.Repos.TeacherRepo().FindByActiveAndAuthId(r.Ctx, grpcResponse.User.Id)
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}

	user, err := r.Repos.UserRepo().FindByActiveAndAuthId(r.Ctx, grpcResponse.User.Id)
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}

	if teacher == nil && user == nil {
		return nil, exception
	}

	return publicPayloads.NewSignInPayload(grpcResponse, teacher, user), nil
}
