package publicResolvers

import (
	publicInputs "vibico-education-api/internal/gqls/inputs/publics"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	publicPayloads "vibico-education-api/internal/gqls/payloads/publics"
	"vibico-education-api/internal/gqls/resolvers"
	"vibico-education-api/internal/models"

	"github.com/rs/zerolog/log"
)

func (r *Resolver) RelatedCourses(args publicInputs.RelatedCoursesInput) (*publicPayloads.CoursesCollectionPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("publicResolvers.RelatedCourses")

	currentCourse, err := r.Repos.CourseRepo().FindBySlug(r.Ctx, args.CourseSlug)
	if err != nil {
		return nil, err
	}

	paginationData, err := r.Repos.CourseRepo().PublicRelatedList(
		r.Ctx,
		args,
		currentCourse,
		r.DetectAssociations("relatedCourses", []resolvers.RequestAssociation{
			{RequestKey: "collection.teacher", Key: "Teacher"},
		})...,
	)

	if err != nil {
		return nil, err
	}

	courses, ok := paginationData.Collection.([]*models.Course)
	resolvers := make([]*publicPayloads.CoursePayload, len(courses))
	if ok {
		for i, e := range courses {
			resolvers[i] = &publicPayloads.CoursePayload{CoursePayload: globalPayloads.CoursePayload{Course: e}}
		}
	}

	return &publicPayloads.CoursesCollectionPayload{
		Collection: &resolvers,
		Metadata: &globalPayloads.MetadataPayload{
			Metadata: paginationData.Metadata,
		},
	}, nil
}
