package publicResolvers

import (
	"context"

	publicInputs "vibico-education-api/internal/gqls/inputs/publics"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	publicServices "vibico-education-api/internal/services/publics"
	translator "vibico-education-api/pkg/translators"

	"github.com/rs/zerolog/log"
)

func (r *Resolver) SignUpVerify(ctx context.Context, args publicInputs.SignUpVerifyInput) (*globalPayloads.MessageInfoPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("publicResolvers.SignUpVerify")

	service := publicServices.NewSignUpVerifyService(r.Ctx, &args)
	if err := service.Execute(); err != nil {
		return nil, err
	}

	return &globalPayloads.MessageInfoPayload{Message: translator.Translate(nil, "infoMsg_registrationVerificationSuccess")}, nil
}
