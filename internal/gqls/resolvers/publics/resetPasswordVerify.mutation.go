package publicResolvers

import (
	publicInputs "vibico-education-api/internal/gqls/inputs/publics"
	publicPayloads "vibico-education-api/internal/gqls/payloads/publics"
	publicServices "vibico-education-api/internal/services/publics"
	translator "vibico-education-api/pkg/translators"

	"github.com/rs/zerolog/log"
)

func (r *Resolver) ResetPasswordVerify(args struct {
	Input publicInputs.PasswordResetVerifyInput
}) (*publicPayloads.PasswordResetVerifyPayloadType, error) {
	log.Info().Ctx(r.Ctx).Msg("publicResolvers.ResetPasswordVerify")

	service := publicServices.NewResetPasswordVerifyService(r.Ctx, args.Input, r.Repos)
	if err := service.Execute(); err != nil {
		return nil, err
	}

	return &publicPayloads.PasswordResetVerifyPayloadType{
		ResetPasswordUrl: service.ResetPasswordUrl,
		Message:          translator.Translate(nil, "general_success"),
	}, nil
}
