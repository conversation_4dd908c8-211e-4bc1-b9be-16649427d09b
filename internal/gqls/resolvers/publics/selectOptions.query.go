package publicResolvers

import (
	"context"
	globalInputs "vibico-education-api/internal/gqls/inputs/global"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	globalServices "vibico-education-api/internal/services/globals"

	"github.com/rs/zerolog/log"
)

func (r *Resolver) SelectOptions(
	ctx context.Context,
	args globalInputs.SelectOptionsInput,
) (*globalPayloads.SelectOptionsPayloadType, error) {
	log.Info().Ctx(r.Ctx).Msg("publicResolvers.SelectOptions")

	service := globalServices.NewSelectOptionsFetchService(
		r.Ctx,
		args.Input.Keys,
		args.Input.Params,
		r.Repos,
	)

	if err := service.Execute(); err != nil {
		return nil, err
	}

	return &service.Result, nil
}
