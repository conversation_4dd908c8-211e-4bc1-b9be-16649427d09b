package publicResolvers

import (
	"vibico-education-api/internal/forms/public/authForms"
	publicInputs "vibico-education-api/internal/gqls/inputs/publics"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	translator "vibico-education-api/pkg/translators"

	"github.com/rs/zerolog/log"
)

func (r *Resolver) SignUp(args struct {
	Input publicInputs.SignUpInput
}) (*globalPayloads.MessageInfoPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("publicResolvers.SignUp")

	form := authForms.NewSignUpForm(r.Ctx, args.Input, r.Repos.UserRepo())
	if err := form.Save(); err != nil {
		return nil, err
	}

	return &globalPayloads.MessageInfoPayload{Message: translator.Translate(nil, "infoMsg_signUpSuccess")}, nil
}
