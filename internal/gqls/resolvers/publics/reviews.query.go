package publicResolvers

import (
	"errors"
	"slices"
	"vibico-education-api/internal/enums"
	publicInputs "vibico-education-api/internal/gqls/inputs/publics"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	publicPayloads "vibico-education-api/internal/gqls/payloads/publics"
	"vibico-education-api/internal/gqls/resolvers"
	"vibico-education-api/internal/models"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/rs/zerolog/log"
)

func (r *Resolver) Reviews(args publicInputs.ReviewsInput) (*publicPayloads.ReviewsCollectionPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("publicResolvers.Reviews")

	targetType, err := enums.ParseCommentTargetType(args.TargetType)
	if err != nil {
		return nil, err
	}

	targetID, err := utils.ParseGraphqlID[uint32](args.TargetID)
	if err != nil {
		return nil, err
	}

	targetTypeReviews := []enums.CommentTargetType{
		enums.CommentTargetTypeCourse,
		enums.CommentTargetTypeTeacher,
	}

	if !slices.Contains(targetTypeReviews, targetType) {
		return nil, errors.New("invalid target type for reviews")
	}

	paginationData, err := r.Repos.CommentRepo().ListReviewsByTarget(
		r.Ctx,
		args,
		r.DetectAssociations("reviews", []resolvers.RequestAssociation{
			{RequestKey: "collection.authorUser", Key: "AuthorUser"},
		})...,
	)
	if err != nil {
		return nil, err
	}

	var reviewStats models.ReviewStats

	if _, ok := r.Operations["reviews"]["stats"]; ok {
		stats, err := r.Repos.CommentRepo().GetReviewsStatsByTarget(r.Ctx, targetID, args.TargetType)
		if err != nil {
			return nil, err
		}

		reviewStats = *stats
	}

	comments, ok := paginationData.Collection.([]*models.Comment)
	resolvers := make([]*publicPayloads.CommentPayload, len(comments))
	if ok {
		for i, c := range comments {
			resolvers[i] = &publicPayloads.CommentPayload{CommentPayload: &globalPayloads.CommentPayload{Comment: c}}
		}
	}

	return &publicPayloads.ReviewsCollectionPayload{
		Collection: &resolvers,
		Stats:      &globalPayloads.ReviewStatsPayload{ReviewStats: &reviewStats},
		Metadata:   &globalPayloads.MetadataPayload{Metadata: paginationData.Metadata},
	}, nil
}
