package publicResolvers

import (
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	publicPayloads "vibico-education-api/internal/gqls/payloads/publics"
	"vibico-education-api/internal/gqls/resolvers"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

func (r *Resolver) Drill(args struct {
	Slug string
}) (*publicPayloads.DrillPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("publicResolvers.Drill")

	drill, err := r.Repos.DrillRepo().FindApprovedBySlug(
		r.Ctx,
		args.Slug,
		r.DetectAssociations("drill", []resolvers.RequestAssociation{
			{RequestKey: "skills", Key: "DrillSkills.Skill"},
			{RequestKey: "diagrams"},
			{RequestKey: "videos", Args: []any{func(db *gorm.DB) *gorm.DB {
				return db.Order("videos.id ASC")
			}}},
			{RequestKey: "videos.videoPlatforms"},
		})...,
	)

	if err != nil {
		return nil, err
	}

	return &publicPayloads.DrillPayload{
		DrillPayload: &globalPayloads.DrillPayload{
			Drill: drill,
		},
	}, nil
}
