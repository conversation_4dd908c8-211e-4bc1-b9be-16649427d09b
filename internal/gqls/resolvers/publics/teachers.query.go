package publicResolvers

import (
	"vibico-education-api/internal/enums"
	publicInputs "vibico-education-api/internal/gqls/inputs/publics"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	publicPayloads "vibico-education-api/internal/gqls/payloads/publics"
	"vibico-education-api/internal/gqls/resolvers"
	"vibico-education-api/internal/models"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

func (r *Resolver) Teachers(args publicInputs.TeachersInput) (*publicPayloads.TeachersCollectionPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("publicResolvers.Teachers")

	paginationData, err := r.Repos.TeacherRepo().PublicTeacherList(
		r.Ctx,
		args,
		r.DetectAssociations("teachers", []resolvers.RequestAssociation{
			{RequestKey: "collection.rating", Key: "Ratings"},
			{RequestKey: "collection.courses", Key: "Courses", Args: []any{
				func(db *gorm.DB) *gorm.DB {
					return db.Where("courses.is_public = true AND courses.status = ?", enums.CourseStatusApproved)
				},
			}},
			{RequestKey: "collection.courseCount", Key: "Courses", Args: []any{
				func(db *gorm.DB) *gorm.DB {
					return db.Where("courses.is_public = true AND courses.status = ?", enums.CourseStatusApproved)
				},
			}},
			// NOTE: current query just count number of students within public courses
			// TODO: Update query to count all students possibly if it is necessary
			{RequestKey: "collection.studentCount", Key: "Courses.CourseUsers", Args: []any{
				func(db *gorm.DB) *gorm.DB {
					return db.Where("course_users.status != ?", enums.CourseUserStatusInvited)
				},
			}},
			{RequestKey: "joinedStudentCount", Key: "Courses", Args: []any{
				func(db *gorm.DB) *gorm.DB {
					return db.Where("courses.is_public = true AND courses.status = ?", enums.CourseStatusApproved)
				},
			}},
		})...,
	)

	if err != nil {
		return nil, err
	}

	teachers, ok := paginationData.Collection.([]*models.Teacher)
	resolvers := make([]*publicPayloads.TeacherPayload, len(teachers))
	if ok {
		for i, e := range teachers {
			resolvers[i] = &publicPayloads.TeacherPayload{TeacherPayload: &globalPayloads.TeacherPayload{Teacher: e}}
		}
	}

	return &publicPayloads.TeachersCollectionPayload{
		Collection: &resolvers,
		Metadata: &globalPayloads.MetadataPayload{
			Metadata: paginationData.Metadata,
		},
	}, nil
}
