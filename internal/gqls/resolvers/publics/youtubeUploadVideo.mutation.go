package publicResolvers

import (
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	"vibico-education-api/internal/models"
	globalServices "vibico-education-api/internal/services/globals"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
	"github.com/rs/zerolog/log"
)

func (r *Resolver) YouTubeVideoUpload(args struct {
	VideoID graphql.ID
}) (*globalPayloads.MessageInfoPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("publicResolvers.YouTubeVideoUpload")

	videoID, err := utils.ParseGraphqlID[uint32](args.VideoID)
	if err != nil {
		return nil, err
	}

	// Get video record
	video, err := r.Repos.VideoRepo().FindByID(r.Ctx, videoID)
	if err != nil {
		return nil, err
	}

	service := globalServices.NewYouTubeUploadService(r.Ctx, []*models.Video{video})
	if err := service.Execute(); err != nil {
		return nil, err
	}

	return &globalPayloads.MessageInfoPayload{
		Message: translator.Translate(nil, "general_updateSuccess"),
	}, nil
}
