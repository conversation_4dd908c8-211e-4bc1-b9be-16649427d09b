package publicResolvers

import (
	publicInputs "vibico-education-api/internal/gqls/inputs/publics"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	publicPayloads "vibico-education-api/internal/gqls/payloads/publics"
	"vibico-education-api/internal/gqls/resolvers"
	"vibico-education-api/internal/models"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

func (r *Resolver) Courses(args publicInputs.CoursesInput) (*publicPayloads.CoursesCollectionPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("publicResolvers.Courses")

	paginationData, err := r.Repos.CourseRepo().PublicList(
		r.Ctx,
		args,
		r.DetectAssociations("courses", []resolvers.RequestAssociation{
			{RequestKey: "collection.teacher", Key: "Teacher"},
			{
				RequestKey: "collection.courseSections",
				Key:        "CourseSections",
				Args: []any{
					func(db *gorm.DB) *gorm.DB {
						return db.Order("course_sections.position ASC")
					},
				},
			},
		})...,
	)

	if err != nil {
		return nil, err
	}

	courses, ok := paginationData.Collection.([]*models.Course)
	resolvers := make([]*publicPayloads.CoursePayload, len(courses))
	if ok {
		for i, e := range courses {
			resolvers[i] = &publicPayloads.CoursePayload{CoursePayload: globalPayloads.CoursePayload{Course: e}}
		}
	}

	return &publicPayloads.CoursesCollectionPayload{
		Collection: &resolvers,
		Metadata: &globalPayloads.MetadataPayload{
			Metadata: paginationData.Metadata,
		},
	}, nil
}
