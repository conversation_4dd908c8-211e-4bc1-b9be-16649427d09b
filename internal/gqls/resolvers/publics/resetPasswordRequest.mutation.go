package publicResolvers

import (
	"context"
	"strings"
	"time"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	"vibico-education-api/pkg/sms"
	translator "vibico-education-api/pkg/translators"
	"vibico-education-api/setup/grpc_clients"

	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	pb "github.com/BehemothLtd/vibico-auth/proto/auth"
	"github.com/rs/zerolog/log"
)

func (r *Resolver) ResetPasswordRequest(args struct{ Identifier *string }) (*globalPayloads.MessageInfoPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("publicResolvers.ResetPasswordRequest")

	if args.Identifier == nil || strings.TrimSpace(*args.Identifier) == "" {
		return nil, exceptions.NewUnprocessableContentError(
			translator.Translate(nil, "errValidationMsg_general"),
			exceptions.ResourceModificationError{"identifier": []any{translator.Translate(nil, "errValidationMsg_required")}})
	}

	identityPoolId := grpc_clients.PoolId()

	grpcResponse, err := grpc_clients.AuthClient().GetUserByPhoneNumber(
		grpc_clients.NewCtx(r.Ctx),
		&pb.UserPhoneNumberRequest{
			IdentityPoolId: identityPoolId,
			PhoneNumber:    *args.Identifier,
		},
	)
	if err != nil {
		return nil, err
	}

	if !grpcResponse.User.IsPhoneVerified {
		message := translator.Translate(nil, "errExceptionMsg_badRequest")
		return nil, exceptions.NewForbiddenError(&message)
	}

	go sendSMS(r.Ctx, grpcResponse.User)

	return &globalPayloads.MessageInfoPayload{
		Message: translator.Translate(nil, "general_success"),
	}, nil
}

func sendSMS(ctx context.Context, user *pb.User) {
	timeDuration := 24 * time.Hour

	smsService := sms.NewSMSService(ctx, utils.GetEnv("TWILIO_SERVICE_SID", ""))
	smsService.SendVerificationCode(*user.PhoneNumber, &timeDuration, nil)
}
