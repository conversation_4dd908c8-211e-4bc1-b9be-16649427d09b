package publicResolvers

import (
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	publicPayloads "vibico-education-api/internal/gqls/payloads/publics"
	"vibico-education-api/internal/gqls/resolvers"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

func (r *Resolver) Course(args struct{ Slug string }) (*publicPayloads.CoursePayload, error) {
	log.Info().Ctx(r.Ctx).Msg("publicResolvers.Course")

	course, err := r.Repos.CourseRepo().FindBySlug(
		r.Ctx,
		args.Slug,
		r.DetectAssociations("course", []resolvers.RequestAssociation{
			{RequestKey: "teacher"},
			{
				RequestKey: "courseSections",
				Args: []any{
					func(db *gorm.DB) *gorm.DB {
						return db.Order("course_sections.position ASC")
					},
				},
			},
			{
				RequestKey: "courseSections.courseSectionItems",
				Args: []any{
					func(db *gorm.DB) *gorm.DB {
						return db.Order("course_section_items.position ASC")
					},
				},
			},
		})...,
	)
	if err != nil {
		return nil, err
	}

	return &publicPayloads.CoursePayload{CoursePayload: globalPayloads.CoursePayload{Course: course}}, nil
}
