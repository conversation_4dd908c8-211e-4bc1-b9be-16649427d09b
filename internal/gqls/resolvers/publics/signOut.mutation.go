package publicResolvers

import (
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	translator "vibico-education-api/pkg/translators"
	"vibico-education-api/setup/grpc_clients"

	pb "github.com/BehemothLtd/vibico-auth/proto/auth"
	"github.com/rs/zerolog/log"
)

func (r *Resolver) SignOut() (*globalPayloads.MessageInfoPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("publicResolvers.SignOut")

	token, err := r.GetBearerToken()
	if err != nil {
		return nil, err
	}

	_, err = grpc_clients.AuthClient().UserSignOut(grpc_clients.NewCtx(r.Ctx), &pb.UserSignOutRequest{
		IdentityPoolId: grpc_clients.PoolId(),
		Token:          token,
	})
	if err != nil {
		return nil, err
	}

	return &globalPayloads.MessageInfoPayload{Message: translator.Translate(nil, "infoMsg_signOutSuccess")}, nil
}
