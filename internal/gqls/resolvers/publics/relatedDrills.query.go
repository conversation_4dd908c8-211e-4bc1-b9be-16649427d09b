package publicResolvers

import (
	publicInputs "vibico-education-api/internal/gqls/inputs/publics"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	publicPayloads "vibico-education-api/internal/gqls/payloads/publics"
	"vibico-education-api/internal/gqls/resolvers"

	"github.com/rs/zerolog/log"
)

func (r *Resolver) RelatedDrills(args publicInputs.RelatedDrillInput) (*publicPayloads.DrillsCollectionPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("publicResolvers.RelatedDrills")

	paginationData, err := r.Repos.DrillRepo().PublicRelatedDrillList(
		r.Ctx,
		args,
		r.DetectAssociations("relatedDrills", []resolvers.RequestAssociation{
			{RequestKey: "collection.skills", Key: "DrillSkills.Skill"},
			{RequestKey: "collection.diagrams", Key: "Diagrams"},
		})...,
	)

	if err != nil {
		return nil, err
	}

	return &publicPayloads.DrillsCollectionPayload{
		Collection: publicPayloads.DrillsSliceToTypes(paginationData.Collection),
		Metadata: &globalPayloads.MetadataPayload{
			Metadata: paginationData.Metadata,
		},
	}, nil
}
