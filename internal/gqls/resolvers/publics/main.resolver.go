package publicResolvers

import (
	"vibico-education-api/internal/gqls/resolvers"
	"vibico-education-api/internal/repository"

	"github.com/gin-gonic/gin"
)

type Resolver struct {
	resolvers.Resolver
}

func NewResolver(repos repository.IRepositories, ginCtx *gin.Context) *Resolver {
	return &Resolver{
		Resolver: *resolvers.NewResolver(repos, ginCtx),
	}
}

func (r *Resolver) GetPublicOperations() map[string]struct{} {
	publicOperations := r.Resolver.GetPublicOperations()

	return publicOperations
}

func (r *Resolver) Auth() error {
	return nil
}
