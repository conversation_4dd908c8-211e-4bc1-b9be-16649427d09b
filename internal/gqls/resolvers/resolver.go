package resolvers

import (
	"errors"
	"strings"
	"vibico-education-api/constants"
	"vibico-education-api/internal/repository"
	"vibico-education-api/internal/repository/repositories"
	"vibico-education-api/pkg/helpers"
	"vibico-education-api/setup/grpc_clients"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"

	pb "github.com/BehemothLtd/vibico-auth/proto/auth"
)

type Resolver struct {
	Ctx        *gin.Context
	Repos      repository.IRepositories
	Operations map[string]map[string]struct{}
}

type RequestAssociation struct {
	RequestKey string
	Key        any
	Args       []any
}

func NewResolver(repos repository.IRepositories, ginCtx *gin.Context) *Resolver {
	return &Resolver{
		Repos: repos,
		Ctx:   ginCtx,
	}
}

func (r *Resolver) GetProtoUser() (*pb.User, error) {
	log.Debug().Ctx(r.Ctx).Msg("Resolver.GetProtoUser")

	tokenString, err := r.GetBearerToken()
	if err != nil {
		log.Error().Err(err).Ctx(r.Ctx).Msg("GetBearerToken error")
		return nil, err
	}

	grpcResponse, err := grpc_clients.AuthClient().GetUserByAccessToken(
		grpc_clients.NewCtx(r.Ctx),
		&pb.UserVerifyRequest{Token: tokenString, IdentityPoolId: grpc_clients.PoolId()},
	)
	if err != nil {
		log.Error().Err(err).Ctx(r.Ctx).Msg("Grpc GetUserByAccessToken error")
		return nil, err
	}

	return grpcResponse.User, nil
}

func (r *Resolver) GetBearerToken() (string, error) {
	log.Debug().Ctx(r.Ctx).Msg("Resolver.GetBearerToken")

	authHeader := r.Ctx.Request.Header.Get(constants.AuthorizationHeader)
	tokenString := strings.TrimPrefix(authHeader, constants.BearerKey+" ")
	if strings.TrimSpace(tokenString) == "" {
		return "", errors.New("token is empty")
	}

	return tokenString, nil
}

func (r *Resolver) SetOperations(operations map[string]map[string]struct{}) {
	r.Operations = operations
}

func (r *Resolver) GetPublicOperations() map[string]struct{} {
	return map[string]struct{}{
		"__schema": {},
	}
}

func (r *Resolver) DetectAssociations(query string, associations []RequestAssociation) []repositories.CustomPreload {
	log.Debug().Ctx(r.Ctx).Msg("Resolver.DetectAssociations")

	preloads := make([]repositories.CustomPreload, 0)
	operations := r.Operations[query]

	for _, assoc := range associations {
		if _, exists := operations[assoc.RequestKey]; exists {
			if assoc.Key == nil || assoc.Key.(string) == "" {
				assoc.Key = assoc.RequestKey
			}

			preloads = append(preloads, repositories.CustomPreload{
				Key:  helpers.GqlQueryCamelToPascalCase(assoc.Key.(string)),
				Args: assoc.Args,
			})
		}
	}

	return preloads
}
