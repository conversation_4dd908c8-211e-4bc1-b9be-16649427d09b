package teacherResolvers

import (
	"errors"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	"vibico-education-api/internal/repository/repositories"
	globalServices "vibico-education-api/internal/services/globals"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

func (r *Resolver) CourseSectionItemDestroy(
	args struct {
		Id              graphql.ID
		CourseId        graphql.ID
		CourseSectionId graphql.ID
	},
) (*globalPayloads.MessageInfoPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("TeacherResolver.CourseSectionItemDestroy")

	courseId, err := utils.GqlIdToUint32(args.CourseId)
	if err != nil {
		return nil, err
	}

	courseSectionId, err := utils.GqlIdToUint32(args.CourseSectionId)
	if err != nil {
		return nil, err
	}

	id, err := utils.GqlIdToUint32(args.Id)
	if err != nil {
		return nil, err
	}

	course, err := r.Repos.CourseRepo().FindByIDAndTeacher(r.Ctx, courseId, r.currentTeacher.ID)
	if err != nil {
		return nil, err
	}

	if course.IsApproved() {
		return nil, errors.New(translator.Translate(nil, "errValidationMsg_status_invalid"))
	}

	sectionItem, err := r.Repos.CourseSectionItemRepo().
		FindByIDAndSectionID(
			r.Ctx,
			id,
			courseSectionId,
			repositories.CustomPreload{
				Key: "Videos",
			},
			repositories.CustomPreload{
				Key: "Videos.VideoPlatforms",
			},
			repositories.CustomPreload{
				Key: "Videos.VideoUpload",
			},
		)

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, exceptions.NewRecordNotFoundError()
		}
		return nil, err
	}

	var videoAfterDestroyServices []func() error
	for _, video := range *sectionItem.Videos {
		capturedVideo := video
		videoAfterDestroyServices = append(videoAfterDestroyServices, func() error {
			service := globalServices.NewVideoAfterDestroyService(r.Ctx, capturedVideo)
			return service.Execute()
		})
	}

	if err := r.Repos.CourseSectionItemRepo().Delete(r.Ctx, sectionItem, courseId); err != nil {
		return nil, err
	}

	// run after services when transaction committed successfully
	for _, serviceFunc := range videoAfterDestroyServices {
		serviceFunc()
	}

	return &globalPayloads.MessageInfoPayload{
		Message: translator.Translate(nil, "general_deleteSuccess"),
	}, nil
}
