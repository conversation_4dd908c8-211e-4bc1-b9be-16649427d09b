package teacherResolvers

import (
	"vibico-education-api/internal/forms/teachers/templateDiagramForms"
	teacherInputs "vibico-education-api/internal/gqls/inputs/teachers"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	teacherPayloads "vibico-education-api/internal/gqls/payloads/teachers"
	translator "vibico-education-api/pkg/translators"

	"github.com/rs/zerolog/log"
)

func (r *Resolver) TemplateDiagramCreate(
	args struct {
		Input *teacherInputs.TemplateDiagramFormInput
	},
) (*teacherPayloads.TemplateDiagramMutatePayload, error) {
	log.Info().Ctx(r.Ctx).Msg("TeacherResolver.TemplateDiagramCreate")

	form := templateDiagramForms.NewCreateForm(
		r.Ctx,
		args.Input,
		r.Repos.TemplateDiagramRepo(),
	)

	if err := form.Save(); err != nil {
		return nil, err
	}

	return &teacherPayloads.TemplateDiagramMutatePayload{
		TemplateDiagram: &teacherPayloads.TemplateDiagramPayload{
			TemplateDiagramPayload: &globalPayloads.TemplateDiagramPayload{
				TemplateDiagram: form.TemplateDiagram,
			},
		},
		Message: translator.Translate(nil, "infoMsg_createSuccess"),
	}, nil
}
