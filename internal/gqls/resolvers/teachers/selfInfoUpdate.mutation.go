package teacherResolvers

import (
	"log"
	"vibico-education-api/internal/forms/teachers/infoForms"
	teacherInputs "vibico-education-api/internal/gqls/inputs/teachers"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
)

func (r *Resolver) UpdateSelfInfo(args struct {
	Input teacherInputs.UpdateSelfInfoInput
}) (*globalPayloads.MessageInfoPayload, error) {
	log.Println("TeacherResolver.UpdateSelfInfo")

	if !r.currentTeacher.BasicEntered {
		return nil, exceptions.NewBadRequestError(nil)
	}

	form := infoForms.NewUpdateForm(r.Ctx, args.Input, r.currentTeacher, r.Repos.TeacherRepo())
	if err := form.Save(); err != nil {
		return nil, err
	}

	return &globalPayloads.MessageInfoPayload{
		Message: translator.Translate(nil, "general_success"),
	}, nil
}
