package teacherResolvers

import (
	"errors"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
	"github.com/rs/zerolog/log"
)

func (r *Resolver) CourseSectionSwapPosition(args struct {
	CourseId        graphql.ID
	CourseSectionId graphql.ID
	NewIndex        int32
}) (*globalPayloads.MessageInfoPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("teacherResolvers.CourseSectionSwapPosition")

	courseId, err := utils.ParseGraphqlID[uint32](args.CourseId)
	if err != nil {
		return nil, err
	}
	course, err := r.Repos.CourseRepo().FindByIDAndTeacher(
		r.Ctx,
		courseId,
		r.currentTeacher.ID,
	)
	if err != nil {
		return nil, err
	}

	if course.IsApproved() {
		return nil, errors.New(translator.Translate(nil, "errValidationMsg_status_invalid"))
	}

	sectionId, err := utils.ParseGraphqlID[uint32](args.CourseSectionId)
	if err != nil {
		return nil, err
	}

	err = r.Repos.CourseSectionRepo().SwapPosition(r.Ctx, courseId, sectionId, args.NewIndex)
	if err != nil {
		return nil, err
	}

	return &globalPayloads.MessageInfoPayload{
		Message: translator.Translate(nil, "general_updateSuccess"),
	}, nil
}
