package teacherResolvers

import (
	"vibico-education-api/internal/forms/teachers/courseForms"
	teacherInputs "vibico-education-api/internal/gqls/inputs/teachers"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	teacherPayloads "vibico-education-api/internal/gqls/payloads/teachers"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/rs/zerolog/log"
)

func (r *Resolver) CourseUpdate(args teacherInputs.CourseUpdateInput) (*teacherPayloads.CourseUpdatePayload, error) {
	log.Info().Ctx(r.Ctx).Msg("teacherResolvers.CourseUpdate")

	courseID, err := utils.ParseGraphqlID[uint32](args.ID)
	if err != nil {
		return nil, err
	}

	courseRepo := r.Repos.CourseRepo()
	course, err := courseRepo.FindByIDAndTeacher(r.Ctx, courseID, r.currentTeacher.ID)
	if err != nil {
		return nil, err
	}

	form := courseForms.NewUpdateForm(
		r.Ctx,
		&args.Input,
		courseRepo,
		course,
	)

	if err := form.Save(); err != nil {
		return nil, err
	}

	return &teacherPayloads.CourseUpdatePayload{
		Course:  &teacherPayloads.CoursePayload{CoursePayload: globalPayloads.CoursePayload{Course: form.Course}},
		Message: translator.Translate(nil, "infoMsg_updateSuccess"),
	}, nil
}
