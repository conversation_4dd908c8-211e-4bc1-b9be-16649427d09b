package teacherResolvers

import (
	"context"
	"fmt"
	"time"

	"vibico-education-api/internal/enums"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	"vibico-education-api/internal/models"
	globalServices "vibico-education-api/internal/services/globals"
	"vibico-education-api/pkg/helpers"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
	"github.com/redis/go-redis/v9"
	"github.com/rs/zerolog/log"
)

func (r *Resolver) SaveAndUploadDrillVideo(Input struct {
	DrillId graphql.ID
	VideoId graphql.ID
}) (*globalPayloads.MessageInfoPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("TeacherResolver.SaveAndUploadDrillVideo")

	drillId, _ := utils.GqlIdToUint32(Input.DrillId)
	drill, err := r.Repos.DrillRepo().FindByID(r.Ctx, drillId)
	if err != nil {
		return nil, err
	}

	videoId, _ := utils.GqlIdToUint32(Input.VideoId)
	video, err := r.Repos.VideoRepo().FindByID(r.Ctx, videoId)
	if err != nil {
		return nil, err
	}

	video.ParentID = drill.ID
	video.ParentType = enums.VideoParentTypeDrill
	video.IsFree = true

	// TODO: also need to update drill censor to draft when remove it own video
	if err := r.Repos.VideoRepo().UpdateWithDrill(r.Ctx, video, drillId, "ParentID", "ParentType", "IsFree"); err != nil {
		return nil, err
	}

	ctx, cancel := context.WithCancel(r.Ctx)
	go func(ctx context.Context, vid *models.Video) {
		defer cancel()
		red := helpers.GetRedisClient()
		lastID := "0"

		for {
			res, err := red.XRead(ctx, &redis.XReadArgs{
				Streams: []string{"video_events", lastID},
				Count:   1,
				Block:   5 * time.Minute,
			}).Result()
			if err != nil {
				fmt.Println("XRead error:", err)
				select {
				case <-time.After(10 * time.Second):
					continue
				case <-ctx.Done():
					return
				}
			}

			for _, sr := range res {
				for _, msg := range sr.Messages {
					if fmt.Sprint(msg.Values["video_id"]) == fmt.Sprint(vid.ID) &&
						msg.Values["event"] == "video_uploaded" {
						service := globalServices.NewYouTubeUploadService(r.Ctx, []*models.Video{vid})
						service.Execute()
						return
					}
					lastID = msg.ID
				}
			}
		}
	}(ctx, video)

	return &globalPayloads.MessageInfoPayload{
		Message: "Video saved successfully",
	}, nil
}
