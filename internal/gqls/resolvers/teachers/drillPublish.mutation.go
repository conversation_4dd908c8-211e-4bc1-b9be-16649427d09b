package teacherResolvers

import (
	"errors"
	"vibico-education-api/internal/enums"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
	"github.com/rs/zerolog/log"
)

func (r *Resolver) DrillPublish(args struct {
	ID     graphql.ID
	Status string
}) (*globalPayloads.MessageInfoPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("TeacherResolver.DrillPublish")

	drillId, err := utils.ParseGraphqlID[uint32](args.ID)
	if err != nil {
		return nil, err
	}
	statusEnum, err := enums.ParseDrillStatus(args.Status)
	if err != nil {
		return nil, errors.New(translator.Translate(nil, "errValidationMsg_status_invalid"))
	}

	drillRepo := r.Repos.DrillRepo()
	drill, err := drillRepo.FindByID(r.Ctx, drillId)
	if err != nil {
		return nil, err
	}

	if !drill.IsMaster {
		return nil, errors.New(translator.Translate(nil, "errMsg_InvalidDrill"))
	}

	if drill.Status == statusEnum {
		return nil, errors.New(translator.Translate(nil, "errValidationMsg_status_invalid"))
	}

	drill.Status = statusEnum
	if err := drillRepo.Update(r.Ctx, drill, "status"); err != nil {
		return nil, err
	}

	return &globalPayloads.MessageInfoPayload{
		Message: translator.Translate(nil, "general_updateSuccess"),
	}, nil
}
