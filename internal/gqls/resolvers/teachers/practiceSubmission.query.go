package teacherResolvers

import (
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	teacherPayloads "vibico-education-api/internal/gqls/payloads/teachers"
	"vibico-education-api/internal/repository/repositories"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
	"github.com/rs/zerolog/log"
)

func (r *Resolver) PracticeSubmission(args struct{ ID graphql.ID }) (*teacherPayloads.PracticeSubmissionPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("TeacherResolver.PracticeSubmission")

	practiceSubmissionID, err := utils.ParseGraphqlID[uint32](args.ID)
	if err != nil {
		return nil, err
	}

	practiceSubmission, err := r.Repos.PracticeSubmissionRepo().FindByIDAndTeacherID(
		r.Ctx,
		practiceSubmissionID,
		r.currentTeacher.ID,
		repositories.CustomPreload{Key: "Comments.AuthorTeacher"},
		repositories.CustomPreload{Key: "Comments.AuthorUser"},
		repositories.CustomPreload{Key: "Videos.VideoPlatforms"},
	)
	if err != nil {
		return nil, err
	}

	return &teacherPayloads.PracticeSubmissionPayload{
		PracticeSubmissionPayload: &globalPayloads.PracticeSubmissionPayload{PracticeSubmission: practiceSubmission},
	}, nil
}
