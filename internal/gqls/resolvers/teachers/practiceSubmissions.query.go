package teacherResolvers

import (
	teacherInputs "vibico-education-api/internal/gqls/inputs/teachers"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	teacherPayloads "vibico-education-api/internal/gqls/payloads/teachers"
	"vibico-education-api/internal/models"
	"vibico-education-api/internal/repository/repositories"

	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
	"github.com/rs/zerolog/log"
)

func (r *Resolver) PracticeSubmissions(args teacherInputs.PracticeSubmissionsInput) (*teacherPayloads.PracticeSubmissionsCollectionPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("TeacherResolver.PracticeSubmissions")

	paginationData, err := r.Repos.PracticeSubmissionRepo().ListByTeacher(
		r.Ctx,
		args,
		r.currentTeacher.ID,
		repositories.CustomPreload{Key: "Videos.VideoPlatforms"},
	)
	if err != nil {
		return nil, err
	}

	practiceSubmissions, ok := paginationData.Collection.([]*models.PracticeSubmission)
	if !ok {
		return nil, exceptions.NewUnprocessableContentError("invalid collection type", nil)
	}

	resolvers := make([]*teacherPayloads.PracticeSubmissionPayload, len(practiceSubmissions))
	for i, ps := range practiceSubmissions {
		resolvers[i] = &teacherPayloads.PracticeSubmissionPayload{
			PracticeSubmissionPayload: &globalPayloads.PracticeSubmissionPayload{
				PracticeSubmission: ps,
			},
		}
	}

	return &teacherPayloads.PracticeSubmissionsCollectionPayload{
		Collection: &resolvers,
		Metadata: &globalPayloads.MetadataPayload{
			Metadata: paginationData.Metadata,
		},
	}, nil
}
