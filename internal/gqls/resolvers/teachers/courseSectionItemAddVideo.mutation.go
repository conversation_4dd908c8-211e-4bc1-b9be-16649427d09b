package teacherResolvers

import (
	"context"
	"errors"
	"fmt"
	"time"
	"vibico-education-api/internal/enums"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	"vibico-education-api/internal/models"
	globalServices "vibico-education-api/internal/services/globals"
	"vibico-education-api/pkg/helpers"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
	"github.com/redis/go-redis/v9"
	"github.com/rs/zerolog/log"
)

func (r *Resolver) CourseSectionItemAddVideo(args struct {
	ItemId   graphql.ID
	CourseId graphql.ID
	VideoId  graphql.ID
}) (*globalPayloads.MessageInfoPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("TeacherResolver.CourseSectionItemAddVideo")

	courseId, err := utils.ParseGraphqlID[uint32](args.CourseId)
	if err != nil {
		return nil, err
	}
	course, err := r.Repos.CourseRepo().FindByIDAndTeacher(r.Ctx, courseId, r.currentTeacher.ID)
	if err != nil {
		return nil, err
	}

	if course.IsApproved() {
		return nil, errors.New(translator.Translate(nil, "errValidationMsg_status_invalid"))
	}

	itemId, err := utils.ParseGraphqlID[uint32](args.ItemId)
	if err != nil {
		return nil, err
	}
	item, err := r.Repos.CourseSectionItemRepo().FindByIdAndCourseId(r.Ctx, itemId, courseId)
	if err != nil {
		return nil, err
	}

	videoId, err := utils.ParseGraphqlID[uint32](args.VideoId)
	if err != nil {
		return nil, err
	}
	video, err := r.Repos.VideoRepo().FindByID(r.Ctx, videoId)
	if err != nil {
		return nil, err
	}
	video.ParentID = item.ID
	video.ParentType = enums.VideoParentTypeCourseSectionItem
	video.IsFree = true
	err = r.Repos.VideoRepo().UpdateWithCourse(r.Ctx, video, courseId, "ParentID", "ParentType", "IsFree")
	if err != nil {
		return nil, err
	}

	ctx, cancel := context.WithCancel(r.Ctx)
	go func(ctx context.Context, vid *models.Video) {
		defer cancel()
		red := helpers.GetRedisClient()
		lastID := "0"

		for {
			res, err := red.XRead(ctx, &redis.XReadArgs{
				Streams: []string{"video_events", lastID},
				Count:   1,
				Block:   5 * time.Minute,
			}).Result()

			if err != nil {
				log.Error().Err(err).Ctx(r.Ctx).Msg("XRead error")
				select {
				case <-time.After(10 * time.Second):
					continue
				case <-ctx.Done():
					return
				}
			}

			for _, sr := range res {
				for _, msg := range sr.Messages {
					if fmt.Sprint(msg.Values["video_id"]) == fmt.Sprint(vid.ID) &&
						msg.Values["event"] == "video_uploaded" {
						service := globalServices.NewYouTubeUploadService(r.Ctx, []*models.Video{vid})
						service.Execute()
						return
					}
					lastID = msg.ID
				}
			}
		}
	}(ctx, video)

	return &globalPayloads.MessageInfoPayload{
		Message: translator.Translate(nil, "general_success"),
	}, nil
}
