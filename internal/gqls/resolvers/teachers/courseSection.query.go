package teacherResolvers

import (
	"context"
	teacherInputs "vibico-education-api/internal/gqls/inputs/teachers"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	teacherPayloads "vibico-education-api/internal/gqls/payloads/teachers"
	"vibico-education-api/internal/gqls/resolvers"
	"vibico-education-api/internal/repository/repositories"

	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

func (r *Resolver) CourseSection(ctx context.Context, args teacherInputs.CourseSectionInput) (*teacherPayloads.CourseSectionPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("teacherResolvers.CourseSection")

	courseSectionID, err := utils.ParseGraphqlID[uint32](args.ID)
	if err != nil {
		return nil, err
	}

	courseID, err := utils.ParseGraphqlID[uint32](args.CourseID)
	if err != nil {
		return nil, err
	}

	courseSection, err := r.Repos.CourseSectionRepo().
		FindBySectionIdCourseIdAndTeacher(
			ctx,
			courseSectionID,
			courseID,
			r.currentTeacher.ID,
			r.DetectAssociations("courseSection", []resolvers.RequestAssociation{
				{RequestKey: "courseSectionItems", Args: []any{
					func(db *gorm.DB) *gorm.DB {
						return db.Order(repositories.CSIOrderPositionAscSQL)
					},
				}},
			})...,
		)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, exceptions.NewRecordNotFoundError()
		}

		return nil, err
	}

	return &teacherPayloads.CourseSectionPayload{
		CourseSectionPayload: &globalPayloads.CourseSectionPayload{
			CourseSection: courseSection,
		},
	}, nil

}
