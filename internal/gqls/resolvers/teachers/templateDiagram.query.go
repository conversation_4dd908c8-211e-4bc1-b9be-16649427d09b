package teacherResolvers

import (
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	teacherPayloads "vibico-education-api/internal/gqls/payloads/teachers"
	"vibico-education-api/internal/repository/repositories"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

func (r *Resolver) TemplateDiagram(args struct{ ID graphql.ID }) (*teacherPayloads.TemplateDiagramPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("TeacherResolver.TemplateDiagram")

	templateId, err := utils.GqlIdToUint32(args.ID)
	if err != nil {
		return nil, err
	}

	template, err := r.Repos.TemplateDiagramRepo().FindById(
		r.Ctx,
		templateId,
		repositories.CustomPreload{
			Key: "Diagrams",
			Args: []any{func(db *gorm.DB) *gorm.DB {
				return db.Order("diagrams.id ASC")
			}},
		},
	)
	if err != nil {
		return nil, err
	}

	return &teacherPayloads.TemplateDiagramPayload{
		TemplateDiagramPayload: &globalPayloads.TemplateDiagramPayload{
			TemplateDiagram: template,
		},
	}, nil
}
