package teacherResolvers

import (
	"vibico-education-api/internal/gqls/resolvers"
	"vibico-education-api/internal/middlewares"
	"vibico-education-api/internal/models"
	"vibico-education-api/internal/repository"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
)

type Resolver struct {
	resolvers.Resolver

	currentTeacher *models.Teacher
}

func NewResolver(repos repository.IRepositories, ginCtx *gin.Context) *Resolver {
	return &Resolver{
		Resolver: *resolvers.NewResolver(repos, ginCtx),
	}
}

func (r *Resolver) Auth() error {
	log.Debug().Ctx(r.Ctx).Msg("teacherResolvers.Auth")

	teacher, err := middlewares.TeacherAuth(r.Ctx, r.Repos)
	if err != nil {
		return err
	}

	r.currentTeacher = teacher

	return nil
}

func (r *Resolver) GetCurrentTeacher() *models.Teacher {
	return r.currentTeacher
}

func (r *Resolver) GetPublicOperations() map[string]struct{} {
	publicOperations := r.Resolver.GetPublicOperations()

	return publicOperations
}
