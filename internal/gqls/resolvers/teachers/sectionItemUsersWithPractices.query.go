package teacherResolvers

import (
	teacherInputs "vibico-education-api/internal/gqls/inputs/teachers"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	teacherPayloads "vibico-education-api/internal/gqls/payloads/teachers"
	"vibico-education-api/internal/models"
	"vibico-education-api/internal/repository/repositories"

	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

func (r *Resolver) SectionItemUsersWithPractices(args teacherInputs.SectionItemUsersWithPracticesInput) (*teacherPayloads.SectionItemUsersCollectionPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("TeacherResolver.SectionItemUsersWithPractices")

	sectionItem, err := r.Repos.CourseSectionItemRepo().FindBySlugAndTeacherWithPractices(
		r.Ctx,
		args.CourseSlug,
		args.SectionItemSlug,
		r.currentTeacher.ID,
	)
	if err != nil {
		return nil, err
	}

	paginationData, err := r.Repos.UserRepo().ListBySectionItem(
		r.Ctx,
		sectionItem.ID,
		args,
		repositories.CustomPreload{Key: "PracticeSubmissions", Args: []any{
			func(db *gorm.DB) *gorm.DB {
				return db.
					Where("practice_submissions.practice_id = ? AND practice_submissions.practice_type = 'CourseSectionItem'", sectionItem.ID).
					Order("practice_submissions.id DESC")
			},
		}},
	)
	if err != nil {
		return nil, err
	}

	stats, err := r.Repos.CourseSectionItemRepo().GetUsersStats(r.Ctx, sectionItem.ID)
	if err != nil {
		return nil, err
	}

	users, ok := paginationData.Collection.([]*models.User)
	if !ok {
		return nil, exceptions.NewUnprocessableContentError("invalid collection type", nil)
	}

	resolvers := make([]*teacherPayloads.SectionItemUserPayload, len(users))
	for i, user := range users {
		resolvers[i] = &teacherPayloads.SectionItemUserPayload{
			UserPayload: &globalPayloads.UserPayload{
				User: user,
			},
		}
	}

	return &teacherPayloads.SectionItemUsersCollectionPayload{
		Stats:      &teacherPayloads.SectionItemUsersStatsPayload{SectionItemUsersStats: stats},
		Collection: &resolvers,
		Metadata:   &globalPayloads.MetadataPayload{Metadata: paginationData.Metadata},
	}, nil
}
