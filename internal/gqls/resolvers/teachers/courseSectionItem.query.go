package teacherResolvers

import (
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	teacherPayloads "vibico-education-api/internal/gqls/payloads/teachers"
	"vibico-education-api/internal/gqls/resolvers"

	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

func (r *Resolver) CourseSectionItem(
	args struct {
		Id              graphql.ID
		CourseId        graphql.ID
		CourseSectionId graphql.ID
	},
) (*teacherPayloads.CourseSectionItemPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("TeacherResolver.CourseSectionItem")

	courseId, err := utils.GqlIdToUint32(args.CourseId)
	if err != nil {
		return nil, err
	}

	courseSectionId, err := utils.GqlIdToUint32(args.CourseSectionId)
	if err != nil {
		return nil, err
	}

	id, err := utils.GqlIdToUint32(args.Id)
	if err != nil {
		return nil, err
	}

	sectionItem, err := r.Repos.CourseSectionItemRepo().
		FindByIdAndTeacherCourse(
			r.Ctx,
			id,
			courseSectionId,
			courseId,
			r.currentTeacher.ID,
			r.DetectAssociations("courseSectionItem", []resolvers.RequestAssociation{
				{RequestKey: "drills", Args: []any{func(db *gorm.DB) *gorm.DB {
					return db.Where("drills.is_master = false").Order("drills.id ASC")
				}}},
				{RequestKey: "drills.diagrams"},
				{RequestKey: "drills.skills", Key: "Drills.DrillSkills.Skill"},
				{RequestKey: "videos", Args: []any{func(db *gorm.DB) *gorm.DB {
					return db.Order("videos.id ASC")
				}}},
				{RequestKey: "videos.videoPlatforms"},
			})...,
		)

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, exceptions.NewRecordNotFoundError()
		}
		return nil, err
	}
	return &teacherPayloads.CourseSectionItemPayload{
		CourseSectionItemPayload: &globalPayloads.CourseSectionItemPayload{
			SectionItem: sectionItem,
		},
	}, nil
}
