package teacherResolvers

import (
	"errors"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	"vibico-education-api/internal/repository/repositories"
	teacherServices "vibico-education-api/internal/services/teachers"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
	"github.com/rs/zerolog/log"
)

func (r *Resolver) CourseSectionItemDeleteVideo(args struct {
	VideoID  graphql.ID
	CourseId graphql.ID
}) (*globalPayloads.MessageInfoPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("TeacherResolver.CourseSectionItemDeleteVideo")

	videoID, err := utils.ParseGraphqlID[uint32](args.VideoID)
	if err != nil {
		return nil, err
	}

	video, err := r.Repos.VideoRepo().FindSelfVideoByID(r.Ctx, videoID, r.currentTeacher.ID, "Teacher",
		repositories.CustomPreload{
			Key: "VideoUpload",
		},
		repositories.CustomPreload{
			Key: "VideoPlatforms",
		})
	if err != nil {
		return nil, err
	}

	courseId, err := utils.ParseGraphqlID[uint32](args.CourseId)
	if err != nil {
		return nil, err
	}

	course, err := r.Repos.CourseRepo().FindByIDAndTeacher(r.Ctx, courseId, r.currentTeacher.ID)
	if err != nil {
		return nil, err
	}

	if course.IsApproved() {
		return nil, errors.New(translator.Translate(nil, "errValidationMsg_status_invalid"))
	}

	service := teacherServices.NewVideoDestroyService(r.Ctx, video, r.Repos.VideoRepo(), courseId)
	if err := service.Execute(); err != nil {
		return nil, err
	}

	return &globalPayloads.MessageInfoPayload{
		Message: translator.Translate(nil, "general_deleteSuccess"),
	}, nil
}
