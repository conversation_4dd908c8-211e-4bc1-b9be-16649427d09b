package teacherResolvers

import (
	"vibico-education-api/internal/forms/teachers/courseForms"
	teacherInputs "vibico-education-api/internal/gqls/inputs/teachers"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	teacherPayloads "vibico-education-api/internal/gqls/payloads/teachers"
	translator "vibico-education-api/pkg/translators"

	"github.com/rs/zerolog/log"
)

func (r *Resolver) CourseCreate(args struct {
	Input teacherInputs.CourseCreateInput
}) (*teacherPayloads.CourseCreatePayload, error) {
	log.Info().Ctx(r.Ctx).Msg("teacherResolvers.CourseCreate")

	form := courseForms.NewCreateForm(
		r.Ctx,
		&args.Input,
		r.Repos.CourseRepo(),
		r.Repos.PackageDealRepo(),
		r.currentTeacher,
	)

	if err := form.Save(); err != nil {
		return nil, err
	}

	return &teacherPayloads.CourseCreatePayload{
		Course:  &teacherPayloads.CoursePayload{CoursePayload: globalPayloads.CoursePayload{Course: form.Course}},
		Message: translator.Translate(nil, "infoMsg_createSuccess"),
	}, nil
}
