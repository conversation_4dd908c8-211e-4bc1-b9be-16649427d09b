package teacherResolvers

import (
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	teacherPayloads "vibico-education-api/internal/gqls/payloads/teachers"
	"vibico-education-api/internal/gqls/resolvers"
	"vibico-education-api/internal/models"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"

	"github.com/rs/zerolog/log"
)

func (r *Resolver) CourseSectionItems(
	args struct {
		CourseId        graphql.ID
		CourseSectionId graphql.ID
	},
) (*[]*teacherPayloads.CourseSectionItemPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("TeacherResolver.CourseSectionItems")

	courseId, err := utils.GqlIDToUint32(args.CourseId)
	if err != nil {
		return nil, err
	}

	courseSectionId, err := utils.GqlIDToUint32(args.CourseSectionId)
	if err != nil {
		return nil, err
	}

	sectionItems, err := r.Repos.CourseSectionItemRepo().
		ListByTeacherCourse(
			r.Ctx, courseId, courseSectionId, r.currentTeacher.ID,
			r.DetectAssociations("courseSectionItem", []resolvers.RequestAssociation{
				{RequestKey: "drills"},
				{RequestKey: "drills.diagrams"},
				{RequestKey: "drills.skills", Key: "Drills.DrillSkills.Skill"},
			})...,
		)
	if err != nil {
		return nil, err
	}

	return sectionItemSliceToType(*sectionItems), nil
}

func sectionItemSliceToType(
	sectionItems []*models.CourseSectionItem,
) *[]*teacherPayloads.CourseSectionItemPayload {
	var sectionItemPayloads []*teacherPayloads.CourseSectionItemPayload
	for _, item := range sectionItems {
		sectionItemPayloads = append(sectionItemPayloads, &teacherPayloads.CourseSectionItemPayload{
			CourseSectionItemPayload: &globalPayloads.CourseSectionItemPayload{
				SectionItem: item,
			},
		})
	}

	return &sectionItemPayloads
}
