package teacherResolvers

import (
	"errors"
	teacherInputs "vibico-education-api/internal/gqls/inputs/teachers"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	teacherPayloads "vibico-education-api/internal/gqls/payloads/teachers"
	courseSectionServices "vibico-education-api/internal/services/teachers/courseSection"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
	"github.com/rs/zerolog/log"
)

func (r *Resolver) CourseSectionCreate(args struct {
	CourseID graphql.ID
	Input    teacherInputs.CourseSectionCreateAndUpdateInput
}) (*teacherPayloads.CourseCreateAndUpdate, error) {
	log.Info().Ctx(r.Ctx).Msg("teacherResolvers.CourseSectionCreate")

	courseID, err := utils.ParseGraphqlID[uint32](args.CourseID)
	if err != nil {
		return nil, err
	}

	course, err := r.Repos.CourseRepo().FindByIDAndTeacher(r.Ctx, courseID, r.currentTeacher.ID)
	if err != nil {
		return nil, err
	}
	if course.IsApproved() {
		return nil, errors.New(translator.Translate(nil, "errValidationMsg_status_invalid"))
	}

	service := courseSectionServices.NewCourseSectionCreateService(r.Ctx, &args.Input, r.Repos.CourseSectionRepo(), courseID)

	if err := service.Execute(); err != nil {
		return nil, err
	}

	return &teacherPayloads.CourseCreateAndUpdate{
		CourseSection: &teacherPayloads.CourseSectionPayload{
			CourseSectionPayload: &globalPayloads.CourseSectionPayload{
				CourseSection: service.CourseSection,
			},
		},
		Message: translator.Translate(nil, "general_createSuccess"),
	}, nil
}
