package teacherResolvers

import (
	"errors"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
	"github.com/rs/zerolog/log"
)

func (r *Resolver) DrillSubmit(args struct {
	ID graphql.ID
}) (*globalPayloads.MessageInfoPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("TeacherResolver.DrillSubmit")

	drillId, err := utils.ParseGraphqlID[uint32](args.ID)
	if err != nil {
		return nil, err
	}

	drillRepo := r.Repos.DrillRepo()
	drill, err := drillRepo.FindByIDAndOwner(r.Ctx, drillId, r.currentTeacher.ID, "Teacher")
	if err != nil {
		return nil, err
	}

	if drill.IsApproved() || drill.IsSubmitted() {
		return nil, errors.New(translator.Translate(nil, "errValidationMsg_invalid_censoring_condition"))
	}

	if err := drillRepo.Submit(r.Ctx, drill); err != nil {
		return nil, err
	}

	return &globalPayloads.MessageInfoPayload{
		Message: translator.Translate(nil, "infoMsg_submitSuccess"),
	}, nil
}
