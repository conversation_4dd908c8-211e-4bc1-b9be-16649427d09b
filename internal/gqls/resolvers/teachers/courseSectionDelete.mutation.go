package teacherResolvers

import (
	"errors"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
	"github.com/rs/zerolog/log"
)

func (r *Resolver) CourseSectionDelete(args struct{ ID, CourseID graphql.ID }) (*globalPayloads.MessageInfoPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("Resolver.CourseSectionDelete")

	courseID, err := utils.ParseGraphqlID[uint32](args.CourseID)
	if err != nil {
		return nil, err
	}

	courseSectionId, err := utils.ParseGraphqlID[uint32](args.ID)
	if err != nil {
		return nil, err
	}

	course, err := r.Repos.CourseRepo().FindByIDAndTeacher(r.Ctx, courseID, r.currentTeacher.ID)
	if err != nil {
		return nil, err
	}
	if course.IsApproved() {
		return nil, errors.New(translator.Translate(nil, "errValidationMsg_status_invalid"))
	}

	sectionRepo := r.Repos.CourseSectionRepo()
	courseSection, err := sectionRepo.FindByIDAndCourseID(
		r.Ctx,
		courseSectionId,
		courseID,
	)

	if err != nil {
		return nil, err
	}

	if err := sectionRepo.Delete(r.Ctx, courseSection); err != nil {
		return nil, err
	}

	return &globalPayloads.MessageInfoPayload{
		Message: translator.Translate(nil, "general_deleteSuccess"),
	}, nil
}
