package teacherResolvers

import (
	teacherDrillForms "vibico-education-api/internal/forms/teachers/drill"
	teacherInputs "vibico-education-api/internal/gqls/inputs/teachers"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	teacherPayloads "vibico-education-api/internal/gqls/payloads/teachers"
	"vibico-education-api/internal/models"
	translator "vibico-education-api/pkg/translators"

	"github.com/rs/zerolog/log"
)

func (r *Resolver) DrillCreate(args struct {
	Input teacherInputs.DrillModifyInput
}) (*teacherPayloads.DrillModifyPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("TeacherResolver.DrillCreate")

	drill := models.Drill{
		OwnerID:   r.currentTeacher.ID,
		OwnerType: "Teacher",
	}
	form := teacherDrillForms.NewDrillCreateForm(
		r.Ctx,
		&drill,
		r.Repos.DrillRepo(),
		r.Repos.SkillRepo(),
		&args.Input,
	)
	if err := form.Save(); err != nil {
		return nil, err
	}

	return &teacherPayloads.DrillModifyPayload{
		Drill: &teacherPayloads.DrillPayload{
			DrillPayload: &globalPayloads.DrillPayload{Drill: &drill},
		},
		Message: translator.Translate(nil, "infoMsg_createSuccess"),
	}, nil
}
