package teacherResolvers

import (
	"errors"
	teacherInputs "vibico-education-api/internal/gqls/inputs/teachers"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	teacherServices "vibico-education-api/internal/services/teachers"
	translator "vibico-education-api/pkg/translators"

	"github.com/rs/zerolog/log"
)

func (r *Resolver) CourseSectionItemRemoveDrill(
	args teacherInputs.CourseSectionItemDrillMutateInput,
) (*globalPayloads.MessageInfoPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("TeacherResolver.CourseSectionItemRemoveDrill")

	itemID, drillID, courseID, courseSectionID, err := r.validateItemAndDrillIds(args)
	if err != nil {
		return nil, err
	}

	course, err := r.Repos.CourseRepo().FindByIDAndTeacher(r.Ctx, courseID, r.currentTeacher.ID)
	if err != nil {
		return nil, err
	}

	if course.IsApproved() {
		return nil, errors.New(translator.Translate(nil, "errValidationMsg_status_invalid"))
	}

	_, drill, err := r.validateItemAndDrillRecord(itemID, drillID, courseID, courseSectionID)
	if err != nil {
		return nil, err
	}

	service := teacherServices.NewDrillDestroyService(r.Ctx, r.Repos.GetDb(), drill, &courseID)
	if err := service.Execute(); err != nil {
		return nil, err
	}

	return &globalPayloads.MessageInfoPayload{
		Message: translator.Translate(nil, "general_deleteSuccess"),
	}, nil
}
