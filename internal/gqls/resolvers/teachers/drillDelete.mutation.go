package teacherResolvers

import (
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	teacherServices "vibico-education-api/internal/services/teachers"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
	"github.com/rs/zerolog/log"
)

func (r *Resolver) DrillDelete(args struct {
	ID graphql.ID
}) (*globalPayloads.MessageInfoPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("TeacherResolver.DrillDelete")

	drillId, err := utils.ParseGraphqlID[uint32](args.ID)
	if err != nil {
		return nil, err
	}

	drillRepo := r.Repos.DrillRepo()
	drill, err := drillRepo.FindByIDAndOwner(
		r.Ctx,
		drillId,
		r.currentTeacher.ID,
		"Teacher",
	)
	if err != nil {
		return nil, err
	}

	userDrillCount, err := drillRepo.AssociationCount(r.Ctx, drill, "UserDrills")
	if err != nil {
		return nil, err
	}
	if userDrillCount > 0 {
		return nil, exceptions.NewUnprocessableContentError(translator.Translate(nil, "errDbMsg_DrillInUser"), nil)
	}

	service := teacherServices.NewDrillDestroyService(r.Ctx, r.Repos.GetDb(), drill, nil)
	if err := service.Execute(); err != nil {
		return nil, err
	}

	return &globalPayloads.MessageInfoPayload{
		Message: translator.Translate(nil, "general_deleteSuccess"),
	}, nil
}
