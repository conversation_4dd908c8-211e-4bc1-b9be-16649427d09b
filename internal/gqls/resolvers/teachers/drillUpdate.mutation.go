package teacherResolvers

import (
	teacherDrillForms "vibico-education-api/internal/forms/teachers/drill"
	teacherInputs "vibico-education-api/internal/gqls/inputs/teachers"
	adminPayloads "vibico-education-api/internal/gqls/payloads/admins"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
	"github.com/rs/zerolog/log"
)

func (r *Resolver) DrillUpdate(args struct {
	ID    graphql.ID
	Input teacherInputs.DrillModifyInput
}) (*adminPayloads.DrillModifyPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("TeacherResolver.DrillUpdate")

	id, err := utils.ParseGraphqlID[uint32](args.ID)
	if err != nil {
		return nil, err
	}

	drill, err := r.Repos.DrillRepo().FindByIDAndOwner(
		r.Ctx,
		id,
		r.currentTeacher.ID,
		"Teacher",
	)
	if err != nil {
		return nil, err
	}

	form := teacherDrillForms.NewDrillUpdateForm(
		r.Ctx,
		drill,
		&args.Input,
		r.Repos.DrillRepo(),
		r.Repos.SkillRepo(),
	)
	if err := form.Save(); err != nil {
		return nil, err
	}

	return &adminPayloads.DrillModifyPayload{
		Drill: &adminPayloads.DrillPayload{
			DrillPayload: &globalPayloads.DrillPayload{Drill: drill},
		},
		Message: translator.Translate(nil, "infoMsg_updateSuccess"),
	}, nil
}
