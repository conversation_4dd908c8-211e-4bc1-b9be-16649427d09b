package teacherResolvers

import (
	"errors"
	"fmt"
	"strings"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	"vibico-education-api/internal/models"
	"vibico-education-api/internal/repository/repositories"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
	"github.com/rs/zerolog/log"
)

func (r *Resolver) CourseSubmit(args struct {
	ID graphql.ID
}) (*globalPayloads.MessageInfoPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("TeacherResolver.CourseSubmit")

	courseID, err := utils.ParseGraphqlID[uint32](args.ID)
	if err != nil {
		return nil, err
	}

	courseRepo := r.Repos.CourseRepo()
	course, err := courseRepo.FindByIDAndTeacher(
		r.Ctx, courseID, r.currentTeacher.ID,
		repositories.CustomPreload{Key: "CourseSections.CourseSectionItems.CourseSectionItemDrills"},
		repositories.CustomPreload{Key: "CourseSections.CourseSectionItems.Videos"},
	)
	if err != nil {
		return nil, err
	}

	if course.IsApproved() || course.IsSubmitted() {
		return nil, errors.New(translator.Translate(nil, "errValidationMsg_status_invalid"))
	}

	message := translator.Translate(nil, "errDbMsg_CourseContentInvalid")
	if err := validateCourseContent(course, &message); err != nil {
		return nil, err
	}

	if err := courseRepo.SubmitCourse(r.Ctx, course); err != nil {
		return nil, err
	}

	return &globalPayloads.MessageInfoPayload{
		Message: translator.Translate(nil, "general_updateSuccess"),
	}, nil
}

func validateCourseContent(course *models.Course, message *string) error {
	if course.CourseSections == nil || len(*course.CourseSections) == 0 {
		return exceptions.NewBadRequestError(message)
	}

	contentErrs := exceptions.NewUnprocessableContentError(*message, nil)

	for _, section := range *course.CourseSections {
		if section.CourseSectionItems == nil || len(*section.CourseSectionItems) == 0 {
			contentErrs.AddError(fmt.Sprintf("courseSection.%d", section.ID), []any{translator.Translate(nil, "errDbMsg_CourseSectionInvalid")})
		}

		for _, item := range *section.CourseSectionItems {
			if (item.CourseSectionItemDrills == nil ||
				len(*item.CourseSectionItemDrills) == 0) &&
				(item.Videos == nil || len(*item.Videos) == 0) &&
				(item.Content == nil || strings.TrimSpace(*item.Content) == "") {
				contentErrs.AddError(fmt.Sprintf("courseSection.%d.courseSectionItem.%d", section.ID, item.ID), []any{translator.Translate(nil, "errDbMsg_CourseSectionItemInvalid")})
			}
		}
	}

	if len(contentErrs.Errors) > 0 {
		return contentErrs
	}

	return nil
}
