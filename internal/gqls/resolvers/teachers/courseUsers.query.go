package teacherResolvers

import (
	teacherInputs "vibico-education-api/internal/gqls/inputs/teachers"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	teacherPayloads "vibico-education-api/internal/gqls/payloads/teachers"
	"vibico-education-api/internal/models"
	"vibico-education-api/internal/repository/repositories"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/rs/zerolog/log"
)

func (r *Resolver) CourseUsers(args teacherInputs.CourseUsersInput) (
	*teacherPayloads.CourseUserCollectionPayload, error,
) {
	log.Info().Ctx(r.Ctx).Msg("TeacherResolver.CourseUsers")

	courseID, err := utils.ParseGraphqlID[uint32](args.CourseId)
	if err != nil {
		return nil, err
	}

	_, err = r.Repos.CourseRepo().FindByIDAndTeacher(r.Ctx, courseID, r.currentTeacher.ID)
	if err != nil {
		return nil, err
	}

	resolvers := []*teacherPayloads.CourseUserPayload{}

	paginationData, err := r.Repos.CourseUserRepo().ListByCourse(r.Ctx, args, repositories.CustomPreload{Key: "User"})
	if err != nil {
		return nil, err
	}

	metadata := paginationData.Metadata
	courseUsers, ok := paginationData.Collection.([]*models.CourseUser)
	if ok {
		for _, e := range courseUsers {
			resolvers = append(resolvers, &teacherPayloads.CourseUserPayload{
				CourseUserPayload: &globalPayloads.CourseUserPayload{CourseUser: e},
			})
		}
	}

	return &teacherPayloads.CourseUserCollectionPayload{
		Collection: &resolvers,
		Metadata: &globalPayloads.MetadataPayload{
			Metadata: metadata,
		},
	}, nil
}
