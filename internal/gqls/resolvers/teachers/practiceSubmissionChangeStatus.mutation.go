package teacherResolvers

import (
	"errors"
	"vibico-education-api/internal/enums"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	teacherPayloads "vibico-education-api/internal/gqls/payloads/teachers"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
	"github.com/rs/zerolog/log"
)

func (r *Resolver) PracticeSubmissionChangeStatus(args struct {
	ID     graphql.ID
	Status string
}) (*teacherPayloads.PracticeSubmissionPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("TeacherResolver.PracticeSubmissionChangeStatus")

	practiceSubmissionID, err := utils.ParseGraphqlID[uint32](args.ID)
	if err != nil {
		return nil, err
	}

	practiceSubmission, err := r.Repos.PracticeSubmissionRepo().FindByIDAndTeacherID(
		r.Ctx,
		practiceSubmissionID,
		r.currentTeacher.ID,
	)
	if err != nil {
		return nil, err
	}

	if practiceSubmission.IsNotSubmitted() {
		return nil, errors.New("practiceSubmission is not submitted")
	}

	newStatus, err := enums.ParsePracticeSubmissionStatus(args.Status)
	if err != nil {
		return nil, err
	}

	if newStatus != enums.PracticeSubmissionStatusApproved &&
		newStatus != enums.PracticeSubmissionStatusRejected {
		return nil, errors.New("invalid status")
	}

	practiceSubmission.Status = newStatus
	if err := r.Repos.PracticeSubmissionRepo().Update(
		r.Ctx,
		practiceSubmission,
		"status",
	); err != nil {
		return nil, err
	}

	return &teacherPayloads.PracticeSubmissionPayload{
		PracticeSubmissionPayload: &globalPayloads.PracticeSubmissionPayload{
			PracticeSubmission: practiceSubmission,
		},
	}, nil
}
