package teacherResolvers

import (
	"vibico-education-api/internal/forms/teachers/templateDiagramForms"
	teacherInputs "vibico-education-api/internal/gqls/inputs/teachers"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	teacherPayloads "vibico-education-api/internal/gqls/payloads/teachers"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
	"github.com/rs/zerolog/log"
)

func (r *Resolver) TemplateDiagramUpdate(
	args struct {
		ID    graphql.ID
		Input *teacherInputs.TemplateDiagramFormInput
	},
) (*teacherPayloads.TemplateDiagramMutatePayload, error) {
	log.Info().Ctx(r.Ctx).Msg("TeacherResolver.TemplateDiagramUpdate")

	templateID, err := utils.ParseGraphqlID[uint32](args.ID)
	if err != nil {
		return nil, err
	}

	templateRepo := r.Repos.TemplateDiagramRepo()
	template, err := templateRepo.FindById(r.Ctx, templateID)
	if err != nil {
		return nil, err
	}

	form := templateDiagramForms.NewUpdateForm(
		r.Ctx,
		args.Input,
		template,
		templateRepo,
	)

	if err := form.Save(); err != nil {
		return nil, err
	}

	return &teacherPayloads.TemplateDiagramMutatePayload{
		TemplateDiagram: &teacherPayloads.TemplateDiagramPayload{
			TemplateDiagramPayload: &globalPayloads.TemplateDiagramPayload{
				TemplateDiagram: form.TemplateDiagram,
			},
		},
		Message: translator.Translate(nil, "infoMsg_updateSuccess"),
	}, nil
}
