package teacherResolvers

import (
	"errors"
	teacherForms "vibico-education-api/internal/forms/teachers"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	teacherServices "vibico-education-api/internal/services/teachers"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
	"github.com/rs/zerolog/log"
)

func (r *Resolver) InviteUser(args struct {
	CourseId        graphql.ID
	PhoneNumber     *string
	CoursePackageID *graphql.ID
}) (*globalPayloads.MessageInfoPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("TeacherResolver.InviteUser")

	if !r.currentTeacher.CanInviteStudents {
		return nil, errors.New(translator.Translate(nil, "errExceptionMsg_forbidden"))
	}

	form := teacherForms.NewInviteUserForm(r.Ctx, args.PhoneNumber)
	if err := form.Validate(); err != nil {
		return nil, err
	}

	if *args.PhoneNumber == *r.currentTeacher.PhoneNumber {
		return nil, errors.New(translator.Translate(nil, "errExceptionMsg_forbidden"))
	}

	courseID, err := utils.ParseGraphqlID[uint32](args.CourseId)
	if err != nil {
		return nil, err
	}

	var coursePackageID uint32
	if args.CoursePackageID != nil {
		coursePackageID, err = utils.ParseGraphqlID[uint32](*args.CoursePackageID)
		if err != nil {
			return nil, err
		}
	}

	service := teacherServices.NewInviteUserService(
		r.Ctx,
		courseID,
		coursePackageID,
		r.currentTeacher,
		*args.PhoneNumber,
		r.Repos.CourseRepo(),
		r.Repos.UserRepo(),
	)

	if err := service.Execute(); err != nil {
		return nil, err
	}

	return &globalPayloads.MessageInfoPayload{
		Message: translator.Translate(nil, "general_success"),
	}, nil
}
