package teacherResolvers

import (
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	teacherPayloads "vibico-education-api/internal/gqls/payloads/teachers"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
	"github.com/rs/zerolog/log"
)

func (r *Resolver) Diagram(args struct{ ID graphql.ID }) (*teacherPayloads.DiagramPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("TeacherResolver.Diagram")

	diagramId, err := utils.GqlIdToUint32(args.ID)
	if err != nil {
		return nil, err
	}

	diagram, err := r.Repos.DiagramRepo().FindById(r.Ctx, diagramId)
	if err != nil {
		return nil, err
	}

	return &teacherPayloads.DiagramPayload{
		DiagramPayload: &globalPayloads.DiagramPayload{
			Diagram: diagram,
		},
	}, nil
}
