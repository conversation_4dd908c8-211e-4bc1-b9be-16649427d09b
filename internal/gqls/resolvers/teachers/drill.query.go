package teacherResolvers

import (
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	teacherPayloads "vibico-education-api/internal/gqls/payloads/teachers"
	"vibico-education-api/internal/gqls/resolvers"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

func (r *Resolver) Drill(args struct {
	Slug string
}) (*teacherPayloads.DrillPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("TeacherResolver.Drill")

	drill, err := r.Repos.DrillRepo().FindBySlugAndOwner(
		r.Ctx,
		args.Slug,
		r.currentTeacher.ID,
		"Teacher",
		r.<PERSON>ect<PERSON>s("drill", []resolvers.RequestAssociation{
			{RequestKey: "skills", Key: "DrillSkills.Skill"},
			{RequestKey: "videos", Args: []any{func(db *gorm.DB) *gorm.DB {
				return db.Order("videos.id ASC")
			}}},
			{RequestKey: "videos.videoPlatforms"},
			{RequestKey: "diagrams"},
			{RequestKey: "censorHistories", Args: []any{func(db *gorm.DB) *gorm.DB {
				return db.Order("censor_histories.id DESC")
			}}},
		})...,
	)

	if err != nil {
		return nil, err
	}

	return &teacherPayloads.DrillPayload{
		DrillPayload: &globalPayloads.DrillPayload{
			Drill: drill,
		},
	}, nil
}
