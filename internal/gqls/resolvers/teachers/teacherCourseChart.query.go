package teacherResolvers

import (
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"

	"github.com/rs/zerolog/log"
)

func (r *Resolver) TeacherCourseChart(args struct{ Year *int32 }) (*[]*globalPayloads.CourseEarningPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("TeacherResolver.TeacherCourseChart")

	results, err := r.Repos.CourseRepo().GetTeacherCourseChart(r.Ctx, r.currentTeacher.ID, args.Year)
	if err != nil {
		return nil, err
	}

	var payloads []*globalPayloads.CourseEarningPayload
	for _, earning := range results {
		payloads = append(payloads, &globalPayloads.CourseEarningPayload{
			CourseEarning: earning,
		})
	}

	return &payloads, nil
}
