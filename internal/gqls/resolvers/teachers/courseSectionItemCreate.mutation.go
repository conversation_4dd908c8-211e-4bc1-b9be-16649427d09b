package teacherResolvers

import (
	"errors"
	teacherInputs "vibico-education-api/internal/gqls/inputs/teachers"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	teacherPayloads "vibico-education-api/internal/gqls/payloads/teachers"
	courseSectionItemServices "vibico-education-api/internal/services/teachers/courseSectionItem"

	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/rs/zerolog/log"
)

func (r *Resolver) CourseSectionItemCreate(args *teacherInputs.CourseSectionItemCreateInput) (*teacherPayloads.CourseSectionItemMutatePayload, error) {
	log.Info().Ctx(r.Ctx).Msg("TeacherResolver.CourseSectionItemCreate")

	courseID, err := utils.ParseGraphqlID[uint32](args.CourseId)
	if err != nil {
		return nil, err
	}

	course, err := r.Repos.CourseRepo().FindByIDAndTeacher(r.Ctx, courseID, r.currentTeacher.ID)
	if err != nil {
		return nil, err
	}

	if course.IsApproved() {
		return nil, errors.New(translator.Translate(nil, "errValidationMsg_status_invalid"))
	}

	service := courseSectionItemServices.NewCreateService(
		r.Ctx,
		r.Repos.CourseSectionRepo(),
		r.Repos.CourseSectionItemRepo(),
		r.Repos.DrillRepo(),
		r.currentTeacher,
		args,
	)

	if err := service.Execute(); err != nil {
		return nil, err
	}

	return &teacherPayloads.CourseSectionItemMutatePayload{
		SectionItem: &teacherPayloads.CourseSectionItemPayload{
			CourseSectionItemPayload: &globalPayloads.CourseSectionItemPayload{
				SectionItem: service.CourseSectionItem,
			},
		},
		Message: translator.Translate(nil, "infoMsg_createSuccess"),
	}, nil
}
