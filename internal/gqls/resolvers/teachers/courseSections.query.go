package teacherResolvers

import (
	"context"
	teacherPayloads "vibico-education-api/internal/gqls/payloads/teachers"
	"vibico-education-api/internal/gqls/resolvers"
	"vibico-education-api/internal/repository/repositories"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

func (r *Resolver) CourseSections(ctx context.Context, args struct{ CourseID graphql.ID }) ([]*teacherPayloads.CourseSectionPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("teacherResolvers.CourseSections")

	courseID, err := utils.ParseGraphqlID[uint32](args.CourseID)
	if err != nil {
		return nil, err
	}
	course, err := r.Repos.CourseRepo().FindByIDAndTeacher(r.Ctx, courseID, r.currentTeacher.ID)
	if err != nil {
		return nil, err
	}

	courseSection, err := r.Repos.CourseSectionRepo().ListByCourseId(
		r.Ctx,
		course.ID,
		r.DetectAssociations("courseSections", []resolvers.RequestAssociation{
			{
				RequestKey: "courseSectionItems",
				Args: []any{func(db *gorm.DB) *gorm.DB {
					return db.Order(repositories.CSIOrderPositionAscSQL)
				}},
			},
		})...,
	)
	if err != nil {
		return nil, err
	}

	return courseSection, nil
}
