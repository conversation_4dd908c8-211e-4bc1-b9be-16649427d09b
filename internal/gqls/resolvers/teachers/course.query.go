package teacherResolvers

import (
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	teacherPayloads "vibico-education-api/internal/gqls/payloads/teachers"
	"vibico-education-api/internal/gqls/resolvers"
	"vibico-education-api/internal/repository/repositories"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

func (r *Resolver) Course(args struct{ Slug string }) (*teacherPayloads.CoursePayload, error) {
	log.Info().Ctx(r.Ctx).Msg("teacherResolvers.Course")

	course, err := r.Repos.CourseRepo().FindBySlugAndTeacher(
		r.Ctx,
		args.Slug,
		r.currentTeacher.ID,
		r.DetectAssociations("course", []resolvers.RequestAssociation{
			{RequestKey: "teacher"},
			{RequestKey: "courseUsers", Args: []any{
				func(db *gorm.DB) *gorm.DB {
					return db.Order("course_users.id DESC")
				},
			}},
			{RequestKey: "courseUsers.user"},
			{RequestKey: "courseSections", Args: []any{
				func(db *gorm.DB) *gorm.DB {
					return db.Order("course_sections.position ASC")
				},
			}},
			{RequestKey: "courseSections.courseSectionItems", Args: []any{
				func(db *gorm.DB) *gorm.DB {
					return db.Order(repositories.CSIOrderPositionAscSQL)
				},
			}},
			{RequestKey: "courseCensorHistories", Args: []any{
				func(db *gorm.DB) *gorm.DB {
					return db.Order("censor_histories.id DESC")
				},
			}},
		})...,
	)

	if err != nil {
		return nil, err
	}

	return &teacherPayloads.CoursePayload{CoursePayload: globalPayloads.CoursePayload{Course: course}}, nil
}
