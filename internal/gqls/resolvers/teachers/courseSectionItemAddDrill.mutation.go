package teacherResolvers

import (
	"errors"
	teacherInputs "vibico-education-api/internal/gqls/inputs/teachers"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	"vibico-education-api/internal/models"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

func (r *Resolver) CourseSectionItemAddDrill(
	args teacherInputs.CourseSectionItemDrillMutateInput,
) (*globalPayloads.MessageInfoPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("TeacherResolver.CourseSectionItemAddDrill")

	itemID, drillID, courseID, courseSectionID, err := r.validateItemAndDrillIds(args)
	if err != nil {
		return nil, err
	}

	_, drill, err := r.validateItemAndDrillRecord(itemID, drillID, courseID, courseSectionID)
	if err != nil {
		return nil, err
	}

	repo := r.Repos.CourseSectionItemDrillRepo()
	_, err = repo.FindByDrillIDAndCourseSectionItemID(r.Ctx, drillID, itemID)

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			createErr := repo.Create(
				r.Ctx,
				&models.CourseSectionItemDrill{
					CourseSectionItemId: itemID,
				},
				drill,
				courseID)
			if createErr != nil {
				return nil, createErr
			}
		} else {
			return nil, err
		}
	} else {
		return nil, errors.New(translator.Translate(nil, "errDbMsg_duplicated"))
	}

	return &globalPayloads.MessageInfoPayload{
		Message: translator.Translate(nil, "infoMsg_createSuccess"),
	}, nil
}

func (r *Resolver) validateItemAndDrillIds(
	args teacherInputs.CourseSectionItemDrillMutateInput,
) (itemID, drillID, courseID, courseSectionID uint32, err error) {
	itemID, err = utils.ParseGraphqlID[uint32](args.ItemId)
	if err != nil {
		return
	}
	drillID, err = utils.ParseGraphqlID[uint32](args.DrillId)
	if err != nil {
		return
	}
	courseID, err = utils.ParseGraphqlID[uint32](args.CourseId)
	if err != nil {
		return
	}
	courseSectionID, err = utils.ParseGraphqlID[uint32](args.CourseSectionId)
	if err != nil {
		return
	}

	return
}

func (r *Resolver) validateItemAndDrillRecord(itemID, drillID, courseID, courseSectionID uint32) (*models.CourseSectionItem, *models.Drill, error) {
	course, err := r.Repos.CourseRepo().FindByIDAndTeacher(r.Ctx, courseID, r.currentTeacher.ID)
	if err != nil {
		return nil, nil, err
	}
	if course.IsApproved() {
		return nil, nil, errors.New(translator.Translate(nil, "errValidationMsg_status_invalid"))
	}

	item, err := r.Repos.CourseSectionItemRepo().FindByIDAndSectionID(
		r.Ctx,
		itemID,
		courseSectionID,
	)
	if err != nil {
		return nil, nil, err
	}

	drill, err := r.Repos.DrillRepo().FindByIDAndOwner(r.Ctx, drillID, r.currentTeacher.ID, "Teacher")
	if err != nil {
		return nil, nil, err
	}

	return item, drill, nil
}
