package teacherResolvers

import (
	"vibico-education-api/internal/enums"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	teacherPayloads "vibico-education-api/internal/gqls/payloads/teachers"
	"vibico-education-api/internal/repository/repositories"

	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

func (r *Resolver) CourseSectionsWithPractices(args struct{ CourseSlug string }) (*[]*teacherPayloads.CourseSectionPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("teacherResolvers.CourseSectionsWithPractices")

	sections, err := r.Repos.CourseSectionRepo().ListWithPractices(
		r.Ctx,
		args.CourseSlug,
		r.currentTeacher.ID,
		repositories.CustomPreload{
			Key: "CourseSectionItems",
			Args: []any{
				func(db *gorm.DB) *gorm.DB {
					return db.Order("course_section_items.id ASC")
				},
			},
		},
		repositories.CustomPreload{
			Key: "CourseSectionItems.PracticeSubmissions",
			Args: []any{
				func(db *gorm.DB) *gorm.DB {
					return db.Where(`practice_submissions.status = ?`, enums.PracticeSubmissionStatusSubmitted)
				},
			},
		},
	)

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, exceptions.NewRecordNotFoundError()
		}
		return nil, err
	}

	result := make([]*teacherPayloads.CourseSectionPayload, len(sections))
	for i, section := range sections {
		result[i] = &teacherPayloads.CourseSectionPayload{
			CourseSectionPayload: &globalPayloads.CourseSectionPayload{
				CourseSection: section,
			},
		}
	}

	return &result, nil
}
