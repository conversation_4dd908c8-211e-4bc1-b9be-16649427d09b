package teacherResolvers

import (
	teacherInputs "vibico-education-api/internal/gqls/inputs/teachers"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	teacherPayloads "vibico-education-api/internal/gqls/payloads/teachers"
	"vibico-education-api/internal/repository/repositories"

	"github.com/rs/zerolog/log"
)

func (r *Resolver) TemplateDiagrams(args teacherInputs.TemplateDiagramListInput) (*teacherPayloads.TemplateDiagramCollection, error) {
	log.Info().Ctx(r.Ctx).Msg("TeacherResolver.TemplateDiagrams")

	paginationData, err := r.Repos.TemplateDiagramRepo().List(
		r.Ctx,
		args,
		repositories.CustomPreload{
			Key: "Diagrams",
		},
	)

	if err != nil {
		return nil, err
	}
	return &teacherPayloads.TemplateDiagramCollection{
		Collection: teacherPayloads.TemplateDiagramsSliceToTypes(paginationData.Collection),
		Metadata: &globalPayloads.MetadataPayload{
			Metadata: paginationData.Metadata,
		},
	}, nil
}
