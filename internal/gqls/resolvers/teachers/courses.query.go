package teacherResolvers

import (
	teacherInputs "vibico-education-api/internal/gqls/inputs/teachers"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	teacherPayloads "vibico-education-api/internal/gqls/payloads/teachers"
	"vibico-education-api/internal/gqls/resolvers"
	"vibico-education-api/internal/models"
	"vibico-education-api/internal/repository/repositories"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

func (r *Resolver) Courses(args teacherInputs.CoursesInput) (*teacherPayloads.CoursesCollectionPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("teacherResolvers.Courses")

	paginationData, err := r.Repos.CourseRepo().ListByTeacher(
		r.Ctx,
		args,
		r.currentTeacher.ID,
		r.DetectAssociations("courses", []resolvers.RequestAssociation{
			{
				RequestKey: "collection.courseSections",
				Key:        "CourseSections",
				Args: []any{
					func(db *gorm.DB) *gorm.DB {
						return db.Order("course_sections.position ASC")
					},
				},
			},
			{
				RequestKey: "courseSections.courseSectionItems",
				Args: []any{
					func(db *gorm.DB) *gorm.DB {
						return db.Order(repositories.CSIOrderPositionAscSQL)
					},
				},
			},
		})...,
	)

	if err != nil {
		return nil, err
	}

	courses, ok := paginationData.Collection.([]*models.Course)
	resolvers := make([]*teacherPayloads.CoursePayload, len(courses))
	if ok {
		for i, e := range courses {
			resolvers[i] = &teacherPayloads.CoursePayload{CoursePayload: globalPayloads.CoursePayload{Course: e}}
		}
	}

	return &teacherPayloads.CoursesCollectionPayload{
		Collection: &resolvers,
		Metadata: &globalPayloads.MetadataPayload{
			Metadata: paginationData.Metadata,
		},
	}, nil
}
