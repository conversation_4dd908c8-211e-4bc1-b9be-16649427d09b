package teacherResolvers

import (
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
	"github.com/rs/zerolog/log"
)

func (r *Resolver) CoursePublic(args struct {
	ID graphql.ID
}) (*globalPayloads.MessageInfoPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("TeacherResolver.CoursePublic")

	courseId, err := utils.ParseGraphqlID[uint32](args.ID)
	if err != nil {
		return nil, err
	}

	courseRepo := r.Repos.CourseRepo()
	course, err := courseRepo.FindByIDAndTeacher(r.Ctx, courseId, r.currentTeacher.ID)
	if err != nil {
		return nil, err
	}

	course.IsPublic = !course.IsPublic
	if err := courseRepo.Update(r.Ctx, course, "is_public"); err != nil {
		return nil, err
	}

	return &globalPayloads.MessageInfoPayload{
		Message: translator.Translate(nil, "general_updateSuccess"),
	}, nil
}
