package teacherResolvers

import (
	teacherInputs "vibico-education-api/internal/gqls/inputs/teachers"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	teacherPayloads "vibico-education-api/internal/gqls/payloads/teachers"
	"vibico-education-api/internal/repository/repositories"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

func (r *Resolver) Drills(args teacherInputs.DrillInput) (*teacherPayloads.DrillsCollectionPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("Resolver.Drills")

	paginationData, err := r.Repos.DrillRepo().ListByTeacher(
		r.Ctx,
		args,
		r.currentTeacher.ID,
		repositories.CustomPreload{Key: "DrillSkills.Skill"},
		repositories.CustomPreload{
			Key: "Diagrams",
			Args: []any{
				func(db *gorm.DB) *gorm.DB {
					return db.Order("diagrams.position ASC")
				},
			}},
	)

	if err != nil {
		return nil, err
	}

	return &teacherPayloads.DrillsCollectionPayload{
		Collection: teacherPayloads.DrillsSliceToTypes(paginationData.Collection),
		Metadata: &globalPayloads.MetadataPayload{
			Metadata: paginationData.Metadata,
		},
	}, nil
}
