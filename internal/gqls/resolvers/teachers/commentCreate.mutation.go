package teacherResolvers

import (
	"vibico-education-api/internal/forms/teachers/commentForms"
	globalInputs "vibico-education-api/internal/gqls/inputs/global"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	teacherPayloads "vibico-education-api/internal/gqls/payloads/teachers"
	translator "vibico-education-api/pkg/translators"

	"github.com/rs/zerolog/log"
)

func (r *Resolver) CommentCreate(args struct {
	Input globalInputs.CommentCreateInput
}) (*teacherPayloads.CommentCreatePayload, error) {
	log.Info().Ctx(r.Ctx).Msg("TeacherResolver.CommentCreate")

	form := commentForms.NewCreateForm(r.Ctx, args.Input, r.currentTeacher, r.Repos.CommentRepo(), r.Repos)
	if err := form.Save(); err != nil {
		return nil, err
	}

	return &teacherPayloads.CommentCreatePayload{
		Comment: &teacherPayloads.CommentPayload{CommentPayload: &globalPayloads.CommentPayload{Comment: form.Comment}},
		Message: translator.Translate(nil, "infoMsg_createSuccess"),
	}, nil
}
