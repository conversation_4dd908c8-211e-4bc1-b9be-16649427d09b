package adminResolvers

import (
	adminInputs "vibico-education-api/internal/gqls/inputs/admins"
	adminPayloads "vibico-education-api/internal/gqls/payloads/admins"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	"vibico-education-api/internal/models"

	"github.com/rs/zerolog/log"
)

func (r *Resolver) Users(args adminInputs.UsersInput) (*adminPayloads.UsersCollectionPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("AdminResolver.Users")

	paginationData, err := r.Repos.UserRepo().AdminList(r.Ctx, args)
	if err != nil {
		return nil, err
	}

	users, ok := paginationData.Collection.([]*models.User)
	resolvers := make([]*adminPayloads.UserPayload, len(users))
	if ok {
		for i, e := range users {
			resolvers[i] = &adminPayloads.UserPayload{UserPayload: &globalPayloads.UserPayload{User: e}}
		}
	}

	return &adminPayloads.UsersCollectionPayload{
		Collection: &resolvers,
		Metadata:   &globalPayloads.MetadataPayload{Metadata: paginationData.Metadata},
	}, nil
}
