package adminResolvers

import (
	adminForms "vibico-education-api/internal/forms/admins"
	adminInputs "vibico-education-api/internal/gqls/inputs/admins"
	adminPayloads "vibico-education-api/internal/gqls/payloads/admins"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"

	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/rs/zerolog/log"
)

func (r *Resolver) CourseSectionItemUpdate(
	args *adminInputs.CourseSectionItemUpdateInput,
) (*adminPayloads.CourseSectionItemMutatePayload, error) {
	log.Info().Ctx(r.Ctx).Msg("AdminResolver.CourseSectionItemUpdate")

	courseId, err := utils.ParseGraphqlID[uint32](args.CourseId)
	if err != nil {
		return nil, err
	}
	courseSectionId, err := utils.ParseGraphqlID[uint32](args.CourseSectionId)
	if err != nil {
		return nil, err
	}
	_, err = r.Repos.CourseSectionRepo().FindByIDAndCourseID(
		r.Ctx,
		courseSectionId,
		courseId,
	)
	if err != nil {
		return nil, err
	}

	itemId, err := utils.ParseGraphqlID[uint32](args.Id)
	if err != nil {
		return nil, err
	}

	itemRepo := r.Repos.CourseSectionItemRepo()
	item, err := itemRepo.FindById(r.Ctx, itemId)
	if err != nil {
		return nil, err
	}

	form := adminForms.NewCourseSectionItemMutateForm(
		r.Ctx,
		args.Input,
		itemRepo,
		item,
		courseId,
	)

	if err := form.Update(); err != nil {
		return nil, err
	}

	return &adminPayloads.CourseSectionItemMutatePayload{
		SectionItem: &adminPayloads.CourseSectionItemPayload{
			CourseSectionItemPayload: &globalPayloads.CourseSectionItemPayload{
				SectionItem: form.CourseSectionItem,
			},
		},
		Message: translator.Translate(nil, "infoMsg_updateSuccess"),
	}, nil
}
