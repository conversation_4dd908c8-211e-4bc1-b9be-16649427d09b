package adminResolvers

import (
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
	"github.com/rs/zerolog/log"
)

func (r *Resolver) DrillDelete(args struct {
	ID graphql.ID
}) (*globalPayloads.MessageInfoPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("AdminResolver.DrillDelete")

	drillId, err := utils.ParseGraphqlID[uint32](args.ID)
	if err != nil {
		return nil, err
	}

	drillRepo := r.Repos.DrillRepo()
	drill, err := drillRepo.FindByID(r.Ctx, drillId)
	if err != nil {
		return nil, err
	}

	userDrillCount, err := drillRepo.AssociationCount(r.Ctx, drill, "UserDrills")
	if err != nil {
		return nil, err
	}
	if userDrillCount > 0 {
		return nil, exceptions.NewUnprocessableContentError("Drill is in use", nil)
	}

	if err := drillRepo.DeleteById(r.Ctx, drillId); err != nil {
		return nil, err
	}

	return &globalPayloads.MessageInfoPayload{
		Message: translator.Translate(nil, "general_deleteSuccess"),
	}, nil
}
