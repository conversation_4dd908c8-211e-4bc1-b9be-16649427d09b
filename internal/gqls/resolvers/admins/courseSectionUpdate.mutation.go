package adminResolvers

import (
	adminCourseSectionForms "vibico-education-api/internal/forms/admins/courseSectionForms"
	adminInputs "vibico-education-api/internal/gqls/inputs/admins"
	adminPayloads "vibico-education-api/internal/gqls/payloads/admins"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
	"github.com/rs/zerolog/log"
)

func (r *Resolver) CourseSectionUpdate(args struct {
	ID       graphql.ID
	CourseID graphql.ID
	Input    adminInputs.CourseSectionMutateInput
}) (*adminPayloads.CourseCreateAndUpdate, error) {
	log.Info().Ctx(r.Ctx).Msg("AdminResolver.CourseSectionUpdate")

	courseID, err := utils.ParseGraphqlID[uint32](args.CourseID)
	if err != nil {
		return nil, err
	}

	courseSectionId, err := utils.ParseGraphqlID[uint32](args.ID)
	if err != nil {
		return nil, err
	}
	courseSection, err := r.Repos.CourseSectionRepo().FindByIDAndCourseID(r.Ctx, courseSectionId, courseID)
	if err != nil {
		return nil, err
	}

	form := adminCourseSectionForms.NewCourseSectionUpdateForm(
		r.Ctx,
		&args.Input,
		r.Repos.CourseSectionRepo(),
		courseSection,
	)

	if err := form.Save(); err != nil {
		return nil, err
	}

	return &adminPayloads.CourseCreateAndUpdate{
		CourseSection: &adminPayloads.CourseSectionPayload{
			CourseSectionPayload: &globalPayloads.CourseSectionPayload{
				CourseSection: form.CourseSection,
			},
		},
		Message: translator.Translate(nil, "general_updateSuccess"),
	}, nil
}
