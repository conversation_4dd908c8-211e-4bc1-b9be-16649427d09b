package adminResolvers

import (
	adminForms "vibico-education-api/internal/forms/admins"
	adminInputs "vibico-education-api/internal/gqls/inputs/admins"
	adminPayloads "vibico-education-api/internal/gqls/payloads/admins"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	"vibico-education-api/internal/models"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"

	"github.com/rs/zerolog/log"
)

func (r *Resolver) CourseSectionItemCreate(
	args *adminInputs.CourseSectionItemCreateInput,
) (*adminPayloads.CourseSectionItemMutatePayload, error) {
	log.Info().Ctx(r.Ctx).Msg("AdminResolver.CourseSectionItemCreate")

	courseID, err := utils.ParseGraphqlID[uint32](args.CourseId)
	if err != nil {
		return nil, err
	}
	courseSectionId, err := utils.ParseGraphqlID[uint32](args.CourseSectionId)
	if err != nil {
		return nil, err
	}
	_, err = r.Repos.CourseSectionRepo().FindByIDAndCourseID(
		r.Ctx,
		courseSectionId,
		courseID,
	)
	if err != nil {
		return nil, err
	}

	form := adminForms.NewCourseSectionItemMutateForm(
		r.Ctx,
		args.Input,
		r.Repos.CourseSectionItemRepo(),
		&models.CourseSectionItem{
			CourseSectionId: courseSectionId,
		},
		courseID,
	)

	if err := form.Create(); err != nil {
		return nil, err
	}

	return &adminPayloads.CourseSectionItemMutatePayload{
		SectionItem: &adminPayloads.CourseSectionItemPayload{
			CourseSectionItemPayload: &globalPayloads.CourseSectionItemPayload{
				SectionItem: form.CourseSectionItem,
			},
		},
		Message: translator.Translate(nil, "infoMsg_createSuccess"),
	}, nil
}
