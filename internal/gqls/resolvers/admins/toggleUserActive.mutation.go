package adminResolvers

import (
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
	"github.com/rs/zerolog/log"
)

func (r *Resolver) ToggleUserActive(args struct {
	ID graphql.ID
}) (*globalPayloads.MessageInfoPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("AdminResolver.ToggleUserActive")

	userID, err := utils.ParseGraphqlID[uint32](args.ID)
	if err != nil {
		return nil, err
	}

	userRepo := r.Repos.UserRepo()
	user, err := userRepo.FindById(r.Ctx, userID)
	if err != nil {
		return nil, err
	}

	user.Active = !user.Active
	if err := userRepo.Update(r.Ctx, user, "active"); err != nil {
		return nil, err
	}

	go func() {
		teacher, err := r.Repos.TeacherRepo().FindByAuthId(r.Ctx, user.AuthId)
		if err != nil {
			log.Error().Err(err).Msg("something error when FindByAuthId teacher")
			return
		}

		if !teacher.Active {
			// TODO: Call gRPC to active/deActive auth account
			// _, _ = grpc_clients.AuthClient().UpdateUserStatus(
			// 	grpc_clients.NewCtx(r.Ctx),
			// 	&pb.UpdateUserStatusRequest{
			// 		UserId: teacher.AuthId,
			// 		Active: user.Active,
			// 	},
			// )
		}
	}()

	return &globalPayloads.MessageInfoPayload{
		Message: translator.Translate(nil, "general_success"),
	}, nil
}
