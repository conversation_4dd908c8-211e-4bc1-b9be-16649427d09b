package adminResolvers

import (
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
	"github.com/rs/zerolog/log"
)

func (r *Resolver) CourseSectionItemDestroy(
	args struct{ Id graphql.ID },
) (*globalPayloads.MessageInfoPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("AdminResolver.CourseSectionItemDestroy")

	id, err := utils.ParseGraphqlID[uint32](args.Id)
	if err != nil {
		return nil, err
	}

	if err := r.Repos.CourseSectionItemRepo().DeleteById(r.Ctx, id); err != nil {
		return nil, err
	}

	return &globalPayloads.MessageInfoPayload{
		Message: translator.Translate(nil, "general_deleteSuccess"),
	}, nil
}
