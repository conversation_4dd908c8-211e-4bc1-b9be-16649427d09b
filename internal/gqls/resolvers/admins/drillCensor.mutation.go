package adminResolvers

import (
	"errors"
	"vibico-education-api/internal/enums"
	"vibico-education-api/internal/forms/admins/censorHistoryForms"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	"vibico-education-api/internal/models"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
	"github.com/rs/zerolog/log"
)

func (r *Resolver) DrillCensor(args struct {
	ID       graphql.ID
	Censor   string
	Feedback *string
}) (*globalPayloads.MessageInfoPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("AdminResolver.DrillCensor")

	drillID, err := utils.ParseGraphqlID[uint32](args.ID)
	if err != nil {
		return nil, err
	}
	drillCensor, err := enums.ParseDrillCensor(args.Censor)
	if err != nil {
		return nil, errors.New(translator.Translate(nil, "errValidationMsg_censor_invalid"))
	}
	if drillCensor != enums.DrillCensorApproved && drillCensor != enums.DrillCensorRejected {
		return nil, errors.New(translator.Translate(nil, "errValidationMsg_invalid_censoring_condition"))
	}

	drillRepo := r.Repos.DrillRepo()
	drill, err := drillRepo.FindByID(r.Ctx, drillID)
	if err != nil {
		return nil, err
	}

	if drill.IsDraft() && !drill.IsMaster {
		return nil, errors.New(translator.Translate(nil, "errValidationMsg_invalid_censoring_condition"))
	}

	if drillCensor == enums.DrillCensorRejected {
		form := censorHistoryForms.NewDrillCensorForm(
			r.Ctx,
			args.Feedback,
			r.Repos.CensorHistoryRepo(),
			&models.CensorHistory{
				ParentID:   drillID,
				ParentType: enums.CensorHistoryParentDrill.String(),
				CreatedBy:  r.currentAdmin.ID,
			},
			drill,
		)
		if err := form.Save(); err != nil {
			return nil, err
		}
	} else {
		if err := drillRepo.ApprovedDrill(r.Ctx, drill); err != nil {
			return nil, err
		}
	}

	return &globalPayloads.MessageInfoPayload{
		Message: translator.Translate(nil, "general_success"),
	}, nil
}
