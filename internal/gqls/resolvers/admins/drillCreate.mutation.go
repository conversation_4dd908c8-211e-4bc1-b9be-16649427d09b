package adminResolvers

import (
	"vibico-education-api/internal/enums"
	drillForms "vibico-education-api/internal/forms/admins/drill"
	adminInputs "vibico-education-api/internal/gqls/inputs/admins"
	adminPayloads "vibico-education-api/internal/gqls/payloads/admins"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	"vibico-education-api/internal/models"
	translator "vibico-education-api/pkg/translators"

	"github.com/rs/zerolog/log"
)

func (r *Resolver) DrillCreate(args struct {
	Input adminInputs.DrillModifyInput
}) (*adminPayloads.DrillModifyPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("AdminResolver.DrillCreate")

	drill := models.Drill{
		OwnerID:   r.currentAdmin.ID,
		OwnerType: "Admin",
		Censor:    enums.DrillCensorApproved,
		IsMaster:  true,
	}
	form := drillForms.NewDrillCreateForm(r.Ctx, &drill, r.Repos.DrillRepo(), r.Repos.SkillRepo(), &args.Input)
	if err := form.Save(); err != nil {
		return nil, err
	}

	return &adminPayloads.DrillModifyPayload{
		Drill: &adminPayloads.DrillPayload{
			DrillPayload: &globalPayloads.DrillPayload{Drill: &drill},
		},
		Message: translator.Translate(nil, "infoMsg_createSuccess"),
	}, nil
}
