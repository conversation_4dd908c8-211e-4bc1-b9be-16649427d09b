package adminResolvers

import (
	adminPayloads "vibico-education-api/internal/gqls/payloads/admins"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	"vibico-education-api/internal/repository/repositories"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

func (r *Resolver) Drill(args struct {
	ID graphql.ID
}) (*adminPayloads.DrillPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("AdminResolver.Drill")

	drillId, err := utils.ParseGraphqlID[uint32](args.ID)
	if err != nil {
		return nil, err
	}

	drill, err := r.Repos.DrillRepo().FindByID(
		r.Ctx,
		drillId,
		repositories.CustomPreload{Key: "DrillSkills.Skill"},
		repositories.CustomPreload{Key: "Diagrams"},
		repositories.CustomPreload{Key: "Videos", Args: []any{func(db *gorm.DB) *gorm.DB {
			return db.Order("videos.id ASC")
		}}},
		repositories.CustomPreload{Key: "Videos.VideoPlatforms"},
		repositories.CustomPreload{Key: "CensorHistories", Args: []any{func(db *gorm.DB) *gorm.DB {
			return db.Order("censor_histories.id ASC")
		}}},
		repositories.CustomPreload{Key: "CensorHistories", Args: []any{func(db *gorm.DB) *gorm.DB {
			return db.Order("censor_histories.id DESC")
		}}},
		repositories.CustomPreload{Key: "CensorHistories.Creator"},
	)

	if err != nil {
		return nil, err
	}

	return &adminPayloads.DrillPayload{
		DrillPayload: &globalPayloads.DrillPayload{
			Drill: drill,
		},
	}, nil
}
