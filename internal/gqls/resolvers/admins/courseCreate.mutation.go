package adminResolvers

import (
	adminCourseForms "vibico-education-api/internal/forms/admins/courseForms"
	adminInputs "vibico-education-api/internal/gqls/inputs/admins"
	adminPayloads "vibico-education-api/internal/gqls/payloads/admins"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	translator "vibico-education-api/pkg/translators"

	"github.com/rs/zerolog/log"
)

func (r *Resolver) CourseCreate(
	args struct {
		Input adminInputs.CourseCreateInput
	}) (*adminPayloads.CourseMutatePayload, error) {
	log.Info().Ctx(r.Ctx).Msg("AdminResolver.CourseCreate")

	form := adminCourseForms.NewCreateForm(
		r.Ctx,
		&args.Input,
		r.Repos.CourseRepo(),
		r.Repos.TeacherRepo(),
	)

	if err := form.Save(); err != nil {
		return nil, err
	}

	return &adminPayloads.CourseMutatePayload{
		Course:  &adminPayloads.CoursePayload{CoursePayload: globalPayloads.CoursePayload{Course: form.Course}},
		Message: translator.Translate(nil, "infoMsg_createSuccess"),
	}, nil
}
