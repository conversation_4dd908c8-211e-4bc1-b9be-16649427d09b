package adminResolvers

import (
	"vibico-education-api/internal/gqls/resolvers"
	"vibico-education-api/internal/middlewares"
	"vibico-education-api/internal/models"
	"vibico-education-api/internal/repository"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
)

type Resolver struct {
	resolvers.Resolver

	currentAdmin *models.Admin
}

func NewResolver(repos repository.IRepositories, ginCtx *gin.Context) *Resolver {
	return &Resolver{
		Resolver: *resolvers.NewResolver(repos, ginCtx),
	}
}

func (r *Resolver) Auth() error {
	log.Debug().Ctx(r.Ctx).Msg("userResolvers.Auth")

	admin, err := middlewares.AdminAuth(r.Ctx, r.Repos)
	if err != nil {
		return err
	}

	r.currentAdmin = admin

	return nil
}

func (r *Resolver) CurrentAdmin() *models.Admin {
	return r.currentAdmin
}

func (r *Resolver) GetPublicOperations() map[string]struct{} {
	publicOperations := r.Resolver.GetPublicOperations()
	publicOperations["signIn"] = struct{}{}
	publicOperations["test"] = struct{}{}

	return publicOperations
}
