package adminResolvers

import (
	adminPayloads "vibico-education-api/internal/gqls/payloads/admins"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	"vibico-education-api/internal/gqls/resolvers"

	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

func (r *Resolver) CourseSectionItem(
	args struct {
		Id graphql.ID
	},
) (*adminPayloads.CourseSectionItemPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("AdminResolver.CourseSectionItem")

	id, err := utils.ParseGraphqlID[uint32](args.Id)
	if err != nil {
		return nil, err
	}

	sectionItem, err := r.Repos.CourseSectionItemRepo().
		FindById(r.Ctx, id,
			r.DetectAssociations("courseSectionItem", []resolvers.RequestAssociation{
				{RequestKey: "drills"},
				{RequestKey: "drills.diagrams"},
				{RequestKey: "drills.skills", Key: "Drills.DrillSkills.Skill"},
				{RequestKey: "videos"},
				{RequestKey: "videos.videoPlatforms"},
			})...,
		)

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, exceptions.NewRecordNotFoundError()
		}
		return nil, err
	}
	return &adminPayloads.CourseSectionItemPayload{
		CourseSectionItemPayload: &globalPayloads.CourseSectionItemPayload{
			SectionItem: sectionItem,
		},
	}, nil
}
