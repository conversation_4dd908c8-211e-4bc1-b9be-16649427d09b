package adminResolvers

import (
	adminInputs "vibico-education-api/internal/gqls/inputs/admins"
	adminPayloads "vibico-education-api/internal/gqls/payloads/admins"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	"vibico-education-api/internal/repository/repositories"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

func (r *Resolver) Drills(args adminInputs.DrillInput) (*adminPayloads.DrillsCollectionPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("AdminResolver.Drills")

	paginationData, err := r.Repos.DrillRepo().AdminList(
		r.Ctx,
		args,
		repositories.CustomPreload{Key: "DrillSkills.Skill"},
		repositories.CustomPreload{
			Key: "Diagrams",
			Args: []any{
				func(db *gorm.DB) *gorm.DB {
					return db.Order("diagrams.position ASC")
				},
			}},
	)

	if err != nil {
		return nil, err
	}

	return &adminPayloads.DrillsCollectionPayload{
		Collection: adminPayloads.DrillsSliceToTypes(paginationData.Collection),
		Metadata: &globalPayloads.MetadataPayload{
			Metadata: paginationData.Metadata,
		},
	}, nil
}
