package adminResolvers

import (
	"errors"
	"vibico-education-api/internal/enums"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
	"github.com/rs/zerolog/log"
)

func (r *Resolver) DrillPublish(args struct {
	ID     graphql.ID
	Status string
}) (*globalPayloads.MessageInfoPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("AdminResolver.DrillPublish")

	drillId, err := utils.ParseGraphqlID[uint32](args.ID)
	if err != nil {
		return nil, err
	}
	statusEnum, err := enums.ParseDrillStatus(args.Status)
	if err != nil {
		return nil, errors.New(translator.Translate(nil, "errValidationMsg_status_invalid"))
	}

	drillRepo := r.Repos.DrillRepo()
	course, err := drillRepo.FindByID(r.Ctx, drillId)
	if err != nil {
		return nil, err
	}

	if course.Status == statusEnum {
		return nil, errors.New(translator.Translate(nil, "errValidationMsg_status_invalid"))
	}

	course.Status = statusEnum
	if err := drillRepo.Update(r.Ctx, course, "status"); err != nil {
		return nil, err
	}

	return &globalPayloads.MessageInfoPayload{
		Message: translator.Translate(nil, "general_updateSuccess"),
	}, nil
}
