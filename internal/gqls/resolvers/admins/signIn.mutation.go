package adminResolvers

import (
	"vibico-education-api/internal/forms/public/authForms"
	globalInputs "vibico-education-api/internal/gqls/inputs/global"
	adminPayloads "vibico-education-api/internal/gqls/payloads/admins"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	translator "vibico-education-api/pkg/translators"
	"vibico-education-api/setup/grpc_clients"

	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
	pb "github.com/BehemothLtd/vibico-auth/proto/auth"
	"github.com/rs/zerolog/log"
)

func (r *Resolver) SignIn(
	args struct {
		Input globalInputs.SignInInput
	}) (*adminPayloads.SignInPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("adminResolvers.SignIn")

	form := authForms.NewSignInForm(r.Ctx, &args.Input)
	if err := form.Validate(); err != nil {
		return nil, err
	}

	exception := exceptions.NewUnprocessableContentError(translator.Translate(nil, "errDbMsg_wrongPassword"), nil)

	grpcResponse, err := grpc_clients.AuthClient().UserSignIn(
		grpc_clients.NewCtx(r.Ctx),
		&pb.UserSignInRequest{
			IdentityPoolId: grpc_clients.PoolId(),
			IdentifierType: "username",
			Identifier:     args.Input.Identifier,
			Password:       args.Input.Password,
		},
	)
	if err != nil {
		exception.AddError("auth_rpc", []any{err.Error()})
		return nil, exception
	}

	admin, err := r.Repos.AdminRepo().FindByAuthId(r.Ctx, grpcResponse.User.Id)
	if err != nil {
		log.Error().Ctx(r.Ctx).Err(err).Msg("Failed to find admin by auth ID")
		return nil, exception
	}

	return &adminPayloads.SignInPayload{
		Admin: &globalPayloads.AdminPayload{Admin: admin},
		TokenPayload: globalPayloads.TokenPayload{
			AccessToken:  grpcResponse.AccessToken,
			RefreshToken: grpcResponse.RefreshToken,
			Message:      translator.Translate(nil, "infoMsg_signInSuccess"),
		},
	}, nil
}
