package adminResolvers

import (
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
	"github.com/rs/zerolog/log"
)

func (r *Resolver) PermitTeacherInviteStudent(args struct {
	ID graphql.ID
}) (*globalPayloads.MessageInfoPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("AdminResolver.PermitTeacherInviteStudent")

	teacherID, err := utils.ParseGraphqlID[uint32](args.ID)
	if err != nil {
		return nil, err
	}

	teacherRepo := r.Repos.TeacherRepo()
	teacher, err := teacherRepo.FindValidTeacherById(
		r.Ctx,
		teacherID,
	)
	if err != nil {
		return nil, err
	}

	teacher.CanInviteStudents = !teacher.CanInviteStudents
	if err := teacherRepo.Update(r.Ctx, teacher, "can_invite_students"); err != nil {
		return nil, err
	}

	return &globalPayloads.MessageInfoPayload{
		Message: translator.Translate(nil, "general_success"),
	}, nil
}
