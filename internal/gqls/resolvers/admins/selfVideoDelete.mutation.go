package adminResolvers

import (
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	"vibico-education-api/internal/repository/repositories"
	globalServices "vibico-education-api/internal/services/globals"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
	"github.com/rs/zerolog/log"
)

func (r *Resolver) SelfVideoDelete(args struct {
	VideoID graphql.ID
}) (*globalPayloads.MessageInfoPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("AdminResolver.SelfVideoDelete")

	videoID, err := utils.ParseGraphqlID[uint32](args.VideoID)
	if err != nil {
		return nil, err
	}

	video, err := r.Repos.VideoRepo().FindSelfVideoByID(r.Ctx, videoID, r.currentAdmin.ID, "Admin",
		repositories.CustomPreload{
			Key: "VideoUpload",
		},
		repositories.CustomPreload{
			Key: "VideoPlatforms",
		},
	)
	if err != nil {
		return nil, err
	}

	service := globalServices.NewVideoAfterDestroyService(r.Ctx, video)

	if err = r.Repos.VideoRepo().Delete(r.Ctx, video); err != nil {
		return nil, err
	}
	service.Execute()

	return &globalPayloads.MessageInfoPayload{
		Message: translator.Translate(nil, "general_deleteSuccess"),
	}, nil
}
