package adminResolvers

import (
	"errors"
	"vibico-education-api/internal/enums"
	"vibico-education-api/internal/forms/admins/censorHistoryForms"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	"vibico-education-api/internal/models"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
	"github.com/rs/zerolog/log"
)

func (r *Resolver) CourseCensor(args struct {
	ID       graphql.ID
	Censor   string
	Feedback *string
}) (*globalPayloads.MessageInfoPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("AdminResolver.CourseCensor")

	courseID, err := utils.ParseGraphqlID[uint32](args.ID)
	if err != nil {
		return nil, err
	}
	censorEnum, err := enums.ParseCourseStatus(args.Censor)
	if err != nil {
		return nil, errors.New(translator.Translate(nil, "errValidationMsg_censor_invalid"))
	}
	if censorEnum != enums.CourseStatusApproved && censorEnum != enums.CourseStatusRejected {
		return nil, errors.New(translator.Translate(nil, "errValidationMsg_invalid_censoring_condition"))
	}

	courseRepo := r.Repos.CourseRepo()
	course, err := courseRepo.FindByID(r.Ctx, courseID)
	if err != nil {
		return nil, err
	}

	if course.IsDraft() {
		return nil, errors.New(translator.Translate(nil, "errValidationMsg_invalid_censoring_condition"))
	}

	if censorEnum == enums.CourseStatusRejected {
		form := censorHistoryForms.NewCourseCensorForm(
			r.Ctx,
			args.Feedback,
			r.Repos.CensorHistoryRepo(),
			&models.CensorHistory{
				ParentID:   courseID,
				ParentType: enums.CensorHistoryParentCourse.String(),
				CreatedBy:  r.currentAdmin.ID,
			},
			course,
		)
		if err := form.Save(); err != nil {
			return nil, err
		}
	} else {
		if err := courseRepo.ApprovedCourse(r.Ctx, course); err != nil {
			return nil, err
		}
	}

	return &globalPayloads.MessageInfoPayload{
		Message: translator.Translate(nil, "general_updateSuccess"),
	}, nil
}
