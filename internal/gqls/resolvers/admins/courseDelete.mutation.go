package adminResolvers

import (
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
	"github.com/rs/zerolog/log"
)

func (r *Resolver) CourseDelete(args struct{ ID graphql.ID }) (*globalPayloads.MessageInfoPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("AdminResolver.CourseDelete")

	courseID, err := utils.ParseGraphqlID[uint32](args.ID)
	if err != nil {
		return nil, err
	}

	courseRepo := r.Repos.CourseRepo()
	course, err := courseRepo.FindByID(r.Ctx, courseID)
	if err != nil {
		return nil, err
	}

	if course.IsApproved() {
		return nil, exceptions.NewUnprocessableContentError("course is being published", nil)
	}

	courseUsersCount, err := courseRepo.AssociationCount(r.Ctx, course, "CourseUsers")
	if err != nil {
		return nil, err
	}

	if courseUsersCount > 0 {
		return nil, exceptions.NewUnprocessableContentError("course in use", nil)
	}

	if err := courseRepo.Delete(r.Ctx, course); err != nil {
		return nil, err
	}

	return &globalPayloads.MessageInfoPayload{
		Message: translator.Translate(nil, "general_deleteSuccess"),
	}, nil
}
