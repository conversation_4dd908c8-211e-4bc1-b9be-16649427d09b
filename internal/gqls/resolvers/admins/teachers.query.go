package adminResolvers

import (
	adminInputs "vibico-education-api/internal/gqls/inputs/admins"
	adminPayloads "vibico-education-api/internal/gqls/payloads/admins"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	"vibico-education-api/internal/models"

	"github.com/rs/zerolog/log"
)

func (r *Resolver) Teachers(args adminInputs.TeachersInput) (*adminPayloads.TeachersCollectionPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("AdminResolver.Teachers")

	paginationData, err := r.Repos.TeacherRepo().AdminTeacherList(
		r.Ctx,
		args,
	)
	if err != nil {
		return nil, err
	}

	teachers, ok := paginationData.Collection.([]*models.Teacher)
	resolvers := make([]*adminPayloads.TeacherPayload, len(teachers))
	if ok {
		for i, e := range teachers {
			resolvers[i] = &adminPayloads.TeacherPayload{TeacherPayload: &globalPayloads.TeacherPayload{Teacher: e}}
		}
	}

	return &adminPayloads.TeachersCollectionPayload{
		Collection: &resolvers,
		Metadata: &globalPayloads.MetadataPayload{
			Metadata: paginationData.Metadata,
		},
	}, nil
}
