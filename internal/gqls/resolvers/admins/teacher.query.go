package adminResolvers

import (
	adminPayloads "vibico-education-api/internal/gqls/payloads/admins"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
	"github.com/rs/zerolog/log"
)

func (r *Resolver) Teacher(args struct {
	ID graphql.ID
}) (*adminPayloads.TeacherPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("AdminResolver.Teacher")

	teacherID, err := utils.ParseGraphqlID[uint32](args.ID)
	if err != nil {
		return nil, err
	}

	teacher, err := r.Repos.TeacherRepo().FindById(
		r.Ctx,
		teacherID,
	)
	if err != nil {
		return nil, err
	}

	return &adminPayloads.TeacherPayload{
		TeacherPayload: &globalPayloads.TeacherPayload{Teacher: teacher},
	}, nil
}
