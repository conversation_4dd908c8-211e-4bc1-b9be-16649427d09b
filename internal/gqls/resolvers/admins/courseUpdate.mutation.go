package adminResolvers

import (
	adminCourseForms "vibico-education-api/internal/forms/admins/courseForms"
	adminInputs "vibico-education-api/internal/gqls/inputs/admins"
	adminPayloads "vibico-education-api/internal/gqls/payloads/admins"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/rs/zerolog/log"
)

func (r *Resolver) CourseUpdate(args adminInputs.CourseUpdateInput) (*adminPayloads.CourseMutatePayload, error) {
	log.Info().Ctx(r.Ctx).Msg("AdminResolver.CourseUpdate")

	courseID, err := utils.ParseGraphqlID[uint32](args.ID)
	if err != nil {
		return nil, err
	}

	courseRepo := r.Repos.CourseRepo()
	course, err := courseRepo.FindByID(r.Ctx, courseID)
	if err != nil {
		return nil, err
	}

	form := adminCourseForms.NewUpdateForm(
		r.Ctx,
		&args.Input,
		courseRepo,
		r.Repos.TeacherRepo(),
		course,
	)

	if err := form.Save(); err != nil {
		return nil, err
	}

	return &adminPayloads.CourseMutatePayload{
		Course:  &adminPayloads.CoursePayload{CoursePayload: globalPayloads.CoursePayload{Course: form.Course}},
		Message: translator.Translate(nil, "infoMsg_updateSuccess"),
	}, nil
}
