package adminResolvers

import (
	adminInputs "vibico-education-api/internal/gqls/inputs/admins"
	adminPayloads "vibico-education-api/internal/gqls/payloads/admins"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	"vibico-education-api/internal/gqls/resolvers"
	"vibico-education-api/internal/models"
	"vibico-education-api/internal/repository/repositories"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

func (r *Resolver) Courses(args adminInputs.CoursesInput) (*adminPayloads.CoursesCollectionPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("AdminResolver.Courses")

	paginationData, err := r.Repos.CourseRepo().AdminList(
		r.Ctx,
		args,
		r.DetectAssociations("courses", []resolvers.RequestAssociation{
			{
				RequestKey: "collection.teacher",
				Key:        "Teacher",
			},
			{
				RequestKey: "collection.courseSections",
				Key:        "CourseSections",
				Args: []any{
					func(db *gorm.DB) *gorm.DB {
						return db.Order("course_sections.position ASC")
					},
				},
			},
			{
				RequestKey: "courseSections.courseSectionItems",
				Args: []any{
					func(db *gorm.DB) *gorm.DB {
						return db.Order(repositories.CSIOrderPositionAscSQL)
					},
				},
			},
		})...,
	)
	if err != nil {
		return nil, err
	}

	courses, ok := paginationData.Collection.([]*models.Course)
	resolvers := make([]*adminPayloads.CoursePayload, len(courses))
	if ok {
		for i, e := range courses {
			resolvers[i] = &adminPayloads.CoursePayload{CoursePayload: globalPayloads.CoursePayload{Course: e}}
		}
	}

	return &adminPayloads.CoursesCollectionPayload{
		Collection: &resolvers,
		Metadata: &globalPayloads.MetadataPayload{
			Metadata: paginationData.Metadata,
		},
	}, nil
}
