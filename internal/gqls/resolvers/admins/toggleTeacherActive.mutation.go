package adminResolvers

import (
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
	"github.com/rs/zerolog/log"
)

func (r *Resolver) ToggleTeacherActive(args struct {
	ID graphql.ID
}) (*globalPayloads.MessageInfoPayload, error) {
	log.Info().Ctx(r.Ctx).Msg("AdminResolver.ToggleTeacherActive")

	teacherID, err := utils.ParseGraphqlID[uint32](args.ID)
	if err != nil {
		return nil, err
	}

	teacherRepo := r.Repos.TeacherRepo()
	teacher, err := teacherRepo.FindById(r.Ctx, teacherID)
	if err != nil {
		return nil, err
	}

	teacher.Active = !teacher.Active
	if err := teacherRepo.Update(r.Ctx, teacher, "active"); err != nil {
		return nil, err
	}

	go func() {
		user, err := r.Repos.UserRepo().FindByAuthId(r.Ctx, teacher.AuthId)
		if err != nil {
			log.Error().Err(err).Msg("something error when FindByAuthId user")
			return
		}

		if !user.Active {
			// TODO: Call gRPC to active/deActive auth account
			// _, _ = grpc_clients.AuthClient().UpdateUserStatus(
			// 	grpc_clients.NewCtx(r.Ctx),
			// 	&pb.UpdateUserStatusRequest{
			// 		UserId: teacher.AuthId,
			// 		Active: teacher.Active,
			// 	},
			// )
		}
	}()

	return &globalPayloads.MessageInfoPayload{
		Message: translator.Translate(nil, "general_success"),
	}, nil
}
