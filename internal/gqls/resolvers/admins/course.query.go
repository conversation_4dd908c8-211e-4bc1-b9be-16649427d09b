package adminResolvers

import (
	adminPayloads "vibico-education-api/internal/gqls/payloads/admins"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	"vibico-education-api/internal/gqls/resolvers"
	"vibico-education-api/internal/repository/repositories"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

func (r *Resolver) Course(args struct {
	ID graphql.ID
}) (*adminPayloads.CoursePayload, error) {
	log.Info().Ctx(r.Ctx).Msg("AdminResolver.Course")

	courseID, err := utils.ParseGraphqlID[uint32](args.ID)
	if err != nil {
		return nil, err
	}

	course, err := r.Repos.CourseRepo().FindByID(
		r.Ctx,
		courseID,
		r.DetectAssociations("course", []resolvers.RequestAssociation{
			{RequestKey: "teacher"},
			{RequestKey: "courseSections", Args: []any{
				func(db *gorm.DB) *gorm.DB {
					return db.Order("course_sections.position ASC")
				},
			}},
			{RequestKey: "courseSections.courseSectionItems", Args: []any{
				func(db *gorm.DB) *gorm.DB {
					return db.Order(repositories.CSIOrderPositionAscSQL)
				},
			}},
			{RequestKey: "courseCensorHistories", Args: []any{
				func(db *gorm.DB) *gorm.DB {
					return db.Order("censor_histories.id DESC")
				},
			}},
			{RequestKey: "courseCensorHistories.creator"},
		})...,
	)
	if err != nil {
		return nil, err
	}

	return &adminPayloads.CoursePayload{CoursePayload: globalPayloads.CoursePayload{Course: course}}, nil
}
