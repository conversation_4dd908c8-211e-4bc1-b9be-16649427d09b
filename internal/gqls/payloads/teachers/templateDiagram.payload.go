package teacherPayloads

import globalPayloads "vibico-education-api/internal/gqls/payloads/global"

type TemplateDiagramPayload struct {
	*globalPayloads.TemplateDiagramPayload
}

func (d *TemplateDiagramPayload) Diagrams() *[]*DiagramPayload {
	if d.TemplateDiagram.Diagrams == nil || len(*d.TemplateDiagram.Diagrams) == 0 {
		return nil
	}

	diagrams := make([]*DiagramPayload, len(*d.TemplateDiagram.Diagrams))

	for i, dia := range *d.TemplateDiagram.Diagrams {
		diagrams[i] = &DiagramPayload{
			&globalPayloads.DiagramPayload{
				Diagram: dia,
			},
		}
	}

	return &diagrams
}
