package teacherPayloads

import globalPayloads "vibico-education-api/internal/gqls/payloads/global"

type CourseUserPayload struct {
	*globalPayloads.CourseUserPayload
}

type CourseUserCollectionPayload struct {
	Collection *[]*CourseUserPayload
	Metadata   *globalPayloads.MetadataPayload
}

func (c *CourseUserPayload) User() *UserPayload {
	if c.CourseUser.User == nil {
		return nil
	}

	return &UserPayload{
		&globalPayloads.UserPayload{
			User: c.CourseUser.User,
		},
	}
}
