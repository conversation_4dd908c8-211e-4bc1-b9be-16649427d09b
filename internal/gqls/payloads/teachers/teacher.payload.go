package teacherPayloads

import (
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
)

type TeacherPayload struct {
	*globalPayloads.TeacherPayload
}

type TeachersCollectionPayload struct {
	Collection *[]*TeacherPayload
	Metadata   *globalPayloads.MetadataPayload
}

func (t *TeacherPayload) Courses() *[]*CoursePayload {
	if t.Teacher.Courses == nil || len(*t.Teacher.Courses) == 0 {
		return nil
	}
	courses := make([]*CoursePayload, len(*t.Teacher.Courses))

	for i, course := range *t.Teacher.Courses {
		courses[i] = &CoursePayload{
			CoursePayload: globalPayloads.CoursePayload{
				Course: course,
			},
		}
	}

	return &courses
}
