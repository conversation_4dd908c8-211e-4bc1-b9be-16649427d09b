package teacherPayloads

import (
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
)

type CourseCreatePayload struct {
	Course  *CoursePayload
	Message string
}

type CourseUpdatePayload struct {
	Course  *CoursePayload
	Message string
}

type CoursePayload struct {
	globalPayloads.CoursePayload
}

type CoursesCollectionPayload struct {
	Collection *[]*CoursePayload
	Metadata   *globalPayloads.MetadataPayload
}

func (c *CoursePayload) CourseSections() *[]*CourseSectionPayload {
	if c.Course.CourseSections == nil || len(*c.Course.CourseSections) == 0 {
		return nil
	}
	sections := make([]*CourseSectionPayload, len(*c.Course.CourseSections))

	for i, section := range *c.Course.CourseSections {
		sections[i] = &CourseSectionPayload{
			&globalPayloads.CourseSectionPayload{
				CourseSection: section,
			},
		}
	}

	return &sections
}

func (c *CoursePayload) Students() *[]*UserPayload {
	if c.Course.Users == nil || len(*c.Course.Users) == 0 {
		return nil
	}

	users := make([]*UserPayload, len(*c.Course.Users))
	for i, user := range *c.Course.Users {
		users[i] = &UserPayload{
			&globalPayloads.UserPayload{
				User: user,
			},
		}
	}

	return &users
}

func (c *CoursePayload) CourseUsers() *[]*CourseUserPayload {
	if len(c.Course.CourseUsers) == 0 {
		return nil
	}

	courseUsers := make([]*CourseUserPayload, len(c.Course.CourseUsers))
	for i, courseUser := range c.Course.CourseUsers {
		courseUsers[i] = &CourseUserPayload{
			&globalPayloads.CourseUserPayload{
				CourseUser: courseUser,
			},
		}
	}

	return &courseUsers
}

func (c *CoursePayload) CourseCensorHistories() *[]*CensorHistoryPayload {
	if c.Course.CourseCensorHistories == nil || len(*c.Course.CourseCensorHistories) == 0 {
		return nil
	}

	histories := make([]*CensorHistoryPayload, len(*c.Course.CourseCensorHistories))
	for i, history := range *c.Course.CourseCensorHistories {
		histories[i] = &CensorHistoryPayload{
			&globalPayloads.CensorHistoryPayload{
				CensorHistory: history,
			},
		}
	}

	return &histories
}
