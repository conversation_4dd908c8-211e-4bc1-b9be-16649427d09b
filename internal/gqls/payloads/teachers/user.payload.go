package teacherPayloads

import (
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	"vibico-education-api/internal/models"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/gql"
	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
)

type SectionItemUsersCollectionPayload struct {
	Collection *[]*SectionItemUserPayload
	Stats      *SectionItemUsersStatsPayload
	Metadata   *globalPayloads.MetadataPayload
}

type SectionItemUsersStatsPayload struct {
	*models.SectionItemUsersStats
}

func (s *SectionItemUsersStatsPayload) TotalEnrolledUsers() gql.Uint32 {
	return gql.Uint32(s.SectionItemUsersStats.TotalEnrolledUsers)
}

func (s *SectionItemUsersStatsPayload) TotalSubmittedUsers() gql.Uint32 {
	return gql.Uint32(s.SectionItemUsersStats.TotalSubmittedUsers)
}

func (s *SectionItemUsersStatsPayload) TotalViewedSectionUsers() gql.Uint32 {
	return gql.Uint32(s.SectionItemUsersStats.TotalViewedSectionUsers)
}

type UserPayload struct {
	*globalPayloads.UserPayload
}

type SectionItemUserPayload struct {
	*globalPayloads.UserPayload
}

func (u *SectionItemUserPayload) PracticeSubmissions() *[]*PracticeSubmissionPayload {
	ps := u.User.PracticeSubmissions
	submissions := make([]*PracticeSubmissionPayload, 0)

	if ps != nil && len(*ps) > 0 {
		for _, submission := range *ps {
			submissions = append(submissions, &PracticeSubmissionPayload{
				PracticeSubmissionPayload: &globalPayloads.PracticeSubmissionPayload{
					PracticeSubmission: submission,
				},
			})
		}
	}

	return &submissions
}

func (u *SectionItemUserPayload) LatestSubmissionTime() *graphql.Time {
	ps := u.User.PracticeSubmissions
	if ps == nil || len(*ps) == 0 {
		return nil
	}

	latestTime := (*ps)[0].CreatedAt

	return &graphql.Time{Time: latestTime}
}

func (u *SectionItemUserPayload) LatestSubmissionStatus() string {
	ps := u.User.PracticeSubmissions
	if ps == nil || len(*ps) == 0 {
		return "notSubmitted"
	}

	latestSubmission := (*ps)[0]

	return latestSubmission.Status.String()
}

func (u *SectionItemUserPayload) LatestSubmissionStatusI18n() string {
	ps := u.User.PracticeSubmissions
	status := "notSubmitted"

	if ps != nil && len(*ps) > 0 {
		latestSubmission := (*ps)[0]

		status = latestSubmission.Status.String()
	}

	statusKey := "PracticeSubmissionStatus" + utils.CamelToPascalCase(status)

	return translator.Translate(nil, statusKey)
}
