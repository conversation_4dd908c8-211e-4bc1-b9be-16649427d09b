package teacherPayloads

import (
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	"vibico-education-api/internal/models"
)

type TemplateDiagramCollection struct {
	Collection *[]*TemplateDiagramPayload
	Metadata   *globalPayloads.MetadataPayload
}

func TemplateDiagramsSliceToTypes(templateInterface interface{}) *[]*TemplateDiagramPayload {
	templates, ok := templateInterface.([]*models.TemplateDiagram)
	resolvers := make([]*TemplateDiagramPayload, len(templates))
	if ok {
		for i, e := range templates {
			resolvers[i] = &TemplateDiagramPayload{
				TemplateDiagramPayload: &globalPayloads.TemplateDiagramPayload{
					TemplateDiagram: e,
				},
			}
		}
	}

	return &resolvers
}
