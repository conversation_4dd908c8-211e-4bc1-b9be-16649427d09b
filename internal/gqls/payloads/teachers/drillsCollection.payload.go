package teacherPayloads

import (
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	"vibico-education-api/internal/models"
)

type DrillsCollectionPayload struct {
	Collection *[]*DrillPayload
	Metadata   *globalPayloads.MetadataPayload
}

func DrillsSliceToTypes(drillsInterface interface{}) *[]*DrillPayload {
	drills, ok := drillsInterface.([]*models.Drill)
	resolvers := make([]*DrillPayload, len(drills))
	if ok {
		for i, e := range drills {
			resolvers[i] = &DrillPayload{
				DrillPayload: &globalPayloads.DrillPayload{
					Drill: e,
				},
			}
		}
	}
	return &resolvers
}
