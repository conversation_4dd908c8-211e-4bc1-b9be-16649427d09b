package userPayloads

import (
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	"vibico-education-api/internal/models"
)

type CourseSectionPayload struct {
	*globalPayloads.CourseSectionPayload
	CurrentUser *models.User
}

func (c *CourseSectionPayload) CourseSectionItems() *[]*CourseSectionItemPayload {
	if c.CourseSection.CourseSectionItems == nil || len(*c.CourseSection.CourseSectionItems) == 0 {
		return nil
	}
	items := make([]*CourseSectionItemPayload, len(*c.CourseSection.CourseSectionItems))

	for i, item := range *c.CourseSection.CourseSectionItems {
		items[i] = &CourseSectionItemPayload{
			CourseSectionItemPayload: &globalPayloads.CourseSectionItemPayload{
				SectionItem: item,
			},
			CurrentUser: c.CurrentUser,
		}
	}

	return &items
}

func (c *CourseSectionPayload) IsCompleted() bool {
	return c.CourseSection.UserCourseSection != nil
}
