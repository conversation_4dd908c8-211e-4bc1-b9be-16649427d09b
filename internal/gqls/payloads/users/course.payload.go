package userPayloads

import (
	"vibico-education-api/internal/enums"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	"vibico-education-api/internal/models"

	"github.com/BehemothLtd/behemoth-pkg/golang/gql"
)

type CoursePayload struct {
	*globalPayloads.CoursePayload
	JoinedUser *models.CourseUser
}

type CoursesCollectionPayload struct {
	Collection *[]*CoursePayload
	Metadata   *globalPayloads.MetadataPayload
}

func (c *CoursePayload) Teacher() *TeacherPayload {
	if c.Course.Teacher == nil {
		return nil
	}

	return &TeacherPayload{
		TeacherPayload: &globalPayloads.TeacherPayload{
			Teacher: c.Course.Teacher,
		},
	}
}

func (c *CoursePayload) CourseSections() *[]*CourseSectionPayload {
	if c.Course.CourseSections == nil || len(*c.Course.CourseSections) == 0 {
		return nil
	}
	sections := make([]*CourseSectionPayload, len(*c.Course.CourseSections))

	for i, section := range *c.Course.CourseSections {
		sections[i] = &CourseSectionPayload{
			CourseSectionPayload: &globalPayloads.CourseSectionPayload{
				CourseSection: section,
			},
		}

		if c.JoinedUser != nil {
			sections[i].CurrentUser = c.JoinedUser.User
		}
	}

	return &sections
}

func (c *CoursePayload) ProcessPercent() gql.Float32 {
	if c.JoinedUser == nil ||
		c.Course.SectionItemCount == 0 ||
		c.JoinedUser.CourseUserMetadata == nil ||
		c.JoinedUser.CourseUserMetadata.CompletedSectionItemCount == nil ||
		*c.JoinedUser.CourseUserMetadata.CompletedSectionItemCount == 0 {
		return 0
	}

	completedSections := float64(*c.JoinedUser.CourseUserMetadata.CompletedSectionItemCount)
	totalSectionItems := float64(c.Course.SectionItemCount)
	return gql.Float32((completedSections / totalSectionItems) * 100)
}

func (c *CoursePayload) Joined() bool {
	if c.JoinedUser == nil || c.JoinedUser.ID == 0 {
		return false
	}

	return c.JoinedUser.Status != enums.CourseUserStatusInvited
}

func (c *CoursePayload) CourseUserMetadata() *models.CourseUserMetadata {
	if c.JoinedUser == nil || c.JoinedUser.CourseUserMetadata == nil {
		return nil
	}

	return c.JoinedUser.CourseUserMetadata
}

func (c *CoursePayload) CurrentUserJoining() *CourseUserPayload {
	if c.JoinedUser == nil {
		return nil
	}

	return &CourseUserPayload{
		CourseUserPayload: &globalPayloads.CourseUserPayload{
			CourseUser: c.JoinedUser,
		},
	}
}

func (c *CoursePayload) MyReview() *CommentPayload {
	if c.JoinedUser == nil || c.JoinedUser.ID == 0 {
		return nil
	}

	myReview := c.Course.GetUserComment(c.JoinedUser.UserID)
	if myReview == nil {
		return nil
	}

	myReview.AuthorUser = c.JoinedUser.User

	return &CommentPayload{
		CommentPayload: &globalPayloads.CommentPayload{
			Comment: myReview,
		},
	}
}
