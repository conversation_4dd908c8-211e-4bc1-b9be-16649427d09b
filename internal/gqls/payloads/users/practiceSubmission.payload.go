package userPayloads

import globalPayloads "vibico-education-api/internal/gqls/payloads/global"

type PracticeSubmissionMutatePayload struct {
	PracticeSubmission *PracticeSubmissionPayload
	Message            string
}

type PracticeSubmissionCreatePayload struct {
	PracticeSubmissionMutatePayload
}

type PracticeSubmissionUpdatePayload struct {
	PracticeSubmissionMutatePayload
}

type PracticeSubmissionPayload struct {
	*globalPayloads.PracticeSubmissionPayload
}

type PracticeSubmissionsCollectionPayload struct {
	Collection *[]*PracticeSubmissionPayload
	Metadata   *globalPayloads.MetadataPayload
}

func (ps *PracticeSubmissionPayload) Comments() *[]*CommentPayload {
	if ps.PracticeSubmission.Comments == nil || len(*ps.PracticeSubmission.Comments) == 0 {
		return nil
	}

	comments := make([]*CommentPayload, len(*ps.PracticeSubmission.Comments))

	for i, comment := range *ps.PracticeSubmission.Comments {
		comments[i] = &CommentPayload{CommentPayload: &globalPayloads.CommentPayload{
			Comment: comment,
		}}
	}

	return &comments
}
