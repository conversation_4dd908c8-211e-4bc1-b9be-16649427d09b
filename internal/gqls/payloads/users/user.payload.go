package userPayloads

import (
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
)

type UserPayload struct {
	*globalPayloads.UserPayload
}

func (u *UserPayload) PracticeSubmissions() *[]*PracticeSubmissionPayload {
	ps := u.User.PracticeSubmissions
	submissions := make([]*PracticeSubmissionPayload, 0)

	if ps != nil && len(*ps) > 0 {
		for _, submission := range *ps {
			submissions = append(submissions, &PracticeSubmissionPayload{
				PracticeSubmissionPayload: &globalPayloads.PracticeSubmissionPayload{PracticeSubmission: submission},
			})
		}
	}

	return &submissions
}

func (u *UserPayload) InvitedCourseUsers() *[]*CourseUserPayload {
	cus := u.User.CourseUsers
	courseUsers := make([]*CourseUserPayload, 0)

	if cus != nil && len(*cus) > 0 {
		for _, cu := range *cus {
			courseUsers = append(courseUsers, &CourseUserPayload{
				CourseUserPayload: &globalPayloads.CourseUserPayload{CourseUser: cu},
			})
		}
	}

	return &courseUsers
}
