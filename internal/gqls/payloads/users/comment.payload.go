package userPayloads

import globalPayloads "vibico-education-api/internal/gqls/payloads/global"

type CommentCreatePayload struct {
	Comment *CommentPayload
	Message string
}

type CommentPayload struct {
	*globalPayloads.CommentPayload
}

func (c *CommentPayload) AuthorUser() *UserPayload {
	if c.Comment.AuthorUser == nil {
		return nil
	}

	return &UserPayload{UserPayload: &globalPayloads.UserPayload{
		User: c.Comment.AuthorUser,
	}}
}

func (c *CommentPayload) AuthorTeacher() *TeacherPayload {
	if c.Comment.AuthorTeacher == nil {
		return nil
	}

	return &TeacherPayload{TeacherPayload: &globalPayloads.TeacherPayload{
		Teacher: c.Comment.AuthorTeacher,
	}}
}
