package userPayloads

import (
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	"vibico-education-api/internal/models"
)

type CourseSectionItemPayload struct {
	*globalPayloads.CourseSectionItemPayload
	CurrentUser *models.User
}

func (c *CourseSectionItemPayload) CourseSection() *CourseSectionPayload {
	if c.SectionItem.CourseSection == nil {
		return nil
	}

	return &CourseSectionPayload{
		CourseSectionPayload: &globalPayloads.CourseSectionPayload{
			CourseSection: c.SectionItem.CourseSection,
		},
	}
}

// TODO: Handle drill data without IsVisible
func (c *CourseSectionItemPayload) Drills() *[]*DrillPayload {
	if c.SectionItem.Drills == nil || len(*c.SectionItem.Drills) == 0 {
		return nil
	}

	drills := make([]*DrillPayload, len(*c.SectionItem.Drills))

	for i, drill := range *c.SectionItem.Drills {
		drills[i] = &DrillPayload{
			DrillPayload: &globalPayloads.DrillPayload{
				Drill: drill,
			},
			IsVisible: true,
		}
	}

	return &drills
}

func (c *CourseSectionItemPayload) IsCompleted() bool {
	if c.CurrentUser == nil {
		return false
	}

	if c.SectionItem.UserCourseSectionItems == nil || len(*c.SectionItem.UserCourseSectionItems) == 0 {
		return false
	}

	currentUserSectionItem := c.SectionItem.GetUserSectionItemByID(c.CurrentUser.ID)

	if currentUserSectionItem == nil {
		return false
	}

	return currentUserSectionItem.IsCompleted()
}

func (c *CourseSectionItemPayload) CurrentUserSectionItem() *globalPayloads.UserCourseSectionItemPayload {
	if c.CurrentUser == nil {
		return nil
	}

	if c.SectionItem.UserCourseSectionItems == nil || len(*c.SectionItem.UserCourseSectionItems) == 0 {
		return nil
	}

	return &globalPayloads.UserCourseSectionItemPayload{
		UserCourseSectionItem: c.SectionItem.GetUserSectionItemByID(c.CurrentUser.ID),
	}
}

func (c *CourseSectionItemPayload) Videos() *[]*globalPayloads.VideoPayload {
	if c.SectionItem.Videos == nil {
		return nil
	}
	videos := make([]*globalPayloads.VideoPayload, len(*c.SectionItem.Videos))

	for i, video := range *c.SectionItem.Videos {
		videos[i] = &globalPayloads.VideoPayload{
			Video: video,
		}
	}

	return &videos
}
