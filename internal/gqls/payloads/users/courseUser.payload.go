package userPayloads

import globalPayloads "vibico-education-api/internal/gqls/payloads/global"

type CourseUserPayload struct {
	*globalPayloads.CourseUserPayload
}

func (c *CourseUserPayload) User() *UserPayload {
	if c.CourseUser.User == nil {
		return nil
	}

	return &UserPayload{
		&globalPayloads.UserPayload{
			User: c.CourseUser.User,
		},
	}
}

func (c *CourseUserPayload) Course() *CoursePayload {
	if c.CourseUser.Course == nil {
		return nil
	}

	return &CoursePayload{
		CoursePayload: &globalPayloads.CoursePayload{
			Course: c.CourseUser.Course,
		},
		JoinedUser: nil,
	}
}
