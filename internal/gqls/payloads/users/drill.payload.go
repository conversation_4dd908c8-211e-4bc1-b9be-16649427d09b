package userPayloads

import (
	"encoding/json"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	"vibico-education-api/internal/models"
	gqlScalar "vibico-education-api/pkg/gql"
)

type DrillPayload struct {
	*globalPayloads.DrillPayload
	IsVisible bool
}

type DrillsCollectionPayload struct {
	Collection *[]*DrillPayload
	Metadata   *globalPayloads.MetadataPayload
}

func DrillsSliceToTypes(drillsInterface interface{}) *[]*DrillPayload {
	drills, ok := drillsInterface.([]*models.Drill)
	resolvers := make([]*DrillPayload, len(drills))
	if ok {
		for i, e := range drills {
			resolvers[i] = &DrillPayload{
				DrillPayload: &globalPayloads.DrillPayload{
					Drill: e,
				},
			}
		}
	}
	return &resolvers
}

func (d *DrillPayload) Skills() []*globalPayloads.SkillPayload {
	if d.Drill.DrillSkills == nil || len(*d.Drill.DrillSkills) == 0 {
		return []*globalPayloads.SkillPayload{}
	}

	skills := make([]*globalPayloads.SkillPayload, len(*d.Drill.DrillSkills))
	for i, ds := range *d.Drill.DrillSkills {
		if ds.Skill == nil {
			return nil
		}

		skills[i] = &globalPayloads.SkillPayload{
			Skill: ds.Skill,
		}
	}

	return skills
}

func (d *DrillPayload) Diagrams() []*DiagramPayload {
	if d.Drill.Diagrams == nil || len(*d.Drill.Diagrams) == 0 {
		return []*DiagramPayload{}
	}

	diagrams := make([]*DiagramPayload, len(*d.Drill.Diagrams))

	for i, dia := range *d.Drill.Diagrams {
		diagrams[i] = &DiagramPayload{
			&globalPayloads.DiagramPayload{
				Diagram: dia,
			},
		}
	}

	return diagrams
}

func (d *DrillPayload) Videos() []*globalPayloads.VideoPayload {
	if d.Drill.Videos == nil || !d.IsVisible {
		return nil
	}
	videos := make([]*globalPayloads.VideoPayload, len(*d.Drill.Videos))
	for i, v := range *d.Drill.Videos {
		videos[i] = &globalPayloads.VideoPayload{
			Video: &v,
		}
	}
	return videos
}

func (d *DrillPayload) VideoCount() int32 {
	if d.Drill.Videos == nil || len(*d.Drill.Videos) == 0 {
		return 0
	}

	return int32(len(*d.Drill.Videos))
}

func (d *DrillPayload) UserDrill() *UserDrillPayload {
	if d.Drill.UserDrills == nil || len(*d.Drill.UserDrills) == 0 {
		return nil
	}

	useDrills := *d.Drill.UserDrills
	return &UserDrillPayload{
		UserDrillPayload: &globalPayloads.UserDrillPayload{
			UserDrill: useDrills[0],
		},
	}
}

func (d *DrillPayload) Step() *gqlScalar.JSON {
	if !d.IsVisible {
		return nil
	}

	return d.DrillPayload.Step()
}

func (dp *DrillPayload) StepCount() int32 {
	if dp.Drill.Step == nil {
		return 0
	}

	var steps []map[string]interface{}
	if err := json.Unmarshal(*dp.Drill.Step, &steps); err != nil {
		return 0
	}

	count := int32(len(steps))
	return count
}
