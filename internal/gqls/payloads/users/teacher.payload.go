package userPayloads

import (
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	"vibico-education-api/internal/models"
)

type TeacherPayload struct {
	*globalPayloads.TeacherPayload
}

type TeachersCollectionPayload struct {
	Collection *[]*TeacherPayload
	Metadata   *globalPayloads.MetadataPayload
}

func (t *TeacherPayload) Courses() *[]*CoursePayload {
	if t.Teacher.Courses == nil || len(*t.Teacher.Courses) == 0 {
		return nil
	}
	courses := make([]*CoursePayload, len(*t.Teacher.Courses))

	for i, course := range *t.Teacher.Courses {
		var joinedUser *models.CourseUser
		if len(course.CourseUsers) > 0 {
			joinedUser = course.CourseUsers[0]
		}
		courses[i] = &CoursePayload{
			CoursePayload: &globalPayloads.CoursePayload{
				Course: course,
			},
			JoinedUser: joinedUser,
		}
	}

	return &courses
}
