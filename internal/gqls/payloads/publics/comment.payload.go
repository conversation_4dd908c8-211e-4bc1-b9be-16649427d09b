package publicPayloads

import (
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
)

type ReviewsCollectionPayload struct {
	Collection *[]*CommentPayload
	Stats      *globalPayloads.ReviewStatsPayload
	Metadata   *globalPayloads.MetadataPayload
}

type CommentPayload struct {
	*globalPayloads.CommentPayload
}

func (c *CommentPayload) AuthorUser() *UserPayload {
	if c.Comment.AuthorUser == nil {
		return nil
	}

	return &UserPayload{UserPayload: &globalPayloads.UserPayload{
		User: c.Comment.AuthorUser,
	}}
}
