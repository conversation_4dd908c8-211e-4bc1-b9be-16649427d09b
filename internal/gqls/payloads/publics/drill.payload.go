package publicPayloads

import (
	"encoding/json"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	gqlScalar "vibico-education-api/pkg/gql"
)

type DrillPayload struct {
	*globalPayloads.DrillPayload
}

func (d *DrillPayload) Videos() []*globalPayloads.VideoPayload {
	if d.Drill.Videos == nil || !d.Drill.IsFree() {
		return []*globalPayloads.VideoPayload{}
	}
	videos := make([]*globalPayloads.VideoPayload, len(*d.Drill.Videos))
	for i, v := range *d.Drill.Videos {
		videos[i] = &globalPayloads.VideoPayload{
			Video: &v,
		}
	}
	return videos
}

func (d *DrillPayload) VideoCount() int32 {
	if d.Drill.Videos == nil || len(*d.Drill.Videos) == 0 {
		return 0
	}

	return int32(len(*d.Drill.Videos))
}

func (d *DrillPayload) Skills() []*globalPayloads.SkillPayload {
	if d.Drill.DrillSkills == nil || len(*d.Drill.DrillSkills) == 0 {
		return []*globalPayloads.SkillPayload{}
	}

	skills := make([]*globalPayloads.SkillPayload, len(*d.Drill.DrillSkills))
	for i, drillSkill := range *d.Drill.DrillSkills {
		if drillSkill.Skill == nil {
			return nil
		}

		skills[i] = &globalPayloads.SkillPayload{
			Skill: drillSkill.Skill,
		}
	}

	return skills
}

func (d *DrillPayload) Diagrams() []*DiagramPayload {
	if d.Drill.Diagrams == nil || len(*d.Drill.Diagrams) == 0 {
		return []*DiagramPayload{}
	}

	diagrams := make([]*DiagramPayload, len(*d.Drill.Diagrams))

	for i, dia := range *d.Drill.Diagrams {
		diagrams[i] = &DiagramPayload{
			&globalPayloads.DiagramPayload{
				Diagram: dia,
			},
		}
	}

	return diagrams
}

func (d *DrillPayload) Step() *gqlScalar.JSON {
	if !d.Drill.IsFree() {
		return nil
	}

	return d.DrillPayload.Step()
}

func (dp *DrillPayload) StepCount() int32 {
	if dp.Drill.Step == nil {
		return 0
	}

	var steps []map[string]interface{}
	if err := json.Unmarshal(*dp.Drill.Step, &steps); err != nil {
		return 0
	}

	count := int32(len(steps))
	return count
}

func (d *DrillPayload) IsVisible() bool {
	return d.Drill.IsFree()
}
