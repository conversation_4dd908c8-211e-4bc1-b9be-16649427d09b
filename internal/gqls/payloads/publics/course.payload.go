package publicPayloads

import (
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
)

type CourseCreatePayload struct {
	Course  *CoursePayload
	Message string
}

type CourseUpdatePayload struct {
	Course  *CoursePayload
	Message string
}

type CoursePayload struct {
	globalPayloads.CoursePayload
}

type CoursesCollectionPayload struct {
	Collection *[]*CoursePayload
	Metadata   *globalPayloads.MetadataPayload
}

func (c *CoursePayload) CourseSections() *[]*CourseSectionPayload {
	if c.Course.CourseSections == nil || len(*c.Course.CourseSections) == 0 {
		return nil
	}
	sections := make([]*CourseSectionPayload, len(*c.Course.CourseSections))

	for i, section := range *c.Course.CourseSections {
		sections[i] = &CourseSectionPayload{
			&globalPayloads.CourseSectionPayload{
				CourseSection: section,
			},
		}
	}

	return &sections
}

func (c *CoursePayload) Teacher() *TeacherPayload {
	if c.Course.Teacher == nil {
		return nil
	}

	return &TeacherPayload{
		TeacherPayload: &globalPayloads.TeacherPayload{
			Teacher: c.Course.Teacher,
		},
	}
}

func (c *CoursePayload) CourseUsers() *[]*CourseUserPayload {
	if len(c.Course.CourseUsers) == 0 {
		return nil
	}
	courseUsers := make([]*CourseUserPayload, len(c.Course.CourseUsers))

	for i, courseUser := range c.Course.CourseUsers {
		courseUsers[i] = &CourseUserPayload{
			&globalPayloads.CourseUserPayload{
				CourseUser: courseUser,
			},
		}
	}

	return &courseUsers
}
