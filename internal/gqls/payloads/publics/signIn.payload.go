package publicPayloads

import (
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	"vibico-education-api/internal/models"

	translator "vibico-education-api/pkg/translators"

	pb "github.com/BehemothLtd/vibico-auth/proto/auth"
)

type ProfilePayload struct {
	*globalPayloads.PbUserPayload
	Teacher *TeacherPayload
	User    *UserPayload
}

type SignInPayload struct {
	Profile *ProfilePayload
	globalPayloads.TokenPayload
}

func NewSignInPayload(
	grpcResponse *pb.UserTokenResponse,
	teacher *models.Teacher,
	user *models.User,
) *SignInPayload {
	profilePayload := &ProfilePayload{
		PbUserPayload: &globalPayloads.PbUserPayload{User: grpcResponse.User},
	}

	if teacher != nil {
		profilePayload.Teacher = &TeacherPayload{&globalPayloads.TeacherPayload{Teacher: teacher}}
	}

	if user != nil {
		profilePayload.User = &UserPayload{
			UserPayload: &globalPayloads.UserPayload{User: user},
		}
	}

	return &SignInPayload{
		Profile: profilePayload,
		TokenPayload: globalPayloads.TokenPayload{
			AccessToken:  grpcResponse.AccessToken,
			RefreshToken: grpcResponse.RefreshToken,
			Message:      translator.Translate(nil, "infoMsg_signInSuccess"),
		},
	}
}
