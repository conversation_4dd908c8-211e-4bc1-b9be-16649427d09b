package publicPayloads

import (
	"vibico-education-api/internal/enums"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
)

type TeacherPayload struct {
	*globalPayloads.TeacherPayload
}

type TeachersCollectionPayload struct {
	Collection *[]*TeacherPayload
	Metadata   *globalPayloads.MetadataPayload
}

func (t *TeacherPayload) Courses() *[]*CoursePayload {
	if t.Teacher.Courses == nil || len(*t.Teacher.Courses) == 0 {
		return nil
	}
	courses := make([]*CoursePayload, len(*t.Teacher.Courses))

	for i, course := range *t.Teacher.Courses {
		courses[i] = &CoursePayload{
			CoursePayload: globalPayloads.CoursePayload{
				Course: course,
			},
		}
	}

	return &courses
}

func (t *TeacherPayload) Ratings() *[]*CommentPayload {
	if t.Teacher.Ratings == nil || len(*t.Teacher.Ratings) == 0 {
		return nil
	}

	ratings := make([]*CommentPayload, len(*t.Teacher.Ratings))

	for i, rating := range *t.Teacher.Ratings {
		ratings[i] = &CommentPayload{CommentPayload: &globalPayloads.CommentPayload{
			Comment: rating,
		}}
	}

	return &ratings
}

func (t *TeacherPayload) JoinedStudentCount() int32 {
	var count int32 = 0

	if t.Teacher.Courses == nil || len(*t.Teacher.Courses) == 0 {
		return count
	}

	for _, course := range *t.Teacher.Courses {
		if !course.IsPublic || course.Status != enums.CourseStatusApproved {
			continue
		}

		count += int32(course.JoinedUserCount)
	}

	return count
}
