package adminPayloads

import (
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
)

type CourseMutatePayload struct {
	Course  *CoursePayload
	Message string
}

type CoursePayload struct {
	globalPayloads.CoursePayload
}

type CoursesCollectionPayload struct {
	Collection *[]*CoursePayload
	Metadata   *globalPayloads.MetadataPayload
}

func (c *CoursePayload) Teacher() *TeacherPayload {
	if c.Course.Teacher == nil {
		return nil
	}

	return &TeacherPayload{
		TeacherPayload: &globalPayloads.TeacherPayload{
			Teacher: c.Course.Teacher,
		},
	}
}

func (c *CoursePayload) CourseSections() *[]*CourseSectionPayload {
	if c.Course.CourseSections == nil || len(*c.Course.CourseSections) == 0 {
		return nil
	}
	sections := make([]*CourseSectionPayload, len(*c.Course.CourseSections))

	for i, section := range *c.Course.CourseSections {
		sections[i] = &CourseSectionPayload{
			&globalPayloads.CourseSectionPayload{
				CourseSection: section,
			},
		}
	}

	return &sections
}

func (c *CoursePayload) CourseCensorHistories() *[]*CensorHistoryPayload {
	if c.Course.CourseCensorHistories == nil || len(*c.Course.CourseCensorHistories) == 0 {
		return nil
	}

	histories := make([]*CensorHistoryPayload, len(*c.Course.CourseCensorHistories))
	for i, history := range *c.Course.CourseCensorHistories {
		histories[i] = &CensorHistoryPayload{
			&globalPayloads.CensorHistoryPayload{
				CensorHistory: history,
			},
		}
	}

	return &histories
}
