package adminPayloads

import globalPayloads "vibico-education-api/internal/gqls/payloads/global"

type CourseSectionItemMutatePayload struct {
	SectionItem *CourseSectionItemPayload
	Message     string
}

type CourseSectionItemPayload struct {
	*globalPayloads.CourseSectionItemPayload
}

func (c *CourseSectionItemPayload) CourseSection() *CourseSectionPayload {
	if c.SectionItem.CourseSection == nil {
		return nil
	}

	return &CourseSectionPayload{
		CourseSectionPayload: &globalPayloads.CourseSectionPayload{
			CourseSection: c.SectionItem.CourseSection,
		},
	}
}

func (c *CourseSectionItemPayload) Drills() *[]*DrillPayload {
	if c.SectionItem.Drills == nil || len(*c.SectionItem.Drills) == 0 {
		return nil
	}

	drills := make([]*DrillPayload, len(*c.SectionItem.Drills))

	for i, drill := range *c.SectionItem.Drills {
		drills[i] = &DrillPayload{
			DrillPayload: &globalPayloads.DrillPayload{
				Drill: drill,
			},
		}
	}

	return &drills
}

func (c *CourseSectionItemPayload) Videos() *[]*globalPayloads.VideoPayload {
	if c.SectionItem.Videos == nil {
		return nil
	}
	videos := make([]*globalPayloads.VideoPayload, len(*c.SectionItem.Videos))

	for i, video := range *c.SectionItem.Videos {
		videos[i] = &globalPayloads.VideoPayload{
			Video: video,
		}
	}

	return &videos
}
