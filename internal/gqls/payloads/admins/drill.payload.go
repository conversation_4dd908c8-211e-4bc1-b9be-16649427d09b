package adminPayloads

import (
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
)

type DrillPayload struct {
	*globalPayloads.DrillPayload
}

func (d *DrillPayload) Videos() []*globalPayloads.VideoPayload {
	if d.Drill.Videos == nil {
		return []*globalPayloads.VideoPayload{}
	}
	videos := make([]*globalPayloads.VideoPayload, len(*d.Drill.Videos))
	for i, v := range *d.Drill.Videos {
		videos[i] = &globalPayloads.VideoPayload{
			Video: &v,
		}
	}
	return videos
}

func (d *DrillPayload) Skills() []*globalPayloads.SkillPayload {
	if d.Drill.DrillSkills == nil || len(*d.Drill.DrillSkills) == 0 {
		return []*globalPayloads.SkillPayload{}
	}

	skills := make([]*globalPayloads.SkillPayload, len(*d.Drill.DrillSkills))
	for i, drillSkill := range *d.Drill.DrillSkills {
		if drillSkill.Skill == nil {
			return nil
		}

		skills[i] = &globalPayloads.SkillPayload{
			Skill: drillSkill.Skill,
		}
	}

	return skills
}

func (d *DrillPayload) Diagrams() []*DiagramPayload {
	if d.Drill.Diagrams == nil || len(*d.Drill.Diagrams) == 0 {
		return []*DiagramPayload{}
	}

	diagrams := make([]*DiagramPayload, len(*d.Drill.Diagrams))

	for i, dia := range *d.Drill.Diagrams {
		diagrams[i] = &DiagramPayload{
			&globalPayloads.DiagramPayload{
				Diagram: dia,
			},
		}
	}

	return diagrams
}

func (d *DrillPayload) CensorHistories() []*CensorHistoryPayload {
	if d.Drill.CensorHistories == nil || len(*d.Drill.CensorHistories) == 0 {
		return nil
	}

	histories := make([]*CensorHistoryPayload, len(*d.Drill.CensorHistories))
	for i, history := range *d.Drill.CensorHistories {
		histories[i] = &CensorHistoryPayload{
			&globalPayloads.CensorHistoryPayload{
				CensorHistory: history,
			},
		}
	}

	return histories
}
