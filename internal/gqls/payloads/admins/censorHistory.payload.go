package adminPayloads

import (
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
)

type CensorHistoryPayload struct {
	*globalPayloads.CensorHistoryPayload
}

func (c *CensorHistoryPayload) Creator() *AdminPayload {
	if c.CensorHistory.Creator == nil {
		return nil
	}

	return &AdminPayload{
		AdminPayload: &globalPayloads.AdminPayload{
			Admin: c.CensorHistory.Creator,
		},
	}
}
