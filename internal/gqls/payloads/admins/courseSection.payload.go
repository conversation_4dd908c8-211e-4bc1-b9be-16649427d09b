package adminPayloads

import globalPayloads "vibico-education-api/internal/gqls/payloads/global"

type CourseCreateAndUpdate struct {
	CourseSection *CourseSectionPayload
	Message       string
}

type CourseSectionPayload struct {
	*globalPayloads.CourseSectionPayload
}

func (c *CourseSectionPayload) CourseSectionItems() *[]*CourseSectionItemPayload {
	if c.CourseSection.CourseSectionItems == nil || len(*c.CourseSection.CourseSectionItems) == 0 {
		return nil
	}
	items := make([]*CourseSectionItemPayload, len(*c.CourseSection.CourseSectionItems))

	for i, item := range *c.CourseSection.CourseSectionItems {
		items[i] = &CourseSectionItemPayload{
			&globalPayloads.CourseSectionItemPayload{
				SectionItem: item,
			},
		}
	}

	return &items
}
