package globalPayloads

import (
	"vibico-education-api/internal/models"

	gqlScalar "github.com/BehemothLtd/behemoth-pkg/golang/gql"
	"github.com/graph-gophers/graphql-go"
)

type UserCourseSectionItemPayload struct {
	UserCourseSectionItem *models.UserCourseSectionItem
}

func (u *UserCourseSectionItemPayload) ID() gqlScalar.Uint32 {
	return gqlScalar.Uint32(u.UserCourseSectionItem.ID)
}

func (u *UserCourseSectionItemPayload) UserID() gqlScalar.Uint32 {
	return gqlScalar.Uint32(u.UserCourseSectionItem.UserID)
}

func (u *UserCourseSectionItemPayload) CourseSectionItemID() gqlScalar.Uint32 {
	return gqlScalar.Uint32(u.UserCourseSectionItem.CourseSectionItemId)
}

func (u *UserCourseSectionItemPayload) Status() string {
	return u.UserCourseSectionItem.Status.String()
}

func (u *UserCourseSectionItemPayload) IsStudying() bool {
	return u.UserCourseSectionItem.IsStudying()
}

func (u *UserCourseSectionItemPayload) IsCompleted() bool {
	return u.UserCourseSectionItem.IsCompleted()
}

func (u *UserCourseSectionItemPayload) CreatedAt() *graphql.Time {
	return &graphql.Time{Time: u.UserCourseSectionItem.CreatedAt}
}

func (u *UserCourseSectionItemPayload) UpdatedAt() *graphql.Time {
	return &graphql.Time{Time: u.UserCourseSectionItem.UpdatedAt}
}
