package globalPayloads

import (
	"vibico-education-api/internal/models"

	"github.com/BehemothLtd/behemoth-pkg/golang/gql"
	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
)

type PackageDealPayload struct {
	PackageDeal *models.PackageDeal
}

func (p *PackageDealPayload) ID() gql.Uint32 {
	return gql.Uint32(p.PackageDeal.ID)
}

func (p *PackageDealPayload) Name() string {
	return p.PackageDeal.Name
}

func (p *PackageDealPayload) Description() string {
	return p.PackageDeal.Description
}

func (p *PackageDealPayload) ApprovalSubmissionRequired() bool {
	return p.PackageDeal.ApprovalSubmissionRequired
}

func (p *PackageDealPayload) Active() bool {
	return p.PackageDeal.Active
}

func (p *PackageDealPayload) CreatedAt() *graphql.Time {
	return utils.GqlTimePointer(p.PackageDeal.CreatedAt)
}

func (p *PackageDealPayload) UpdatedAt() *graphql.Time {
	return utils.GqlTimePointer(p.PackageDeal.UpdatedAt)
}
