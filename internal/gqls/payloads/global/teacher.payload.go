package globalPayloads

import (
	"vibico-education-api/internal/models"

	"github.com/BehemothLtd/behemoth-pkg/golang/gql"
	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
)

type TeacherPayload struct {
	*models.Teacher
}

func (t *TeacherPayload) ID() gql.Uint32 {
	return gql.Uint32(t.Teacher.ID)
}

func (t *TeacherPayload) Name() string {
	return t.Teacher.Name
}

func (s *TeacherPayload) Active() bool {
	return s.Teacher.Active
}

func (t *TeacherPayload) Slug() string {
	return t.Teacher.Slug
}

func (t *TeacherPayload) Award() *string {
	return t.Teacher.Award
}

func (t *TeacherPayload) Address() *string {
	return t.Teacher.Address
}

func (t *TeacherPayload) PhoneNumber() *string {
	return t.Teacher.PhoneNumber
}

func (t *TeacherPayload) ContactEmail() *string {
	return t.Teacher.ContactEmail
}

func (t *TeacherPayload) Description() *string {
	return t.Teacher.Description
}

func (t *TeacherPayload) CreatedAt() *graphql.Time {
	return utils.GqlTimePointer(&t.Teacher.CreatedAt)
}

func (t *TeacherPayload) BasicEntered() bool {
	return t.Teacher.BasicEntered
}

func (t *TeacherPayload) CanInviteStudents() bool {
	return t.Teacher.CanInviteStudents
}

func (t *TeacherPayload) ImageUrl() *string {
	return t.Teacher.ImageUrl
}

func (t *TeacherPayload) AverageRating() *float64 {
	if t.Teacher.AverageRating == nil {
		return nil
	}

	avg := float64(*t.Teacher.AverageRating)
	return &avg
}

func (t *TeacherPayload) UpdatedAt() *graphql.Time {
	return utils.GqlTimePointer(&t.Teacher.UpdatedAt)
}

func (c *TeacherPayload) ApprovedCourseCount() gql.Uint32 {
	return gql.Uint32(c.Teacher.ApprovedCourseCount)
}

func (c *TeacherPayload) StudentCount() gql.Uint32 {
	return gql.Uint32(c.Teacher.StudentCount)
}
