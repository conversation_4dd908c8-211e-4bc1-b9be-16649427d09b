package globalPayloads

import (
	"vibico-education-api/internal/models"
)

type ReviewStatsPayload struct {
	*models.ReviewStats
}

func (r *ReviewStatsPayload) Star1() int32 {
	return int32(r.ReviewStats.Star1)
}

func (r *ReviewStatsPayload) Star2() int32 {
	return int32(r.ReviewStats.Star2)
}

func (r *ReviewStatsPayload) Star3() int32 {
	return int32(r.ReviewStats.Star3)
}

func (r *ReviewStatsPayload) Star4() int32 {
	return int32(r.ReviewStats.Star4)
}

func (r *ReviewStatsPayload) Star5() int32 {
	return int32(r.ReviewStats.Star5)
}

func (r *ReviewStatsPayload) AverageRating() float64 {
	return r.ReviewStats.AverageRating
}

func (r *ReviewStatsPayload) Total() int32 {
	return int32(r.ReviewStats.Total)
}
