package globalPayloads

import (
	"vibico-education-api/internal/models"

	"github.com/BehemothLtd/behemoth-pkg/golang/gql"
	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
)

type UserPayload struct {
	*models.User
}

func (s *UserPayload) ID() gql.Uint32 {
	return gql.Uint32(s.User.ID)
}

func (s *UserPayload) Name() string {
	return s.User.Name
}

func (s *UserPayload) Active() bool {
	return s.User.Active
}

func (s *UserPayload) Gender() *string {
	return s.User.Gender
}

func (s *UserPayload) BirthDate() *string {
	if s.User.BirthDate != nil {
		date := *s.User.BirthDate
		formattedDate := date.Format("2006-01-02")
		return &formattedDate
	}

	return nil
}

func (s *UserPayload) ImageUrl() *string {
	return s.User.ImageUrl
}

func (s *UserPayload) PhoneNumber() *string {
	return s.User.PhoneNumber
}

func (s *UserPayload) CreatedAt() *graphql.Time {
	return utils.GqlTimePointer(&s.User.CreatedAt)
}

func (s *UserPayload) UpdatedAt() *graphql.Time {
	return utils.GqlTimePointer(&s.User.UpdatedAt)
}

func (s *UserPayload) Notifications() *[]*NotificationPayload {
	if s.User.NotificationRecipients == nil {
		return nil
	}
	notifications := make([]*NotificationPayload, len(*s.User.NotificationRecipients))
	for i, n := range *s.User.NotificationRecipients {
		notifications[i] = &NotificationPayload{Notification: n}
	}
	return &notifications
}
