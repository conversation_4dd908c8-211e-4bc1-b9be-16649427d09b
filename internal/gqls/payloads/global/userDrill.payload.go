package globalPayloads

import (
	"vibico-education-api/internal/models"

	"github.com/BehemothLtd/behemoth-pkg/golang/gql"
)

type UserDrillPayload struct {
	*models.UserDrill
}

func (s *UserDrillPayload) ID() gql.Uint32 {
	return gql.Uint32(s.UserDrill.ID)
}

func (s *UserDrillPayload) UserId() gql.Uint32 {
	return gql.Uint32(s.UserDrill.UserId)
}

func (s *UserDrillPayload) DrillId() gql.Uint32 {
	return gql.Uint32(s.UserDrill.DrillId)
}
