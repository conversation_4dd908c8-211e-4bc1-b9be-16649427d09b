package globalPayloads

import (
	"vibico-education-api/internal/models"
)

// Define NotificationPayload if not already defined or import from the correct package
type NotificationPayload struct {
	*models.Notification
}

func (n *NotificationPayload) ID() uint32 {
	return n.Notification.ID
}

func (n *NotificationPayload) RecipientUser() *UserPayload {
	if n.Notification.RecipientUser == nil {
		return nil
	}

	return &UserPayload{User: n.Notification.RecipientUser}
}
func (n *NotificationPayload) RecipientTeacher() *TeacherPayload {
	if n.Notification.RecipientTeacher == nil {
		return nil
	}

	return &TeacherPayload{Teacher: n.Notification.RecipientTeacher}
}

func (n *NotificationPayload) RecipientAdmin() *AdminPayload {
	if n.Notification.RecipientAdmin == nil {
		return nil
	}

	return &AdminPayload{Admin: n.Notification.RecipientAdmin}
}

func (n *NotificationPayload) SenderUser() *UserPayload {
	if n.Notification.SenderUser == nil {
		return nil
	}

	return &UserPayload{User: n.Notification.SenderUser}
}

func (n *NotificationPayload) SenderTeacher() *TeacherPayload {
	if n.Notification.SenderTeacher == nil {
		return nil
	}

	return &TeacherPayload{Teacher: n.Notification.SenderTeacher}
}

func (n *NotificationPayload) SenderAdmin() *AdminPayload {
	if n.Notification.SenderAdmin == nil {
		return nil
	}

	return &AdminPayload{Admin: n.Notification.SenderAdmin}
}

func (n *NotificationPayload) NotifiableCourse() *CoursePayload {
	if n.Notification.NotifiableCourse == nil {
		return nil
	}

	return &CoursePayload{Course: n.Notification.NotifiableCourse}
}

func (n *NotificationPayload) NoticeKind() string {
	return n.Notification.NoticeKind.String()
}

func (n *NotificationPayload) Title() string {
	content, err := n.Notification.GetContent()
	if err != nil {
		return ""
	}
	return content.Title
}

func (n *NotificationPayload) Body() string {
	content, err := n.Notification.GetContent()
	if err != nil {
		return ""
	}
	return content.Body
}

func (n *NotificationPayload) IsRead() bool {
	return n.Notification.IsRead
}
