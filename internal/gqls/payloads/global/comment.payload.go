package globalPayloads

import (
	"vibico-education-api/internal/models"

	"github.com/BehemothLtd/behemoth-pkg/golang/gql"
	"github.com/graph-gophers/graphql-go"
)

type CommentPayload struct {
	*models.Comment
}

func (c *CommentPayload) ID() gql.Uint32 {
	return gql.Uint32(c.Comment.ID)
}

func (c *CommentPayload) AuthorID() gql.Uint32 {
	return gql.Uint32(c.Comment.AuthorID)
}

func (c *CommentPayload) AuthorType() string {
	return c.Comment.AuthorType
}

func (c *CommentPayload) TargetID() gql.Uint32 {
	return gql.Uint32(c.Comment.TargetID)
}

func (c *CommentPayload) TargetType() string {
	return c.Comment.TargetType.String()
}

func (c *CommentPayload) Content() string {
	return c.Comment.Content
}

func (c *CommentPayload) Rating() *gql.Float32 {
	if c.Comment.Rating == nil {
		return nil
	}

	val := gql.Float32(*c.Comment.Rating)
	return &val
}

func (c *CommentPayload) CreatedAt() graphql.Time {
	return graphql.Time{Time: c.Comment.CreatedAt}
}

func (c *CommentPayload) UpdatedAt() graphql.Time {
	return graphql.Time{Time: c.Comment.UpdatedAt}
}
