package globalPayloads

import (
	"vibico-education-api/internal/models"

	"github.com/BehemothLtd/behemoth-pkg/golang/gql"
	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
)

type AdminPayload struct {
	*models.Admin
}

func (t *AdminPayload) ID() gql.Uint32 {
	return gql.Uint32(t.Admin.ID)
}

func (t *AdminPayload) Name() string {
	return t.Admin.Name
}

func (t *AdminPayload) CreatedAt() *graphql.Time {
	return utils.GqlTimePointer(&t.Admin.CreatedAt)
}

func (t *AdminPayload) UpdatedAt() *graphql.Time {
	return utils.GqlTimePointer(&t.Admin.UpdatedAt)
}
