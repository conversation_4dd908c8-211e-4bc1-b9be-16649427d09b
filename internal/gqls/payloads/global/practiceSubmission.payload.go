package globalPayloads

import (
	"vibico-education-api/internal/models"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/gql"
	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
)

type PracticeSubmissionPayload struct {
	PracticeSubmission *models.PracticeSubmission
}

func (ps *PracticeSubmissionPayload) ID() gql.Uint32 {
	return gql.Uint32(ps.PracticeSubmission.ID)
}

func (ps *PracticeSubmissionPayload) PracticeID() gql.Uint32 {
	return gql.Uint32(ps.PracticeSubmission.PracticeID)
}

func (ps *PracticeSubmissionPayload) PracticeType() string {
	return ps.PracticeSubmission.PracticeType.String()
}

func (ps *PracticeSubmissionPayload) UserID() gql.Uint32 {
	return gql.Uint32(ps.PracticeSubmission.UserID)
}

func (ps *PracticeSubmissionPayload) Content() *string {
	return ps.PracticeSubmission.Content
}

func (ps *PracticeSubmissionPayload) Status() string {
	return ps.PracticeSubmission.Status.String()
}

func (ps *PracticeSubmissionPayload) StatusI18n() string {
	statusKey := "PracticeSubmissionStatus" + utils.CamelToPascalCase(ps.PracticeSubmission.Status.String())
	return translator.Translate(nil, statusKey)
}

func (ps *PracticeSubmissionPayload) CreatedAt() graphql.Time {
	return graphql.Time{Time: ps.PracticeSubmission.CreatedAt}
}

func (ps *PracticeSubmissionPayload) UpdatedAt() graphql.Time {
	return graphql.Time{Time: ps.PracticeSubmission.UpdatedAt}
}

func (ps *PracticeSubmissionPayload) Videos() *[]*VideoPayload {
	psVideo := ps.PracticeSubmission.Videos
	videos := make([]*VideoPayload, 0)

	if psVideo == nil {
		return &videos
	}

	for _, video := range *psVideo {
		videos = append(videos, &VideoPayload{Video: video})
	}

	return &videos
}
