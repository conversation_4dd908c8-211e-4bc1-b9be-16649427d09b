package globalPayloads

import (
	"vibico-education-api/internal/models"
	gqlScalar "vibico-education-api/pkg/gql"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/gql"
	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
)

type DrillPayload struct {
	Drill *models.Drill
}

func (dp *DrillPayload) Id() gql.Uint32 {
	return gql.Uint32(dp.Drill.ID)
}

func (dp *DrillPayload) Title() string {
	return dp.Drill.Title
}

func (dp *DrillPayload) Description() *string {
	return dp.Drill.Description
}

func (dp *DrillPayload) Level() string {
	return dp.Drill.Level.String()
}

func (dp *DrillPayload) LevelI18n() string {
	levelKey := "DrillLevel" + utils.CamelToPascalCase(dp.Drill.Level.String())

	return translator.Translate(nil, levelKey)
}

func (dp *DrillPayload) Status() string {
	return dp.Drill.Status.String()
}

func (dp *DrillPayload) StatusI18n() string {
	statusKey := "DrillStatus" + utils.CamelToPascalCase(dp.Drill.Status.String())

	return translator.Translate(nil, statusKey)
}

func (dp *DrillPayload) Censor() string {
	return dp.Drill.Censor.String()
}

func (dp *DrillPayload) CensorI18n() string {
	censorKey := "DrillCensor" + utils.CamelToPascalCase(dp.Drill.Censor.String())

	return translator.Translate(nil, censorKey)
}

func (dp *DrillPayload) SalePrice() *int32 {
	return dp.Drill.SalePrice
}

func (dp *DrillPayload) Price() *int32 {
	return dp.Drill.Price
}

func (dp *DrillPayload) Slug() string {
	return dp.Drill.Slug
}

func (dp *DrillPayload) Step() *gqlScalar.JSON {
	if dp.Drill.Step == nil {
		return nil
	}

	data := gqlScalar.JSON(*dp.Drill.Step)
	return &data
}

func (dp *DrillPayload) OwnerID() gql.Uint32 {
	return gql.Uint32(dp.Drill.OwnerID)
}

func (dp *DrillPayload) OwnerType() string {
	return dp.Drill.OwnerType
}

func (dp *DrillPayload) IsMaster() bool {
	return dp.Drill.IsMaster
}
