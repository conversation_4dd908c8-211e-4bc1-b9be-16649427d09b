package globalPayloads

import (
	"vibico-education-api/internal/models"
	gqlScalar "vibico-education-api/pkg/gql"

	"github.com/BehemothLtd/behemoth-pkg/golang/gql"
	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
)

type DiagramPayload struct {
	Diagram *models.Diagram
}

func (dp *DiagramPayload) ID() gql.Uint32 {
	return gql.Uint32(dp.Diagram.ID)
}

func (dp *DiagramPayload) ParentID() gql.Uint32 {
	return gql.Uint32(dp.Diagram.ParentID)
}

func (dp *DiagramPayload) ParentType() string {
	return dp.Diagram.ParentType
}

func (dp *DiagramPayload) ImageUrl() *string {
	if dp.Diagram.ImageUrl == nil {
		return nil
	}

	return dp.Diagram.ImageUrl
}

// TODO: Change to behemoth-pkg/golang/gql JSON
// and remove pkg/gql/json.scalar.go
func (dp *DiagramPayload) Setting() *gqlScalar.JSON {
	if dp.Diagram.Setting == nil {
		return nil
	}

	data := gqlScalar.JSON(*dp.Diagram.Setting)
	return &data
}

func (dp *DiagramPayload) Position() gql.Uint32 {
	return gql.Uint32(dp.Diagram.Position)
}

func (dp *DiagramPayload) CreatedAt() *graphql.Time {
	return utils.GqlTimePointer(&dp.Diagram.CreatedAt)
}

func (dp *DiagramPayload) UpdatedAt() *graphql.Time {
	return utils.GqlTimePointer(&dp.Diagram.UpdatedAt)
}
