package globalPayloads

import (
	"fmt"
	"vibico-education-api/internal/models"

	"github.com/BehemothLtd/behemoth-pkg/golang/gql"
	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
)

type VideoPayload struct {
	Video *models.Video
}

func (s *VideoPayload) ID() gql.Uint32 {
	return gql.Uint32(s.Video.ID)
}

func (s *VideoPayload) Title() string {
	return s.Video.Title
}

func (s *VideoPayload) Status() string {
	return s.Video.Status.String()
}

func (v *VideoPayload) ThumbnailURL() *string {
	if v.Video.ThumbnailURL == nil || *v.Video.ThumbnailURL == "" {
		return nil
	}

	bucketName := utils.GetEnv("GCS_BUCKET_NAME", "example")
	fullURL := fmt.Sprintf("https://storage.googleapis.com/%s/%s", bucketName, *v.Video.ThumbnailURL)

	return &fullURL
}

func (v *VideoPayload) IsPlayable() bool {
	return v.Video.IsPlayable()
}

func (s *VideoPayload) VideoPlatforms() *[]*VideoPlatformPayload {
	if len(s.Video.VideoPlatforms) == 0 {
		return nil
	}
	platforms := make([]*VideoPlatformPayload, len(s.Video.VideoPlatforms))

	for i, platform := range s.Video.VideoPlatforms {
		platforms[i] = &VideoPlatformPayload{
			VideoPlatform: platform,
		}
	}

	return &platforms
}

func (v *VideoPayload) Duration() int32 {
	return v.Video.Duration
}

func (v *VideoPayload) CurrentPosition() int32 {
	if v.Video.VideoProgress == nil {
		return 0
	}
	return v.Video.VideoProgress.LastPosition
}
