package globalPayloads

import (
	gqlScalar "github.com/BehemothLtd/behemoth-pkg/golang/gql"
	"github.com/BehemothLtd/behemoth-pkg/golang/pagination"
)

type MetadataPayload struct {
	Metadata pagination.Metadata
}

func (mt *MetadataPayload) Total() *gqlScalar.Uint32 {
	return (*gqlScalar.Uint32)(&mt.Metadata.Total)
}

func (mt *MetadataPayload) PerPage() *gqlScalar.Uint32 {
	return (*gqlScalar.Uint32)(&mt.Metadata.PerPage)
}

func (mt *MetadataPayload) Page() *gqlScalar.Uint32 {
	return (*gqlScalar.Uint32)(&mt.Metadata.Page)
}

func (mt *MetadataPayload) Pages() *gqlScalar.Uint32 {
	return (*gqlScalar.Uint32)(&mt.Metadata.Pages)
}

func (mt *MetadataPayload) Count() *gqlScalar.Uint32 {
	return (*gqlScalar.Uint32)(&mt.Metadata.Count)
}

func (mt *MetadataPayload) Next() *gqlScalar.Uint32 {
	return (*gqlScalar.Uint32)(&mt.Metadata.Next)
}

func (mt *MetadataPayload) Prev() *gqlScalar.Uint32 {
	return (*gqlScalar.Uint32)(&mt.Metadata.Prev)
}

func (mt *MetadataPayload) From() *gqlScalar.Uint32 {
	return (*gqlScalar.Uint32)(&mt.Metadata.From)
}

func (mt *MetadataPayload) To() *gqlScalar.Uint32 {
	return (*gqlScalar.Uint32)(&mt.Metadata.To)
}
