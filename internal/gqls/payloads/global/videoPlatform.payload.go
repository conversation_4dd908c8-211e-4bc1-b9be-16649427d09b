package globalPayloads

import (
	"vibico-education-api/internal/models"

	"github.com/BehemothLtd/behemoth-pkg/golang/gql"
)

type VideoPlatformPayload struct {
	VideoPlatform *models.VideoPlatform
}

func (s *VideoPlatformPayload) ID() gql.Uint32 {
	return gql.Uint32(s.VideoPlatform.ID)
}

func (s *VideoPlatformPayload) Url() string {
	return s.VideoPlatform.URL
}

func (s *VideoPlatformPayload) Status() string {
	return s.VideoPlatform.Status.String()
}
