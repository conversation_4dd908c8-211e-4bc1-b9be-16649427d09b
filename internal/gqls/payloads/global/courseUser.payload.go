package globalPayloads

import (
	"vibico-education-api/internal/models"

	"github.com/BehemothLtd/behemoth-pkg/golang/gql"
	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
)

type CourseUserPayload struct {
	CourseUser *models.CourseUser
}

func (c *CourseUserPayload) ID() gql.Uint32 {
	return gql.Uint32(c.CourseUser.ID)
}

func (c *CourseUserPayload) UserID() gql.Uint32 {
	return gql.Uint32(c.CourseUser.UserID)
}

func (c *CourseUserPayload) CourseID() gql.Uint32 {
	return gql.Uint32(c.CourseUser.CourseID)
}

func (c *CourseUserPayload) CreatedAt() *graphql.Time {
	return utils.GqlTimePointer(&c.CourseUser.CreatedAt)
}

func (c *CourseUserPayload) JoinedAt() *graphql.Time {
	return utils.GqlTimePointer(c.CourseUser.JoinedAt)
}

func (c *CourseUserPayload) UpdatedAt() *graphql.Time {
	return utils.GqlTimePointer(&c.CourseUser.UpdatedAt)
}

func (c *CourseUserPayload) CourseUserMetadata() *models.CourseUserMetadata {
	return c.CourseUser.CourseUserMetadata
}

func (c *CourseUserPayload) Status() string {
	return string(c.CourseUser.Status)
}

func (c *CourseUserPayload) CoursePackageId() *gql.Uint32 {
	if c.CourseUser.CoursePackageID == nil {
		return nil
	}

	id := gql.Uint32(*c.CourseUser.CoursePackageID)
	return &id
}
