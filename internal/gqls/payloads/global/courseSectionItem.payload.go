package globalPayloads

import (
	"vibico-education-api/internal/enums"
	"vibico-education-api/internal/models"

	gqlScalar "github.com/BehemothLtd/behemoth-pkg/golang/gql"
	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
)

type CourseSectionItemPayload struct {
	SectionItem *models.CourseSectionItem
}

func (c *CourseSectionItemPayload) ID() gqlScalar.Uint32 {
	return gqlScalar.Uint32(c.SectionItem.ID)
}

func (c *CourseSectionItemPayload) CourseSectionId() gqlScalar.Uint32 {
	return gqlScalar.Uint32(c.SectionItem.CourseSectionId)
}

func (c *CourseSectionItemPayload) Position() gqlScalar.Uint32 {
	return gqlScalar.Uint32(c.SectionItem.Position)
}

func (c *CourseSectionItemPayload) Title() string {
	return c.SectionItem.Title
}

func (c *CourseSectionItemPayload) Slug() string {
	return c.SectionItem.Slug
}

func (c *CourseSectionItemPayload) Content() *string {
	val := utils.GetStringOrDefault(c.SectionItem.Content)
	return &val
}

func (c *CourseSectionItemPayload) Type() string {
	return enums.CourseSectionItemType.String(c.SectionItem.Type)
}

func (c *CourseSectionItemPayload) CreatedAt() *graphql.Time {
	return utils.GqlTimePointer(&c.SectionItem.CreatedAt)
}

func (c *CourseSectionItemPayload) UpdatedAt() *graphql.Time {
	return utils.GqlTimePointer(&c.SectionItem.UpdatedAt)
}
