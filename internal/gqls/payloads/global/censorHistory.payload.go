package globalPayloads

import (
	"vibico-education-api/internal/models"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/gql"
	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
)

type CensorHistoryPayload struct {
	CensorHistory *models.CensorHistory
}

func (c *CensorHistoryPayload) ID() gql.Uint32 {
	return gql.Uint32(c.CensorHistory.ID)
}

func (c *CensorHistoryPayload) ParentID() gql.Uint32 {
	return gql.Uint32(c.CensorHistory.ParentID)
}

func (c *CensorHistoryPayload) ParentType() string {
	return c.CensorHistory.ParentType
}

func (c *CensorHistoryPayload) CreatedBy() gql.Uint32 {
	return gql.Uint32(c.<PERSON>nsor<PERSON>istory.CreatedBy)
}

func (c *CensorHistoryPayload) Feedback() string {
	return c.CensorHistory.Feedback
}

func (c *CensorHistoryPayload) Status() string {
	return c.CensorHistory.Status.String()
}

func (c *CensorHistoryPayload) StatusI18n() string {
	statusKey := "CensorStatus" + utils.CamelToPascalCase(c.CensorHistory.Status.String())
	return translator.Translate(nil, statusKey)
}

func (c *CensorHistoryPayload) CreatedAt() *graphql.Time {
	return utils.GqlTimePointer(&c.CensorHistory.CreatedAt)
}

func (c *CensorHistoryPayload) UpdatedAt() *graphql.Time {
	return utils.GqlTimePointer(&c.CensorHistory.UpdatedAt)
}
