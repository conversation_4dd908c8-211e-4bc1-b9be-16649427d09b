package globalPayloads

import (
	"encoding/json"
	"vibico-education-api/internal/models"
)

type CourseEarningPayload struct {
	CourseEarning *models.CourseEarning
}

func (c *CourseEarningPayload) Title() string {
	return c.CourseEarning.Title
}

func (c *CourseEarningPayload) Data() []int32 {
	if c.CourseEarning == nil || c.CourseEarning.Data == "" {
		return make([]int32, 12)
	}
	var result []int32
	_ = json.Unmarshal([]byte(c.CourseEarning.Data), &result)

	if len(result) < 12 {
		padding := make([]int32, 12-len(result))
		result = append(result, padding...)
	} else if len(result) > 12 {
		result = result[:12]
	}
	return result
}
