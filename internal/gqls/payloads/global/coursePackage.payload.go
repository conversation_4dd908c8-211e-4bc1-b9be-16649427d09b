package globalPayloads

import (
	"vibico-education-api/internal/models"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
)

type CoursePackagePayload struct {
	CoursePackage *models.CoursePackage
}

func (p *CoursePackagePayload) ID() graphql.ID {
	return graphql.ID(p.CoursePackage.ID)
}

func (p *CoursePackagePayload) CourseID() graphql.ID {
	return graphql.ID(p.CoursePackage.CourseID)
}

func (p *CoursePackagePayload) PackageDealID() graphql.ID {
	return graphql.ID(p.CoursePackage.PackageDealID)
}

func (c *CoursePackagePayload) SalePrice() *int32 {
	return c.CoursePackage.SalePrice
}

func (c *CoursePackagePayload) Price() *int32 {
	return c.CoursePackage.Price
}

func (p *CoursePackagePayload) CreatedAt() *graphql.Time {
	return utils.GqlTimePointer(p.CoursePackage.CreatedAt)
}

func (p *CoursePackagePayload) UpdatedAt() *graphql.Time {
	return utils.GqlTimePointer(p.CoursePackage.UpdatedAt)
}
