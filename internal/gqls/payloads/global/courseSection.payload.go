package globalPayloads

import (
	"vibico-education-api/internal/models"

	gqlScalar "github.com/BehemothLtd/behemoth-pkg/golang/gql"
	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
)

type CourseSectionPayload struct {
	CourseSection *models.CourseSection
}

func (c *CourseSectionPayload) ID() gqlScalar.Uint32 {
	return gqlScalar.Uint32(c.CourseSection.ID)

}

func (c *CourseSectionPayload) Title() string {
	return c.CourseSection.Title
}

func (c *CourseSectionPayload) Slug() string {
	return c.CourseSection.Slug
}

func (c *CourseSectionPayload) CourseID() gqlScalar.Uint32 {
	return gqlScalar.Uint32(c.CourseSection.CourseID)

}

func (c *CourseSectionPayload) Position() int32 {
	return c.CourseSection.Position
}

func (c *CourseSectionPayload) CreatedAt() *graphql.Time {
	return utils.GqlTimePointer(&c.CourseSection.CreatedAt)
}

func (c *CourseSectionPayload) UpdatedAt() *graphql.Time {
	return utils.GqlTimePointer(&c.CourseSection.UpdatedAt)
}
