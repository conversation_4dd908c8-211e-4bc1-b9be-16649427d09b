package globalPayloads

import "github.com/BehemothLtd/behemoth-pkg/golang/gql"

type SelectOptionsPayloadType struct {
	SkillOptions                    []GeneralOptionType
	LevelOptions                    []EnumValueOptionType
	CourseStatusOptions             []StringValueOptionType
	CourseInstructionalLevelOptions []StringValueOptionType
	TeacherOptions                  []GeneralOptionType
}

type GeneralOptionType struct {
	Value gql.Uint32
	Label string
}

type StringValueOptionType struct {
	Value string
	Label string
}

type EnumValueOptionType struct {
	Value       gql.Uint32
	Label       string
	Description string
}
