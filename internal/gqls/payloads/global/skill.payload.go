package globalPayloads

import (
	"strings"
	"vibico-education-api/internal/models"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/gql"
	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
)

type SkillPayload struct {
	Skill *models.Skill
}

func (s *SkillPayload) Id() gql.Uint32 {
	return gql.Uint32(s.Skill.ID)
}

func (s *SkillPayload) Name() string {
	return s.Skill.Name
}

func (s *SkillPayload) NameI18n() string {
	skillKey := "Skill" + strings.ReplaceAll(utils.CamelToPascalCase(s.Skill.Name), " ", "")

	return translator.Translate(nil, skillKey)
}

func (s *SkillPayload) Description() *string {
	if s.Skill.Description == nil {
		return nil
	}

	return s.Skill.Description
}

func (s *SkillPayload) CreatedAt() *graphql.Time {
	return utils.GqlTimePointer(s.Skill.CreatedAt)
}

func (s *SkillPayload) UpdatedAt() *graphql.Time {
	return utils.GqlTimePointer(s.Skill.UpdatedAt)
}
