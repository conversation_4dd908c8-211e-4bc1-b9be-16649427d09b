package globalPayloads

import (
	"vibico-education-api/internal/models"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/gql"
	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
)

type CoursePayload struct {
	Course *models.Course
}

func (c *CoursePayload) ID() gql.Uint32 {
	return gql.Uint32(c.Course.ID)
}

func (c *CoursePayload) TeacherId() gql.Uint32 {
	return gql.Uint32(c.Course.TeacherId)
}

func (c *CoursePayload) Title() string {
	return c.Course.Title
}

func (c *CoursePayload) Description() *string {
	return c.Course.Description
}

func (c *CoursePayload) SalePrice() *int32 {
	return c.Course.SalePrice
}

func (c *CoursePayload) Price() *int32 {
	return c.Course.Price
}

func (c *CoursePayload) BonusPoint() *int32 {
	return c.Course.BonusPoint
}

func (c *CoursePayload) BonusPointPercent() *int32 {
	return c.Course.BonusPointPercent
}

func (c *CoursePayload) Status() string {
	return c.Course.Status.String()
}

func (c *CoursePayload) StatusI18n() string {
	statusKey := "CourseStatus" + utils.CamelToPascalCase(c.Course.Status.String())
	return translator.Translate(nil, statusKey)
}

func (c *CoursePayload) InstructionalLevel() string {
	return c.Course.InstructionalLevel.String()
}

func (c *CoursePayload) InstructionalLevelI18n() string {
	statusKey := "CourseInstructionalLevel" + utils.CamelToPascalCase(c.Course.InstructionalLevel.String())
	return translator.Translate(nil, statusKey)
}

func (c *CoursePayload) CreatedAt() *graphql.Time {
	return utils.GqlTimePointer(&c.Course.CreatedAt)
}

func (c *CoursePayload) UpdatedAt() *graphql.Time {
	return utils.GqlTimePointer(&c.Course.UpdatedAt)
}

func (c *CoursePayload) SectionCount() gql.Uint32 {
	return gql.Uint32(c.Course.SectionCount)
}

func (c *CoursePayload) SectionItemCount() gql.Uint32 {
	return gql.Uint32(c.Course.SectionItemCount)
}

func (c *CoursePayload) JoinedUserCount() gql.Uint32 {
	return gql.Uint32(c.Course.JoinedUserCount)
}

func (c *CoursePayload) Banner() *string {
	return c.Course.Banner
}

func (c *CoursePayload) Slug() string {
	return c.Course.Slug
}

func (c *CoursePayload) IsPublic() bool {
	return c.Course.IsPublic
}

func (c *CoursePayload) AverageRating() *gql.Float32 {
	if c.Course.AverageRating == nil {
		return nil
	}
	val := gql.Float32(*c.Course.AverageRating)
	return &val
}

func (c *CoursePayload) IsSettingPackage() bool {
	return c.Course.IsSettingPackage
}
