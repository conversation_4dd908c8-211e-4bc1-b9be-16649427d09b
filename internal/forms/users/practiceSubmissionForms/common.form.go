package practiceSubmissionForms

import (
	"context"
	"vibico-education-api/internal/forms"
	"vibico-education-api/internal/forms/formAttributes"
	userInputs "vibico-education-api/internal/gqls/inputs/users"
	"vibico-education-api/internal/models"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
)

const (
	contentDateNameField = "Content"
)

type CommonForm struct {
	forms.Form
	ctx         context.Context
	input       *userInputs.PracticeSubmissionCommonInput
	currentUser *models.User

	PracticeSubmission *models.PracticeSubmission
}

func (form *CommonForm) assignAttributes() {
	form.AddAttributes(
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: contentDateNameField,
			},
			Value: form.input.Content,
		},
	)
}

func (form *CommonForm) validate() error {
	form.
		validateContent().
		SummaryErrors()

	if form.IsValid() {
		return nil
	}

	return exceptions.NewUnprocessableContentError(translator.Translate(nil, "errValidationMsg_general"), form.Errors)
}

func (form *CommonForm) validateContent() *CommonForm {
	field := form.GetAttribute(contentDateNameField)

	field.ValidateLtEq(1000, nil)

	if field.IsClean() {
		form.PracticeSubmission.Content = form.input.Content
	}

	return form
}
