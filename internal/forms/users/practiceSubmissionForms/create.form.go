package practiceSubmissionForms

import (
	"context"
	"fmt"
	"vibico-education-api/internal/enums"
	"vibico-education-api/internal/forms"
	"vibico-education-api/internal/forms/formAttributes"
	userInputs "vibico-education-api/internal/gqls/inputs/users"
	"vibico-education-api/internal/models"
	"vibico-education-api/internal/repository"
	"vibico-education-api/internal/repository/repositories"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/graph-gophers/graphql-go"
	"github.com/rs/zerolog/log"
)

const (
	practiceIDNameField   = "PracticeID"
	practiceTypeNameField = "PracticeType"
	videoIDsNameField     = "VideoIDs"
)

type IPracticeSubmissionCreateRepository interface {
	CreateWithVideo(ctx context.Context, practiceSubmission *models.PracticeSubmission, videoIDs []uint32, selectFields ...string) error
}

type CreateForm struct {
	CommonForm
	repo  IPracticeSubmissionCreateRepository
	repos repository.IRepositories

	input    *userInputs.PracticeSubmissionCreateInput
	videoIDs []uint32
}

func NewCreateForm(
	ctx context.Context,
	input userInputs.PracticeSubmissionCreateInput,
	currentUser *models.User,
	repo IPracticeSubmissionCreateRepository,
	repos repository.IRepositories,
) *CreateForm {
	form := &CreateForm{
		CommonForm: CommonForm{
			Form:               forms.Form{},
			ctx:                ctx,
			currentUser:        currentUser,
			input:              &input.PracticeSubmissionCommonInput,
			PracticeSubmission: &models.PracticeSubmission{Status: enums.PracticeSubmissionStatusSubmitted, UserID: currentUser.ID},
		},
		input: &input,
		repo:  repo,
		repos: repos,
	}

	form.assignAttributes()

	return form
}

func (form *CreateForm) assignAttributes() {
	form.CommonForm.assignAttributes()

	form.AddAttributes(
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: practiceIDNameField,
			},
			Value: (*string)(form.input.PracticeID),
		},
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: practiceTypeNameField,
			},
			Value: form.input.PracticeType,
		},
		&formAttributes.SliceAttribute[graphql.ID]{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: videoIDsNameField,
			},
			Value: form.input.VideoIDs,
		},
	)
}

func (form *CreateForm) Save() error {
	if err := form.validate(); err != nil {
		return err
	}

	selectFields := append(form.GetAllCode(), "UserID", "TeacherID", "Status")

	if err := form.repo.CreateWithVideo(form.ctx, form.PracticeSubmission, form.videoIDs, selectFields...); err != nil {
		return exceptions.NewUnprocessableContentError(err.Error(), nil)
	}

	return nil
}

func (form *CreateForm) validate() error {
	form.
		validatePracticeID().
		validatePracticeType().
		validatePractice().
		validateVideoIDs().
		validateContent().
		SummaryErrors()

	if form.IsValid() {
		return nil
	}

	return exceptions.NewUnprocessableContentError(translator.Translate(nil, "errValidationMsg_general"), form.Errors)
}

func (form *CreateForm) validatePracticeID() *CreateForm {
	field := form.GetAttribute(practiceIDNameField)

	field.ValidateRequired(nil)

	if field.IsClean() {
		practiceID, err := utils.ParseGraphqlID[uint32](*form.input.PracticeID)
		if err != nil {
			field.AddError(err.Error())
		}

		form.PracticeSubmission.PracticeID = practiceID
	}

	return form
}

func (form *CreateForm) validatePracticeType() *CreateForm {
	field := form.GetAttribute(practiceTypeNameField)

	field.ValidateRequired(nil)

	if !field.IsClean() {
		return form
	}

	fieldEnum, err := enums.ParsePracticeSubmissionPracticeType(*form.input.PracticeType)
	if err != nil {
		field.AddError(translator.Translate(nil, "errValidation_notExist"))
	}

	if field.IsClean() {
		form.PracticeSubmission.PracticeType = fieldEnum
	}

	return form
}

func (form *CreateForm) validatePractice() *CreateForm {
	fieldPracticeID := form.GetAttribute(practiceIDNameField)
	fieldPracticeType := form.GetAttribute(practiceTypeNameField)

	if !fieldPracticeID.IsClean() || !fieldPracticeType.IsClean() {
		return form
	}

	switch form.PracticeSubmission.PracticeType {
	case enums.PracticeSubmissionPracticeTypeCourseSectionItem:
		userCourseSectionItem, err := form.repos.UserCourseSectionItemRepo().FindByIdAndUserId(
			form.ctx,
			form.currentUser.ID,
			form.PracticeSubmission.PracticeID,
			repositories.CustomPreload{Key: "CourseSectionItem.CourseSection.Course"},
		)
		if err != nil {
			log.Error().Err(err).Ctx(form.ctx).Msg("something error when find user course section item")
			fieldPracticeID.AddError(translator.Translate(nil, "general_invalidId"))
			break
		}

		form.PracticeSubmission.TeacherID = userCourseSectionItem.CourseSectionItem.CourseSection.Course.TeacherId
	default:
		fieldPracticeType.AddError(translator.Translate(nil, "errValidation_notExist"))
	}

	return form
}

func (form *CreateForm) validateVideoIDs() *CreateForm {
	field := form.GetAttribute(videoIDsNameField)

	if field.Blank() {
		return form
	}

	videoIDsExist := map[uint32]bool{}

	for index, videoID := range *form.input.VideoIDs {
		videoIDUint, err := utils.ParseGraphqlID[uint32](videoID)
		if err != nil {
			form.AddNestedErrors(videoIDsNameField, index, exceptions.ResourceModificationError{
				"id": []any{err.Error()},
			})

			continue
		}

		videoIDsExist[videoIDUint] = false
		form.videoIDs = append(form.videoIDs, videoIDUint)
	}

	if !field.IsClean() {
		return form
	}

	videos, err := form.repos.VideoRepo().ListByIDs(form.ctx, form.videoIDs)
	if err != nil {
		log.Ctx(form.ctx).Error().Err(err).Msg("something error when find videos")
		field.AddError(translator.Translate(nil, "general_error"))
		return form
	}

	form.PracticeSubmission.Videos = &[]*models.Video{}

	for _, video := range *videos {
		if _, ok := videoIDsExist[video.ID]; ok {
			videoIDsExist[video.ID] = true
		}

		if video.ParentID != 0 {
			field.AddError(fmt.Sprintf("video id %d has parent", video.ID))
			continue
		}

		*form.PracticeSubmission.Videos = append(*form.PracticeSubmission.Videos, &video)
	}

	if !field.IsClean() {
		return form
	}

	for videoID, exist := range videoIDsExist {
		if !exist {
			field.AddError(fmt.Sprintf("video id %d not exist", videoID))
		}
	}

	return form
}
