package practiceSubmissionForms

import (
	"context"
	"vibico-education-api/internal/forms"
	userInputs "vibico-education-api/internal/gqls/inputs/users"
	"vibico-education-api/internal/models"

	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
)

type IPracticeSubmissionRepository interface {
	Update(ctx context.Context, practiceSubmission *models.PracticeSubmission, selectFields ...string) error
}

type UpdateForm struct {
	CommonForm
	repo IPracticeSubmissionRepository

	input *userInputs.PracticeSubmissionUpdateFormInput
}

func NewUpdateForm(
	ctx context.Context,
	input userInputs.PracticeSubmissionUpdateFormInput,
	practiceSubmission *models.PracticeSubmission,
	repo IPracticeSubmissionRepository,
) *UpdateForm {
	form := &UpdateForm{
		CommonForm: CommonForm{
			Form:               forms.Form{},
			ctx:                ctx,
			input:              &input.PracticeSubmissionCommonInput,
			PracticeSubmission: practiceSubmission,
		},
		input: &input,
		repo:  repo,
	}

	form.assignAttributes()

	return form
}

func (form *UpdateForm) Save() error {
	if err := form.validate(); err != nil {
		return err
	}

	selectFields := form.GetAllCode()

	if err := form.repo.Update(form.ctx, form.PracticeSubmission, selectFields...); err != nil {
		return exceptions.NewUnprocessableContentError(err.Error(), nil)
	}

	return nil
}
