package commentForms

import (
	"context"
	"vibico-education-api/internal/enums"
	"vibico-education-api/internal/forms"
	globalInputs "vibico-education-api/internal/gqls/inputs/global"
	"vibico-education-api/internal/models"
	"vibico-education-api/internal/repository"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
	"github.com/rs/zerolog/log"
)

type CreateForm struct {
	CommonForm
}

func NewCreateForm(
	ctx context.Context,
	input globalInputs.CommentCreateInput,
	currentUser *models.User,
	repos repository.IRepositories,
) *CreateForm {
	form := &CreateForm{
		CommonForm: CommonForm{
			Form:        forms.Form{},
			ctx:         ctx,
			currentUser: currentUser,
			Comment:     &models.Comment{AuthorID: currentUser.ID, AuthorType: "User", AuthorUser: currentUser},
			input:       &input,
			repos:       repos,
		},
	}

	form.assignAttributes()

	return form
}

func (form *CreateForm) Save() error {
	if err := form.validate(); err != nil {
		return err
	}

	selectFields := append(form.GetAllCode(), "AuthorID", "AuthorType")

	if err := form.repos.CommentRepo().Create(form.ctx, form.Comment, selectFields...); err != nil {
		return exceptions.NewUnprocessableContentError(err.Error(), nil)
	}

	return nil
}

func (form *CreateForm) validate() error {
	form.
		validateTargetID().
		validateTargetType().
		validateTarget().
		validateContent().
		SummaryErrors()

	if form.IsValid() {
		return nil
	}

	return exceptions.NewUnprocessableContentError(translator.Translate(nil, "errValidationMsg_general"), form.Errors)
}

func (form *CreateForm) validateTargetID() *CreateForm {
	form.CommonForm.validateTargetID()

	return form
}

func (form *CreateForm) validateTargetType() *CreateForm {
	form.CommonForm.validateTargetType()

	return form
}

func (form *CreateForm) validateTarget() *CreateForm {
	fieldTargetID := form.GetAttribute(targetIDNameField)
	fieldTargetType := form.GetAttribute(targetTypeNameField)

	if !fieldTargetID.IsClean() || !fieldTargetType.IsClean() {
		return form
	}

	switch form.Comment.TargetType {
	case enums.CommentTargetTypePracticeSubmission:
		_, err := form.repos.PracticeSubmissionRepo().FindByIDAndUserID(
			form.ctx,
			form.Comment.TargetID,
			form.currentUser.ID,
		)
		if err != nil {
			log.Error().Err(err).Ctx(form.ctx).Msg("something error when find practice submission")
			fieldTargetID.AddError(translator.Translate(nil, "general_invalidId"))
		}

	default:
		fieldTargetType.AddError(translator.Translate(nil, "errValidation_notExist"))
	}

	return form
}

func (form *CreateForm) validateContent() *CreateForm {
	form.CommonForm.validateContent()

	return form
}
