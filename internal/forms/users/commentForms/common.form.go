package commentForms

import (
	"context"
	"vibico-education-api/internal/enums"
	"vibico-education-api/internal/forms"
	"vibico-education-api/internal/forms/formAttributes"
	globalInputs "vibico-education-api/internal/gqls/inputs/global"
	"vibico-education-api/internal/models"
	"vibico-education-api/internal/repository"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
)

const (
	targetIDNameField    = "TargetID"
	targetTypeNameField  = "TargetType"
	contentNameField     = "Content"
	referenceIDNameField = "parentID"
)

type CommonForm struct {
	forms.Form
	repos repository.IRepositories

	ctx         context.Context
	input       *globalInputs.CommentCreateInput
	currentUser *models.User

	Comment *models.Comment
}

func (form *CommonForm) assignAttributes() {
	form.AddAttributes(
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: targetIDNameField,
			},
			Value: (*string)(form.input.TargetID),
		},
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: targetTypeNameField,
			},
			Value: form.input.TargetType,
		},
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: contentNameField,
			},
			Value: form.input.Content,
		},
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: referenceIDNameField,
			},
			Value: (*string)(form.input.ReferenceID),
		},
	)
}

func (form *CommonForm) validateTargetID() *CommonForm {
	field := form.GetAttribute(targetIDNameField)

	field.ValidateRequired(nil)

	if field.IsClean() {
		targetID, err := utils.ParseGraphqlID[uint32](*form.input.TargetID)
		if err != nil {
			field.AddError(err.Error())
		}

		form.Comment.TargetID = targetID
	}

	return form
}

func (form *CommonForm) validateTargetType() *CommonForm {
	field := form.GetAttribute(targetTypeNameField)

	field.ValidateRequired(nil)

	if !field.IsClean() {
		return form
	}

	fieldEnum, err := enums.ParseCommentTargetType(*form.input.TargetType)
	if err != nil {
		field.AddError(translator.Translate(nil, "errValidation_notExist"))
	}

	if field.IsClean() {
		form.Comment.TargetType = fieldEnum
	}

	return form
}

func (form *CommonForm) validateContent() *CommonForm {
	field := form.GetAttribute(contentNameField)

	field.ValidateRequired(nil)

	if field.IsClean() {
		form.Comment.Content = *form.input.Content
	}

	return form
}

func (form *CommonForm) validateReferenceID() *CommonForm {
	field := form.GetAttribute(referenceIDNameField)

	if field.Present() {
		parentID, err := utils.ParseGraphqlID[uint32](*form.input.ReferenceID)
		if err != nil {
			field.AddError(err.Error())
		}

		form.Comment.ParentID = &parentID
	}

	return form
}
