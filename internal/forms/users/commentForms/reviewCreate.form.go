package commentForms

import (
	"context"
	"errors"
	"vibico-education-api/internal/enums"
	"vibico-education-api/internal/forms"
	"vibico-education-api/internal/forms/formAttributes"
	globalInputs "vibico-education-api/internal/gqls/inputs/global"
	"vibico-education-api/internal/models"
	"vibico-education-api/internal/repository"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

const (
	ratingNameField = "Rating"
)

type ReviewCreateForm struct {
	CommonForm

	input *globalInputs.ReviewCreateInput
}

func NewReviewCreateForm(
	ctx context.Context,
	input globalInputs.ReviewCreateInput,
	currentUser *models.User,
	repos repository.IRepositories,
) *ReviewCreateForm {
	form := &ReviewCreateForm{
		CommonForm: CommonForm{
			Form:        forms.Form{},
			ctx:         ctx,
			currentUser: currentUser,
			Comment:     &models.Comment{AuthorID: currentUser.ID, AuthorType: "User", AuthorUser: currentUser},
			input:       &input.CommentCreateInput,
			repos:       repos,
		},
		input: &input,
	}

	form.assignAttributes()

	return form
}

func (form *ReviewCreateForm) assignAttributes() {
	form.CommonForm.assignAttributes()

	form.AddAttributes(
		&formAttributes.FloatAttribute[float64]{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: ratingNameField,
			},
			Value: form.input.Rating,
		},
	)
}

func (form *ReviewCreateForm) Save() error {
	if err := form.validate(); err != nil {
		return err
	}

	selectFields := append(form.GetAllCode(), "AuthorID", "AuthorType")

	if form.Comment.IsValidTargetTypeTeacher() {
		if form.Comment.ParentID != nil {
			courseId := *form.Comment.ParentID
			form.Comment.ParentID = nil

			err := form.repos.CommentRepo().CreateTeacherCourseReview(form.ctx, form.Comment, courseId, selectFields...)
			if err != nil {
				return err
			}
		}
		return nil
	}
	if err := form.repos.CommentRepo().Create(form.ctx, form.Comment, selectFields...); err != nil {
		return exceptions.NewUnprocessableContentError(err.Error(), nil)
	}

	return nil
}

func (form *ReviewCreateForm) validate() error {

	form.
		validateReferenceID().
		validateTargetID().
		validateTargetType().
		validateTarget().
		validateContent().
		validateRating().
		SummaryErrors()

	if form.IsValid() {
		return nil
	}

	return exceptions.NewUnprocessableContentError(translator.Translate(nil, "errValidationMsg_general"), form.Errors)
}

func (form *ReviewCreateForm) validateTargetID() *ReviewCreateForm {
	form.CommonForm.validateTargetID()

	return form
}

func (form *ReviewCreateForm) validateTargetType() *ReviewCreateForm {
	form.CommonForm.validateTargetType()

	return form
}

func (form *ReviewCreateForm) validateTarget() *ReviewCreateForm {
	fieldTargetID := form.GetAttribute(targetIDNameField)
	fieldTargetType := form.GetAttribute(targetTypeNameField)
	fieldReferenceID := form.GetAttribute(referenceIDNameField)

	if !fieldTargetID.IsClean() || !fieldTargetType.IsClean() {
		return form
	}

	switch form.Comment.TargetType {

	case enums.CommentTargetTypeTeacher:
		_, err := form.repos.TeacherRepo().FindById(form.ctx, form.Comment.TargetID)
		if err != nil {
			if !errors.Is(err, gorm.ErrRecordNotFound) {
				log.Error().Err(err).Ctx(form.ctx).Msg("something error when find teacher")
				fieldTargetID.AddError(translator.Translate(nil, "general_invalidId"))
			}
			return form
		}

		review, err := form.repos.CommentRepo().FindByTargetAndAuthor(
			form.ctx,
			form.Comment.TargetID,
			form.currentUser.ID,
			string(form.Comment.TargetType),
			"User")

		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			log.Error().Err(err).Ctx(form.ctx).Msg("something error when find comment")
			fieldTargetID.AddError(translator.Translate(nil, "general_invalidId"))
			return form
		}

		if review != nil {
			form.AddErrorDirectlyToField("base", []any{translator.Translate(nil, "errValidation_uniq")})
			return form
		}

		if form.Comment.ParentID != nil {
			results, err := form.repos.CourseUserRepo().FindByUserAndCourse(form.ctx, *form.Comment.ParentID, form.currentUser.ID)
			if err != nil {
				log.Error().Err(err).Ctx(form.ctx).Msg("something error when find course user")
				fieldReferenceID.AddError(translator.Translate(nil, "general_invalidId"))
				return form
			}

			if !results.IsCompleted() {
				form.AddErrorDirectlyToField("base", []any{translator.Translate(nil, "errDbMsg_CourseNotCompleted")})
				return form
			}

			if results.CourseUserMetadata != nil &&
				results.CourseUserMetadata.IsReviewTeacherInCourse != nil &&
				*results.CourseUserMetadata.IsReviewTeacherInCourse {
				form.AddErrorDirectlyToField("base", []any{translator.Translate(nil, "errValidation_existReviewInCourse")})
				return form
			}
		} else {
			fieldReferenceID.AddError(translator.Translate(nil, "errValidationMsg_required"))
			return form
		}

	case enums.CommentTargetTypeCourse:
		_, err := form.repos.CourseUserRepo().FindByUserEnrolledAndCourse(
			form.ctx,
			form.Comment.TargetID,
			form.currentUser.ID,
		)
		if err != nil {
			log.Error().Err(err).Ctx(form.ctx).Msg("something error when find course user")
			fieldTargetID.AddError(translator.Translate(nil, "general_invalidId"))
			return form
		}

		_, err = form.repos.CommentRepo().FindByTargetAndAuthor(
			form.ctx,
			form.Comment.TargetID,
			form.currentUser.ID,
			form.Comment.TargetType.String(),
			"User",
		)
		if err != nil {
			if !errors.Is(err, gorm.ErrRecordNotFound) {
				log.Error().Err(err).Ctx(form.ctx).Msg("something error when find review by target and author")
				fieldTargetID.AddError(err.Error())
			}

			return form
		}

		form.AddErrorDirectlyToField("base", []any{translator.Translate(nil, "errValidation_uniq")})
	default:
		fieldTargetType.AddError(translator.Translate(nil, "errValidation_notExist"))
	}

	return form
}

func (form *ReviewCreateForm) validateContent() *ReviewCreateForm {
	field := form.GetAttribute(contentNameField)

	if field.Present() {
		form.Comment.Content = *form.input.Content
	}

	return form
}

func (form *ReviewCreateForm) validateRating() *ReviewCreateForm {
	field := form.GetAttribute(ratingNameField)

	field.ValidateRequired(nil)

	if field.IsClean() {
		rating := float32(*form.input.Rating)
		form.Comment.Rating = &rating
	}

	return form
}

func (form *ReviewCreateForm) validateReferenceID() *ReviewCreateForm {
	form.CommonForm.validateReferenceID()

	return form
}
