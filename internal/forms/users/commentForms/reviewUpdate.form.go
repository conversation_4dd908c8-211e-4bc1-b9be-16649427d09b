package commentForms

import (
	"context"

	"vibico-education-api/internal/forms"
	"vibico-education-api/internal/forms/formAttributes"
	globalinputs "vibico-education-api/internal/gqls/inputs/global"
	"vibico-education-api/internal/models"
	"vibico-education-api/internal/repository"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/rs/zerolog/log"
)

type ReviewUpdateForm struct {
	forms.Form
	ctx         context.Context
	input       *globalinputs.ReviewUpdateInput
	Comment     *models.Comment
	repos       repository.IRepositories
	currentUser *models.User
	courseId    *uint32
}

func NewReviewUpdateForm(
	ctx context.Context,
	input globalinputs.ReviewUpdateInput,
	currentUser *models.User,
	comment *models.Comment,
	repos repository.IRepositories,
) *ReviewUpdateForm {
	form := &ReviewUpdateForm{
		Form:        forms.Form{},
		ctx:         ctx,
		input:       &input,
		Comment:     comment,
		repos:       repos,
		currentUser: currentUser,
	}

	form.assignAttribute()
	return form
}

func (form *ReviewUpdateForm) assignAttribute() {
	form.AddAttributes(
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: contentNameField,
			},
			Value: form.input.Content,
		},
		&formAttributes.FloatAttribute[float64]{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: ratingNameField,
			},
			Value: form.input.Rating,
		},
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: referenceIDNameField,
			},
			Value: (*string)(form.input.ReferenceID),
		},
	)
}

func (form *ReviewUpdateForm) Save() error {
	if err := form.validate(); err != nil {
		return err
	}

	selecteFields := []string{contentNameField, ratingNameField}

	if form.Comment.IsValidTargetTypeTeacher() {
		err := form.repos.CommentRepo().UpdateTeacherCourseReview(form.ctx, form.Comment, *form.courseId, selecteFields...)
		if err != nil {
			return err
		}
	}

	return nil

}

func (form *ReviewUpdateForm) validate() error {
	form.validateContent().
		validateRating().
		validateReferenceID().
		validateIsAbleToRateTeacherInCourse().
		SummaryErrors()

	if form.IsValid() {
		return nil
	}
	return exceptions.NewUnprocessableContentError(translator.Translate(nil, "errValidationMsg_general"), form.Errors)
}

func (form *ReviewUpdateForm) validateRating() *ReviewUpdateForm {
	fieldRating := form.GetAttribute(ratingNameField)

	fieldRating.ValidateRequired(nil)

	if fieldRating.IsClean() {
		rating := float32(*form.input.Rating)
		form.Comment.Rating = &rating
	}

	return form
}

func (form *ReviewUpdateForm) validateContent() *ReviewUpdateForm {
	fieldContent := form.GetAttribute(contentNameField)

	if fieldContent.Present() {
		form.Comment.Content = *form.input.Content

	}

	return form
}

func (form *ReviewUpdateForm) validateReferenceID() *ReviewUpdateForm {
	fieldReferenceID := form.GetAttribute(referenceIDNameField)

	if form.Comment.IsValidTargetTypeTeacher() {
		fieldReferenceID.ValidateRequired(nil)

		if !fieldReferenceID.IsClean() {
			return form
		}
		courseId, err := utils.ParseGraphqlID[uint32](*form.input.ReferenceID)
		if err != nil {
			log.Error().Err(err).Ctx(form.ctx).Msg("something error when parsing referenceID")
			fieldReferenceID.AddError(translator.Translate(nil, "general_invalidId"))
			return form
		}

		form.courseId = &courseId

	}

	return form
}

func (form *ReviewUpdateForm) validateIsAbleToRateTeacherInCourse() *ReviewUpdateForm {
	fieldReferenceID := form.GetAttribute(referenceIDNameField)

	if !fieldReferenceID.IsClean() {
		return form
	}

	review, err := form.repos.CourseUserRepo().FindByUserAndCourse(form.ctx, *form.courseId, form.currentUser.ID)
	if err != nil {
		log.Error().Err(err).Ctx(form.ctx).Msg("something error when finding course user")
		fieldReferenceID.AddError(translator.Translate(nil, "general_invalidId"))
		return form
	}

	if !review.IsCompleted() {
		fieldReferenceID.AddError(translator.Translate(nil, "errDbMsg_CourseNotCompleted"))
		return form
	}

	if review.CourseUserMetadata != nil &&
		review.CourseUserMetadata.IsReviewTeacherInCourse != nil &&
		*review.CourseUserMetadata.IsReviewTeacherInCourse {
		fieldReferenceID.AddError(translator.Translate(nil, "errValidation_existReviewInCourse"))
		return form
	}
	return form
}
