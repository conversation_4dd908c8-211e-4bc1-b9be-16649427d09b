package userForms

import (
	"context"
	"vibico-education-api/internal/forms"
	"vibico-education-api/internal/forms/formAttributes"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
)

const (
	verifyCodeField = "VerifyCode"
)

type JoinCourseForm struct {
	forms.Form

	ctx        context.Context
	verifyCode *string
}

func NewJoinCourseForm(ctx context.Context, verifyCode *string) *JoinCourseForm {
	form := &JoinCourseForm{
		ctx:        ctx,
		verifyCode: verifyCode,
	}
	form.assignAttributes()
	return form
}

func (form *JoinCourseForm) assignAttributes() {
	form.AddAttributes(
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: verifyCodeField,
			},
			Value: form.verifyCode,
		},
	)
}

func (form *JoinCourseForm) Validate() error {
	form.validateVerifyCode().
		SummaryErrors()

	if form.IsValid() {
		return nil
	}

	return exceptions.NewUnprocessableContentError(
		translator.Translate(nil, "errValidationMsg_general"),
		form.Errors,
	)
}

func (form *JoinCourseForm) validateVerifyCode() *JoinCourseForm {
	field := form.GetAttribute(verifyCodeField)

	field.ValidateRequired(nil)
	field.ValidateFormat(`^\d{6}$`, nil)

	return form
}
