package infoForms

import (
	"context"
	"time"
	"vibico-education-api/internal/enums"
	"vibico-education-api/internal/forms"
	"vibico-education-api/internal/forms/formAttributes"
	userInputs "vibico-education-api/internal/gqls/inputs/users"
	"vibico-education-api/internal/models"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/constants"
	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
)

const (
	nameNameField      = "Name"
	genderNameField    = "Gender"
	birthDateNameField = "BirthDate"
	ImageUrlNameField  = "ImageUrl"
)

type IUserRepository interface {
	Update(ctx context.Context, user *models.User, selectFields ...string) error
}

type UpdateForm struct {
	forms.Form
	ctx   context.Context
	input *userInputs.SelfInfoInput
	user  *models.User
	repo  IUserRepository
}

func NewUpdateForm(
	ctx context.Context,
	input userInputs.SelfInfoInput,
	user *models.User,
	repo IUserRepository,
) *UpdateForm {
	form := &UpdateForm{
		ctx:   ctx,
		input: &input,
		user:  user,
		repo:  repo,
	}
	form.assignAttributes()

	return form
}

func (form *UpdateForm) assignAttributes() {
	form.AddAttributes(
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: nameNameField,
			},
			Value: form.input.Name,
		},
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: genderNameField,
			},
			Value: form.input.Gender,
		},
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: birthDateNameField,
			},
			Value: form.input.BirthDate,
		},
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: ImageUrlNameField,
			},
			Value: form.input.ImageUrl,
		},
	)
}

func (form *UpdateForm) Save() error {
	if err := form.validate(); err != nil {
		return err
	}

	selectFields := []string{nameNameField, genderNameField, birthDateNameField, ImageUrlNameField}

	if err := form.repo.Update(form.ctx, form.user, selectFields...); err != nil {
		return err
	}
	return nil
}

func (form *UpdateForm) validate() error {
	form.
		validateName().
		validateGender().
		validateBirthDate().
		validateImageUrl().
		SummaryErrors()

	if form.IsValid() {
		return nil
	}

	return exceptions.NewUnprocessableContentError(translator.Translate(nil, "errValidationMsg_general"), form.Errors)
}

func (form *UpdateForm) validateName() *UpdateForm {
	field := form.GetAttribute(nameNameField)
	field.ValidateRequired(nil)
	field.ValidateLtEq(constants.MaxLength30, nil)
	if field.IsClean() {
		form.user.Name = *form.input.Name
	}
	return form
}

func (form *UpdateForm) validateGender() *UpdateForm {
	field := form.GetAttribute(genderNameField)
	field.ValidateRequired(nil)
	fieldValue := enums.Gender(*form.input.Gender)
	if !fieldValue.IsValid() {
		field.AddError(translator.Translate(nil, "errValidation_notExist"))
	}

	if field.IsClean() {
		gender := fieldValue.String()
		form.user.Gender = &gender
	}
	return form
}

func (form *UpdateForm) validateBirthDate() *UpdateForm {
	field := form.GetAttribute(birthDateNameField)
	field.ValidateRequired(nil)
	if field.IsClean() {
		birthDate, err := time.Parse(constants.YYYYMMDD_DateFormat, *form.input.BirthDate)
		if err != nil {
			field.AddError(translator.Translate(nil, "errValidationMsg_general"))
			return form
		}
		form.user.BirthDate = &birthDate
	}
	return form
}

func (form *UpdateForm) validateImageUrl() *UpdateForm {
	field := form.GetAttribute(ImageUrlNameField)
	if field.Blank() {
		return form
	}

	field.ValidateFormat(constants.UrlFormat, nil)
	if field.IsClean() {
		form.user.ImageUrl = form.input.ImageUrl
	}
	return form
}
