package userAuthForms

import (
	"context"
	"vibico-education-api/internal/forms"
	"vibico-education-api/internal/forms/formAttributes"
	userInputs "vibico-education-api/internal/gqls/inputs/users"
	"vibico-education-api/internal/models"
	"vibico-education-api/pkg/helpers"
	translator "vibico-education-api/pkg/translators"
	"vibico-education-api/setup/grpc_clients"

	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
	"github.com/rs/zerolog/log"

	pb "github.com/BehemothLtd/vibico-auth/proto/auth"
)

const (
	passwordFieldName        = "Password"
	newPasswordFieldName     = "NewPassword"
	confirmPasswordFieldName = "ConfirmPassword"
)

type UserChangePasswordForm struct {
	forms.Form

	ctx         context.Context
	currentUser *models.User
	input       userInputs.ChangePasswordInput
}

func NewUserChangePasswordForm(
	ctx context.Context, currentUser *models.User, input userInputs.ChangePasswordInput,
) *UserChangePasswordForm {
	form := &UserChangePasswordForm{
		ctx:         ctx,
		currentUser: currentUser,
		input:       input,
	}
	form.assignAttributes()

	return form
}

func (form *UserChangePasswordForm) assignAttributes() {
	form.AddAttributes(
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: passwordFieldName,
			},
			Value: form.input.Password,
		},
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: newPasswordFieldName,
			},
			Value: form.input.NewPassword,
		},
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: confirmPasswordFieldName,
			},
			Value: form.input.ConfirmPassword,
		},
	)
}

func (form *UserChangePasswordForm) validate() error {
	form.validatePassword().
		validateNewPassword().
		validateConfirmPassword().
		SummaryErrors()

	if form.IsValid() {
		return nil
	}

	return exceptions.NewUnprocessableContentError(translator.Translate(nil, "errValidationMsg_general"), form.Errors)
}

func (form *UserChangePasswordForm) validatePassword() *UserChangePasswordForm {
	log.Debug().Ctx(form.ctx).Msg("UserRepository.validatePassword")

	field := form.GetAttribute(passwordFieldName)

	field.ValidateRequired(nil)

	return form
}

func (form *UserChangePasswordForm) validateNewPassword() *UserChangePasswordForm {
	log.Debug().Ctx(form.ctx).Msg("UserRepository.validateNewPassword")

	field := form.GetAttribute(newPasswordFieldName)

	field.ValidateRequired(nil)
	if field.IsClean() {
		if !helpers.IsValidPassword(*form.input.NewPassword) {
			field.AddError(translator.Translate(nil, "errValidationMsg_passwordFormat"))
		}
	}

	return form
}

func (form *UserChangePasswordForm) validateConfirmPassword() *UserChangePasswordForm {
	log.Debug().Ctx(form.ctx).Msg("UserRepository.validateConfirmPassword")

	field := form.GetAttribute(confirmPasswordFieldName)
	newPassword := form.GetAttribute(newPasswordFieldName)

	field.ValidateRequired(nil)
	if newPassword.IsClean() && field.IsClean() {
		if *form.input.NewPassword != *form.input.ConfirmPassword {
			field.AddError(translator.Translate(nil, "errValidationMsg_passwordConfirmation"))
		}
	}
	if field.IsClean() {
		if !helpers.IsValidPassword(*form.input.ConfirmPassword) {
			field.AddError(translator.Translate(nil, "errValidationMsg_passwordFormat"))
		}
	}

	return form
}

func (form *UserChangePasswordForm) Save() error {
	if err := form.validate(); err != nil {
		return err
	}

	_, err := grpc_clients.AuthClient().UpdatePassword(
		grpc_clients.NewCtx(form.ctx),
		&pb.UserUpdatePasswordRequest{
			IdentityPoolId: grpc_clients.PoolId(),
			UserId:         form.currentUser.AuthId,
			Password:       *form.input.Password,
			NewPassword:    *form.input.NewPassword,
		},
	)

	if err != nil {
		return exceptions.NewUnprocessableContentError(translator.Translate(nil, "errDbMsg_IncorrectPassword"), nil)
	}

	return nil
}
