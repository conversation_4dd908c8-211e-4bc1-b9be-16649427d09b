package infoForms

import (
	"context"
	vCts "vibico-education-api/constants"
	"vibico-education-api/internal/forms"
	"vibico-education-api/internal/forms/formAttributes"
	teacherInputs "vibico-education-api/internal/gqls/inputs/teachers"
	"vibico-education-api/internal/models"
	"vibico-education-api/internal/repository/repositories"
	"vibico-education-api/pkg/helpers"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/constants"
	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
	"gorm.io/gorm"
)

const (
	contactEmailNameField = "ContactEmail"
	nameNameField         = "Name"
	slugNameField         = "Slug"
	descriptionNameField  = "Description"
	phoneNumberNameField  = "PhoneNumber"
	addressNameField      = "Address"
	basicEnteredNameField = "BasicEntered"
	ImageUrlNameField     = "ImageUrl"
)

type ITeacherRepository interface {
	Update(ctx context.Context, teacher *models.Teacher, selectFields ...string) error
	FindByContactEmail(ctx context.Context, email string, preloads ...repositories.CustomPreload) (*models.Teacher, error)
}

type CommonForm struct {
	forms.Form

	ctx context.Context

	input       *teacherInputs.CommonInfoInput
	Teacher     *models.Teacher
	TeacherRepo ITeacherRepository
}

func (form *CommonForm) assignAttributes() {
	form.AddAttributes(
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: contactEmailNameField,
			},
			Value: form.input.ContactEmail,
		},
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: nameNameField,
			},
			Value: form.input.Name,
		},
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: phoneNumberNameField,
			},
			Value: form.input.PhoneNumber,
		},
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: addressNameField,
			},
			Value: form.input.Address,
		},
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: descriptionNameField,
			},
			Value: form.input.Description,
		},
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: ImageUrlNameField,
			},
			Value: form.input.ImageUrl,
		},
	)
}

func (form *CommonForm) validate() error {
	form.
		validateName().
		validateContactEmail().
		validatePhoneNumber().
		validateAddress().
		validateDescription().
		validateImageUrl().
		SummaryErrors()

	if form.IsValid() {
		return nil
	}

	return exceptions.NewUnprocessableContentError(translator.Translate(nil, "errValidationMsg_general"), form.Errors)
}

func (form *CommonForm) validateName() *CommonForm {
	field := form.GetAttribute(nameNameField)

	field.ValidateRequired(nil)
	field.ValidateLtEq(constants.MaxLength30, nil)

	if field.IsClean() {
		form.Teacher.Name = *form.input.Name
	}

	return form
}

func (form *CommonForm) validateSlug() *CommonForm {
	field := form.GetAttribute(nameNameField)

	if field.IsClean() {
		form.Teacher.Slug = helpers.GenerateSlug(*form.input.Name)
	}

	return form
}

func (form *CommonForm) validateContactEmail() *CommonForm {
	field := form.GetAttribute(contactEmailNameField)

	field.ValidateRequired(nil)
	field.ValidateFormat(constants.EmailFormat, nil)
	field.ValidateLtEq(constants.MaxStringLength, nil)

	if field.IsClean() && form.input.ContactEmail != nil {
		existing, err := form.TeacherRepo.FindByContactEmail(form.ctx, *form.input.ContactEmail)
		switch {
		case err != nil && err != gorm.ErrRecordNotFound:
			field.AddError(translator.Translate(nil, "errValidation_uniq"))
		case err == gorm.ErrRecordNotFound:
			form.Teacher.ContactEmail = form.input.ContactEmail
		default:
			if existing != nil && existing.ID != form.Teacher.ID {
				field.AddError(translator.Translate(nil, "errValidation_uniq"))
			} else if field.IsClean() {
				form.Teacher.ContactEmail = form.input.ContactEmail
			}
		}
	} else if field.IsClean() {
		form.Teacher.ContactEmail = form.input.ContactEmail
	}
	return form
}

func (form *CommonForm) validatePhoneNumber() *CommonForm {
	field := form.GetAttribute(phoneNumberNameField)
	field.ValidateRequired(nil)
	field.ValidateFormat(vCts.PhoneNumberFormatter, nil)
	if field.IsClean() {
		form.Teacher.PhoneNumber = form.input.PhoneNumber
	}
	return form
}

func (form *CommonForm) validateAddress() *CommonForm {
	field := form.GetAttribute(addressNameField)
	field.ValidateLtEq(constants.MaxStringLength, nil)
	if field.IsClean() {
		form.Teacher.Address = form.input.Address
	}
	return form
}

func (form *CommonForm) validateDescription() *CommonForm {
	field := form.GetAttribute(descriptionNameField)
	field.ValidateLtEq(constants.MaxStringLength, nil)

	if field.IsClean() {
		form.Teacher.Description = form.input.Description
	}
	return form
}

func (form *CommonForm) validateImageUrl() *CommonForm {
	field := form.GetAttribute(ImageUrlNameField)
	if field.Blank() {
		return form
	}

	field.ValidateFormat(constants.UrlFormat, nil)
	if field.IsClean() {
		form.Teacher.ImageUrl = form.input.ImageUrl
	}
	return form
}
