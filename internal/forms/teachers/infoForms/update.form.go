package infoForms

import (
	"context"
	"vibico-education-api/internal/forms"
	teacherInputs "vibico-education-api/internal/gqls/inputs/teachers"
	"vibico-education-api/internal/models"
)

type UpdateForm struct {
	CommonForm
}

func NewUpdateForm(
	ctx context.Context,
	input teacherInputs.UpdateSelfInfoInput,
	teacher *models.Teacher,
	teacherRepo ITeacherRepository,
) *UpdateForm {
	form := &UpdateForm{
		CommonForm: CommonForm{
			ctx:         ctx,
			Form:        forms.Form{},
			Teacher:     teacher,
			input:       &input.CommonInfoInput,
			TeacherRepo: teacherRepo,
		},
	}

	form.assignAttributes()

	return form
}

func (form *UpdateForm) Save() error {
	if err := form.validate(); err != nil {
		return err
	}

	selectFields := []string{nameNameField, contactEmailNameField, phoneNumberNameField, addressNameField, descriptionNameField, ImageUrlNameField}

	if err := form.TeacherRepo.Update(form.ctx, form.Teacher, selectFields...); err != nil {
		return err
	}
	return nil
}
