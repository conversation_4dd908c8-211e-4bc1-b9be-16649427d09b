package infoForms

import (
	"context"
	"vibico-education-api/internal/forms"
	teacherInputs "vibico-education-api/internal/gqls/inputs/teachers"
	"vibico-education-api/internal/models"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
)

type BasicSetupForm struct {
	CommonForm
}

func NewBasicSetupForm(
	ctx context.Context,
	input teacherInputs.BasicSetupInput,
	teacher *models.Teacher,
	teacherRepo ITeacherRepository,
) *BasicSetupForm {
	form := &BasicSetupForm{
		CommonForm: CommonForm{
			ctx:         ctx,
			Form:        forms.Form{},
			Teacher:     teacher,
			input:       &input.CommonInfoInput,
			TeacherRepo: teacherRepo,
		},
	}

	form.assignAttributes()

	return form
}

func (form *BasicSetupForm) Save() error {
	if err := form.validate(); err != nil {
		return err
	}

	selectFields := []string{nameN<PERSON><PERSON>ield, slugN<PERSON><PERSON>ield, contact<PERSON><PERSON><PERSON><PERSON><PERSON>ield, address<PERSON><PERSON><PERSON>ield, description<PERSON><PERSON><PERSON>ield, basicE<PERSON>ed<PERSON><PERSON><PERSON>ield, ImageUrlNameField}
	form.Teacher.BasicEntered = true

	if err := form.TeacherRepo.Update(form.ctx, form.Teacher, selectFields...); err != nil {
		return err
	}
	return nil
}

func (form *BasicSetupForm) validate() error {
	form.
		validateName().
		validateSlug().
		validateContactEmail().
		validatePhoneNumber().
		validateAddress().
		validateDescription().
		validateImageUrl().
		SummaryErrors()

	if form.IsValid() {
		return nil
	}

	return exceptions.NewUnprocessableContentError(translator.Translate(nil, "errValidationMsg_general"), form.Errors)
}
