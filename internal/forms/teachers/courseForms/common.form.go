package courseForms

import (
	"context"
	"vibico-education-api/internal/enums"
	"vibico-education-api/internal/forms"
	"vibico-education-api/internal/forms/formAttributes"
	teacherInputs "vibico-education-api/internal/gqls/inputs/teachers"
	"vibico-education-api/internal/models"
	"vibico-education-api/pkg/helpers"
	translator "vibico-education-api/pkg/translators"

	vbcConstants "vibico-education-api/constants"

	"github.com/BehemothLtd/behemoth-pkg/golang/constants"
	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
)

const (
	titleNameField              = "Title"
	descriptionNameField        = "Description"
	salePriceNameField          = "SalePrice"
	priceNameField              = "Price"
	bonusPointNameField         = "BonusPoint"
	bonusPointPercentNameField  = "BonusPointPercent"
	bannerNameField             = "Banner"
	isPublicNameField           = "IsPublic"
	instructionalLevelNameField = "InstructionalLevel"
)

type CommonForm struct {
	forms.Form

	ctx context.Context

	input  *teacherInputs.CourseCommonInput
	Course *models.Course
}

func (form *CommonForm) assignAttributes() {
	form.AddAttributes(
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: titleNameField,
			},
			Value: form.input.Title,
		},
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: descriptionNameField,
			},
			Value: form.input.Description,
		},
		&formAttributes.IntAttribute[int32]{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: salePriceNameField,
			},
			Value: form.input.SalePrice,
		},
		&formAttributes.IntAttribute[int32]{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: priceNameField,
			},
			Value: form.input.Price,
		},
		&formAttributes.IntAttribute[int32]{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: bonusPointNameField,
			},
			Value: form.input.BonusPoint,
		},
		&formAttributes.IntAttribute[int32]{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: bonusPointPercentNameField,
			},
			Value: form.input.BonusPointPercent,
		},
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: bannerNameField,
			},
			Value: form.input.Banner,
		},
		&formAttributes.BoolAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: isPublicNameField,
			},
			Value: form.input.IsPublic,
		},
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: instructionalLevelNameField,
			},
			Value: form.input.InstructionalLevel,
		},
	)
}

func (form *CommonForm) validate() error {
	form.validateTitle().
		validateDescription().
		validatePrice().
		validateSalePrice().
		validateBonusPoint().
		validateBonusPointPercent().
		validateBanner().
		validateIsPublic().
		validateInstructionalLevel().
		SummaryErrors()

	if form.IsValid() {
		return nil
	}

	return exceptions.NewUnprocessableContentError(translator.Translate(nil, "errValidationMsg_general"), form.Errors)
}

func (form *CommonForm) validateTitle() *CommonForm {
	field := form.GetAttribute(titleNameField)

	field.ValidateRequired(nil)
	field.ValidateLtEq(constants.MaxStringLength, nil)

	if field.IsClean() {
		if form.Course.Title != *form.input.Title {
			slug := helpers.GenerateSlug(*form.input.Title)
			form.Course.Slug = slug
		}
		form.Course.Title = *form.input.Title
	}

	return form
}

func (form *CommonForm) validateDescription() *CommonForm {
	field := form.GetAttribute(descriptionNameField)
	field.ValidateLtEq(vbcConstants.MaxTextLength, nil)

	if field.IsClean() {
		form.Course.Description = form.input.Description
	}

	return form
}

func (form *CommonForm) validatePrice() *CommonForm {
	field := form.GetAttribute(priceNameField)

	field.ValidateGtEq(0, nil)

	if field.IsClean() {
		form.Course.Price = form.input.Price
	}

	return form
}

func (form *CommonForm) validateSalePrice() *CommonForm {
	field := form.GetAttribute(salePriceNameField)

	price := form.Course.Price

	if price != nil {
		field.ValidateLtEq(*price, nil)
	}

	field.ValidateGtEq(0, nil)

	if field.IsClean() {
		form.Course.SalePrice = form.input.SalePrice
	}

	return form
}

func (form *CommonForm) validateBonusPoint() *CommonForm {
	field := form.GetAttribute(bonusPointNameField)

	field.ValidateGtEq(0, nil)

	if field.IsClean() {
		form.Course.BonusPoint = form.input.BonusPoint
	}

	return form
}

func (form *CommonForm) validateBonusPointPercent() *CommonForm {
	field := form.GetAttribute(bonusPointPercentNameField)

	field.ValidateGtEq(0, nil)
	field.ValidateLtEq(100, nil)

	if field.IsClean() {
		form.Course.BonusPointPercent = form.input.BonusPointPercent
	}

	return form
}

func (form *CommonForm) validateBanner() *CommonForm {
	field := form.GetAttribute(bannerNameField)

	field.ValidateLtEq(constants.MaxStringLength, nil)
	field.ValidateFormat(constants.UrlFormat, nil)

	if field.IsClean() {
		form.Course.Banner = form.input.Banner
	}

	return form
}

func (form *CommonForm) validateIsPublic() *CommonForm {
	field := form.GetAttribute(isPublicNameField)

	if field.Present() {
		form.Course.IsPublic = *form.input.IsPublic
	}

	return form
}

func (form *CommonForm) validateInstructionalLevel() *CommonForm {
	field := form.GetAttribute(instructionalLevelNameField)

	if field.Present() {
		instructionalLevel, err := enums.ParseCourseInstructionalLevel(*form.input.InstructionalLevel)
		if err != nil {
			field.AddError(translator.Translate(nil, "errValidationMsg_invalid"))
			return form
		}

		form.Course.InstructionalLevel = instructionalLevel
	}

	return form
}
