package courseForms

import (
	"context"
	"vibico-education-api/internal/enums"
	"vibico-education-api/internal/forms"
	teacherInputs "vibico-education-api/internal/gqls/inputs/teachers"
	"vibico-education-api/internal/models"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
)

type ICourseUpdateRepository interface {
	Update(ctx context.Context, clubUser *models.Course, selectFields ...string) error
}

type UpdateForm struct {
	CommonForm
	courseUpdateRepo ICourseUpdateRepository

	input *teacherInputs.CourseUpdateFormInput
}

func NewUpdateForm(
	ctx context.Context,
	input *teacherInputs.CourseUpdateFormInput,
	courseUpdateRepo ICourseUpdateRepository,
	course *models.Course,
) *UpdateForm {
	form := &UpdateForm{
		CommonForm: CommonForm{
			ctx:    ctx,
			Form:   forms.Form{},
			Course: course,
			input:  &input.CourseCommonInput,
		},

		input:            input,
		courseUpdateRepo: courseUpdateRepo,
	}

	form.assignAttributes()

	return form
}

func (form *UpdateForm) Save() error {
	if err := form.validate(); err != nil {
		return err
	}

	selectFields := append(form.GetAllCode(), "Slug", "Status")
	if form.Course.IsApproved() {
		selectFields = []string{priceNameField, salePriceNameField}
	} else {
		var filteredFields []string
		for _, field := range selectFields {
			if field != isPublicNameField {
				filteredFields = append(filteredFields, field)
			}
		}

		selectFields = filteredFields
		form.Course.Status = enums.CourseStatusDraft
	}

	if err := form.courseUpdateRepo.Update(form.ctx, form.Course, selectFields...); err != nil {
		return exceptions.NewUnprocessableContentError(err.Error(), nil)
	}

	return nil
}

func (form *UpdateForm) validate() error {
	form.validatePrice().
		validateSalePrice()

	if !form.Course.IsApproved() {
		form.validateTitle().
			validateDescription().
			validateBonusPoint().
			validateBonusPointPercent().
			validateBanner().
			validateInstructionalLevel()
	}

	form.SummaryErrors()

	if form.IsValid() {
		return nil
	}

	return exceptions.NewUnprocessableContentError(translator.Translate(nil, "errValidationMsg_general"), form.Errors)
}
