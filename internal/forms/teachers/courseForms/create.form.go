package courseForms

import (
	"context"
	"vibico-education-api/internal/enums"
	"vibico-education-api/internal/forms"
	teacherInputs "vibico-education-api/internal/gqls/inputs/teachers"
	"vibico-education-api/internal/models"

	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
)

type ICourseCreateRepository interface {
	Create(ctx context.Context, clubUser *models.Course, selectFields ...string) error
}

type IPackageDealRepository interface {
	FindFundamentalPackage(ctx context.Context) (*[]*models.PackageDeal, error)
}

type CreateForm struct {
	CommonForm

	courseCreateRepo ICourseCreateRepository
	packageDealRepo  IPackageDealRepository

	input *teacherInputs.CourseCreateInput
}

func NewCreateForm(
	ctx context.Context,
	input *teacherInputs.CourseCreateInput,
	courseCreateRepo ICourseCreateRepository,
	packageDealRepo IPackageDealRepository,
	currentTeacher *models.Teacher,
) *CreateForm {
	form := &CreateForm{
		CommonForm: CommonForm{
			ctx:    ctx,
			Form:   forms.Form{},
			Course: &models.Course{TeacherId: currentTeacher.ID, Status: enums.CourseStatusDraft},
			input:  &input.CourseCommonInput,
		},

		input:            input,
		courseCreateRepo: courseCreateRepo,
		packageDealRepo:  packageDealRepo,
	}

	form.assignAttributes()

	return form
}

func (form *CreateForm) Save() error {
	if err := form.validate(); err != nil {
		return err
	}

	selectFields := append(form.GetAllCode(), "TeacherId", "Status", "Slug")

	fundamentalPackages, err := form.packageDealRepo.FindFundamentalPackage(form.ctx)
	if err != nil {
		return exceptions.NewUnprocessableContentError(err.Error(), nil)
	}
	coursePackages := []*models.CoursePackage{}
	if fundamentalPackages != nil && len(*fundamentalPackages) > 0 {
		for _, packageDeal := range *fundamentalPackages {
			coursePackage := &models.CoursePackage{
				PackageDealID: packageDeal.ID,
			}
			coursePackages = append(coursePackages, coursePackage)
		}

		form.Course.CoursePackages = &coursePackages
		selectFields = append(selectFields, "CoursePackages")
	}

	if err := form.courseCreateRepo.Create(form.ctx, form.Course, selectFields...); err != nil {
		return exceptions.NewUnprocessableContentError(err.Error(), nil)
	}

	return nil
}
