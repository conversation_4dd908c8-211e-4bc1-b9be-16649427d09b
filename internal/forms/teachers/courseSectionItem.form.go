package teacherForms

import (
	"context"
	"vibico-education-api/internal/enums"
	"vibico-education-api/internal/forms"
	"vibico-education-api/internal/forms/formAttributes"
	teacherInputs "vibico-education-api/internal/gqls/inputs/teachers"
	"vibico-education-api/internal/models"
	"vibico-education-api/internal/repository/repositories"
	"vibico-education-api/pkg/helpers"

	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/constants"
	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
	"github.com/rs/zerolog/log"

	vbcConstants "vibico-education-api/constants"
)

const (
	titleFieldName   = "Title"
	contentFieldName = "Content"
	typeFieldName    = "Type"
)

type ICourseSectionItemRepository interface {
	Create(ctx context.Context, sectionItem *models.CourseSectionItem, courseId uint32, selectFields []string) error
	Update(ctx context.Context, sectionItem *models.CourseSectionItem, courseId uint32, selectFields []string) error
	FindByIdAndTeacherCourse(ctx context.Context, id uint32, courseSectionId uint32, courseId uint32, teacherId uint32, preloads ...repositories.CustomPreload) (*models.CourseSectionItem, error)
}

type IDrillRepository interface {
	FindByIDAndOwner(ctx context.Context, id, ownerId uint32, ownerType string, preloads ...repositories.CustomPreload) (*models.Drill, error)
}

type MutateForm struct {
	forms.Form

	ctx                   context.Context
	courseSectionItemRepo ICourseSectionItemRepository
	drillRepo             IDrillRepository

	input             *teacherInputs.CourseSectionItemInput
	CourseSectionItem *models.CourseSectionItem
	currentTeacher    *models.Teacher
	courseId          uint32
	TypeEnum          enums.CourseSectionItemType
}

func NewCourseSectionItemMutateForm(
	ctx context.Context,
	input *teacherInputs.CourseSectionItemInput,
	courseSectionItemRepo ICourseSectionItemRepository,
	drillRepo IDrillRepository,

	courseSectionItem *models.CourseSectionItem,
	currentTeacher *models.Teacher,
	courseId uint32,
) *MutateForm {
	form := &MutateForm{
		ctx:                   ctx,
		Form:                  forms.Form{},
		input:                 input,
		courseSectionItemRepo: courseSectionItemRepo,
		drillRepo:             drillRepo,

		CourseSectionItem: courseSectionItem,
		currentTeacher:    currentTeacher,
		courseId:          courseId,
	}

	form.assignAttributes()
	return form
}

func (form *MutateForm) assignAttributes() {
	form.AddAttributes(
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: titleFieldName,
			},
			Value: form.input.Title,
		},
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: typeFieldName,
			},
			Value: form.input.Type,
		},
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: contentFieldName,
			},
			Value: form.input.Content,
		},
	)
}

func (form *MutateForm) validate() error {
	log.Debug().Ctx(form.ctx).Msg("CourseSectionItemForm.MutateForm.Validate")

	form.validateTitle().
		validateType().
		validateContent().
		// validateDrillIds().
		SummaryErrors()

	if form.IsValid() {
		return nil
	}

	return exceptions.NewUnprocessableContentError(translator.Translate(nil, "errValidationMsg_general"), form.Errors)
}

func (form *MutateForm) validateContent() *MutateForm {
	log.Debug().Ctx(form.ctx).Msg("CourseSectionItemForm.MutateForm.validateContent")

	content := form.GetAttribute(contentFieldName)
	content.ValidateLtEq(vbcConstants.MaxTextLength, nil)

	if content.IsClean() {
		form.CourseSectionItem.Content = form.input.Content
	}

	return form
}

func (form *MutateForm) validateTitle() *MutateForm {
	log.Debug().Ctx(form.ctx).Msg("CourseSectionItemForm.MutateForm.validateTitle")

	title := form.GetAttribute(titleFieldName)

	title.ValidateRequired(nil)
	title.ValidateLtEq(constants.MaxStringLength, nil)

	if title.IsClean() {
		if form.CourseSectionItem.Title != *form.input.Title {
			form.CourseSectionItem.Slug = helpers.GenerateSlug(*form.input.Title)
		}

		form.CourseSectionItem.Title = *form.input.Title
	}

	return form
}

func (form *MutateForm) validateType() *MutateForm {
	log.Debug().Ctx(form.ctx).Msg("CourseSectionItemForm.MutateForm.validateType")

	typeField := form.GetAttribute(typeFieldName)
	typeField.ValidateRequired(nil)

	if typeField.IsClean() {
		form.TypeEnum = enums.CourseSectionItemType(*form.input.Type)

		if !form.TypeEnum.IsValid() {
			typeField.AddError(translator.Translate(nil, "errValidation_notExist"))
		}

		if typeField.IsClean() {
			form.CourseSectionItem.Type = form.TypeEnum
		}
	}

	return form
}

// func (form *MutateForm) validateDrillIds() *MutateForm {
// 	log.Debug().Ctx(form.ctx).Msg("CourseSectionItemForm.MutateForm.validateDrillIds")

// 	if form.input.DrillIds == nil || form.TypeEnum != enums.CourseSectionItemTypeDrill {
// 		return form
// 	}

// 	uniqueDrillIds := make(map[int32]struct{})
// 	acceptedDrillIds := make([]uint32, 0)

// 	for _, drillId := range *form.input.DrillIds {
// 		if _, exists := uniqueDrillIds[drillId]; !exists {
// 			uniqueDrillIds[drillId] = struct{}{}
// 			acceptedDrillIds = append(acceptedDrillIds, uint32(drillId))
// 		}
// 	}

// 	itemDrills := make([]*models.CourseSectionItemDrill, 0)
// 	for i, id := range acceptedDrillIds {
// 		drill, err := form.drillRepo.FindByIDAndOwner(
// 			form.ctx,
// 			uint32(id),
// 			form.currentTeacher.ID,
// 			"Teacher",
// 		)
// 		if err != nil {
// 			form.AddNestedErrors("drills", i, exceptions.ResourceModificationError{
// 				"id": []interface{}{translator.Translate(nil, "errDbMsg_notFound")},
// 			})
// 			continue
// 		}

// 		itemDrills = append(itemDrills, &models.CourseSectionItemDrill{
// 			DrillId:             drill.ID,
// 			CourseSectionItemId: form.CourseSectionItem.ID,
// 		})
// 	}
// 	if !form.IsValid() {
// 		return form
// 	}

// 	form.CourseSectionItem.CourseSectionItemDrills = &itemDrills
// 	return form
// }

func (form *MutateForm) Create() error {
	log.Debug().Ctx(form.ctx).Msg("CourseSectionItemForm.MutateForm.Create")

	if validateErr := form.validate(); validateErr != nil {
		return validateErr
	}

	selectFields := append(form.GetAllCode(), "CourseSectionId", "Slug", "Position", "CourseId")

	return form.courseSectionItemRepo.Create(form.ctx, form.CourseSectionItem, form.courseId, selectFields)
}

func (form *MutateForm) Update() error {
	log.Debug().Ctx(form.ctx).Msg("CourseSectionItemForm.MutateForm.Update")

	if validateErr := form.validate(); validateErr != nil {
		return validateErr
	}

	selectFields := append(form.GetAllCode(), "Slug")

	return form.courseSectionItemRepo.Update(form.ctx, form.CourseSectionItem, form.courseId, selectFields)
}
