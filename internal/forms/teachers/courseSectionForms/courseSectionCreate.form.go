package courseSectionForms

import (
	"context"
	"vibico-education-api/internal/forms"
	teacherInputs "vibico-education-api/internal/gqls/inputs/teachers"
	"vibico-education-api/internal/models"

	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
	"github.com/rs/zerolog/log"
)

type ICourseSectionRepositoryForCourseSectionCreateForm interface {
	Create(ctx context.Context, courseSection *models.CourseSection, selectFields ...string) error
	ICourseSectionRepositoryForCourse
}

type CourseSectionCreateForm struct {
	CommonForm
	ctx      context.Context
	repo     ICourseSectionRepositoryForCourseSectionCreateForm
	input    *teacherInputs.CourseSectionCreateAndUpdateInput
	CourseID uint32
}

func NewCourseSectionCreateForm(
	ctx context.Context,
	input *teacherInputs.CourseSectionCreateAndUpdateInput,
	repo ICourseSectionRepositoryForCourseSectionCreateForm,
	CourseID uint32,
) *CourseSectionCreateForm {
	log.Debug().Ctx(ctx).Msg("CourseSectionCreateForm.NewCourseSectionCreateForm")

	form := &CourseSectionCreateForm{
		CommonForm: CommonForm{
			ctx:           ctx,
			Form:          forms.Form{},
			CourseSection: &models.CourseSection{CourseID: CourseID},
			input:         input,
			repo:          repo,
		},
		input:    input,
		repo:     repo,
		CourseID: CourseID,
	}

	form.assignAttributes()
	return form
}

func (form *CourseSectionCreateForm) Save() error {
	if err := form.validate(); err != nil {
		return err
	}

	selectFields := append(form.GetAllCode(), "Slug", "CourseID", "Position")

	err := form.repo.Create(form.ctx, form.CourseSection, selectFields...)
	if err != nil {
		return exceptions.NewUnprocessableContentError(err.Error(), nil)
	}

	return nil
}
