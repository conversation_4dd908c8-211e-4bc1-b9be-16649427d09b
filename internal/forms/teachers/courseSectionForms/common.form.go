package courseSectionForms

import (
	"context"
	"vibico-education-api/internal/forms"
	"vibico-education-api/internal/forms/formAttributes"
	teacherInputs "vibico-education-api/internal/gqls/inputs/teachers"
	"vibico-education-api/internal/models"
	"vibico-education-api/pkg/helpers"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/constants"
	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
)

type ICourseSectionRepositoryForCourse interface {
	FindByTitle(ctx context.Context, title string) (*models.CourseSection, error)
}

const (
	titleNameField = "Title"
)

type CommonForm struct {
	forms.Form

	ctx           context.Context
	repo          ICourseSectionRepositoryForCourse
	input         *teacherInputs.CourseSectionCreateAndUpdateInput
	CourseSection *models.CourseSection
}

func (form *CommonForm) assignAttributes() {
	form.AddAttributes(
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: titleNameField,
			},
			Value: form.input.Title,
		},
	)
}

func (form *CommonForm) validate() error {
	form.validateTitle().
		SummaryErrors()

	if form.IsValid() {
		return nil
	}

	return exceptions.NewUnprocessableContentError(translator.Translate(nil, "errValidationMsg_general"), form.Errors)
}

func (form *CommonForm) validateTitle() *CommonForm {
	field := form.GetAttribute(titleNameField)

	field.ValidateRequired(nil)
	field.ValidateLtEq(constants.MaxStringLength, nil)

	if !field.IsClean() {
		return form
	}

	if form.CourseSection.Title != *form.input.Title {
		form.CourseSection.Slug = helpers.GenerateSlug(*form.input.Title)
	}

	form.CourseSection.Title = *form.input.Title

	return form
}
