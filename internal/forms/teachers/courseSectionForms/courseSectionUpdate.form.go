package courseSectionForms

import (
	"context"
	"vibico-education-api/internal/forms"
	teacherInputs "vibico-education-api/internal/gqls/inputs/teachers"
	"vibico-education-api/internal/models"

	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
	"github.com/rs/zerolog/log"
)

type ICourseSectionRepositoryForCourseSectionUpdateForm interface {
	Update(ctx context.Context, course *models.CourseSection, selectFields ...string) error
	ICourseSectionRepositoryForCourse
}

const (
	TitleFieldName = "title"
)

type CourseSectionUpdateForm struct {
	CommonForm
	ctx           context.Context
	repo          ICourseSectionRepositoryForCourseSectionUpdateForm
	input         *teacherInputs.CourseSectionCreateAndUpdateInput
	CourseSection *models.CourseSection
}

func NewCourseSectionUpdateForm(
	ctx context.Context,
	input *teacherInputs.CourseSectionCreateAndUpdateInput,
	repo ICourseSectionRepositoryForCourseSectionUpdateForm,
	CourseSection *models.CourseSection,
) *CourseSectionUpdateForm {
	log.Debug().Ctx(ctx).Msg("CourseSectionUpdateForm.NewCourseSectionUpdateForm")

	form := &CourseSectionUpdateForm{
		CommonForm: CommonForm{
			Form:          forms.Form{},
			ctx:           ctx,
			repo:          repo,
			input:         input,
			CourseSection: CourseSection,
		},
		input:         input,
		repo:          repo,
		CourseSection: CourseSection,
	}

	form.assignAttributes()
	return form
}

func (form *CourseSectionUpdateForm) Save() error {
	if err := form.validate(); err != nil {
		return err
	}

	selectFields := append(form.GetAllCode(), "Slug")

	err := form.repo.Update(form.ctx, form.CourseSection, selectFields...)
	if err != nil {
		return exceptions.NewUnprocessableContentError(err.Error(), nil)
	}

	return nil
}
