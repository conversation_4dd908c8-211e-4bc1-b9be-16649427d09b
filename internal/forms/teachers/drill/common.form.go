package teacherDrillForms

import (
	"context"
	"vibico-education-api/internal/enums"
	"vibico-education-api/internal/forms"
	"vibico-education-api/internal/forms/formAttributes"
	teacherForms "vibico-education-api/internal/forms/teachers"
	teacherInputs "vibico-education-api/internal/gqls/inputs/teachers"
	"vibico-education-api/internal/models"
	"vibico-education-api/pkg/helpers"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/constants"
	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
	"github.com/rs/zerolog/log"
	"gorm.io/datatypes"

	vbcConstants "vibico-education-api/constants"
)

const (
	titleFieldName             = "Title"
	descriptionFieldName       = "Description"
	levelFieldName             = "Level"
	salePriceFieldName         = "SalePrice"
	priceFieldName             = "Price"
	diagramAttributesFieldName = "DiagramAttributes"
	stepFieldName              = "Step"
	isMasterFieldName          = "IsMaster"
)

type ISkillRepository interface {
	ListByIds(ctx context.Context, ids []uint32) (*[]models.Skill, error)
}

type CommonForm struct {
	forms.Form
	ctx            context.Context
	Drill          *models.Drill
	input          *teacherInputs.DrillModifyInput
	skillRepo      ISkillRepository
	uniqueSkillIds []uint32
	diagrams       []*models.Diagram
}

func (form *CommonForm) assignAttributes() {
	form.AddAttributes(
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: titleFieldName,
			},
			Value: form.input.Title,
		},
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: descriptionFieldName,
			},
			Value: form.input.Description,
		},
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: levelFieldName,
			},
			Value: form.input.Level,
		},
		&formAttributes.IntAttribute[int32]{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: salePriceFieldName,
			},
			Value: form.input.SalePrice,
		},
		&formAttributes.IntAttribute[int32]{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: priceFieldName,
			},
			Value: form.input.Price,
		},
		&formAttributes.JsonAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: stepFieldName,
			},
			Value: (*datatypes.JSON)(form.input.Step),
		},
		&formAttributes.BoolAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: isMasterFieldName,
			},
			Value: form.input.IsMaster,
		},
	)
}

func (form *CommonForm) validate() error {
	log.Debug().Ctx(form.ctx).Msg("DrillForm.Validate")

	form.validateTitle().
		validateDescription().
		validateLevel().
		validatePrice().
		validateSalePrice().
		validateSkills().
		validateDiagramAttributes().
		SummaryErrors()

	if form.IsValid() {
		form.Drill.Step = (*datatypes.JSON)(form.input.Step)
		return nil
	}

	return exceptions.NewUnprocessableContentError(translator.Translate(nil, "errValidationMsg_general"), form.Errors)
}

func (form *CommonForm) validateTitle() *CommonForm {
	field := form.GetAttribute(titleFieldName)

	field.ValidateRequired(nil)
	field.ValidateLtEq(int64(constants.MaxStringLength), nil)

	if field.IsClean() {
		if form.Drill.Title != *form.input.Title {
			slug := helpers.GenerateSlug(*form.input.Title)
			form.Drill.Slug = slug
		}
		form.Drill.Title = *form.input.Title
	}

	return form
}

func (form *CommonForm) validateDescription() *CommonForm {
	field := form.GetAttribute(descriptionFieldName)

	field.ValidateLtEq(int64(vbcConstants.MaxTextLength), nil)

	if field.IsClean() {
		form.Drill.Description = form.input.Description
	}

	return form
}

func (form *CommonForm) validateLevel() *CommonForm {
	field := form.GetAttribute(levelFieldName)

	if field.Blank() {
		return form
	}

	fieldValue := enums.DrillLevel(*form.input.Level)
	if !fieldValue.IsValid() {
		field.AddError(translator.Translate(nil, "errValidation_notExist"))
	}

	if field.IsClean() {
		form.Drill.Level = fieldValue
	}

	return form
}

func (form *CommonForm) validatePrice() *CommonForm {
	field := form.GetAttribute(priceFieldName)

	if field.Present() {
		field.ValidateGtEq(0, nil)

		if field.IsClean() {
			form.Drill.Price = form.input.Price
		}
	}

	return form
}

func (form *CommonForm) validateSalePrice() *CommonForm {
	salePriceField := form.GetAttribute(salePriceFieldName)

	if salePriceField.Present() {
		var maxPrice int32
		if form.GetAttribute(priceFieldName).Present() {
			maxPrice = *form.Drill.Price
		}
		message := translator.Translate(nil, "errValidation_lessThanOrEqualString", translator.Translate(nil, "ValidatePrice"))
		salePriceField.ValidateLtEq(maxPrice, &message)
		salePriceField.ValidateGtEq(0, nil)

		if salePriceField.IsClean() {
			form.Drill.SalePrice = form.input.SalePrice
		}
	}

	return form
}

func (form *CommonForm) validateSkills() *CommonForm {
	log.Debug().Ctx(form.ctx).Msg("CommonForm.validateSkills")

	if form.input.SkillIds == nil {
		return form
	}

	if len(*form.input.SkillIds) > 0 {
		uniqueSkills := make(map[int32]struct{})

		for _, skillId := range *form.input.SkillIds {
			if _, exists := uniqueSkills[*skillId]; !exists {
				uniqueSkills[*skillId] = struct{}{}
				form.uniqueSkillIds = append(form.uniqueSkillIds, uint32(*skillId))
			}
		}

		skills, err := form.skillRepo.ListByIds(form.ctx, form.uniqueSkillIds)
		if err != nil {
			return form
		}

		if len(*skills) != len(form.uniqueSkillIds) {
			form.AddErrorDirectlyToField("Skill", []interface{}{translator.Translate(nil, "errValidationMsg_invalid")})
		}

		form.Drill.DrillSkills = form.Drill.StoreSkill(form.uniqueSkillIds)
	}

	return form
}

func (form *CommonForm) validateDiagramAttributes() *CommonForm {
	log.Debug().Ctx(form.ctx).Msg("Drill.CommonForm.validateDiagramAttributes")

	if form.input.DiagramAttributes == nil {
		form.Drill.Diagrams = nil
		return form
	}

	for i, inputData := range *form.input.DiagramAttributes {
		diagramForm := teacherForms.NewNestedDiagramForm(form.ctx, *inputData)

		err := diagramForm.Validate()
		if err != nil {
			form.AddNestedErrors(diagramAttributesFieldName, i, err)
		} else {
			form.diagrams = append(form.diagrams, &models.Diagram{
				ImageUrl: inputData.ImageUrl,
				Setting:  (*datatypes.JSON)(inputData.Setting),
				Position: uint32(i + 1),
			})
		}
	}

	form.Drill.Diagrams = &form.diagrams
	return form
}
