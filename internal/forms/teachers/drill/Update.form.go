package teacherDrillForms

import (
	"context"
	"vibico-education-api/internal/enums"
	teacherInputs "vibico-education-api/internal/gqls/inputs/teachers"
	"vibico-education-api/internal/models"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
	"github.com/rs/zerolog/log"
	"gorm.io/datatypes"
)

type IDrillUpdateRepository interface {
	Update(ctx context.Context, drill *models.Drill, selectFields ...string) error
	UpdateWithNestedAttrs(ctx context.Context, drill *models.Drill, selectFields []string) error
}

type DrillUpdateForm struct {
	CommonForm
	DrillRepo IDrillUpdateRepository
}

func NewDrillUpdateForm(
	ctx context.Context,
	drill *models.Drill,
	input *teacherInputs.DrillModifyInput,

	drillRepo IDrillUpdateRepository,
	skillRepo ISkillRepository,
) *DrillUpdateForm {
	form := &DrillUpdateForm{
		CommonForm: CommonForm{
			ctx:       ctx,
			Drill:     drill,
			input:     input,
			skillRepo: skillRepo,
		},
		DrillRepo: drillRepo,
	}

	form.assignAttributes()
	return form
}

func (form *DrillUpdateForm) Save() error {
	log.Debug().Ctx(form.ctx).Msg("DrillUpdateForm.Save")

	if err := form.validate(); err != nil {
		return err
	}

	return form.updateDrill()
}

func (form *DrillUpdateForm) updateDrill() error {
	selectFields := append(form.GetAllCode(), "Slug", "Censor")

	if form.Drill.IsApproved() {
		selectFields = []string{priceFieldName, salePriceFieldName}

		return form.DrillRepo.Update(form.ctx, form.Drill, selectFields...)
	} else {
		form.Drill.Censor = enums.DrillCensorDraft
		if form.input.SkillIds == nil && form.input.DiagramAttributes == nil {
			return form.DrillRepo.Update(form.ctx, form.Drill, selectFields...)
		}

		selectFields = append(selectFields, "DrillSkills", "Diagrams")
		return form.DrillRepo.UpdateWithNestedAttrs(form.ctx, form.Drill, selectFields)
	}
}

func (form *DrillUpdateForm) validate() error {
	form.validatePrice().validateSalePrice()

	if !form.Drill.IsApproved() {
		form.validateTitle().
			validateDescription().
			validateLevel().
			validateSkills().
			validateDiagramAttributes()
	}
	form.SummaryErrors()

	if form.IsValid() {
		form.Drill.Step = (*datatypes.JSON)(form.input.Step)
		return nil
	}

	return exceptions.NewUnprocessableContentError(translator.Translate(nil, "errValidationMsg_general"), form.Errors)
}
