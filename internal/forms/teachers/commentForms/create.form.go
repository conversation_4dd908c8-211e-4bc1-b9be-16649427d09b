package commentForms

import (
	"context"
	"vibico-education-api/internal/enums"
	"vibico-education-api/internal/forms"
	"vibico-education-api/internal/forms/formAttributes"
	globalInputs "vibico-education-api/internal/gqls/inputs/global"
	"vibico-education-api/internal/models"
	"vibico-education-api/internal/repository"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/rs/zerolog/log"
)

const (
	targetIDNameField   = "TargetID"
	targetTypeNameField = "TargetType"
	contentNameField    = "Content"
	parentIDNameFiled   = "parentID"
)

type ICommentCreateRepository interface {
	Create(ctx context.Context, comment *models.Comment, selectFields ...string) error
}

type CreateForm struct {
	forms.Form
	repo  ICommentCreateRepository
	repos repository.IRepositories

	ctx            context.Context
	input          *globalInputs.CommentCreateInput
	currentTeacher *models.Teacher

	Comment *models.Comment
}

func NewCreateForm(
	ctx context.Context,
	input globalInputs.CommentCreateInput,
	currentTeacher *models.Teacher,
	repo ICommentCreateRepository,
	repos repository.IRepositories,

) *CreateForm {
	form := &CreateForm{
		Form:           forms.Form{},
		ctx:            ctx,
		currentTeacher: currentTeacher,
		Comment:        &models.Comment{AuthorID: currentTeacher.ID, AuthorType: "Teacher", AuthorTeacher: currentTeacher},
		input:          &input,
		repo:           repo,
		repos:          repos,
	}

	form.assignAttributes()

	return form
}

func (form *CreateForm) assignAttributes() {
	form.AddAttributes(
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: targetIDNameField,
			},
			Value: (*string)(form.input.TargetID),
		},
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: targetTypeNameField,
			},
			Value: form.input.TargetType,
		},
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: contentNameField,
			},
			Value: form.input.Content,
		},
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: parentIDNameFiled,
			},
			Value: (*string)(form.input.ReferenceID),
		},
	)
}

func (form *CreateForm) Save() error {
	if err := form.validate(); err != nil {
		return err
	}

	selectFields := append(form.GetAllCode(), "AuthorID", "AuthorType")

	if err := form.repo.Create(form.ctx, form.Comment, selectFields...); err != nil {
		return exceptions.NewUnprocessableContentError(err.Error(), nil)
	}

	return nil
}

func (form *CreateForm) validate() error {
	form.
		validateTargetID().
		validateTargetType().
		validateTarget().
		validateContent().
		SummaryErrors()

	if form.IsValid() {
		return nil
	}

	return exceptions.NewUnprocessableContentError(translator.Translate(nil, "errValidationMsg_general"), form.Errors)
}

func (form *CreateForm) validateTargetID() *CreateForm {
	field := form.GetAttribute(targetIDNameField)

	field.ValidateRequired(nil)

	if field.IsClean() {
		targetID, err := utils.ParseGraphqlID[uint32](*form.input.TargetID)
		if err != nil {
			field.AddError(err.Error())
		}

		form.Comment.TargetID = targetID
	}

	return form
}

func (form *CreateForm) validateTargetType() *CreateForm {
	field := form.GetAttribute(targetTypeNameField)

	field.ValidateRequired(nil)

	if !field.IsClean() {
		return form
	}

	fieldEnum, err := enums.ParseCommentTargetType(*form.input.TargetType)
	if err != nil {
		field.AddError(translator.Translate(nil, "errValidation_notExist"))
	}

	if field.IsClean() {
		form.Comment.TargetType = fieldEnum
	}

	return form
}

func (form *CreateForm) validateTarget() *CreateForm {
	fieldTargetID := form.GetAttribute(targetIDNameField)
	fieldTargetType := form.GetAttribute(targetTypeNameField)

	if !fieldTargetID.IsClean() || !fieldTargetType.IsClean() {
		return form
	}

	switch form.Comment.TargetType {
	case enums.CommentTargetTypePracticeSubmission:
		_, err := form.repos.PracticeSubmissionRepo().FindByIDAndTeacherID(
			form.ctx,
			form.Comment.TargetID,
			form.currentTeacher.ID,
		)
		if err != nil {
			log.Error().Err(err).Ctx(form.ctx).Msg("something error when find practice submission")
			fieldTargetID.AddError(translator.Translate(nil, "general_invalidId"))
		}
	default:
		fieldTargetType.AddError(translator.Translate(nil, "errValidation_notExist"))
	}

	return form
}

func (form *CreateForm) validateContent() *CreateForm {
	field := form.GetAttribute(contentNameField)

	field.ValidateRequired(nil)

	if field.IsClean() {
		form.Comment.Content = *form.input.Content
	}

	return form
}
