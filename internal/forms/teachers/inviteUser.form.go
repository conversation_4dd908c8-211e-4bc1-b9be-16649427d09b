package teacherForms

import (
	"context"
	"vibico-education-api/internal/forms"
	"vibico-education-api/internal/forms/formAttributes"

	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/constants"
	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
)

const (
	phoneNumberField = "PhoneNumber"
)

type InviteUserForm struct {
	forms.Form

	ctx         context.Context
	phoneNumber *string
}

func NewInviteUserForm(ctx context.Context, phoneNumber *string) *InviteUserForm {
	form := &InviteUserForm{
		ctx:         ctx,
		phoneNumber: phoneNumber,
	}
	form.assignAttributes()
	return form
}

func (form *InviteUserForm) assignAttributes() {
	form.AddAttributes(
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: phoneNumberField,
			},
			Value: form.phoneNumber,
		},
	)
}

func (form *InviteUserForm) Validate() error {
	form.validatePhoneNumber().
		SummaryErrors()

	if form.IsValid() {
		return nil
	}

	return exceptions.NewUnprocessableContentError(
		translator.Translate(nil, "errValidationMsg_general"),
		form.Errors,
	)
}

func (form *InviteUserForm) validatePhoneNumber() *InviteUserForm {
	field := form.GetAttribute(phoneNumberField)

	field.ValidateRequired(nil)
	field.ValidateFormat(constants.PhoneNumberFormat, nil)

	return form
}
