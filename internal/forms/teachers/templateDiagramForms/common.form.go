package templateDiagramForms

import (
	"context"
	"vibico-education-api/internal/forms"
	"vibico-education-api/internal/forms/formAttributes"
	teacherForms "vibico-education-api/internal/forms/teachers"
	teacherInputs "vibico-education-api/internal/gqls/inputs/teachers"
	"vibico-education-api/internal/models"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/constants"
	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
	"github.com/rs/zerolog/log"
	"gorm.io/datatypes"
)

const (
	nameField                  = "Name"
	diagramAttributesFieldName = "DiagramAttributes"
)

type CommonForm struct {
	forms.Form

	ctx             context.Context
	input           *teacherInputs.TemplateDiagramFormInput
	TemplateDiagram *models.TemplateDiagram
	diagrams        []*models.Diagram
}

func (form *CommonForm) assignAttributes() {
	form.AddAttributes(
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: nameField,
			},
			Value: form.input.Name,
		},
	)
}

func (form *CommonForm) validate() error {
	form.validateName().validateDiagramAttributes().SummaryErrors()

	if form.IsValid() {
		return nil
	}

	return exceptions.NewUnprocessableContentError(translator.Translate(nil, "errValidationMsg_general"), form.Errors)
}

func (form *CommonForm) validateName() *CommonForm {
	log.Debug().Ctx(form.ctx).Msg("TemplateDiagramForm.CommonForm.validateName")

	field := form.GetAttribute(nameField)

	field.ValidateRequired(nil)
	field.ValidateLtEq(int64(constants.MaxStringLength), nil)

	if field.IsClean() {
		form.TemplateDiagram.Name = *form.input.Name
	}

	return form
}

func (form *CommonForm) validateDiagramAttributes() *CommonForm {
	log.Debug().Ctx(form.ctx).Msg("TemplateDiagramForm.CommonForm.validateDiagramAttributes")

	if form.input.DiagramAttributes == nil {
		form.TemplateDiagram.Diagrams = nil
		return form
	}

	for i, inputData := range *form.input.DiagramAttributes {
		diagramForm := teacherForms.NewNestedDiagramForm(form.ctx, *inputData)

		err := diagramForm.Validate()
		if err != nil {
			form.AddNestedErrors(diagramAttributesFieldName, i, err)
		} else {
			form.diagrams = append(form.diagrams, &models.Diagram{
				ImageUrl: inputData.ImageUrl,
				Setting:  (*datatypes.JSON)(inputData.Setting),
				Position: uint32(i + 1),
			})
		}
	}

	form.TemplateDiagram.Diagrams = &form.diagrams
	return form
}
