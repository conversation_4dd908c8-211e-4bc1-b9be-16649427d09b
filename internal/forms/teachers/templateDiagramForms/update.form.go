package templateDiagramForms

import (
	"context"
	teacherInputs "vibico-education-api/internal/gqls/inputs/teachers"
	"vibico-education-api/internal/models"
	"vibico-education-api/internal/repository/repositories"

	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
)

type ITemplateDiagramUpdateRepo interface {
	UpdateWithNestedAttrs(ctx context.Context, template *models.TemplateDiagram, selectFields []string) error
	FindById(ctx context.Context, id uint32, preloads ...repositories.CustomPreload) (*models.TemplateDiagram, error)
}

type UpdateForm struct {
	CommonForm

	templateDiagramRepo ITemplateDiagramUpdateRepo
}

func NewUpdateForm(
	ctx context.Context,
	input *teacherInputs.TemplateDiagramFormInput,
	templateDiagram *models.TemplateDiagram,
	templateDiagramRepo ITemplateDiagramUpdateRepo,
) *UpdateForm {
	form := &UpdateForm{
		CommonForm: CommonForm{
			ctx:             ctx,
			input:           input,
			TemplateDiagram: templateDiagram,
		},
		templateDiagramRepo: templateDiagramRepo,
	}

	form.assignAttributes()

	return form
}

func (form *UpdateForm) Save() error {
	if err := form.validate(); err != nil {
		return err
	}

	if err := form.templateDiagramRepo.UpdateWithNestedAttrs(form.ctx, form.TemplateDiagram, []string{"name"}); err != nil {
		return exceptions.NewUnprocessableContentError(err.Error(), nil)
	}

	return nil
}
