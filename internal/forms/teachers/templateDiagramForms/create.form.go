package templateDiagramForms

import (
	"context"
	teacherInputs "vibico-education-api/internal/gqls/inputs/teachers"
	"vibico-education-api/internal/models"

	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
)

type ITemplateDiagramCreateRepo interface {
	Create(ctx context.Context, template *models.TemplateDiagram, selectFields []string) error
}

type CreateForm struct {
	CommonForm

	templateDiagramRepo ITemplateDiagramCreateRepo
}

func NewCreateForm(
	ctx context.Context,
	input *teacherInputs.TemplateDiagramFormInput,
	templateDiagramRepo ITemplateDiagramCreateRepo,
) *CreateForm {
	form := &CreateForm{
		CommonForm: CommonForm{
			ctx:             ctx,
			input:           input,
			TemplateDiagram: &models.TemplateDiagram{},
		},
		templateDiagramRepo: templateDiagramRepo,
	}

	form.assignAttributes()

	return form
}

func (form *CreateForm) Save() error {
	if err := form.validate(); err != nil {
		return err
	}

	if err := form.templateDiagramRepo.Create(form.ctx, form.TemplateDiagram, []string{"name"}); err != nil {
		return exceptions.NewUnprocessableContentError(err.Error(), nil)
	}

	return nil
}
