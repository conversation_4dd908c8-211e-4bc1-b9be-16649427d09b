package authForms

import (
	"context"
	"vibico-education-api/internal/forms"
	"vibico-education-api/internal/forms/formAttributes"
	globalInputs "vibico-education-api/internal/gqls/inputs/global"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
	"github.com/rs/zerolog/log"
)

const (
	identifierNameField = "Identifier"
	passwordNameField   = "Password"
)

type SignInForm struct {
	forms.Form
	ctx context.Context

	input *globalInputs.SignInInput
}

func NewSignInForm(
	ctx context.Context,
	input *globalInputs.SignInInput,
) *SignInForm {
	log.Debug().Ctx(ctx).Msg("SignInForm.NewSignInForm")

	form := &SignInForm{
		ctx:   ctx,
		Form:  forms.Form{},
		input: input,
	}

	form.assignAttributes()

	return form
}

func (form *SignInForm) assignAttributes() {
	log.Debug().Ctx(form.ctx).Msg("SignInForm.assignAttributes")

	form.AddAttributes(
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: identifierNameField,
			},
			Value: &form.input.Identifier,
		},
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: passwordNameField,
			},
			Value: &form.input.Password,
		},
	)
}

func (form *SignInForm) Validate() error {
	log.Debug().Ctx(form.ctx).Msg("SignInForm.Validate")

	form.validateIdentifier().
		validatePassword().
		SummaryErrors()

	if form.IsValid() {
		return nil
	}

	return exceptions.NewUnprocessableContentError(translator.Translate(nil, "errDbMsg_wrongPassword"), form.Errors)
}

func (form *SignInForm) validateIdentifier() *SignInForm {
	log.Debug().Ctx(form.ctx).Msg("SignInForm.validateIdentifier")

	identifier := form.GetAttribute(identifierNameField)

	identifier.ValidateRequired(nil)

	return form
}

func (form *SignInForm) validatePassword() *SignInForm {
	log.Debug().Ctx(form.ctx).Msg("SignInForm.validatePassword")

	password := form.GetAttribute(passwordNameField)

	password.ValidateRequired(nil)

	return form
}
