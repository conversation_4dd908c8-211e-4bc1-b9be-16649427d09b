package authForms

import (
	"context"
	vibicoConstants "vibico-education-api/constants"
	"vibico-education-api/internal/forms"
	"vibico-education-api/internal/forms/formAttributes"
	publicInputs "vibico-education-api/internal/gqls/inputs/publics"
	"vibico-education-api/setup/grpc_clients"

	pb "github.com/BehemothLtd/vibico-auth/proto/auth"

	"vibico-education-api/internal/models"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/constants"
	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
)

const (
	phoneNumberNameField      = "PhoneNumber"
	passwordConfirmationField = "PasswordConfirmation"
)

type IUserRepository interface {
	CreateAccounts(ctx context.Context, user *models.User, pbUserParams *pb.UserParams, selectFields ...string) error
}

type SignUpForm struct {
	forms.Form

	ctx   context.Context
	input publicInputs.SignUpInput

	User         *models.User
	pbUserParams *pb.UserParams
	userRepo     IUserRepository
}

func NewSignUpForm(
	ctx context.Context,
	input publicInputs.SignUpInput,
	userRepo IUserRepository,
) *SignUpForm {
	form := &SignUpForm{
		ctx:          ctx,
		input:        input,
		User:         &models.User{},
		pbUserParams: &pb.UserParams{Status: "ACTIVE", Role: utils.PointerString(vibicoConstants.RoleUser)},
		userRepo:     userRepo,
	}

	form.assignAttributes()

	return form
}

func (form *SignUpForm) assignAttributes() {
	form.AddAttributes(
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: phoneNumberNameField,
			},
			Value: form.input.PhoneNumber,
		},
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: passwordNameField,
			},
			Value: form.input.Password,
		},
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: passwordConfirmationField,
			},
			Value: form.input.PasswordConfirmation,
		},
	)
}

func (form *SignUpForm) Save() error {
	if err := form.validate(); err != nil {
		return err
	}

	grpcResponse, err := grpc_clients.AuthClient().CreateUser(
		grpc_clients.NewCtx(form.ctx),
		&pb.CreateUserRequest{
			IdentityPoolId: grpc_clients.PoolId(),
			UserParams:     form.pbUserParams,
		},
	)
	if err != nil {
		form.AddErrorFromGRPC(err)

		return exceptions.NewUnprocessableContentError(translator.Translate(nil, "errValidationMsg_general"), form.Errors)
	}

	// also add transaction
	form.User.AuthId = grpcResponse.User.Id
	selectFields := []string{"AuthId", "Name", "PhoneNumber"}
	if err := form.userRepo.CreateAccounts(form.ctx, form.User, form.pbUserParams, selectFields...); err != nil {
		// TODO: Delete user in auth service
		return exceptions.NewUnprocessableContentError(err.Error(), nil)
	}

	return nil
}

func (form *SignUpForm) validate() error {
	form.validatePhoneNumber().
		validatePassword().
		validatePasswordConfirmation().
		SummaryErrors()

	if form.IsValid() {
		return nil
	}

	return exceptions.NewUnprocessableContentError(translator.Translate(nil, "errValidationMsg_general"), form.Errors)
}

func (form *SignUpForm) validatePhoneNumber() *SignUpForm {
	field := form.GetAttribute(phoneNumberNameField)

	field.ValidateRequired(nil)
	field.ValidateLtEq(constants.MaxStringLength, nil)
	field.ValidateFormat(constants.PhoneNumberFormat, nil)

	if field.IsClean() {
		form.pbUserParams.PhoneNumber = form.input.PhoneNumber
		form.User.PhoneNumber = form.input.PhoneNumber
	}

	return form
}

func (form *SignUpForm) validatePassword() *SignUpForm {
	field := form.GetAttribute(passwordNameField)

	field.ValidateRequired(nil)

	if !field.IsClean() {
		return form
	}

	field.ValidateGtEq(constants.MinPasswordLength, nil)
	field.ValidateLt(constants.MaxPasswordLength, nil)

	if field.IsClean() {
		form.pbUserParams.RawPassword = *form.input.Password
	}

	return form
}

func (form *SignUpForm) validatePasswordConfirmation() *SignUpForm {
	confirmField := form.GetAttribute(passwordConfirmationField)
	passwordField := form.GetAttribute(passwordNameField)

	confirmField.ValidateRequired(nil)

	if passwordField.IsClean() && confirmField.IsClean() {
		if *form.input.Password != *form.input.PasswordConfirmation {
			confirmField.AddError(translator.Translate(nil, "errValidationMsg_passwordConfirmation"))
			return form
		}
	}

	return form
}
