package authForms

import (
	"context"
	"vibico-education-api/internal/forms"
	"vibico-education-api/internal/forms/formAttributes"
	publicInputs "vibico-education-api/internal/gqls/inputs/publics"
	"vibico-education-api/pkg/helpers"
	translator "vibico-education-api/pkg/translators"
	"vibico-education-api/setup/grpc_clients"

	"github.com/BehemothLtd/behemoth-pkg/golang/constants"
	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
	"github.com/rs/zerolog/log"

	pb "github.com/BehemothLtd/vibico-auth/proto/auth"
)

const (
	codeField = "Code"
)

type ResetPasswordForm struct {
	forms.Form
	ctx context.Context

	input publicInputs.ResetPasswordInput
	user  pb.User
}

func NewResetPasswordForm(
	ctx context.Context,
	input publicInputs.ResetPasswordInput,
) *ResetPasswordForm {
	form := &ResetPasswordForm{
		ctx:   ctx,
		input: input,
	}

	form.assignAttributes()

	return form
}

func (form *ResetPasswordForm) assignAttributes() {
	form.AddAttributes(
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: phoneNumberNameField,
			},
			Value: form.input.PhoneNumber,
		},
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: codeField,
			},
			Value: form.input.Code,
		},
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: passwordNameField,
			},
			Value: form.input.Password,
		},
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: passwordConfirmationField,
			},
			Value: form.input.PasswordConfirmation,
		},
	)
}

func (form *ResetPasswordForm) Save() error {
	if err := form.valiate(); err != nil {
		return err
	}

	identityPoolId := grpc_clients.PoolId()
	_, err := grpc_clients.AuthClient().ResetPassword(
		grpc_clients.NewCtx(form.ctx),
		&pb.ResetPasswordRequest{
			IdentityPoolId:    identityPoolId,
			PhoneNumber:       *form.input.PhoneNumber,
			Password:          *form.input.Password,
			PasswordResetCode: *form.input.Code,
		},
	)

	if err != nil {
		return err
	}

	return nil
}

func (form *ResetPasswordForm) valiate() error {
	log.Debug().Ctx(form.ctx).Msg("ResetPasswordForm.Validate")

	form.validateUser().
		validatePassword().
		validatePasswordConfirmation().
		SummaryErrors()

	if form.IsValid() {
		return nil
	}

	return exceptions.NewUnprocessableContentError(translator.Translate(nil, "errValidationMsg_general"), form.Errors)
}

func (form *ResetPasswordForm) validateUser() *ResetPasswordForm {
	field := form.GetAttribute(phoneNumberNameField)

	field.ValidateRequired(nil)
	field.ValidateFormat(constants.PhoneNumberFormat, nil)

	return form
}

func (form *ResetPasswordForm) validateCode() *ResetPasswordForm {
	field := form.GetAttribute(codeField)

	field.ValidateRequired(nil)

	return form
}

func (form *ResetPasswordForm) validatePassword() *ResetPasswordForm {
	field := form.GetAttribute(passwordNameField)

	field.ValidateRequired(nil)

	if !field.IsClean() {
		return form
	}

	field.ValidateGtEq(constants.MinPasswordLength, nil)
	field.ValidateLt(constants.MaxPasswordLength, nil)

	if field.IsClean() {
		if !helpers.IsValidPassword(*form.input.Password) {
			field.AddError(translator.Translate(nil, "errValidationMsg_passwordFormat"))
		}
	}

	return form
}

func (form *ResetPasswordForm) validatePasswordConfirmation() *ResetPasswordForm {
	confirmField := form.GetAttribute(passwordConfirmationField)
	passwordField := form.GetAttribute(passwordNameField)

	confirmField.ValidateRequired(nil)

	if passwordField.IsClean() && confirmField.IsClean() {
		if *form.input.Password != *form.input.PasswordConfirmation {
			confirmField.AddError(translator.Translate(nil, "errValidationMsg_passwordConfirmation"))
			return form
		}
	}

	return form
}
