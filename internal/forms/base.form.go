package forms

import (
	"fmt"
	"slices"
	"vibico-education-api/internal/forms/formAttributes"
	"vibico-education-api/setup/grpc_clients"

	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/BehemothLtd/vibico-auth/proto/auth"
	"google.golang.org/grpc/status"
)

type Form struct {
	Attributes []formAttributes.FieldAttributeInterface
	Errors     exceptions.ResourceModificationError
}

func (form *Form) IsValid() bool {
	return len(form.Errors) == 0
}

func (form *Form) AddAttributes(attributes ...formAttributes.FieldAttributeInterface) {
	form.Attributes = append(form.Attributes, attributes...)
}

func (form *Form) GetAttribute(code string) formAttributes.FieldAttributeInterface {
	idx := slices.IndexFunc(form.Attributes, func(a formAttributes.FieldAttributeInterface) bool { return a.GetCode() == code })

	if idx != -1 {
		return form.Attributes[idx]
	} else {
		return nil
	}
}

func (form *Form) GetAllCode() []string {
	codes := make([]string, 0)
	for _, attr := range form.Attributes {
		codes = append(codes, attr.GetCode())
	}

	return codes
}

// SummaryErrors summarizes errors in the form.
func (form *Form) SummaryErrors() {
	err := exceptions.NewUnprocessableContentError("", nil)

	if form.Errors != nil {
		err.Errors = form.Errors
	}

	for _, attribute := range form.Attributes {
		attributeErr := attribute.GetErrors()

		if len(attributeErr) > 0 {
			err.AddError(utils.PascalCaseToCamelCase(attribute.GetCode()), attributeErr)
		}
	}

	form.Errors = err.Errors
}

func (form *Form) AddNestedErrors(fieldKey string, index int, errors exceptions.ResourceModificationError) {
	for key, innerErr := range errors {
		form.AddErrorDirectlyToField(form.NestedFieldKey(fieldKey, index, key), innerErr)
	}
}

// NestedFieldKey output a key for response
// such as `projectIssueStatuses.1.issueStatusId`
// use for nested attributes
func (form *Form) NestedFieldKey(wrapperFieldKey string, index int, nestedFieldKey string) string {
	return fmt.Sprintf("%s.%d.%s", wrapperFieldKey, index, nestedFieldKey)
}

func (form *Form) NestedDirectItemFieldKey(wrapperFieldKey string, index int) string {
	return fmt.Sprintf("%s.%d", wrapperFieldKey, index)
}

func (form *Form) AddErrorDirectlyToField(field string, errors []any) {
	field = utils.PascalCaseToCamelCase(field)

	if len(form.Errors) == 0 {
		form.Errors = exceptions.ResourceModificationError{}
	}

	if len(form.Errors[field]) == 0 {
		form.Errors[field] = []any{}
	}

	form.Errors[field] = append(form.Errors[field], errors...)
}

func (form *Form) AddErrorFromGRPC(grpcError error) {
	st, ok := status.FromError(grpcError)
	if !ok {
		form.AddErrorDirectlyToField("grpc", []any{grpcError.Error()})
		return
	}

	for _, detail := range st.Details() {
		switch d := detail.(type) {
		case *auth.CustomError:
			for _, err := range d.Errors {
				form.AddErrorDirectlyToField(d.Field, []any{grpc_clients.MapErrorCodeToTranslate(err.Code)})
			}
		default:
			form.AddErrorDirectlyToField("grpc", []any{d})
		}
	}
}
