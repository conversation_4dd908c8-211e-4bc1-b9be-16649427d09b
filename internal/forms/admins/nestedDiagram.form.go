package adminForms

import (
	"context"
	"vibico-education-api/internal/forms"
	"vibico-education-api/internal/forms/formAttributes"
	adminInputs "vibico-education-api/internal/gqls/inputs/admins"

	"github.com/BehemothLtd/behemoth-pkg/golang/constants"
	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
	"github.com/rs/zerolog/log"
)

const (
	imageUrlFieldName = "ImageUrl"
)

type NestedDiagramForm struct {
	forms.Form
	ctx               context.Context
	input             adminInputs.DiagramFormInput
	DeletedDiagramIds []uint32
}

func NewNestedDiagramForm(ctx context.Context, input adminInputs.DiagramFormInput) *NestedDiagramForm {
	diagramForm := &NestedDiagramForm{
		ctx:   ctx,
		Form:  forms.Form{},
		input: input,
	}

	diagramForm.assignAttributes()
	return diagramForm
}

func (form *NestedDiagramForm) assignAttributes() {
	form.AddAttributes(
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: imageUrlFieldName,
			},
			Value: form.input.ImageUrl,
		},
	)
}

func (form *NestedDiagramForm) Validate() exceptions.ResourceModificationError {
	form.validateImageUrl().SummaryErrors()

	if form.IsValid() {
		return nil
	}

	return form.Errors
}

func (form *NestedDiagramForm) validateImageUrl() *NestedDiagramForm {
	log.Debug().Ctx(form.ctx).Msg("DiagramForm.validateImageUrl")

	field := form.GetAttribute(imageUrlFieldName)

	if field.Present() {
		field.ValidateFormat(constants.UrlFormat, nil)
		field.ValidateLt(constants.MaxStringLength, nil)
	}

	return form
}
