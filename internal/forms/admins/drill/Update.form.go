package drillForms

import (
	"context"
	adminInputs "vibico-education-api/internal/gqls/inputs/admins"
	"vibico-education-api/internal/models"

	"github.com/rs/zerolog/log"
)

type IDrillUpdateRepository interface {
	Update(ctx context.Context, drill *models.Drill, selectFields ...string) error
	UpdateWithNestedAttrs(ctx context.Context, drill *models.Drill, selectFields []string) error
}

type DrillUpdateForm struct {
	CommonForm
	DrillRepo IDrillUpdateRepository
}

func NewDrillUpdateForm(
	ctx context.Context,
	drill *models.Drill,
	input *adminInputs.DrillModifyInput,

	drillRepo IDrillUpdateRepository,
	skillRepo ISkillRepository,
) *DrillUpdateForm {
	form := &DrillUpdateForm{
		CommonForm: CommonForm{
			ctx:       ctx,
			Drill:     drill,
			input:     input,
			skillRepo: skillRepo,
		},
		DrillRepo: drillRepo,
	}

	form.assignAttributes()
	return form
}

func (form *DrillUpdateForm) Save() error {
	log.Debug().Ctx(form.ctx).Msg("DrillUpdateForm.Save")

	if err := form.validate(); err != nil {
		return err
	}

	return form.updateDrill()
}

func (form *DrillUpdateForm) updateDrill() error {
	selectFields := append(form.GetAllCode(), "Slug")

	if form.input.SkillIds == nil && form.input.DiagramAttributes == nil {
		return form.DrillRepo.Update(form.ctx, form.Drill, selectFields...)
	}

	selectFields = append(selectFields, "DrillSkills", "Diagrams")
	return form.DrillRepo.UpdateWithNestedAttrs(form.ctx, form.Drill, selectFields)
}
