package drillForms

import (
	"context"
	adminInputs "vibico-education-api/internal/gqls/inputs/admins"
	"vibico-education-api/internal/models"

	"github.com/rs/zerolog/log"
)

type IDrillCreateRepository interface {
	Create(ctx context.Context, drill *models.Drill, selectFields []string) error
}

type DrillCreateForm struct {
	CommonForm
	DrillRepo IDrillCreateRepository
}

func NewDrillCreateForm(
	ctx context.Context,
	drill *models.Drill,
	drillRepo IDrillCreateRepository,
	skillRepo ISkillRepository,
	input *adminInputs.DrillModifyInput,
) *DrillCreateForm {
	form := &DrillCreateForm{
		CommonForm: CommonForm{
			ctx:       ctx,
			Drill:     drill,
			input:     input,
			skillRepo: skillRepo,
		},
		DrillRepo: drillRepo,
	}

	form.assignAttributes()
	return form
}

func (form *DrillCreateForm) Save() error {
	log.Debug().Ctx(form.ctx).Msg("DrillCreateForm.Save")

	if validateErr := form.validate(); validateErr != nil {
		return validateErr
	}

	selectFields := append(form.GetAllCode(), "OwnerID", "OwnerType", "DrillSkills", "Diagrams", "Slug", "Censor", "IsMaster")

	err := form.DrillRepo.Create(form.ctx, form.Drill, selectFields)
	return err
}
