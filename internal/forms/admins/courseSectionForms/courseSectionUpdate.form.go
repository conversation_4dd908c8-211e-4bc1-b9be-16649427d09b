package adminCourseSectionForms

import (
	"context"
	"vibico-education-api/internal/forms"
	adminInputs "vibico-education-api/internal/gqls/inputs/admins"
	"vibico-education-api/internal/models"

	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
	"github.com/rs/zerolog/log"
)

type ICourseSectionRepositoryForCourseSectionUpdateForm interface {
	Update(ctx context.Context, course *models.CourseSection, selectFields ...string) error
}

const (
	TitleFieldName = "Title"
)

type CourseSectionUpdateForm struct {
	CommonForm
	ctx           context.Context
	repo          ICourseSectionRepositoryForCourseSectionUpdateForm
	input         *adminInputs.CourseSectionMutateInput
	CourseSection *models.CourseSection
}

func NewCourseSectionUpdateForm(
	ctx context.Context,
	input *adminInputs.CourseSectionMutateInput,
	repo ICourseSectionRepositoryForCourseSectionUpdateForm,
	CourseSection *models.CourseSection,
) *CourseSectionUpdateForm {
	log.Debug().Ctx(ctx).Msg("CourseSectionUpdateForm.NewCourseSectionUpdateForm")

	form := &CourseSectionUpdateForm{
		CommonForm: CommonForm{
			Form:          forms.Form{},
			ctx:           ctx,
			input:         input,
			CourseSection: CourseSection,
		},
		input:         input,
		repo:          repo,
		CourseSection: CourseSection,
	}

	form.assignAttributes()
	return form
}

func (form *CourseSectionUpdateForm) Save() error {
	if err := form.validate(); err != nil {
		return err
	}

	err := form.repo.Update(form.ctx, form.CourseSection, form.GetAllCode()...)
	if err != nil {
		return exceptions.NewUnprocessableContentError(err.Error(), nil)
	}

	return nil
}
