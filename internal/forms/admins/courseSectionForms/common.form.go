package adminCourseSectionForms

import (
	"context"
	"vibico-education-api/internal/forms"
	"vibico-education-api/internal/forms/formAttributes"
	adminInputs "vibico-education-api/internal/gqls/inputs/admins"
	"vibico-education-api/internal/models"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/constants"
	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
)

const (
	titleNameField = "Title"
)

type CommonForm struct {
	forms.Form

	ctx           context.Context
	input         *adminInputs.CourseSectionMutateInput
	CourseSection *models.CourseSection
}

func (form *CommonForm) assignAttributes() {
	form.AddAttributes(
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: titleNameField,
			},
			Value: form.input.Title,
		},
	)
}

func (form *CommonForm) validate() error {
	form.validateTitle().
		SummaryErrors()

	if form.IsValid() {
		return nil
	}

	return exceptions.NewUnprocessableContentError(translator.Translate(nil, "errValidationMsg_general"), form.Errors)
}

func (form *CommonForm) validateTitle() *CommonForm {
	field := form.GetAttribute(titleNameField)

	field.ValidateRequired(nil)
	field.ValidateLtEq(int64(constants.MaxStringLength), nil)

	if !field.IsClean() {
		return form
	}

	form.CourseSection.Title = *form.input.Title

	return form
}
