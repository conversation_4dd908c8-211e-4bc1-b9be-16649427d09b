package adminCourseSectionForms

import (
	"context"
	"vibico-education-api/internal/forms"
	adminInputs "vibico-education-api/internal/gqls/inputs/admins"
	"vibico-education-api/internal/models"

	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
	"github.com/rs/zerolog/log"
)

type ICourseSectionRepositoryForCourseSectionCreateForm interface {
	Create(ctx context.Context, courseSection *models.CourseSection, selectFields ...string) error
}

type CourseSectionCreateForm struct {
	CommonForm
	ctx      context.Context
	repo     ICourseSectionRepositoryForCourseSectionCreateForm
	input    *adminInputs.CourseSectionMutateInput
	CourseID uint32
}

func NewCourseSectionCreateForm(
	ctx context.Context,
	input *adminInputs.CourseSectionMutateInput,
	repo ICourseSectionRepositoryForCourseSectionCreateForm,
	CourseID uint32,
) *CourseSectionCreateForm {
	log.Debug().Ctx(ctx).Msg("CourseSectionCreateForm.NewCourseSectionCreateForm")

	form := &CourseSectionCreateForm{
		CommonForm: CommonForm{
			ctx:           ctx,
			Form:          forms.Form{},
			CourseSection: &models.CourseSection{CourseID: CourseID},
			input:         input,
		},
		input: input,
		repo:  repo,
	}

	form.assignAttributes()
	return form
}

func (form *CourseSectionCreateForm) Save() error {
	if err := form.validate(); err != nil {
		return err
	}

	selectFields := append(form.GetAllCode(), "CourseID", "Position")

	err := form.repo.Create(form.ctx, form.CourseSection, selectFields...)
	if err != nil {
		return exceptions.NewUnprocessableContentError(err.Error(), nil)
	}

	return nil
}
