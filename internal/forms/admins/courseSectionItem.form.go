package adminForms

import (
	"context"
	"vibico-education-api/internal/enums"
	"vibico-education-api/internal/forms"
	"vibico-education-api/internal/forms/formAttributes"
	adminInputs "vibico-education-api/internal/gqls/inputs/admins"
	"vibico-education-api/internal/models"

	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/constants"
	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
	"github.com/rs/zerolog/log"
)

const (
	titleFieldName   = "Title"
	contentFieldName = "Content"
	typeFieldName    = "Type"
	courseSectionId  = "CourseSectionId"
)

type ICourseSectionItemRepository interface {
	Create(ctx context.Context, sectionItem *models.CourseSectionItem, courseId uint32, selectFields []string) error
	Update(ctx context.Context, sectionItem *models.CourseSectionItem, courseId uint32, selectFields []string) error
}

type MutateForm struct {
	forms.Form

	ctx                   context.Context
	courseSectionItemRepo ICourseSectionItemRepository

	input             *adminInputs.CourseSectionItemInput
	SectionItem       *models.CourseSectionItem
	CourseSectionItem *models.CourseSectionItem
	courseId          uint32
}

func NewCourseSectionItemMutateForm(
	ctx context.Context,
	input *adminInputs.CourseSectionItemInput,
	courseSectionItemRepo ICourseSectionItemRepository,
	courseSectionItem *models.CourseSectionItem,
	courseId uint32,
) *MutateForm {
	form := &MutateForm{
		ctx:                   ctx,
		Form:                  forms.Form{},
		input:                 input,
		courseSectionItemRepo: courseSectionItemRepo,
		CourseSectionItem:     courseSectionItem,
		courseId:              courseId,
	}

	form.assignAttributes()
	return form
}

func (form *MutateForm) assignAttributes() {
	form.AddAttributes(
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: titleFieldName,
			},
			Value: form.input.Title,
		},
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: typeFieldName,
			},
			Value: form.input.Type,
		},
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: contentFieldName,
			},
			Value: form.input.Content,
		},
	)
}

func (form *MutateForm) validate() error {
	log.Debug().Ctx(form.ctx).Msg("CourseSectionItemForm.MutateForm.Validate")

	form.validateTitle().validateType().validateContent().SummaryErrors()

	if form.IsValid() {
		return nil
	}

	return exceptions.NewUnprocessableContentError(translator.Translate(nil, "errValidationMsg_general"), form.Errors)
}

func (form *MutateForm) validateContent() *MutateForm {
	log.Debug().Ctx(form.ctx).Msg("CourseSectionItemForm.MutateForm.validateContent")

	content := form.GetAttribute(contentFieldName)
	if *form.input.Type == "text" {
		content.ValidateRequired(nil)

		if content.IsClean() {
			form.CourseSectionItem.Content = form.input.Content
		}
	}

	return form
}

func (form *MutateForm) validateTitle() *MutateForm {
	log.Debug().Ctx(form.ctx).Msg("CourseSectionItemForm.MutateForm.validateTitle")

	title := form.GetAttribute(titleFieldName)

	title.ValidateRequired(nil)
	title.ValidateLtEq(int64(constants.MaxStringLength), nil)

	if title.IsClean() {
		form.CourseSectionItem.Title = *form.input.Title
	}

	return form
}

func (form *MutateForm) validateType() *MutateForm {
	log.Debug().Ctx(form.ctx).Msg("CourseSectionItemForm.MutateForm.validateType")

	typeField := form.GetAttribute(typeFieldName)
	typeField.ValidateRequired(nil)

	if typeField.IsClean() {
		fieldValue := enums.CourseSectionItemType(*form.input.Type)

		if !fieldValue.IsValid() {
			typeField.AddError(translator.Translate(nil, "errValidation_notExist"))
		}

		if typeField.IsClean() {
			form.CourseSectionItem.Type = fieldValue
		}
	}

	return form
}

func (form *MutateForm) Create() error {
	log.Debug().Ctx(form.ctx).Msg("CourseSectionItemForm.MutateForm.Create")

	if validateErr := form.validate(); validateErr != nil {
		return validateErr
	}

	selectFields := []string{"title", "type", "course_section_id"}
	if form.CourseSectionItem.Type.String() == "text" {
		selectFields = append(selectFields, "content")
	}

	return form.courseSectionItemRepo.Create(form.ctx, form.CourseSectionItem, form.courseId, selectFields)
}

func (form *MutateForm) Update() error {
	log.Debug().Ctx(form.ctx).Msg("CourseSectionItemForm.MutateForm.Update")

	if validateErr := form.validate(); validateErr != nil {
		return validateErr
	}
	var selectFields []string
	if form.CourseSectionItem.Type.String() == "text" {
		selectFields = []string{"title", "content"}
	} else {
		selectFields = []string{"title"}
	}

	return form.courseSectionItemRepo.Update(form.ctx, form.CourseSectionItem, form.courseId, selectFields)
}
