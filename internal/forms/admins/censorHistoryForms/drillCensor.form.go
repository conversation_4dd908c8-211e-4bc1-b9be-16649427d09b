package censorHistoryForms

import (
	"context"
	"vibico-education-api/internal/forms"
	"vibico-education-api/internal/forms/formAttributes"
	"vibico-education-api/internal/models"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
	"github.com/rs/zerolog/log"
)

const (
	feedbackField = "Feedback"
)

type IDrillCensorHistoryRepository interface {
	CreateWithDrill(ctx context.Context, CensorHistoryRepo *models.CensorHistory, drill *models.Drill, selectFields ...string) error
}

type DrillCensorForm struct {
	forms.Form

	ctx               context.Context
	feedback          *string
	censorHistoryRepo IDrillCensorHistoryRepository
	CensorHistory     *models.CensorHistory
	Drill             *models.Drill
}

func NewDrillCensorForm(
	ctx context.Context,
	feedback *string,
	censorHistoryRepo IDrillCensorHistoryRepository,
	CensorHistory *models.CensorHistory,
	drill *models.Drill,
) *DrillCensorForm {
	form := &DrillCensorForm{
		ctx:               ctx,
		feedback:          feedback,
		censorHistoryRepo: censorHistoryRepo,
		CensorHistory:     CensorHistory,
		Drill:             drill,
	}
	form.assignAttributes()

	return form
}

func (form *DrillCensorForm) assignAttributes() {
	form.AddAttributes(
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: feedbackField,
			},
			Value: form.feedback,
		},
	)
}

func (form *DrillCensorForm) validate() error {
	log.Debug().Ctx(form.ctx).Msg("DrillCensorForm.validate")

	form.validateFeedback().
		SummaryErrors()

	if form.IsValid() {
		return nil
	}

	return exceptions.NewUnprocessableContentError(
		translator.Translate(nil, "errValidationMsg_general"),
		form.Errors,
	)
}

func (form *DrillCensorForm) validateFeedback() *DrillCensorForm {
	field := form.GetAttribute(feedbackField)
	field.ValidateRequired(nil)

	if field.IsClean() {
		form.CensorHistory.Feedback = *form.feedback
	}

	return form
}

func (form *DrillCensorForm) Save() error {
	log.Debug().Ctx(form.ctx).Msg("DrillCensorForm.Save")

	if err := form.validate(); err != nil {
		return err
	}

	selectFields := []string{"feedback", "parent_id", "parent_type", "status", "created_by"}
	return form.censorHistoryRepo.CreateWithDrill(form.ctx, form.CensorHistory, form.Drill, selectFields...)
}
