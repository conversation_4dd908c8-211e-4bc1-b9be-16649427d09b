package censorHistoryForms

import (
	"context"
	"vibico-education-api/internal/forms"
	"vibico-education-api/internal/forms/formAttributes"
	"vibico-education-api/internal/models"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
	"github.com/rs/zerolog/log"
)

type ICensorHistoryRepository interface {
	CreateWithCourse(ctx context.Context, courseCensorHistory *models.CensorHistory, course *models.Course, selectFields ...string) error
}

type CourseCensorForm struct {
	forms.Form

	ctx                     context.Context
	feedback                *string
	courseCensorHistoryRepo ICensorHistoryRepository
	CensorHistory           *models.CensorHistory
	Course                  *models.Course
}

func NewCourseCensorForm(
	ctx context.Context,
	feedback *string,
	courseCensorHistoryRepo ICensorHistoryRepository,
	courseCensorHistory *models.CensorHistory,
	course *models.Course,
) *CourseCensorForm {
	form := &CourseCensorForm{
		ctx:                     ctx,
		feedback:                feedback,
		courseCensorHistoryRepo: courseCensorHistoryRepo,
		CensorHistory:           courseCensorHistory,
		Course:                  course,
	}
	form.assignAttributes()
	return form
}

func (form *CourseCensorForm) assignAttributes() {
	form.AddAttributes(
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: feedbackField,
			},
			Value: form.feedback,
		},
	)
}

func (form *CourseCensorForm) validate() error {
	log.Debug().Ctx(form.ctx).Msg("CourseCensorForm.validate")

	form.validateFeedback().
		SummaryErrors()

	if form.IsValid() {
		return nil
	}

	return exceptions.NewUnprocessableContentError(
		translator.Translate(nil, "errValidationMsg_general"),
		form.Errors,
	)
}

func (form *CourseCensorForm) validateFeedback() *CourseCensorForm {
	field := form.GetAttribute(feedbackField)
	field.ValidateRequired(nil)

	if field.IsClean() {
		form.CensorHistory.Feedback = *form.feedback
	}

	return form
}

func (form *CourseCensorForm) Save() error {
	log.Debug().Ctx(form.ctx).Msg("CourseCensorForm.Save")

	if err := form.validate(); err != nil {
		return err
	}

	selectFields := []string{"feedback", "parent_id", "parent_type", "status", "created_by"}
	return form.courseCensorHistoryRepo.CreateWithCourse(form.ctx, form.CensorHistory, form.Course, selectFields...)
}
