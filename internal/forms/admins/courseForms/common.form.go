package adminCourseForms

import (
	"context"
	"vibico-education-api/internal/forms"
	"vibico-education-api/internal/forms/formAttributes"
	adminInputs "vibico-education-api/internal/gqls/inputs/admins"
	"vibico-education-api/internal/models"
	"vibico-education-api/internal/repository/repositories"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/constants"
	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
	"github.com/rs/zerolog/log"
)

const (
	titleNameField             = "Title"
	teacherIdNameField         = "TeacherId"
	descriptionNameField       = "Description"
	salePriceNameField         = "SalePrice"
	priceNameField             = "Price"
	bonusPointNameField        = "BonusPoint"
	bonusPointPercentNameField = "BonusPointPercent"
)

type ITeacherRepository interface {
	FindById(ctx context.Context, id uint32, preloads ...repositories.CustomPreload) (*models.Teacher, error)
}

type CommonForm struct {
	forms.Form

	ctx context.Context

	teacherRepo ITeacherRepository
	input       *adminInputs.CourseCommonInput
	Course      *models.Course
}

func (form *CommonForm) assignAttributes() {
	form.AddAttributes(
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: titleNameField,
			},
			Value: form.input.Title,
		},
		&formAttributes.StringAttribute{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: descriptionNameField,
			},
			Value: form.input.Description,
		},
		&formAttributes.IntAttribute[int32]{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: salePriceNameField,
			},
			Value: form.input.SalePrice,
		},
		&formAttributes.IntAttribute[int32]{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: priceNameField,
			},
			Value: form.input.Price,
		},
		&formAttributes.IntAttribute[int32]{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: bonusPointNameField,
			},
			Value: form.input.BonusPoint,
		},
		&formAttributes.IntAttribute[int32]{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: bonusPointPercentNameField,
			},
			Value: form.input.BonusPointPercent,
		},
		&formAttributes.IntAttribute[int32]{
			FieldAttribute: formAttributes.FieldAttribute{
				Code: teacherIdNameField,
			},
			Value: form.input.TeacherId,
		},
	)
}

func (form *CommonForm) validate() error {
	form.validateTitle().
		validateDescription().
		validateSalePrice().
		validatePrice().
		validateBonusPoint().
		validateBonusPointPercent().
		validateTeacher().
		SummaryErrors()

	if form.IsValid() {
		return nil
	}

	return exceptions.NewUnprocessableContentError(translator.Translate(nil, "errValidationMsg_general"), form.Errors)
}

func (form *CommonForm) validateTitle() *CommonForm {
	log.Debug().Ctx(form.ctx).Msg("AdminCourseForm.CommonForm.validateTitle")

	field := form.GetAttribute(titleNameField)

	field.ValidateRequired(nil)
	field.ValidateLtEq(constants.MaxStringLength, nil)

	if field.IsClean() {
		form.Course.Title = *form.input.Title
	}

	return form
}

func (form *CommonForm) validateDescription() *CommonForm {
	log.Debug().Ctx(form.ctx).Msg("AdminCourseForm.CommonForm.validateDescription")

	field := form.GetAttribute(descriptionNameField)

	if field.IsClean() {
		form.Course.Description = form.input.Description
	}

	return form
}

func (form *CommonForm) validatePrice() *CommonForm {
	log.Debug().Ctx(form.ctx).Msg("AdminCourseForm.CommonForm.validatePrice")

	field := form.GetAttribute(priceNameField)

	field.ValidateGtEq(0, nil)

	if field.IsClean() {
		form.Course.Price = form.input.Price
	}

	return form
}

func (form *CommonForm) validateSalePrice() *CommonForm {
	log.Debug().Ctx(form.ctx).Msg("AdminCourseForm.CommonForm.validateSalePrice")

	field := form.GetAttribute(salePriceNameField)

	price := form.Course.Price

	if price != nil {
		field.ValidateLtEq(*price, nil)
	}

	field.ValidateGtEq(0, nil)

	if field.IsClean() {
		form.Course.SalePrice = form.input.SalePrice
	}

	return form
}

func (form *CommonForm) validateBonusPoint() *CommonForm {
	log.Debug().Ctx(form.ctx).Msg("AdminCourseForm.CommonForm.validateBonusPoint")

	field := form.GetAttribute(bonusPointNameField)

	field.ValidateGtEq(0, nil)

	if field.IsClean() {
		form.Course.BonusPoint = form.input.BonusPoint
	}

	return form
}

func (form *CommonForm) validateBonusPointPercent() *CommonForm {
	log.Debug().Ctx(form.ctx).Msg("AdminCourseForm.CommonForm.validateBonusPointPercent")

	field := form.GetAttribute(bonusPointPercentNameField)

	field.ValidateGtEq(0, nil)
	field.ValidateLtEq(100, nil)

	if field.IsClean() {
		form.Course.BonusPointPercent = form.input.BonusPointPercent
	}

	return form
}

func (form *CommonForm) validateTeacher() *CommonForm {
	log.Debug().Ctx(form.ctx).Msg("AdminCourseForm.CommonForm.validateTeacher")

	field := form.GetAttribute(teacherIdNameField)

	field.ValidateRequired(nil)
	if field.IsClean() {
		teacher, err := form.teacherRepo.FindById(form.ctx, uint32(*form.input.TeacherId))

		if err != nil {
			field.AddError(translator.Translate(nil, "errValidationMsg_invalid"))
			return form
		}

		form.Course.TeacherId = uint32(*form.input.TeacherId)
		form.Course.Teacher = teacher
	}

	return form
}
