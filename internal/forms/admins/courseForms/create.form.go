package adminCourseForms

import (
	"context"
	"vibico-education-api/internal/enums"
	"vibico-education-api/internal/forms"
	adminInputs "vibico-education-api/internal/gqls/inputs/admins"
	"vibico-education-api/internal/models"

	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
)

type ICourseCreateRepository interface {
	Create(ctx context.Context, clubUser *models.Course, selectFields ...string) error
}

type CreateForm struct {
	CommonForm

	courseCreateRepo ICourseCreateRepository

	input *adminInputs.CourseCreateInput
}

func NewCreateForm(
	ctx context.Context,
	input *adminInputs.CourseCreateInput,
	courseCreateRepo ICourseCreateRepository,
	teacherRepo ITeacherRepository,
) *CreateForm {
	form := &CreateForm{
		CommonForm: CommonForm{
			ctx:         ctx,
			Form:        forms.Form{},
			teacherRepo: teacherRepo,
			Course:      &models.Course{Status: enums.CourseStatusDraft},
			input:       &input.CourseCommonInput,
		},

		input:            input,
		courseCreateRepo: courseCreateRepo,
	}

	form.assignAttributes()

	return form
}

func (form *CreateForm) Save() error {
	if err := form.validate(); err != nil {
		return err
	}

	if err := form.courseCreateRepo.Create(form.ctx, form.Course, form.GetAllCode()...); err != nil {
		return exceptions.NewUnprocessableContentError(err.Error(), nil)
	}

	return nil
}
