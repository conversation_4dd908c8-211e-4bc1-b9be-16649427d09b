package adminCourseForms

import (
	"context"
	"vibico-education-api/internal/forms"
	adminInputs "vibico-education-api/internal/gqls/inputs/admins"
	"vibico-education-api/internal/models"

	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
)

type ICourseUpdateRepository interface {
	Update(ctx context.Context, clubUser *models.Course, selectFields ...string) error
}

type UpdateForm struct {
	CommonForm
	courseUpdateRepo ICourseUpdateRepository

	input *adminInputs.CourseUpdateFormInput
}

func NewUpdateForm(
	ctx context.Context,
	input *adminInputs.CourseUpdateFormInput,
	courseUpdateRepo ICourseUpdateRepository,
	teacherRepo ITeacherRepository,
	course *models.Course,
) *UpdateForm {
	form := &UpdateForm{
		CommonForm: CommonForm{
			ctx:         ctx,
			Form:        forms.Form{},
			teacherRepo: teacherRepo,
			Course:      course,
			input:       &input.CourseCommonInput,
		},

		input:            input,
		courseUpdateRepo: courseUpdateRepo,
	}

	form.assignAttributes()

	return form
}

func (form *UpdateForm) Save() error {
	if err := form.validate(); err != nil {
		return err
	}

	if err := form.courseUpdateRepo.Update(form.ctx, form.Course, form.GetAllCode()...); err != nil {
		return exceptions.NewUnprocessableContentError(err.Error(), nil)
	}

	return nil
}
