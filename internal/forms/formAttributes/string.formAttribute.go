package formAttributes

import (
	translator "vibico-education-api/pkg/translators"

	"regexp"
	"unicode/utf8"

	"github.com/rs/zerolog/log"
)

// StringAttribute represents a string attribute validator.
type StringAttribute struct {
	FieldAttribute
	Value *string
}

func (attribute *StringAttribute) Present() bool {
	return StringPresent(attribute.Value)
}

func (attribute *StringAttribute) Blank() bool {
	return !attribute.Present()
}

// ValidateRequired validates if the string attribute is required.
func (attribute *StringAttribute) ValidateRequired(message *string) {
	if attribute.Present() {
		return
	}

	if message != nil {
		attribute.AddError(*message)
	} else {
		attribute.AddError(translator.Translate(nil, "errValidationMsg_required"))
	}
}

func (attribute *StringAttribute) ValidateFormat(formatter string, message *string) {
	if attribute.Blank() {
		return
	}

	re := regexp.MustCompile(formatter)

	if re.MatchString(*attribute.Value) {
		return
	}

	if message != nil {
		attribute.AddError(*message)
	} else {
		attribute.AddError(translator.Translate(nil, "errValidation_wrongFormat"))
	}
}

func (attribute *StringAttribute) ValidateGt(value interface{}, message *string) {
	if attribute.Blank() {
		return
	}

	switch value.(type) {
	case int64, int32, int16, int8, int, uint, uint8, uint16, uint32, uint64:
		valueInt64, err := ConvertToInt64(value)
		if err != nil {
			log.Error().Err(err).Msg("convert to int 64 failed")
			return
		}

		if int64(utf8.RuneCountInString(*attribute.Value)) <= valueInt64 {
			if message != nil {
				attribute.AddError(*message)
			} else {
				attribute.AddError(translator.Translate(nil, "errValidation_minLength", value))
			}
		}
	default:
		panic("Need to provide integer interface{} as params")
	}
}

func (attribute *StringAttribute) ValidateGtEq(value interface{}, message *string) {
	if attribute.Blank() {
		return
	}

	switch value.(type) {
	case int64, int32, int16, int8, int, uint, uint8, uint16, uint32, uint64:
		valueInt64, err := ConvertToInt64(value)
		if err != nil {
			log.Error().Err(err).Msg("convert to int 64 failed")
			return
		}

		if int64(utf8.RuneCountInString(*attribute.Value)) < valueInt64 {
			if message != nil {
				attribute.AddError(*message)
			} else {
				attribute.AddError(translator.Translate(nil, "errValidation_minLength", value))
			}
		}
	default:
		panic("Need to provide integer interface{} as params")
	}
}

func (attribute *StringAttribute) ValidateLt(value interface{}, message *string) {
	if attribute.Blank() {
		return
	}

	switch value.(type) {
	case int64, int32, int16, int8, int, uint, uint8, uint16, uint32, uint64:
		valueInt64, err := ConvertToInt64(value)
		if err != nil {
			log.Error().Err(err).Msg("convert to int 64 failed")
			return
		}

		if int64(utf8.RuneCountInString(*attribute.Value)) >= valueInt64 {
			if message != nil {
				attribute.AddError(*message)
			} else {
				attribute.AddError(translator.Translate(nil, "errValidation_maxLength", value))
			}
		}
	default:
		panic("Need to provide integer interface{} as params")
	}
}

func (attribute *StringAttribute) ValidateLtEq(value interface{}, message *string) {
	if attribute.Blank() {
		return
	}

	switch value.(type) {
	case int64, int32, int16, int8, int, uint, uint8, uint16, uint32, uint64:
		valueInt64, err := ConvertToInt64(value)
		if err != nil {
			log.Error().Err(err).Msg("convert to int 64 failed")
			return
		}

		if int64(utf8.RuneCountInString(*attribute.Value)) > valueInt64 {
			if message != nil {
				attribute.AddError(*message)
			} else {
				attribute.AddError(translator.Translate(nil, "errValidation_maxLength", value))
			}
		}
	default:
		panic("Need to provide integer interface{} as params")
	}
}
