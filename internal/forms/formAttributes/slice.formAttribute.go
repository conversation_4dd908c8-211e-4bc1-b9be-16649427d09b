package formAttributes

import (
	translator "vibico-education-api/pkg/translators"

	"github.com/graph-gophers/graphql-go"
)

type NestedSlices interface {
	string | int32 | graphql.ID
}

type SliceAttribute[T NestedSlices] struct {
	FieldAttribute
	Value *[]T
}

func (attribute *SliceAttribute[T]) Present() bool {
	return attribute.Value != nil && len(*attribute.Value) > 0
}

func (attribute *SliceAttribute[T]) Blank() bool {
	return !attribute.Present()
}

// ValidateRequired validates if the attribute is required.
func (attribute *SliceAttribute[T]) ValidateRequired(message *string) {
	if attribute.Present() {
		return
	}

	if message != nil {
		attribute.AddError(*message)
	} else {
		attribute.AddError(translator.Translate(nil, "errValidationMsg_required"))
	}
}
