package formAttributes

import (
	"strconv"
	translator "vibico-education-api/pkg/translators"

	"fmt"
	"math"

	"github.com/rs/zerolog/log"
)

type FloatAttribute[T Float] struct {
	FieldAttribute
	Value *T
}

func (attribute *FloatAttribute[T]) Present() bool {
	return attribute.Value != nil
}

func (attribute *FloatAttribute[T]) Blank() bool {
	return !attribute.Present()
}

// ValidateRequired validates if the attribute is required.
func (attribute *FloatAttribute[T]) ValidateRequired(message *string) {
	if attribute.Present() {
		return
	}

	if message != nil {
		attribute.AddError(*message)
	} else {
		attribute.AddError(translator.Translate(nil, "errValidationMsg_required"))
	}
}

func (attribute *FloatAttribute[T]) ValidateEq(value interface{}, message *string) {
	if attribute.Blank() {
		return
	}

	switch value.(type) {
	case int64, int32, int16, int8, int, uint, uint8, uint16, uint32, uint64, float64, float32, string:
		valueFLoat64, err := ConvertToFloat64(value)
		if err != nil {
			log.Error().Err(err).Msg("convert to float 64 failed")
			return
		}

		if float64(*attribute.Value) != valueFLoat64 {
			if message != nil {
				attribute.AddError(*message)
			} else {
				attribute.AddError(translator.Translate(nil, "errValidation_notEqual", value))
			}
		}
	default:
		panic("Need to provide float interface{} as params")
	}
}

func (attribute *FloatAttribute[T]) ValidateGt(value interface{}, message *string) {
	if attribute.Blank() {
		return
	}

	switch value.(type) {
	case int64, int32, int16, int8, int, uint, uint8, uint16, uint32, uint64, float64, float32, string:
		valueFLoat64, err := ConvertToFloat64(value)
		if err != nil {
			log.Error().Err(err).Msg("convert to float 64 failed")
			return
		}

		if float64(*attribute.Value) <= valueFLoat64 {
			if message != nil {
				attribute.AddError(*message)
			} else {
				attribute.AddError(translator.Translate(nil, "ValidationFloatMin", value))
			}
		}
	default:
		panic("Need to provide float interface{} as params")
	}
}

func (attribute *FloatAttribute[T]) ValidateGtEq(value interface{}, message *string) {
	if attribute.Blank() {
		return
	}

	switch value.(type) {
	case int64, int32, int16, int8, int, uint, uint8, uint16, uint32, uint64, float64, float32, string:
		valueFLoat64, err := ConvertToFloat64(value)
		if err != nil {
			log.Error().Err(err).Msg("convert to float 64 failed")
			return
		}

		if float64(*attribute.Value) < valueFLoat64 {
			if message != nil {
				attribute.AddError(*message)
			} else {
				attribute.AddError(translator.Translate(nil, "ValidationFloatMin", value))
			}
		}
	default:
		panic("Need to provide float interface{} as params")
	}
}

func (attribute *FloatAttribute[T]) ValidateLt(value interface{}, message *string) {
	if attribute.Blank() {
		return
	}

	switch value.(type) {
	case int64, int32, int16, int8, int, uint, uint8, uint16, uint32, uint64, float64, float32, string:
		valueFLoat64, err := ConvertToFloat64(value)
		if err != nil {
			log.Error().Err(err).Msg("convert to float 64 failed")
			return
		}

		if float64(*attribute.Value) >= valueFLoat64 {
			if message != nil {
				attribute.AddError(*message)
			} else {
				attribute.AddError(translator.Translate(nil, "ValidationFloatMax", value))
			}
		}
	default:
		panic("Need to provide float interface{} as params")
	}
}

func (attribute *FloatAttribute[T]) ValidateLtEq(value interface{}, message *string) {
	if attribute.Blank() {
		return
	}

	switch value.(type) {
	case int64, int32, int16, int8, int, uint, uint8, uint16, uint32, uint64, float64, float32, string:
		valueFLoat64, err := ConvertToFloat64(value)
		if err != nil {
			log.Error().Err(err).Msg("convert to float 64 failed")
			return
		}

		if float64(*attribute.Value) > valueFLoat64 {
			if message != nil {
				attribute.AddError(*message)
			} else {
				attribute.AddError(translator.Translate(nil, "ValidationFloatMax", value))
			}
		}
	default:
		panic("Need to provide float interface{} as params")
	}
}

func (attribute *FloatAttribute[T]) ValidateIsDivisibleBy(value interface{}, message *string) {
	if attribute.Blank() {
		return
	}

	valueInString := fmt.Sprintf("%v", value)

	switch value.(type) {
	case int64, int32, int16, int8, int, uint, uint8, uint16, uint32, uint64, float64, float32, string:
		valueFLoat64, err := ConvertToFloat64(value)
		if err != nil {
			log.Error().Err(err).Msg("convert to float 64 failed")
			return
		}

		if math.Mod(float64(*attribute.Value), valueFLoat64) != 0 {
			if message != nil {
				attribute.AddError(*message)
			} else {
				attribute.AddError(translator.Translate(nil, "errValidation_notDivisibleBy", valueInString))
			}
		}
	default:
		panic("Need to provide float interface{} as params")
	}
}

// TODO: move to lib
func ConvertToFloat64(value any) (float64, error) {
	switch v := value.(type) {
	case int:
		return float64(v), nil
	case int8:
		return float64(v), nil
	case int16:
		return float64(v), nil
	case int32:
		return float64(v), nil
	case int64:
		return float64(v), nil
	case uint:
		return float64(v), nil
	case uint8:
		return float64(v), nil
	case uint16:
		return float64(v), nil
	case uint32:
		return float64(v), nil
	case uint64:
		return float64(v), nil
	case float32:
		return float64(v), nil
	case float64:
		return v, nil
	case string:
		return strconv.ParseFloat(v, 64)
	default:
		return 0, fmt.Errorf("không thể chuyển đổi kiểu %T sang float64", value)
	}
}
