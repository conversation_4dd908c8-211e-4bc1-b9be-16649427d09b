package formAttributes

import (
	translator "vibico-education-api/pkg/translators"

	"gorm.io/datatypes"
)

type JsonAttribute struct {
	FieldAttribute
	Value *datatypes.JSON
}

func (attribute *JsonAttribute) Present() bool {
	return attribute.Value != nil
}

func (attribute *JsonAttribute) Blank() bool {
	return !attribute.Present()
}

// ValidateRequired validates if the json attribute is required.
func (attribute *JsonAttribute) ValidateRequired(message *string) {
	if attribute.Present() {
		return
	}

	if message != nil {
		attribute.AddError(*message)
	} else {
		attribute.AddError(translator.Translate(nil, "errValidationMsg_required"))
	}
}
