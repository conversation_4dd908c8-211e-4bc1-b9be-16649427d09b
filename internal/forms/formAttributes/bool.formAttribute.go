package formAttributes

import (
	translator "vibico-education-api/pkg/translators"
)

// <PERSON>olAttribute represents a bool attribute validator.
type BoolAttribute struct {
	FieldAttribute
	Value *bool
}

func (attribute *BoolAttribute) Present() bool {
	return attribute.Value != nil
}

func (attribute *BoolAttribute) Blank() bool {
	return !attribute.Present()
}

// ValidateRequired validates if the bool attribute is required.
func (attribute *BoolAttribute) ValidateRequired(message *string) {
	if attribute.Present() {
		return
	}

	if message != nil {
		attribute.AddError(*message)
	} else {
		attribute.AddError(translator.Translate(nil, "errValidationMsg_required"))
	}
}
