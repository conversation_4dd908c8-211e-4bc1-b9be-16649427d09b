package formAttributes

import (
	translator "vibico-education-api/pkg/translators"

	"fmt"
	"time"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
)

// TODO: mang qua thư viện
type Signed interface {
	~int | ~int8 | ~int16 | ~int32 | ~int64
}

type Unsigned interface {
	~uint | ~uint8 | ~uint16 | ~uint32 | ~uint64 | ~uintptr
}
type Integer interface {
	Signed | Unsigned
}

type Float interface {
	~float32 | ~float64
}

type FieldAttribute struct {
	Name   string
	Code   string
	Errors []any
}

// GetCode returns the code of the bool attribute.
func (attribute *FieldAttribute) GetCode() string {
	return attribute.Code
}

// GetFieldCode returns the code of the bool attribute.
func (attribute *FieldAttribute) GetFieldI18nCode() string {
	if attribute.Name != "" {
		return attribute.Name
	}
	return attribute.Code
}

// GetErrors returns the errors of the bool attribute.
func (attribute *FieldAttribute) GetErrors() []any {
	return attribute.Errors
}

// AddError adds an error to the bool attribute.
func (attribute *FieldAttribute) AddError(message any) {
	attribute.Errors = append(attribute.Errors, message)
}

func (attribute *FieldAttribute) AddErrorWithFieldCode(message any) {
	attribute.Errors = append(attribute.Errors, ValidationMessage(attribute.GetFieldI18nCode(), message))
}

func (attribute *FieldAttribute) IsClean() bool {
	return len(attribute.Errors) == 0
}

func (attribute *FieldAttribute) Time() *time.Time {
	return nil
}

func (attribute *FieldAttribute) Present() bool {
	panic("Need to implement")
}

func (attribute *FieldAttribute) Blank() bool {
	panic("Need to implement")
}

func (attribute *FieldAttribute) ValidateRequired(message *string) {}

func (attribute *FieldAttribute) ValidateFormat(formatter string, message *string) {}

func (attribute *FieldAttribute) ValidateEq(value any, message *string) {}

func (attribute *FieldAttribute) ValidateGt(value any, message *string) {}

func (attribute *FieldAttribute) ValidateGtEq(value any, message *string) {}

func (attribute *FieldAttribute) ValidateLt(value any, message *string) {}

func (attribute *FieldAttribute) ValidateLtEq(value any, message *string) {}

func (attribute *FieldAttribute) ValidateIsPowerOf(value any, message *string) {}

func (attribute *FieldAttribute) ValidateIsDivisibleBy(value any, message *string) {}

type FieldAttributeInterface interface {
	AddError(message any)
	AddErrorWithFieldCode(message any)
	GetCode() string
	GetFieldI18nCode() string
	GetErrors() []any
	Time() *time.Time
	IsClean() bool
	Present() bool
	Blank() bool

	// Validators
	ValidateRequired(message *string)
	ValidateIsPowerOf(value any, message *string)
	ValidateIsDivisibleBy(value any, message *string)
	ValidateFormat(formatter string, message *string)
	ValidateEq(value any, message *string)
	ValidateGt(value any, message *string)
	ValidateGtEq(value any, message *string)
	ValidateLt(value any, message *string)
	ValidateLtEq(value any, message *string)
}

// ValidationMessage returns a formatted validation message.
func ValidationMessage(column string, message any) any {
	return fmt.Sprintf("%s %s", translator.Translate(nil, utils.CamelToPascalCase(column)), message)
}
