package formAttributes

import (
	"fmt"
	"math"
	"strconv"
	"strings"
	translator "vibico-education-api/pkg/translators"

	"github.com/rs/zerolog/log"
)

type IntAttribute[T Integer] struct {
	FieldAttribute
	Value *T
}

func (attribute *IntAttribute[T]) Present() bool {
	return attribute.Value != nil
}

func (attribute *IntAttribute[T]) Blank() bool {
	return !attribute.Present()
}

// ValidateRequired validates if the attribute is required.
func (attribute *IntAttribute[T]) ValidateRequired(message *string) {
	if attribute.Present() {
		return
	}

	if message != nil {
		attribute.AddError(*message)
	} else {
		attribute.AddError(translator.Translate(nil, "errValidationMsg_required"))
	}
}

func (attribute *IntAttribute[T]) ValidateEq(value interface{}, message *string) {
	if attribute.Blank() {
		return
	}

	switch value.(type) {
	case int64, int32, int16, int8, int, uint, uint8, uint16, uint32, uint64, string:
		valueInt64, err := ConvertToInt64(value)
		if err != nil {
			log.Error().Err(err).Msg("convert to int 64 failed")
			return
		}

		if int64(*attribute.Value) != valueInt64 {
			if message != nil {
				attribute.AddError(*message)
			} else {
				attribute.AddError(translator.Translate(nil, "errValidation_notEqual", value))
			}
		}
	default:
		panic("Need to provide integer interface{} as params")
	}
}

func (attribute *IntAttribute[T]) ValidateGt(value interface{}, message *string) {
	if attribute.Blank() {
		return
	}

	switch value.(type) {
	case int64, int32, int16, int8, int, uint, uint8, uint16, uint32, uint64, string:
		valueInt64, err := ConvertToInt64(value)
		if err != nil {
			log.Error().Err(err).Msg("convert to int 64 failed")
			return
		}

		if int64(*attribute.Value) <= valueInt64 {
			if message != nil {
				attribute.AddError(*message)
			} else {
				attribute.AddError(translator.Translate(nil, "errValidation_greaterThanInt", value))
			}
		}
	default:
		panic("Need to provide integer interface{} as params")
	}
}

func (attribute *IntAttribute[T]) ValidateGtEq(value interface{}, message *string) {
	if attribute.Blank() {
		return
	}

	switch value.(type) {
	case int64, int32, int16, int8, int, uint, uint8, uint16, uint32, uint64, string:
		valueInt64, err := ConvertToInt64(value)
		if err != nil {
			log.Error().Err(err).Msg("convert to int 64 failed")
			return
		}

		if int64(*attribute.Value) < valueInt64 {
			if message != nil {
				attribute.AddError(*message)
			} else {
				attribute.AddError(translator.Translate(nil, "errValidation_greaterThanOrEqualInt", value))
			}
		}
	default:
		panic("Need to provide integer interface{} as params")
	}
}

func (attribute *IntAttribute[T]) ValidateLt(value interface{}, message *string) {
	if attribute.Blank() {
		return
	}

	switch value.(type) {
	case int64, int32, int16, int8, int, uint, uint8, uint16, uint32, uint64, string:
		valueInt64, err := ConvertToInt64(value)
		if err != nil {
			log.Error().Err(err).Msg("convert to int 64 failed")
			return
		}

		if int64(*attribute.Value) >= valueInt64 {
			if message != nil {
				attribute.AddError(*message)
			} else {
				attribute.AddError(translator.Translate(nil, "errValidation_lessThanInt", value))
			}
		}
	default:
		panic("Need to provide integer interface{} as params")
	}
}

func (attribute *IntAttribute[T]) ValidateLtEq(value interface{}, message *string) {
	if attribute.Blank() {
		return
	}

	switch value.(type) {
	case int64, int32, int16, int8, int, uint, uint8, uint16, uint32, uint64, string:
		valueInt64, err := ConvertToInt64(value)
		if err != nil {
			log.Error().Err(err).Msg("convert to int 64 failed")
			return
		}

		if int64(*attribute.Value) > valueInt64 {
			if message != nil {
				attribute.AddError(*message)
			} else {
				attribute.AddError(translator.Translate(nil, "errValidation_lessThanOrEqualInt", value))
			}
		}
	default:
		panic("Need to provide integer interface{} as params")
	}
}

func (attribute *IntAttribute[T]) ValidateIsDivisibleBy(value interface{}, message *string) {
	if attribute.Blank() {
		return
	}

	switch value.(type) {
	case int64, int32, int16, int8, int, uint, uint8, uint16, uint32, uint64, string:
		valueInt64, err := ConvertToInt64(value)
		if err != nil {
			log.Error().Err(err).Msg("convert to int 64 failed")
			return
		}

		if int64(*attribute.Value)%valueInt64 != 0 {
			if message != nil {
				attribute.AddError(*message)
			} else {
				attribute.AddError(translator.Translate(nil, "errValidation_notDivisibleBy", valueInt64))
			}
		}
	default:
		panic("Need to provide integer interface{} as params")
	}
}

func (attribute *IntAttribute[T]) ValidateIsPowerOf(value interface{}, message *string) {
	if attribute.Blank() {
		return
	}

	switch value.(type) {
	case int64, int32, int16, int8, int, uint, uint8, uint16, uint32, uint64, string:
		valueInt64, err := ConvertToInt64(value)
		if err != nil {
			log.Error().Err(err).Msg("convert to int 64 failed")
			return
		}

		if isPowerOf(int64(*attribute.Value), valueInt64) {
			return
		}

		if message != nil {
			attribute.AddError(*message)
		} else {
			attribute.AddError(translator.Translate(nil, "errValidation_validationIsPowerOf", valueInt64))
		}
	default:
		panic("Need to provide integer interface{} as params")
	}
}

// TODO: move to lib
func ConvertToInt64(value interface{}) (int64, error) {
	switch v := value.(type) {
	case int:
		return int64(v), nil
	case int8:
		return int64(v), nil
	case int16:
		return int64(v), nil
	case int32:
		return int64(v), nil
	case int64:
		return v, nil
	case uint:
		return int64(v), nil
	case uint8:
		return int64(v), nil
	case uint16:
		return int64(v), nil
	case uint32:
		return int64(v), nil
	case uint64:
		if v > math.MaxInt64 { // Kiểm tra tràn số
			return 0, fmt.Errorf("uint64 value too large for int64")
		}
		return int64(v), nil
	case string:
		return strconv.ParseInt(v, 10, 64)
	default:
		return 0, fmt.Errorf("type not supported")
	}
}

func StringPresent(s *string) bool {
	return s != nil && strings.TrimSpace(*s) != ""
}

// Hàm kiểm tra n có phải là lũy thừa của base hay không
func isPowerOf[T Integer](base, n T) bool {
	if base == 0 || base == 1 {
		return n == base
	}
	if n == 0 {
		return false
	}

	// Nếu base là số dương, n không thể là số âm
	if base > 0 && n < 0 {
		return false
	}

	// Nếu base âm, chỉ chấp nhận n dương khi mũ chẵn, n âm khi mũ lẻ
	exp := 0
	var cur T = 1
	for cur < abs(n) {
		cur *= abs(base)
		exp++
	}

	// Kiểm tra xem base^exp có đúng bằng n không
	if cur != abs(n) {
		return false
	}

	// Nếu base là số âm, cần kiểm tra chẵn/lẻ
	if base < 0 {
		return (n > 0 && exp%2 == 0) || (n < 0 && exp%2 == 1)
	}

	return true
}

// abs trả về giá trị tuyệt đối của một số nguyên
func abs[T Integer](x T) T {
	if x < 0 {
		return -x
	}

	return x
}
