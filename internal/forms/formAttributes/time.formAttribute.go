package formAttributes

import (
	translator "vibico-education-api/pkg/translators"

	"fmt"
	"time"
)

type TimeAttribute struct {
	FieldAttribute
	Value     *string
	TimeValue *time.Time
}

func (attribute *TimeAttribute) Time() *time.Time {
	return attribute.TimeValue
}

func (attribute *TimeAttribute) Present() bool {
	return StringPresent(attribute.Value)
}

func (attribute *TimeAttribute) Blank() bool {
	return !attribute.Present()
}

// ValidateRequired validates if the attribute is required.
func (attribute *TimeAttribute) ValidateRequired(message *string) {
	if attribute.Present() {
		return
	}

	if message != nil {
		attribute.AddError(*message)
	} else {
		attribute.AddError(translator.Translate(nil, "errValidationMsg_required"))
	}
}

func (attribute *TimeAttribute) ValidateFormat(formatter string, message *string) {
	if attribute.Blank() {
		return
	}

	if timeValue, err := time.ParseInLocation(formatter, *attribute.Value, time.Local); err != nil {
		if message != nil {
			attribute.AddError(*message)
		} else {
			attribute.AddError(translator.Translate(nil, "errValidation_wrongFormat"))
		}
	} else {
		attribute.TimeValue = &timeValue
	}
}

func (attribute *TimeAttribute) ValidateEq(value interface{}, message *string) {
	switch v := value.(type) {
	case time.Time:
		if attribute.TimeValue == nil || attribute.TimeValue.Equal(v) {
			return
		}

		if message != nil {
			attribute.AddError(*message)
		} else {
			attribute.AddError(translator.Translate(nil, "ValidationTimeMin", fmt.Sprintf("%+v", v)))
		}
	default:
		panic("Need to provide time interface{} as params")
	}
}

func (attribute *TimeAttribute) ValidateGt(value interface{}, message *string) {
	switch v := value.(type) {
	case time.Time:
		if attribute.TimeValue == nil || attribute.TimeValue.After(v) {
			return
		}

		if message != nil {
			attribute.AddError(*message)
		} else {
			attribute.AddError(translator.Translate(nil, "ValidationTimeMin", fmt.Sprintf("%+v", v)))
		}
	default:
		panic("Need to provide time interface{} as params")
	}
}

func (attribute *TimeAttribute) ValidateGtEq(value interface{}, message *string) {
	switch v := value.(type) {
	case time.Time:
		if attribute.TimeValue != nil && v.After(*attribute.TimeValue) {
			if message != nil {
				attribute.AddError(*message)
			} else {
				attribute.AddError(translator.Translate(nil, "ValidationTimeMin", fmt.Sprintf("%+v", v)))
			}
		}
	default:
		panic("Need to provide time interface{} as params")
	}
}

func (attribute *TimeAttribute) ValidateLt(value interface{}, message *string) {
	switch v := value.(type) {
	case time.Time:
		if attribute.TimeValue == nil || attribute.TimeValue.Before(v) {
			return
		}

		if message != nil {
			attribute.AddError(*message)
		} else {
			attribute.AddError(translator.Translate(nil, "ValidationTimeMax", fmt.Sprintf("%+v", v)))
		}
	default:
		panic("Need to provide time interface{} as params")
	}
}

func (attribute *TimeAttribute) ValidateLtEq(value interface{}, message *string) {
	switch v := value.(type) {
	case time.Time:
		if attribute.TimeValue != nil && v.Before(*attribute.TimeValue) {
			if message != nil {
				attribute.AddError(*message)
			} else {
				attribute.AddError(translator.Translate(nil, "ValidationTimeMax", fmt.Sprintf("%+v", v)))
			}
		}
	default:
		panic("Need to provide time interface{} as params")
	}
}
