package cronjobs

import (
	"context"
	"fmt"
	"vibico-education-api/internal/repository"
	"vibico-education-api/internal/repository/repositories"
	globalServices "vibico-education-api/internal/services/globals"

	"github.com/rs/zerolog/log"
)

func CleanupOrphanVideos(ctx context.Context, repos repository.IRepositories) error {
	log.Info().Msg("Starting orphan videos cleanup")

	// Get all orphan videos
	videos, err := repos.VideoRepo().FindOrphans(ctx,
		repositories.CustomPreload{
			Key: "VideoUpload",
		},
		repositories.CustomPreload{
			Key: "VideoPlatforms",
		},
	)
	if err != nil {
		return fmt.Errorf("failed to get videos: %w", err)
	}

	// Delete videos
	for _, video := range *videos {
		deleteVideoService := globalServices.NewVideoAfterDestroyService(ctx, &video)
		repos.VideoRepo().Delete(ctx, &video)
		err = deleteVideoService.Execute()
		if err != nil {
			log.Error().Err(err).Msg("Failed to delete video")
			continue
		}
	}

	log.Info().Msg("Orphan videos cleanup completed")
	return nil
}
