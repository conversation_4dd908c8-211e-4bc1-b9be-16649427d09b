package middlewares

import (
	"net/http"
	"regexp"
	"strings"
	"vibico-education-api/pkg/helpers"

	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
)

type ResolverAuthInterface interface {
	Auth() error
	GetPublicOperations() map[string]struct{}
	SetOperations(operations map[string]map[string]struct{})
}

func GraphqlAuthHandle(resolver ResolverAuthInterface) gin.HandlerFunc {
	return func(c *gin.Context) {
		requestBody, err := helpers.GetRequestBody(c)
		if err != nil {
			log.Error().Err(err).Ctx(c).Msg("Error when get request body")
			c.Next()
			return
		}

		blurredRequest := helpers.BlurSensitiveDataMap(requestBody)

		query, ok := blurredRequest["query"].(string)
		if !ok {
			log.Error().Err(err).Ctx(c).Msg("Error when get query from request body")
			c.Next()
			return
		}

		operations := ExtractGraphQLFields(query)

		resolver.SetOperations(operations)

		publicOperations := resolver.GetPublicOperations()

		for op := range operations {
			if _, exists := publicOperations[op]; !exists {
				if err := resolver.Auth(); err != nil {
					exception := exceptions.NewUnauthorizedError(nil)

					c.AbortWithStatusJSON(http.StatusOK, gin.H{
						"errors": []gin.H{
							{
								"message":    exception.Error(),
								"path":       []string{"auth"},
								"extensions": exception.Extensions(),
							},
						},
						"data": nil,
					})
				}

				return
			}
		}

		c.Next()
	}
}

func ExtractGraphQLFields(query string) map[string]map[string]struct{} {
	fields := make(map[string]map[string]struct{})
	query = cleanQuery(query)

	startIndex := strings.Index(query, "{")
	if startIndex == -1 {
		return fields
	}

	query = query[startIndex+1 : len(query)-1]
	extractFields(query, "", "", &fields)
	return fields
}

// TODO: refactor this function
func extractFields(query, root string, prefix string, fields *map[string]map[string]struct{}) {
	level := 0
	fieldName := ""
	subQuery := ""
	preFieldName := ""
	parent := ""
	skipArgs := false

	for i := range query {
		char := query[i]

		if char == '(' || char == ')' {
			skipArgs = char == '('
			continue
		}

		if skipArgs {
			continue
		}

		switch char {
		case '{':
			level++
			if level == 1 {
				subQuery = ""

				parent = preFieldName
				preFieldName = ""

				// Initialize map for new parent if not exists
				if _, ok := (*fields)[parent]; !ok && root == "" {
					root = parent
					(*fields)[parent] = make(map[string]struct{})
				}
			} else {
				subQuery += string(char)
			}
		case '}':
			level--
			if level == 0 {
				extractFields(subQuery, root, parent+".", fields)
			} else {
				subQuery += string(char)
			}
		default:
			if level == 0 {
				if char == ' ' && fieldName != "" {
					if prefix == "" {
						root = fieldName
						// Initialize map for root level field if not exists
						if _, ok := (*fields)[fieldName]; !ok {
							(*fields)[fieldName] = make(map[string]struct{})
						}
					} else {
						// Add field to parent's map after removing the root key (root key can be query or mutation)
						// Examples:
						// - For mutation "createUser": "createUser.message" becomes "message"
						// - For nested fields: "createUser.user.name" becomes "user.name"
						prefixField := strings.TrimPrefix(prefix, root+".")
						(*fields)[root][prefixField+fieldName] = struct{}{}
					}

					preFieldName = fieldName
					fieldName = ""
				} else if char != ' ' {
					fieldName += string(char)
				}
			} else {
				subQuery += string(char)
			}
		}
	}
}

func cleanQuery(query string) string {
	// Create a map to store fragment contents
	fragments := make(map[string]string)

	// Find and store fragment definitions
	fragmentRegex := regexp.MustCompile(`fragment\s+(\w+)\s+on\s+\w+\s*{([^}]*)}`)
	matches := fragmentRegex.FindAllStringSubmatch(query, -1)
	for _, match := range matches {
		if len(match) >= 3 {
			fragments[match[1]] = strings.TrimSpace(match[2])
		}
	}

	// Remove fragment definitions
	query = fragmentRegex.ReplaceAllString(query, "")

	// Replace fragment spreads with corresponding content
	spreadRegex := regexp.MustCompile(`\.{3}(\w+)`)
	query = spreadRegex.ReplaceAllStringFunc(query, func(spread string) string {
		fragmentName := strings.TrimPrefix(spread, "...")
		if content, ok := fragments[fragmentName]; ok {
			return content
		}
		return ""
	})

	// Normalize whitespace
	query = strings.ReplaceAll(query, "\n", " ")
	query = regexp.MustCompile(`\s+`).ReplaceAllString(query, " ")

	// Format query for better readability
	query = strings.ReplaceAll(query, "{", " { ")
	query = strings.ReplaceAll(query, "}", " } ")

	return strings.TrimSpace(query)
}
