package middlewares

import (
	"errors"
	"strings"
	"vibico-education-api/constants"
	"vibico-education-api/internal/models"
	"vibico-education-api/internal/repository"
	"vibico-education-api/setup/grpc_clients"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"

	pb "github.com/BehemothLtd/vibico-auth/proto/auth"
)

func AdminAuth(ctx *gin.Context, repos repository.IRepositories) (*models.Admin, error) {
	log.Debug().Ctx(ctx).Msg("Middlewares.AdminAuth")

	protoUser, err := GetProtoUser(ctx)
	if err != nil {
		log.Error().Err(err).Ctx(ctx).Msg("GetProtoUser error")
		return nil, err
	}

	admin, err := repos.AdminRepo().FindByAuthId(ctx, protoUser.Id)
	if err != nil {
		log.Error().Err(err).Ctx(ctx).Msg("FindByAuthId error")
		return nil, err
	}

	return admin, nil
}

func TeacherAuth(ctx *gin.Context, repos repository.IRepositories) (*models.Teacher, error) {
	log.Debug().Ctx(ctx).Msg("Middlewares.TeacherAuth")

	protoUser, err := GetProtoUser(ctx)
	if err != nil {
		log.Error().Err(err).Ctx(ctx).Msg("GetProtoUser error")
		return nil, err
	}

	teacher, err := repos.TeacherRepo().FindByAuthId(ctx, protoUser.Id)
	if err != nil {
		log.Error().Err(err).Ctx(ctx).Msg("FindByAuthId error")
		return nil, err
	}

	return teacher, nil
}

func UserAuth(ctx *gin.Context, repos repository.IRepositories) (*models.User, error) {
	log.Debug().Ctx(ctx).Msg("Middlewares.UserAuth")

	protoUser, err := GetProtoUser(ctx)
	if err != nil {
		log.Error().Err(err).Ctx(ctx).Msg("GetProtoUser error")
		return nil, err
	}

	user, err := repos.UserRepo().FindByAuthId(ctx, protoUser.Id)
	if err != nil {
		log.Error().Err(err).Ctx(ctx).Msg("FindByAuthId error")
		return nil, err
	}

	return user, nil
}

func GetBearerToken(ctx *gin.Context) (string, error) {
	log.Debug().Ctx(ctx).Msg("Middlewares.GetBearerToken")

	authHeader := ctx.Request.Header.Get(constants.AuthorizationHeader)
	tokenString := strings.TrimPrefix(authHeader, constants.BearerKey+" ")

	if strings.TrimSpace(tokenString) == "" {
		tokenString = ctx.Query("token")
	}

	if strings.TrimSpace(tokenString) == "" {
		return "", errors.New("token is empty")
	}

	return tokenString, nil
}

func GetProtoUser(ctx *gin.Context) (*pb.User, error) {
	log.Debug().Ctx(ctx).Msg("Middlewares.GetProtoUser")

	tokenString, err := GetBearerToken(ctx)
	if err != nil {
		log.Error().Err(err).Ctx(ctx).Msg("GetBearerToken error")
		return nil, err
	}

	grpcResponse, err := grpc_clients.AuthClient().GetUserByAccessToken(
		grpc_clients.NewCtx(ctx),
		&pb.UserVerifyRequest{Token: tokenString, IdentityPoolId: grpc_clients.PoolId()},
	)
	if err != nil {
		log.Error().Err(err).Ctx(ctx).Msg("Grpc GetUserByAccessToken error")
		return nil, err
	}

	return grpcResponse.User, nil
}
