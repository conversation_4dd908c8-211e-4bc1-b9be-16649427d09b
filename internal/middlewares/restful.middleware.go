package middlewares

import (
	"net/http"
	"vibico-education-api/constants"
	"vibico-education-api/internal/repository"

	"github.com/gin-gonic/gin"
)

func AdminAuthMiddleware(repos repository.IRepositories) gin.HandlerFunc {
	return func(c *gin.Context) {
		admin, err := AdminAuth(c, repos)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
			return
		}

		c.Set(constants.CurrentUserID, admin.ID)
		c.Set(constants.CurrentUserRole, "Admin")

		c.Next()
	}
}

func UserAuthMiddleware(repos repository.IRepositories) gin.HandlerFunc {
	return func(c *gin.Context) {
		user, err := UserAuth(c, repos)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
			return
		}

		c.Set(constants.CurrentUserID, user.ID)
		c.Set(constants.CurrentUserRole, "User")

		c.Next()
	}
}

func TeacherAuthMiddleware(repos repository.IRepositories) gin.HandlerFunc {
	return func(c *gin.Context) {
		teacher, err := TeacherAuth(c, repos)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
			return
		}

		c.Set(constants.CurrentUserID, teacher.ID)
		c.Set(constants.CurrentUserRole, "Teacher")

		c.Next()
	}
}
