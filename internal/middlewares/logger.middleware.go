package middlewares

import (
	"slices"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
)

var ignoreLogPaths = []string{"/healthz"}

func RequestLogger() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		if slices.Contains(ignoreLogPaths, ctx.Request.RequestURI) {
			ctx.Next()
			return
		}

		log.Info().Ctx(ctx).Msg("Received Request")
		startTime := time.Now()

		ctx.Next()

		endTime := time.Now()
		statusCode := ctx.Writer.Status()

		log.Info().Ctx(ctx).
			TimeDiff("latency", endTime, startTime).
			Time("start", startTime).
			Time("end", endTime).
			Int("status", statusCode).
			Msg("Request Completed")
	}
}
