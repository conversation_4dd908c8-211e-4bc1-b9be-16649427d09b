package video

import (
	"bytes"
	"context"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"strconv"
	"strings"
	"time"
)

// Metadata contains video file information
type Metadata struct {
	Duration  float64
	Width     int
	Height    int
	Format    string
	Codec     string
	Bitrate   string
	FrameRate float64
}

// VideoService provides methods for video manipulation using ffmpeg
type VideoService struct {
	ffmpegPath string
}

// NewVideoService creates a new VideoService instance
func NewVideoService(ffmpegPath string) (*VideoService, error) {
	// If ffmpegPath is empty, try to find ffmpeg in system PATH
	if ffmpegPath == "" {
		var err error
		ffmpegPath, err = exec.LookPath("ffmpeg")
		if err != nil {
			return nil, fmt.Errorf("ffmpeg not found in system PATH: %v", err)
		}
	} else {
		// Verify that provided ffmpeg path exists
		if _, err := os.Stat(ffmpegPath); err != nil {
			return nil, fmt.Errorf("ffmpeg not found at path %s: %v", ffmpegPath, err)
		}
	}

	// Verify that ffprobe is also available (needed for metadata)
	ffprobePath := strings.Replace(ffmpegPath, "ffmpeg", "ffprobe", 1)
	if _, err := os.Stat(ffprobePath); err != nil {
		// If ffprobe not found with path replacement, try to look it up in system PATH
		_, err = exec.LookPath("ffprobe")
		if err != nil {
			return nil, fmt.Errorf("ffprobe not found, which is required for metadata extraction: %v", err)
		}
	}

	return &VideoService{
		ffmpegPath: ffmpegPath,
	}, nil
}

// GetMetadata retrieves video file metadata optimized for streaming workflows
func (s *VideoService) GetMetadata(inputPath string) (Metadata, error) {
	var metadata Metadata
	var stdout, stderr bytes.Buffer

	// Try to find ffprobe path based on ffmpeg path
	ffprobePath := strings.Replace(s.ffmpegPath, "ffmpeg", "ffprobe", 1)
	if _, err := os.Stat(ffprobePath); err != nil {
		// If ffprobe not found with path replacement, try to look it up in system PATH
		ffprobePath, err = exec.LookPath("ffprobe")
		if err != nil {
			return metadata, fmt.Errorf("ffprobe not found: %v", err)
		}
	}

	// Use optimized settings for streaming use cases
	cmd := exec.Command(
		ffprobePath,
		"-v", "quiet",
		"-print_format", "json",
		"-show_format",
		"-show_streams",
		"-probesize", "10000000", // 10MB probe size for accurate metadata
		"-analyzeduration", "10000000", // 10 seconds analysis
		inputPath,
	)
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr

	// Use a context with timeout to prevent hanging on problematic files
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	cmd = exec.CommandContext(ctx, cmd.Path, cmd.Args[1:]...)
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr

	if err := cmd.Run(); err != nil {
		// If timeout or other error, fall back to direct measurements
		width, height, err1 := s.GetVideoResolution(inputPath)
		duration, err2 := s.GetVideoDuration(inputPath)

		if err1 == nil && err2 == nil {
			metadata.Width = width
			metadata.Height = height
			metadata.Duration = duration
			return metadata, nil
		}

		return metadata, fmt.Errorf("failed to get metadata: %v, stderr: %s", err, stderr.String())
	}

	// In a real implementation, you would parse the JSON output
	// This is a simplified version that would need JSON parsing
	output := stdout.String()

	// Extract basic metadata (simplified approach)
	if durationStr := extractValue(output, "duration"); durationStr != "" {
		metadata.Duration, _ = strconv.ParseFloat(durationStr, 64)
	}
	if widthStr := extractValue(output, "width"); widthStr != "" {
		metadata.Width, _ = strconv.Atoi(widthStr)
	}
	if heightStr := extractValue(output, "height"); heightStr != "" {
		metadata.Height, _ = strconv.Atoi(heightStr)
	}
	metadata.Format = extractValue(output, "format_name")
	metadata.Codec = extractValue(output, "codec_name")
	metadata.Bitrate = extractValue(output, "bit_rate")

	if fpsStr := extractValue(output, "r_frame_rate"); fpsStr != "" {
		parts := strings.Split(fpsStr, "/")
		if len(parts) == 2 {
			num, _ := strconv.ParseFloat(parts[0], 64)
			den, _ := strconv.ParseFloat(parts[1], 64)
			if den > 0 {
				metadata.FrameRate = num / den
			}
		}
	}

	return metadata, nil
}

// Helper function to extract values from ffmpeg output (simplified)
func extractValue(output, key string) string {
	// This is a simplified implementation
	// A real implementation would use proper JSON parsing
	index := strings.Index(output, "\""+key+"\"")
	if index == -1 {
		return ""
	}

	valueStart := strings.Index(output[index:], ":") + index
	if valueStart == -1 {
		return ""
	}

	valueStart = strings.Index(output[valueStart:], "\"") + valueStart + 1
	if valueStart == -1 {
		return ""
	}

	valueEnd := strings.Index(output[valueStart:], "\"") + valueStart
	if valueEnd == -1 {
		return ""
	}

	return output[valueStart:valueEnd]
}

// ConvertFormat converts a video to the specified format
func (s *VideoService) ConvertFormat(inputPath, outputPath, format string) error {
	args := []string{
		"-i", inputPath,
		"-y", // Overwrite output file if it exists
		outputPath,
	}

	_, _, err := s.RunFfmpeg(args)
	return err
}

// Trim cuts a video from start time for the specified duration
func (s *VideoService) Trim(inputPath, outputPath string, start, duration time.Duration) error {
	startSeconds := fmt.Sprintf("%f", start.Seconds())
	durationSeconds := fmt.Sprintf("%f", duration.Seconds())

	args := []string{
		"-i", inputPath,
		"-ss", startSeconds,
		"-t", durationSeconds,
		"-c:v", "copy", // Copy video codec to avoid re-encoding
		"-c:a", "copy", // Copy audio codec
		"-y", // Overwrite output file if it exists
		outputPath,
	}

	_, _, err := s.RunFfmpeg(args)
	return err
}

// Merge combines multiple videos into a single file
func (s *VideoService) Merge(inputs []string, outputPath string) error {
	// Create a temporary file containing the list of input files
	tmpFile, err := os.CreateTemp("", "ffmpeg-concat-*.txt")
	if err != nil {
		return fmt.Errorf("failed to create temp file: %v", err)
	}
	defer os.Remove(tmpFile.Name())

	// Write the list of files to the temporary file
	for _, input := range inputs {
		// Format each line according to ffmpeg concat protocol requirements
		if _, err := fmt.Fprintf(tmpFile, "file '%s'\n", input); err != nil {
			return fmt.Errorf("failed to write to temp file: %v", err)
		}
	}
	tmpFile.Close()

	args := []string{
		"-f", "concat",
		"-safe", "0", // Allow absolute paths
		"-i", tmpFile.Name(),
		"-c", "copy", // Copy both audio and video to avoid re-encoding
		"-y", // Overwrite output file if it exists
		outputPath,
	}

	_, _, err = s.RunFfmpeg(args)
	return err
}

// ExtractFrames extracts frames from the video at the specified rate
func (s *VideoService) ExtractFrames(inputPath, outputDir string, rate int) error {
	// Ensure output directory exists
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		return fmt.Errorf("failed to create output directory: %v", err)
	}

	outputPattern := filepath.Join(outputDir, "frame-%04d.jpg")

	args := []string{
		"-i", inputPath,
		"-vf", fmt.Sprintf("fps=%d", rate),
		"-q:v", "2", // Quality level (1 is highest, 31 is lowest)
		"-y", // Overwrite output files if they exist
		outputPattern,
	}

	_, _, err := s.RunFfmpeg(args)
	return err
}

// GenerateThumbnail creates a thumbnail from the video at the specified time
// GenerateThumbnail creates a thumbnail from the video at the specified time
func (s *VideoService) GenerateThumbnail(inputPath, outputPath string, at *time.Duration) error {
	var atSeconds string

	// Check if input file exists
	if _, err := os.Stat(inputPath); err != nil {
		return fmt.Errorf("input file not found: %v", err)
	}

	// If at is nil, calculate the middle point of the video
	if at == nil {
		// Get video metadata to find its duration
		metadata, err := s.GetMetadata(inputPath)
		if err != nil {
			return fmt.Errorf("failed to get video duration: %v", err)
		}

		// Calculate middle point
		midPoint := time.Duration(metadata.Duration * float64(time.Second) / 2)
		at = &midPoint
		fmt.Printf("No timestamp provided, using video midpoint: %.2f seconds\n", metadata.Duration/2)
	}

	atSeconds = fmt.Sprintf("%f", at.Seconds())

	// Create output directory if it doesn't exist
	outputDir := filepath.Dir(outputPath)
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		return fmt.Errorf("failed to create output directory: %v", err)
	}

	args := []string{
		"-i", inputPath,
		"-ss", atSeconds,
		"-vframes", "1", // Extract 1 frame only
		"-q:v", "2", // Quality level
		"-y", // Overwrite output file if it exists
		outputPath,
	}

	_, stderr, err := s.RunFfmpeg(args)
	if err != nil {
		return fmt.Errorf("failed to generate thumbnail: %v\nffmpeg output: %s", err, stderr.String())
	}

	// Verify output file was created
	if _, err := os.Stat(outputPath); err != nil {
		return fmt.Errorf("failed to create thumbnail file: %v", err)
	}

	return nil
}

// Resize changes the resolution of a video
func (s *VideoService) Resize(inputPath, outputPath string, width, height int) error {
	args := []string{
		"-i", inputPath,
		"-vf", fmt.Sprintf("scale=%d:%d", width, height),
		"-y", // Overwrite output file if it exists
		outputPath,
	}

	_, _, err := s.RunFfmpeg(args)
	return err
}

// AddWatermark adds a watermark image to the video
// Uses an optimized approach with streaming API:
// 1. Uses a single FFmpeg process with pipe mechanism
// 2. Applies hardware acceleration when available
// 3. Optimizes buffer sizes for smooth streaming
// 4. Controls memory usage with proper parameters
func (s *VideoService) AddWatermark(inputPath, watermarkPath, outputPath, position string) error {
	// Determine overlay position
	overlay := ""
	switch position {
	case "top-left":
		overlay = "10:10"
	case "top-right":
		overlay = "(main_w-overlay_w-10):10"
	case "bottom-left":
		overlay = "10:(main_h-overlay_h-10)"
	case "bottom-right":
		overlay = "(main_w-overlay_w-10):(main_h-overlay_h-10)"
	case "center":
		overlay = "(main_w-overlay_w)/2:(main_h-overlay_h)/2"
	default:
		overlay = "10:10" // Default to top-left if unspecified
	}

	// Create temp directory for intermediate files
	tempDir, err := os.MkdirTemp("", "ffmpeg_watermark_*")
	if err != nil {
		return fmt.Errorf("failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Get video metadata for intelligent processing decisions
	metadata, err := s.GetMetadata(inputPath)
	if err != nil {
		return fmt.Errorf("failed to get video metadata: %v", err)
	}

	// Handle zero width/height in metadata
	if metadata.Width <= 0 || metadata.Height <= 0 {
		// Fallback to direct measurement
		width, height, err := s.GetVideoResolution(inputPath)
		if err != nil {
			fmt.Printf("Warning: Could not determine video resolution, using defaults\n")
			metadata.Width = 1280 // Default HD width
			metadata.Height = 720 // Default HD height
		} else {
			metadata.Width = width
			metadata.Height = height
			fmt.Printf("Using directly measured resolution: %dx%d\n", width, height)
		}
	}

	// Scale the watermark image once
	tempWatermarkPath := filepath.Join(tempDir, "watermark_scaled.png")

	// Calculate watermark size - always 1/10 of video width as per spec
	watermarkTargetWidth := metadata.Width / 10

	// Set minimum size to ensure it's visible
	if watermarkTargetWidth < 40 {
		watermarkTargetWidth = 40 // Minimum size to be visible
	}

	fmt.Printf("Video resolution: %dx%d, Target watermark width: %d\n", metadata.Width, metadata.Height, watermarkTargetWidth)

	// Scale watermark
	scaleArgs := []string{
		"-i", watermarkPath,
		"-vf", fmt.Sprintf("scale=%d:-1", watermarkTargetWidth),
		"-y",
		tempWatermarkPath,
	}

	var stdout, stderr bytes.Buffer
	scaleCmd := exec.Command(s.ffmpegPath, scaleArgs...)
	scaleCmd.Stdout = &stdout
	scaleCmd.Stderr = &stderr

	if err := scaleCmd.Run(); err != nil {
		return fmt.Errorf("failed to scale watermark: %v, stderr: %s", err, stderr.String())
	}

	return s.ProcessStreamingWatermark(
		inputPath,
		tempWatermarkPath,
		outputPath,
		overlay,
		metadata,
	)
}

// ProcessStreamingWatermark applies watermark to video using streaming approach
// - Uses a single FFmpeg command with streaming
// - Applies hardware acceleration
// - Optimizes buffer sizes and memory usage
// - Avoids segment-based processing completely
func (s *VideoService) ProcessStreamingWatermark(
	inputPath, watermarkPath, outputPath, overlay string, metadata Metadata,
) error {
	// Create output directory if it doesn't exist
	outputDir := filepath.Dir(outputPath)
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		return fmt.Errorf("failed to create output directory: %v", err)
	}

	// Build the scale and fps filter based on input video properties
	scaleFilter := "format=yuv420p" // Default to just ensure correct pixel format
	if metadata.Height > 1080 || metadata.Width > 1920 {
		scaleFilter = "scale='min(iw,1920)':'min(ih,1080)':force_original_aspect_ratio=decrease,format=yuv420p"
	}

	fpsFilter := "" // Default to keep original fps
	if metadata.FrameRate > 30 {
		fpsFilter = ",fps=30"
	}

	// Add pad filter to ensure even width and height
	padFilter := ",pad=ceil(iw/2)*2:ceil(ih/2)*2"

	// Build the filter chain
	filterChain := fmt.Sprintf("[0:v]%s%s%s[scaled];[scaled][1:v]overlay=%s[out]", scaleFilter, fpsFilter, padFilter, overlay)

	// Build a single ffmpeg command with highly optimized parameters for faster processing
	// while maintaining acceptable quality
	args := []string{
		"-loglevel", "error", // Use "error" for less noisy logs in production
		"-hide_banner",     // Hide ffmpeg banner for cleaner output
		"-hwaccel", "auto", // Enable hardware acceleration if available
		"-hwaccel_output_format", "auto", // Use hardware output format when possible
		"-i", inputPath,
		"-i", watermarkPath,
		"-filter_complex", filterChain,
		"-map", "[out]",
		"-map", "0:a?", // Copy audio if present
		"-c:v", "h264", // Use h264 for better compatibility
		"-c:a", "aac", // Use AAC for audio
		"-b:v", "4M", // Lower bitrate for faster encoding
		"-maxrate", "6M", // Maximum bitrate
		"-bufsize", "8M", // Buffer size for rate control
		"-preset", "veryfast", // Fastest preset for encoding speed
		"-tune", "fastdecode", // Optimize for fast decoding
		"-max_muxing_queue_size", "4096", // Increase muxing queue for complex streams
		"-threads", "0", // Use optimal number of threads
		"-movflags", "+faststart", // Optimize for web playback
		"-g", "60", // Keyframe interval (2 seconds at 30fps)
		"-rc-lookahead", "20", // Reduced lookahead for faster encoding
		"-y", // Overwrite output file if it exists
		outputPath,
	}

	fmt.Printf("Processing video with watermark using optimized streaming parameters...\n")
	_, stderr, err := s.RunFfmpeg(args)
	if err != nil {
		return fmt.Errorf("failed to process video with watermark: %v\nffmpeg output: %s", err, stderr.String())
	}

	// Verify output file was created
	if _, err := os.Stat(outputPath); err != nil {
		return fmt.Errorf("failed to create output file: %v", err)
	}

	return nil
}

// GetVideoDuration gets video duration directly using ffprobe
// Enhanced for streaming approach to minimize full file reads
func (s *VideoService) GetVideoDuration(inputPath string) (float64, error) {
	ffprobePath := strings.Replace(s.ffmpegPath, "ffmpeg", "ffprobe", 1)
	if _, err := os.Stat(ffprobePath); err != nil {
		ffprobePath, err = exec.LookPath("ffprobe")
		if err != nil {
			return 0, fmt.Errorf("ffprobe not found: %v", err)
		}
	}

	var stdout, stderr bytes.Buffer
	// Use optimized approach: read only header information instead of scanning the whole file
	cmd := exec.Command(
		ffprobePath,
		"-v", "error",
		"-select_streams", "v:0",
		"-show_entries", "format=duration",
		"-of", "default=noprint_wrappers=1:nokey=1",
		"-probesize", "5000000", // 5MB probe size
		"-analyzeduration", "10000000", // 10 seconds analysis
		inputPath,
	)
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr

	if err := cmd.Run(); err != nil {
		return 0, fmt.Errorf("failed to get duration: %v, stderr: %s", err, stderr.String())
	}

	durationStr := strings.TrimSpace(stdout.String())
	duration, err := strconv.ParseFloat(durationStr, 64)
	if err != nil {
		return 0, fmt.Errorf("failed to parse duration: %v", err)
	}

	return duration, nil
}

// GetVideoResolution gets video width and height directly using ffprobe
// Enhanced for streaming approach with optimized buffering
func (s *VideoService) GetVideoResolution(inputPath string) (int, int, error) {
	ffprobePath := strings.Replace(s.ffmpegPath, "ffmpeg", "ffprobe", 1)
	if _, err := os.Stat(ffprobePath); err != nil {
		ffprobePath, err = exec.LookPath("ffprobe")
		if err != nil {
			return 0, 0, fmt.Errorf("ffprobe not found: %v", err)
		}
	}

	var stdout, stderr bytes.Buffer
	cmd := exec.Command(
		ffprobePath,
		"-v", "error",
		"-select_streams", "v:0",
		"-show_entries", "stream=width,height",
		"-of", "csv=s=x:p=0",
		"-probesize", "5000000", // 5MB probe size to minimize read
		"-analyzeduration", "5000000", // 5 seconds analysis
		inputPath,
	)
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr

	if err := cmd.Run(); err != nil {
		return 0, 0, fmt.Errorf("failed to get resolution: %v, stderr: %s", err, stderr.String())
	}

	// Parse output format like "1920x1080"
	dimensions := strings.TrimSpace(stdout.String())
	parts := strings.Split(dimensions, "x")
	if len(parts) != 2 {
		return 0, 0, fmt.Errorf("unexpected resolution format: %s", dimensions)
	}

	width, err := strconv.Atoi(parts[0])
	if err != nil {
		return 0, 0, fmt.Errorf("failed to parse width: %v", err)
	}

	height, err := strconv.Atoi(parts[1])
	if err != nil {
		return 0, 0, fmt.Errorf("failed to parse height: %v", err)
	}

	return width, height, nil
}

// ExtractAudio extracts the audio track from a video
func (s *VideoService) ExtractAudio(inputPath, outputPath, format string) error {
	args := []string{
		"-i", inputPath,
		"-vn",             // Disable video
		"-acodec", format, // Set audio codec based on format
		"-y", // Overwrite output file if it exists
		outputPath,
	}

	_, _, err := s.RunFfmpeg(args)
	return err
}

// MergeAudio replaces or adds an audio track to a video
func (s *VideoService) MergeAudio(inputVideoPath, inputAudioPath, outputPath string) error {
	args := []string{
		"-i", inputVideoPath,
		"-i", inputAudioPath,
		"-map", "0:v", // Use video from first input
		"-map", "1:a", // Use audio from second input
		"-c:v", "copy", // Copy video without re-encoding
		"-shortest", // Finish encoding when the shortest input stream ends
		"-y",        // Overwrite output file if it exists
		outputPath,
	}

	_, _, err := s.RunFfmpeg(args)
	return err
}

// RunFfmpeg runs ffmpeg with the given arguments and returns stdout and stderr
// This enhanced version adds better support for streaming operations:
// - Improved error handling
// - Resource management for long-running processes
// - Configurable buffer sizes
func (s *VideoService) RunFfmpeg(args []string) (stdout, stderr bytes.Buffer, err error) {
	cmd := exec.Command(s.ffmpegPath, args...)
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr

	// Set environment variables to improve performance
	cmd.Env = append(os.Environ(),
		"FFREPORT=file=ffreport.log:level=32", // Enable detailed reporting
		"AV_LOG_FORCE_NOCOLOR=1",              // Disable color in logs
		"FFMPEG_FORCE_STDERR=1",               // Force all messages to stderr
	)

	// Start the command
	if err = cmd.Start(); err != nil {
		return stdout, stderr, fmt.Errorf("failed to start ffmpeg: %v", err)
	}

	// Create a channel to receive any errors from goroutine
	done := make(chan error, 1)

	// Monitor process
	go func() {
		done <- cmd.Wait()
	}()

	// Wait for the process to complete with timeout handling
	select {
	case err := <-done:
		if err != nil {
			return stdout, stderr, fmt.Errorf("ffmpeg error: %v, stderr: %s", err, stderr.String())
		}
	case <-time.After(12 * time.Hour): // Very long timeout for large videos
		// Kill the process if it exceeds timeout
		if err := cmd.Process.Kill(); err != nil {
			return stdout, stderr, fmt.Errorf("failed to kill timed out ffmpeg process: %v", err)
		}
		return stdout, stderr, fmt.Errorf("ffmpeg process timed out after 12 hours")
	}

	return stdout, stderr, nil
}
