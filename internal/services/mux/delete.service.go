package mux

import (
	"context"
	"fmt"

	"github.com/rs/zerolog/log"
)

// DeleteService handles deleting videos from Mux
type DeleteService struct {
	ctx     context.Context
	client  *Client
	assetID string
}

// NewDeleteService creates a new DeleteService
func NewDeleteService(
	ctx context.Context,
	client *Client,
	assetID string,
) *DeleteService {
	return &DeleteService{
		ctx:     ctx,
		client:  client,
		assetID: assetID,
	}
}

// NewDeleteServiceFromEnv creates a new DeleteService using environment variables for client configuration
func NewDeleteServiceFromEnv(
	ctx context.Context,
	assetID string,
) (*DeleteService, error) {
	client, err := NewClient(nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create Mux client: %w", err)
	}
	return NewDeleteService(ctx, client, assetID), nil
}

// Execute deletes a video from Mux
func (s *DeleteService) Execute() error {
	// Validate inputs
	if err := s.validateInputs(); err != nil {
		return err
	}

	// Delete the asset
	err := s.client.DeleteAsset(s.ctx, s.assetID)
	if err != nil {
		log.Error().Err(err).Str("assetID", s.assetID).Msg("Failed to delete video")
		return NewMuxDeleteError(fmt.Sprintf("failed to delete video: %v", err))
	}

	log.Info().Str("assetID", s.assetID).Msg("Video deleted successfully")
	return nil
}

// validateInputs validates the service inputs
func (s *DeleteService) validateInputs() error {
	if s.client == nil {
		return NewMuxDeleteError("client cannot be nil")
	}

	if s.assetID == "" {
		return NewMuxDeleteError("asset ID cannot be empty")
	}

	return nil
}
