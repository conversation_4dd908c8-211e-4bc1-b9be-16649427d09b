package mux

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"time"

	"github.com/rs/zerolog/log"
)

// UploadService handles uploading videos to Mux
type UploadService struct {
	ctx              context.Context
	client           *Client
	metadata         VideoMetadata
	progressCallback ProgressCallback
	uploadResponse   *UploadResponse
	asset            *Asset
}

// DirectUploadService handles direct uploading of videos to Mux
type DirectUploadService struct {
	*UploadService
	videoPath string
}

// URLUploadService handles uploading videos to Mux from a URL
type URLUploadService struct {
	*UploadService
	sourceURL string
}

// NewUploadService creates a new UploadService
func NewUploadService(
	ctx context.Context,
	client *Client,
	metadata VideoMetadata,
	progressCallback ProgressCallback,
) *UploadService {
	return &UploadService{
		ctx:              ctx,
		client:           client,
		metadata:         metadata,
		progressCallback: progressCallback,
	}
}

// NewDirectUploadService creates a new DirectUploadService
func NewDirectUploadService(
	ctx context.Context,
	client *Client,
	videoPath string,
	metadata VideoMetadata,
	progressCallback ProgressCallback,
) *DirectUploadService {
	return &DirectUploadService{
		UploadService: NewUploadService(ctx, client, metadata, progressCallback),
		videoPath:     videoPath,
	}
}

// NewURLUploadService creates a new URLUploadService
func NewURLUploadService(
	ctx context.Context,
	client *Client,
	sourceURL string,
	metadata VideoMetadata,
	progressCallback ProgressCallback,
) *URLUploadService {
	return &URLUploadService{
		UploadService: NewUploadService(ctx, client, metadata, progressCallback),
		sourceURL:     sourceURL,
	}
}

// Execute creates a direct upload and uploads a video
func (s *DirectUploadService) Execute() error {
	// Validate inputs
	if err := s.validateInputs(); err != nil {
		return err
	}

	// Create direct upload
	uploadOptions := &UploadOptions{
		Timeout:    3600, // 1 hour
		CorsOrigin: "*",
		NewAssetSettings: &AssetSettings{
			PlaybackPolicy: []map[string]string{
				{"public": ""},
			},
			PassThrough: s.metadata.PassThrough,
		},
	}

	uploadResponse, err := s.client.CreateDirectUpload(s.ctx, uploadOptions)
	if err != nil {
		return err
	}

	s.uploadResponse = uploadResponse

	// Upload the file
	if err := s.uploadFile(); err != nil {
		return err
	}

	// Set upload ID initially
	s.asset = &Asset{
		ID:     uploadResponse.ID,
		Status: AssetStatusPreparing,
	}

	// Wait for the upload to complete and get the asset ID
	asset, err := s.waitForAssetCreated()
	if err != nil {
		return err
	}

	// Update asset with the real asset ID from Mux
	if asset != nil {
		s.asset = asset
	}

	return nil
}

// waitForAssetCreated polls the upload status until the asset is created and returns the asset
func (s *DirectUploadService) waitForAssetCreated() (*Asset, error) {
	log.Info().Str("uploadID", s.uploadResponse.ID).Msg("Waiting for asset to be created")

	// Create a context with timeout for polling
	ctx, cancel := context.WithTimeout(s.ctx, 5*time.Minute)
	defer cancel()

	// Create a ticker for polling
	ticker := time.NewTicker(2 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return nil, NewMuxUploadError("timeout waiting for asset to be created")
		case <-ticker.C:
			// Check upload status
			upload, err := s.client.GetUpload(ctx, s.uploadResponse.ID)
			if err != nil {
				log.Error().Err(err).Str("uploadID", s.uploadResponse.ID).Msg("Failed to get upload status")
				continue
			}

			// Log the current status
			log.Info().Str("uploadID", s.uploadResponse.ID).Str("status", upload.Status).Msg("Checking upload status")

			// If the upload has errored, return an error
			if upload.Status == "error" {
				return nil, NewMuxUploadError("upload failed with status: error")
			}

			// If the upload has an asset ID, get the asset
			if upload.AssetID != "" {
				log.Info().Str("uploadID", s.uploadResponse.ID).Str("assetID", upload.AssetID).Msg("Asset created successfully")

				// Get the asset details
				asset, err := s.client.GetAsset(ctx, upload.AssetID)
				if err != nil {
					log.Error().Err(err).Str("assetID", upload.AssetID).Msg("Failed to get asset details")
					continue
				}

				return asset, nil
			}
		}
	}
}

// Execute creates a direct upload for a URL
func (s *URLUploadService) Execute() error {
	// Validate inputs
	if err := s.validateURLInputs(); err != nil {
		return err
	}

	// Configure asset settings
	assetSettings := &AssetSettings{
		PlaybackPolicy: []map[string]string{
			{"public": ""},
		},
		PassThrough: s.metadata.PassThrough,
	}

	// Create asset from URL
	log.Info().Str("sourceURL", s.sourceURL).Msg("Creating asset from URL")
	asset, err := s.client.CreateAssetFromURL(s.ctx, s.sourceURL, assetSettings)
	if err != nil {
		log.Error().Err(err).Str("sourceURL", s.sourceURL).Msg("Failed to create asset from URL")
		return err
	}

	// Store the asset in the service
	s.asset = asset

	log.Info().Str("assetID", asset.ID).Str("sourceURL", s.sourceURL).Msg("Asset created successfully from URL")

	return nil
}

// uploadFile uploads a file to Mux using a direct upload URL
func (s *DirectUploadService) uploadFile() error {
	file, err := os.Open(s.videoPath)
	if err != nil {
		return NewMuxUploadError(fmt.Sprintf("failed to open video file: %v", err))
	}
	defer file.Close()

	// Get file info
	fileInfo, err := file.Stat()
	if err != nil {
		return NewMuxUploadError(fmt.Sprintf("failed to get file info: %v", err))
	}

	// Create HTTP client
	httpClient := &http.Client{
		Timeout: 30 * time.Minute,
	}

	// Create request
	req, err := http.NewRequestWithContext(s.ctx, "PUT", s.uploadResponse.URL, file)
	if err != nil {
		return NewMuxUploadError(fmt.Sprintf("failed to create request: %v", err))
	}

	// Set content length
	req.ContentLength = fileInfo.Size()

	// Set content type based on file extension
	contentType := determineContentType(s.videoPath)
	req.Header.Set("Content-Type", contentType)

	// Execute request
	resp, err := httpClient.Do(req)
	if err != nil {
		return NewMuxUploadError(fmt.Sprintf("failed to upload file: %v", err))
	}
	defer resp.Body.Close()

	// Check response
	if resp.StatusCode >= 400 {
		bodyBytes, _ := io.ReadAll(resp.Body)
		return NewMuxUploadError(fmt.Sprintf("upload failed with status %d: %s", resp.StatusCode, string(bodyBytes)))
	}

	// Upload successful
	log.Info().Str("uploadID", s.uploadResponse.ID).Msg("File uploaded successfully")

	return nil
}

// GetUploadResponse returns the upload response
func (s *UploadService) GetUploadResponse() *UploadResponse {
	return s.uploadResponse
}

// GetAsset returns the asset
func (s *UploadService) GetAsset() *Asset {
	return s.asset
}

// validateInputs validates the inputs for direct upload
func (s *DirectUploadService) validateInputs() error {
	if s.client == nil {
		return NewMuxUploadError("client cannot be nil")
	}

	if s.videoPath == "" {
		return NewMuxUploadError("video path cannot be empty")
	}

	if _, err := os.Stat(s.videoPath); os.IsNotExist(err) {
		return NewMuxUploadError(fmt.Sprintf("video file not found: %s", s.videoPath))
	}

	if s.metadata.Title == "" {
		return NewMuxUploadError("video title cannot be empty")
	}

	return nil
}

// validateURLInputs validates the inputs for URL upload
func (s *URLUploadService) validateURLInputs() error {
	if s.client == nil {
		return NewMuxUploadError("client cannot be nil")
	}

	if s.sourceURL == "" {
		return NewMuxUploadError("source URL cannot be empty")
	}

	if s.metadata.Title == "" {
		return NewMuxUploadError("video title cannot be empty")
	}

	return nil
}

// determineContentType determines the content type based on file extension
func determineContentType(filePath string) string {
	ext := filepath.Ext(filePath)
	switch ext {
	case ".mp4":
		return "video/mp4"
	case ".mov":
		return "video/quicktime"
	case ".mkv":
		return "video/x-matroska"
	case ".avi":
		return "video/x-msvideo"
	case ".webm":
		return "video/webm"
	default:
		return "application/octet-stream"
	}
}
