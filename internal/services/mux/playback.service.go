package mux

import (
	"context"
	"fmt"

	"github.com/rs/zerolog/log"
)

// PlaybackService handles playback and signed URLs for Mux videos
type PlaybackService struct {
	ctx    context.Context
	client *Client
}

// NewPlaybackService creates a new PlaybackService
func NewPlaybackService(ctx context.Context, client *Client) *PlaybackService {
	return &PlaybackService{
		ctx:    ctx,
		client: client,
	}
}

// CreatePlaybackID creates a playback ID for an asset
func (s *PlaybackService) CreatePlaybackID(assetID string, policy string) (*PlaybackID, error) {
	if assetID == "" {
		return nil, NewMuxPlaybackError("asset ID cannot be empty")
	}

	// Default to public if policy not specified
	if policy == "" {
		policy = PlaybackPolicyPublic
	}

	// Validate policy
	if policy != PlaybackPolicyPublic && policy != PlaybackPolicySigned {
		return nil, NewMuxPlaybackError(fmt.Sprintf("invalid policy: %s", policy))
	}

	playbackID, err := s.client.CreatePlaybackID(s.ctx, assetID, policy)
	if err != nil {
		log.Error().Err(err).Str("assetID", assetID).Str("policy", policy).Msg("Failed to create playback ID")
		return nil, err
	}

	log.Info().Str("assetID", assetID).Str("playbackID", playbackID.ID).Str("policy", playbackID.Policy).Msg("Created playback ID")
	return playbackID, nil
}

// CreateSignedURL creates a signed URL for a playback ID
func (s *PlaybackService) CreateSignedURL(options *SignedURLOptions) (string, error) {
	if options == nil {
		return "", NewMuxPlaybackError("options cannot be nil")
	}

	if options.PlaybackID == "" {
		return "", NewMuxPlaybackError("playback ID cannot be empty")
	}

	// Set default expiration if not provided
	if options.ExpiresIn <= 0 {
		options.ExpiresIn = PlaybackSignedExpiry
	}

	signedURL, err := s.client.CreateSignedURL(options.PlaybackID, options.ExpiresIn)
	if err != nil {
		log.Error().Err(err).Str("playbackID", options.PlaybackID).Msg("Failed to create signed URL")
		return "", err
	}

	log.Info().Str("playbackID", options.PlaybackID).Dur("expiresIn", options.ExpiresIn).Msg("Created signed URL")
	return signedURL, nil
}

// GetPlaybackURL returns a playback URL for a given playback ID
func (s *PlaybackService) GetPlaybackURL(playbackID string, shouldSign bool) (string, error) {
	if playbackID == "" {
		return "", NewMuxPlaybackError("playback ID cannot be empty")
	}

	// Create a signed URL if requested
	if shouldSign {
		return s.CreateSignedURL(&SignedURLOptions{
			PlaybackID: playbackID,
			ExpiresIn:  PlaybackSignedDuration,
		})
	}

	// Return a regular, non-signed playback URL
	return fmt.Sprintf("https://stream.mux.com/%s.m3u8", playbackID), nil
}

// GetThumbnailURL returns a thumbnail URL for a given playback ID
func (s *PlaybackService) GetThumbnailURL(playbackID string, width int, height int, time float64) string {
	if playbackID == "" {
		return ""
	}

	// Set default dimensions
	if width <= 0 {
		width = 640
	}
	if height <= 0 {
		height = 360
	}

	// Create the thumbnail URL
	url := fmt.Sprintf("https://image.mux.com/%s/thumbnail.jpg", playbackID)

	// Add dimensions
	url = fmt.Sprintf("%s?width=%d&height=%d", url, width, height)

	// Add time if specified
	if time > 0 {
		url = fmt.Sprintf("%s&time=%.2f", url, time)
	}

	return url
}

// GetAnimatedGifURL returns an animated GIF URL for a given playback ID
func (s *PlaybackService) GetAnimatedGifURL(playbackID string, width int, height int, startTime float64, endTime float64) string {
	if playbackID == "" {
		return ""
	}

	// Set default dimensions
	if width <= 0 {
		width = 640
	}
	if height <= 0 {
		height = 360
	}

	// Create the animated GIF URL
	url := fmt.Sprintf("https://image.mux.com/%s/animated.gif", playbackID)

	// Add dimensions
	url = fmt.Sprintf("%s?width=%d&height=%d", url, width, height)

	// Add start and end time if specified
	if startTime > 0 {
		url = fmt.Sprintf("%s&start=%.2f", url, startTime)
	}
	if endTime > 0 {
		url = fmt.Sprintf("%s&end=%.2f", url, endTime)
	}

	return url
}

// GetPosterURL returns a poster URL for a given playback ID
func (s *PlaybackService) GetPosterURL(playbackID string, width int, height int) string {
	if playbackID == "" {
		return ""
	}

	// Set default dimensions
	if width <= 0 {
		width = 640
	}
	if height <= 0 {
		height = 360
	}

	// Create the poster URL
	url := fmt.Sprintf("https://image.mux.com/%s/poster.jpg", playbackID)

	// Add dimensions
	url = fmt.Sprintf("%s?width=%d&height=%d", url, width, height)

	return url
}

// GetStoryboardURL returns a storyboard URL for a given playback ID
func (s *PlaybackService) GetStoryboardURL(playbackID string) string {
	if playbackID == "" {
		return ""
	}

	// Create the storyboard URL
	return fmt.Sprintf("https://image.mux.com/%s/storyboard.vtt", playbackID)
}
