package mux

import (
	"fmt"
)

// MuxError is the base error type for Mux service
type MuxError struct {
	Message string
	Code    string
}

// Error implements the error interface
func (e *MuxError) Error() string {
	return fmt.Sprintf("%s: %s", e.Code, e.Message)
}

// NewMuxClientError creates a new MuxClientError
func NewMuxClientError(message string) *MuxError {
	return &MuxError{
		Message: message,
		Code:    "MUX_CLIENT_ERROR",
	}
}

// NewMuxUploadError creates a new MuxUploadError
func NewMuxUploadError(message string) *MuxError {
	return &MuxError{
		Message: message,
		Code:    "MUX_UPLOAD_ERROR",
	}
}

// NewMuxAssetError creates a new MuxAssetError
func NewMuxAssetError(message string) *MuxError {
	return &MuxError{
		Message: message,
		Code:    "MUX_ASSET_ERROR",
	}
}

// NewMuxPlaybackError creates a new MuxPlaybackError
func NewMuxPlaybackError(message string) *MuxError {
	return &MuxError{
		Message: message,
		Code:    "MUX_PLAYBACK_ERROR",
	}
}

// NewMuxDeleteError creates a new MuxDeleteError
func NewMuxDeleteError(message string) *MuxError {
	return &MuxError{
		Message: message,
		Code:    "MUX_DELETE_ERROR",
	}
}

// IsMuxError checks if an error is a MuxError
func IsMuxError(err error) bool {
	_, ok := err.(*MuxError)
	return ok
}
