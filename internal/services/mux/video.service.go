package mux

import (
	"context"
	"time"

	"vibico-education-api/internal/models"

	"github.com/rs/zerolog/log"
)

// VideoService handles management of videos using Mux
type VideoService struct {
	ctx         context.Context
	client      *Client
	assetSvc    *AssetService
	playbackSvc *PlaybackService
}

// VideoUploadResult represents the result of a video upload
type VideoUploadResult struct {
	Video         *models.Video
	VideoVersion  *models.VideoVersion
	VideoPlatform *models.VideoPlatform
	UploadID      string // ID của upload process
	Asset         *Asset
	PlaybackID    *PlaybackID
	ThumbnailURL  string
	ManifestURL   string
}

// NewVideoService creates a new VideoService
func NewVideoService(ctx context.Context) (*VideoService, error) {
	// Create a Mux client
	client, err := NewClient(nil)
	if err != nil {
		return nil, err
	}

	// Create asset and playback services
	assetSvc := NewAssetService(ctx, client)
	playbackSvc := NewPlaybackService(ctx, client)

	return &VideoService{
		ctx:         ctx,
		client:      client,
		assetSvc:    assetSvc,
		playbackSvc: playbackSvc,
	}, nil
}

// UploadVideo uploads a video to Mux
func (s *VideoService) UploadVideo(
	videoPath string,
	metadata VideoMetadata,
	isFree bool,
	progressCallback ProgressCallback,
) (*VideoUploadResult, error) {
	log.Info().Str("videoPath", videoPath).Msg("Uploading video to Mux")

	// Create a direct upload service
	uploadSvc := NewDirectUploadService(s.ctx, s.client, videoPath, metadata, progressCallback)

	// Execute the upload
	err := uploadSvc.Execute()
	if err != nil {
		return nil, err
	}

	// Get the asset and upload response
	asset := uploadSvc.GetAsset()
	uploadResponse := uploadSvc.GetUploadResponse()

	if asset == nil {
		return nil, NewMuxAssetError("asset not found after upload")
	}

	// Log successful upload with both IDs
	log.Info().
		Str("uploadID", uploadResponse.ID).
		Str("assetID", asset.ID).
		Msg("Upload completed successfully")

	// Create the result
	result := &VideoUploadResult{
		UploadID: uploadResponse.ID,
		Asset:    asset,
	}

	return result, nil
}

// UploadVideoFromURL uploads a video to Mux from a URL
func (s *VideoService) UploadVideoFromURL(
	videoURL string,
	metadata VideoMetadata,
	isFree bool,
	progressCallback ProgressCallback,
) (*VideoUploadResult, error) {
	log.Info().Str("videoURL", videoURL).Msg("Uploading video from URL to Mux")

	// Create a URL upload service
	uploadSvc := NewURLUploadService(s.ctx, s.client, videoURL, metadata, progressCallback)

	// Execute the upload
	err := uploadSvc.Execute()
	if err != nil {
		return nil, err
	}

	// Get the asset
	asset := uploadSvc.GetAsset()
	if asset == nil {
		return nil, NewMuxAssetError("asset not found after URL upload")
	}

	// Log successful upload with asset ID
	log.Info().
		Str("assetID", asset.ID).
		Msg("URL upload completed successfully")

	// Create the result with asset ID (no upload ID for URL uploads)
	result := &VideoUploadResult{
		Asset: asset,
	}

	return result, nil
}

// DeleteVideo deletes a video from Mux
func (s *VideoService) DeleteVideo(assetID string) error {
	log.Info().Str("assetID", assetID).Msg("Deleting video from Mux")

	// Delete the asset
	return s.assetSvc.DeleteAsset(assetID)
}

// GetSignedURL gets a signed URL for a playback ID
func (s *VideoService) GetSignedURL(playbackID string, expiresIn time.Duration) (string, error) {
	log.Info().Str("playbackID", playbackID).Msg("Getting signed URL")

	// Create a signed URL
	return s.playbackSvc.CreateSignedURL(&SignedURLOptions{
		PlaybackID: playbackID,
		ExpiresIn:  expiresIn,
	})
}

// GetThumbnailURL gets a thumbnail URL for a playback ID
func (s *VideoService) GetThumbnailURL(playbackID string, width int, height int) string {
	return s.playbackSvc.GetThumbnailURL(playbackID, width, height, 0)
}

// GetAsset gets an asset by ID
func (s *VideoService) GetAsset(assetID string) (*Asset, error) {
	return s.assetSvc.GetAsset(assetID)
}

// Helper methods to create database models
