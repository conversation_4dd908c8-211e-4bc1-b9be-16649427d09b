package mux

import (
	"context"
	"time"
)

// Asset status constants
const (
	AssetStatusPreparing  = "preparing"
	AssetStatusReady      = "ready"
	AssetStatusErrored    = "errored"
	AssetStatusProcessing = "processing"
)

// Playback policy types
const (
	PlaybackPolicyPublic   = "public"
	PlaybackPolicySigned   = "signed"
	PlaybackSignedDuration = 1 * time.Hour
	PlaybackSignedExpiry   = 24 * time.Hour
)

// Default playback options
const (
	DefaultPlaybackPolicy = PlaybackPolicyPublic
)

// ProgressCallback is a function type for upload progress callbacks
type ProgressCallback func(progress float64)

// MuxConfig contains configuration parameters for the Mux client
type MuxConfig struct {
	TokenID          string
	TokenSecret      string
	SigningKeyID     string // Mux Signing Key ID for creating signed URLs
	PrivateKeyBase64 string // Base64 encoded private key for RSA signing
}

// VideoMetadata contains metadata for a Mux video
type VideoMetadata struct {
	Title       string
	Description string
	PublicID    string // Custom ID for reference
	PassThrough string // Additional data to pass through
}

// Asset represents a Mux asset
type Asset struct {
	ID                  string
	Status              string
	Duration            float64
	AspectRatio         string
	MaxStoredResolution string
	MaxResolutionTier   string
	CreatedAt           time.Time
	UpdatedAt           time.Time
	PlaybackIDs         []PlaybackID
	MP4Support          string
	Tracks              []AssetTrack
	Errors              []AssetError
}

// AssetTrack represents a track within a Mux asset
type AssetTrack struct {
	ID        string
	Type      string
	Duration  float64
	MaxWidth  int
	MaxHeight int
	Codec     string
}

// AssetError represents an error that occurred during asset processing
type AssetError struct {
	Type    string
	Message string
}

// PlaybackID represents a Mux playback ID
type PlaybackID struct {
	ID     string
	Policy string
}

// PlaybackInfo contains information for video playback
type PlaybackInfo struct {
	PlaybackID string
	Policy     string
	URL        string
}

// SignedURLOptions contains options for creating a signed URL
type SignedURLOptions struct {
	PlaybackID string
	ExpiresIn  time.Duration
}

// UploadOptions contains options for creating a direct upload
type UploadOptions struct {
	Timeout          int // in seconds
	CorsOrigin       string
	NewAssetSettings *AssetSettings
}

// AssetSettings contains settings for creating a new asset
type AssetSettings struct {
	PlaybackPolicy []map[string]string
	MP4Support     string
	NormalizaAudio bool
	PerTitleEncode bool
	PassThrough    string
	Test           bool
}

// MuxService interface defines methods that any Mux service should implement
type MuxService interface {
	Execute(ctx context.Context) error
}

// PlaybackPolicyOptions contains options for creating playback policies
type PlaybackPolicyOptions struct {
	Type     string
	Referrer *ReferrerPolicy `json:"referrer,omitempty"`
}

// ReferrerPolicy defines referrer restrictions
type ReferrerPolicy struct {
	Allowed    []string `json:"allowed,omitempty"`
	AllowEmpty bool     `json:"allow_empty,omitempty"`
}

// UploadResponse represents a response from the Mux direct upload API
type UploadResponse struct {
	ID               string         `json:"id"`
	URL              string         `json:"url"`
	Status           string         `json:"status"`
	AssetID          string         `json:"asset_id,omitempty"`
	NewAssetSettings *AssetSettings `json:"new_asset_settings,omitempty"`
	CorsOrigin       string         `json:"cors_origin,omitempty"`
	Timeout          int            `json:"timeout,omitempty"`
	CreatedAt        time.Time      `json:"created_at"`
}

// DeleteResponse represents a response from the Mux delete API
type DeleteResponse struct {
	Data interface{} `json:"data"`
}
