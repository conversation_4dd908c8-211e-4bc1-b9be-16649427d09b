package mux

import (
	"context"

	"github.com/rs/zerolog/log"
)

// AssetService handles management of Mux assets
type AssetService struct {
	ctx    context.Context
	client *Client
}

// NewAssetService creates a new AssetService
func NewAssetService(ctx context.Context, client *Client) *AssetService {
	return &AssetService{
		ctx:    ctx,
		client: client,
	}
}

// GetAsset retrieves an asset by ID
func (s *AssetService) GetAsset(assetID string) (*Asset, error) {
	if assetID == "" {
		return nil, NewMuxAssetError("asset ID cannot be empty")
	}

	asset, err := s.client.GetAsset(s.ctx, assetID)
	if err != nil {
		log.Error().Err(err).Str("assetID", assetID).Msg("Failed to get asset")
		return nil, err
	}

	return asset, nil
}

// DeleteAsset deletes an asset by ID
func (s *AssetService) DeleteAsset(assetID string) error {
	if assetID == "" {
		return NewMuxDeleteError("asset ID cannot be empty")
	}

	err := s.client.DeleteAsset(s.ctx, assetID)
	if err != nil {
		log.Error().Err(err).Str("assetID", assetID).Msg("Failed to delete asset")
		return err
	}

	log.Info().Str("assetID", assetID).Msg("Asset deleted successfully")
	return nil
}

// CreatePlaybackID creates a playback ID for an asset
func (s *AssetService) CreatePlaybackID(assetID string, policy string) (*PlaybackID, error) {
	if assetID == "" {
		return nil, NewMuxAssetError("asset ID cannot be empty")
	}

	// Default to public if policy not specified
	if policy == "" {
		policy = PlaybackPolicyPublic
	}

	// Create a playback service
	playbackService := NewPlaybackService(s.ctx, s.client)
	return playbackService.CreatePlaybackID(assetID, policy)
}
