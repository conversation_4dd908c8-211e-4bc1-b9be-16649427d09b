package mux

import (
	"context"
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"os"
	"strconv"
	"sync"
	"time"

	"github.com/cenkalti/backoff/v5"
	muxgo "github.com/muxinc/mux-go/v7"
	"github.com/rs/zerolog/log"
)

// Client for interacting with the Mux API
type Client struct {
	muxClient    *muxgo.APIClient
	tokenID      string
	tokenSecret  string
	signingKeyID string
	privateKey   *rsa.PrivateKey
	mu           sync.Mutex
}

// NewClient creates a new Mux client from environment variables or explicit config
func NewClient(config *MuxConfig) (*Client, error) {
	var tokenID, tokenSecret, signingKeyID, privateKeyBase64 string

	// If config is not provided, try to get values from environment variables
	if config == nil {
		tokenID = os.Getenv("MUX_TOKEN_ID")
		tokenSecret = os.Getenv("MUX_TOKEN_SECRET")
		signingKeyID = os.Getenv("MUX_SIGNING_KEY_ID")
		privateKeyBase64 = os.Getenv("MUX_BASE64_ENCODED_PRIVATE_KEY")

		if tokenID == "" {
			return nil, NewMuxClientError("MUX_TOKEN_ID environment variable not set")
		}

		if tokenSecret == "" {
			return nil, NewMuxClientError("MUX_TOKEN_SECRET environment variable not set")
		}
	} else {
		// Use values from config
		if config.TokenID == "" {
			return nil, NewMuxClientError("token ID not specified")
		}

		if config.TokenSecret == "" {
			return nil, NewMuxClientError("token secret not specified")
		}

		tokenID = config.TokenID
		tokenSecret = config.TokenSecret
		signingKeyID = config.SigningKeyID
		privateKeyBase64 = config.PrivateKeyBase64
	}

	client := &Client{
		tokenID:      tokenID,
		tokenSecret:  tokenSecret,
		signingKeyID: signingKeyID,
	}

	// Parse private key if available
	if privateKeyBase64 != "" {
		privateKeyBytes, err := base64.StdEncoding.DecodeString(privateKeyBase64)
		if err != nil {
			return nil, NewMuxClientError(fmt.Sprintf("failed to decode private key: %v", err))
		}

		block, _ := pem.Decode(privateKeyBytes)
		if block == nil {
			return nil, NewMuxClientError("failed to parse PEM block containing the private key")
		}

		key, err := x509.ParsePKCS1PrivateKey(block.Bytes)
		if err != nil {
			return nil, NewMuxClientError(fmt.Sprintf("failed to parse private key: %v", err))
		}

		client.privateKey = key
	}

	// Initialize Mux API client
	if err := client.initClient(); err != nil {
		return nil, fmt.Errorf("failed to initialize Mux client: %w", err)
	}

	return client, nil
}

// initClient initializes the Mux API client
func (c *Client) initClient() error {
	c.mu.Lock()
	defer c.mu.Unlock()

	// Create Mux API client configuration with basic auth
	config := muxgo.NewConfiguration(
		muxgo.WithBasicAuth(c.tokenID, c.tokenSecret),
	)
	c.muxClient = muxgo.NewAPIClient(config)

	return nil
}

// GetMuxClient returns the Mux API client
func (c *Client) GetMuxClient() *muxgo.APIClient {
	return c.muxClient
}

// RetryOperation retries an operation with exponential backoff
func (c *Client) RetryOperation(operation func() error) error {
	b := backoff.NewExponentialBackOff()
	b.InitialInterval = 1 * time.Second
	b.MaxInterval = 5 * time.Second

	// Create a context with timeout for overall retry duration
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Use backoff package to handle retries
	// Create a retry operation function
	retryOperation := func() (bool, error) {
		err := operation()
		return err == nil, err
	}

	_, err := backoff.Retry(ctx, retryOperation, backoff.WithBackOff(b))
	return err
}

// CreateDirectUpload creates a direct upload URL
func (c *Client) CreateDirectUpload(ctx context.Context, options *UploadOptions) (*UploadResponse, error) {
	// Set up request parameters
	createUploadRequest := muxgo.CreateUploadRequest{}

	// Add new asset settings if provided
	if options != nil {
		if options.Timeout > 0 {
			createUploadRequest.Timeout = int32(options.Timeout)
		}

		if options.CorsOrigin != "" {
			createUploadRequest.CorsOrigin = options.CorsOrigin
		}

		if options.NewAssetSettings != nil {
			newAssetSettings := muxgo.CreateAssetRequest{}

			// Configure playback policy if specified
			if len(options.NewAssetSettings.PlaybackPolicy) > 0 {
				policies := []muxgo.PlaybackPolicy{}
				for _, policy := range options.NewAssetSettings.PlaybackPolicy {
					for k := range policy {
						policies = append(policies, muxgo.PlaybackPolicy(k))
					}
				}
				newAssetSettings.PlaybackPolicy = policies
			}

			// Set MP4 support
			if options.NewAssetSettings.MP4Support != "" {
				newAssetSettings.Mp4Support = options.NewAssetSettings.MP4Support
			}

			// Set other options
			if options.NewAssetSettings.PassThrough != "" {
				newAssetSettings.Passthrough = options.NewAssetSettings.PassThrough
			}

			perTitleEncode := options.NewAssetSettings.PerTitleEncode
			normalizeAudio := options.NewAssetSettings.NormalizaAudio
			testMode := options.NewAssetSettings.Test

			newAssetSettings.PerTitleEncode = perTitleEncode
			newAssetSettings.NormalizeAudio = normalizeAudio
			newAssetSettings.Test = testMode

			createUploadRequest.NewAssetSettings = newAssetSettings
		}
	}

	// Make the API request
	var result muxgo.UploadResponse
	var err error

	err = c.RetryOperation(func() error {
		result, err = c.muxClient.DirectUploadsApi.CreateDirectUpload(createUploadRequest, muxgo.WithContext(ctx))
		if err != nil {
			return err
		}
		return nil
	})

	if err != nil {
		log.Error().Err(err).Msg("Failed to create direct upload")
		return nil, NewMuxUploadError(fmt.Sprintf("failed to create direct upload: %v", err))
	}

	// Map the result to our UploadResponse struct
	response := &UploadResponse{
		ID:        result.Data.Id,
		URL:       result.Data.Url,
		Status:    result.Data.Status,
		Timeout:   int(result.Data.Timeout),
		CreatedAt: time.Now(), // Mux doesn't return creation time
	}

	if result.Data.CorsOrigin != "" {
		response.CorsOrigin = result.Data.CorsOrigin
	}

	// Check if the asset has been created
	if result.Data.AssetId != "" {
		response.AssetID = result.Data.AssetId
	}

	return response, nil
}

// GetUpload retrieves the status of an upload by ID
func (c *Client) GetUpload(ctx context.Context, uploadID string) (*UploadResponse, error) {
	if uploadID == "" {
		return nil, NewMuxUploadError("upload ID cannot be empty")
	}

	var result muxgo.UploadResponse
	var err error

	err = c.RetryOperation(func() error {
		result, err = c.muxClient.DirectUploadsApi.GetDirectUpload(uploadID, muxgo.WithContext(ctx))
		if err != nil {
			return err
		}
		return nil
	})

	if err != nil {
		log.Error().Err(err).Str("uploadID", uploadID).Msg("Failed to get upload status")
		return nil, NewMuxUploadError(fmt.Sprintf("failed to get upload status: %v", err))
	}

	// Map the result to our UploadResponse struct
	response := &UploadResponse{
		ID:        result.Data.Id,
		URL:       result.Data.Url,
		Status:    result.Data.Status,
		Timeout:   int(result.Data.Timeout),
		CreatedAt: time.Now(), // Use current time as fallback
	}

	if result.Data.CorsOrigin != "" {
		response.CorsOrigin = result.Data.CorsOrigin
	}

	// Check if the asset has been created
	if result.Data.AssetId != "" {
		response.AssetID = result.Data.AssetId
	}

	return response, nil
}

// GetAsset retrieves an asset by ID
func (c *Client) GetAsset(ctx context.Context, assetID string) (*Asset, error) {
	if assetID == "" {
		return nil, NewMuxAssetError("asset ID cannot be empty")
	}

	var result muxgo.AssetResponse
	var err error

	err = c.RetryOperation(func() error {
		result, err = c.muxClient.AssetsApi.GetAsset(assetID, muxgo.WithContext(ctx))
		if err != nil {
			return err
		}
		return nil
	})

	if err != nil {
		log.Error().Err(err).Str("assetID", assetID).Msg("Failed to get asset")
		return nil, NewMuxAssetError(fmt.Sprintf("failed to get asset: %v", err))
	}

	return mapAssetResponse(&result.Data), nil
}

// DeleteAsset deletes an asset by ID
func (c *Client) DeleteAsset(ctx context.Context, assetID string) error {
	if assetID == "" {
		return NewMuxDeleteError("asset ID cannot be empty")
	}

	var err error

	err = c.RetryOperation(func() error {
		err = c.muxClient.AssetsApi.DeleteAsset(assetID, muxgo.WithContext(ctx))
		if err != nil {
			return err
		}
		return nil
	})

	if err != nil {
		log.Error().Err(err).Str("assetID", assetID).Msg("Failed to delete asset")
		return NewMuxDeleteError(fmt.Sprintf("failed to delete asset: %v", err))
	}

	return nil
}

// CreatePlaybackID creates a playback ID for an asset
func (c *Client) CreatePlaybackID(ctx context.Context, assetID string, policy string) (*PlaybackID, error) {
	if assetID == "" {
		return nil, NewMuxPlaybackError("asset ID cannot be empty")
	}

	// Validate policy
	if policy != PlaybackPolicyPublic && policy != PlaybackPolicySigned {
		policy = DefaultPlaybackPolicy
	}

	var result muxgo.CreatePlaybackIdResponse
	var err error

	// Create playback policy
	createPlaybackIDRequest := muxgo.CreatePlaybackIdRequest{
		Policy: muxgo.PlaybackPolicy(policy),
	}

	err = c.RetryOperation(func() error {
		result, err = c.muxClient.AssetsApi.CreateAssetPlaybackId(assetID, createPlaybackIDRequest, muxgo.WithContext(ctx))
		if err != nil {
			return err
		}
		return nil
	})

	if err != nil {
		log.Error().Err(err).Str("assetID", assetID).Str("policy", policy).Msg("Failed to create playback ID")
		return nil, NewMuxPlaybackError(fmt.Sprintf("failed to create playback ID: %v", err))
	}

	return &PlaybackID{
		ID:     result.Data.Id,
		Policy: string(result.Data.Policy),
	}, nil
}

// CreateSignedURL creates a signed URL for a playback ID using RSA signing
func (c *Client) CreateSignedURL(playbackID string, expiresIn time.Duration) (string, error) {
	if playbackID == "" {
		return "", NewMuxPlaybackError("playback ID cannot be empty")
	}

	// Check if we have signing key and private key
	if c.signingKeyID == "" {
		return "", NewMuxPlaybackError("signing key ID (MUX_SIGNING_KEY_ID) is required for creating signed URLs")
	}

	if c.privateKey == nil {
		return "", NewMuxPlaybackError("private key (MUX_BASE64_ENCODED_PRIVATE_KEY) is required for creating signed URLs")
	}

	// Default expiration time if not provided
	if expiresIn <= 0 {
		expiresIn = PlaybackSignedExpiry
	}

	// Calculate expiration timestamp
	expiresAt := time.Now().Add(expiresIn).Unix()

	// Create the JWT header
	header := map[string]interface{}{
		"alg": "RS256",
		"typ": "JWT",
		"kid": c.signingKeyID,
	}

	// Create the JWT payload/claims
	payload := map[string]interface{}{
		"sub": playbackID,
		"aud": "v",
		"exp": expiresAt,
	}

	// Convert the header to JSON
	headerJSON, err := json.Marshal(header)
	if err != nil {
		return "", NewMuxPlaybackError(fmt.Sprintf("failed to create signed URL header: %v", err))
	}

	// Base64 encode the header
	encodedHeader := base64.RawURLEncoding.EncodeToString(headerJSON)

	// Convert the payload to JSON
	payloadJSON, err := json.Marshal(payload)
	if err != nil {
		return "", NewMuxPlaybackError(fmt.Sprintf("failed to create signed URL payload: %v", err))
	}

	// Base64 encode the payload
	encodedPayload := base64.RawURLEncoding.EncodeToString(payloadJSON)

	// Create the JWT signature using RSA-SHA256
	signatureInput := encodedHeader + "." + encodedPayload
	hashed := sha256.Sum256([]byte(signatureInput))

	signature, err := rsa.SignPKCS1v15(rand.Reader, c.privateKey, crypto.SHA256, hashed[:])
	if err != nil {
		return "", NewMuxPlaybackError(fmt.Sprintf("failed to create RSA signature: %v", err))
	}
	encodedSignature := base64.RawURLEncoding.EncodeToString(signature)

	// Create the JWT token
	token := encodedHeader + "." + encodedPayload + "." + encodedSignature

	// Create the signed URL
	signedURL := fmt.Sprintf("https://stream.mux.com/%s.m3u8?token=%s", playbackID, token)

	return signedURL, nil
}

// CreateAssetFromURL creates an asset from a URL
func (c *Client) CreateAssetFromURL(ctx context.Context, sourceURL string, options *AssetSettings) (*Asset, error) {
	if sourceURL == "" {
		return nil, NewMuxAssetError("source URL cannot be empty")
	}

	// Set up request parameters
	createAssetRequest := muxgo.CreateAssetRequest{
		Input: []muxgo.InputSettings{
			{
				Url: sourceURL,
			},
		},
	}

	// Configure playback policy if specified
	if options != nil {
		if len(options.PlaybackPolicy) > 0 {
			policies := []muxgo.PlaybackPolicy{}
			for _, policy := range options.PlaybackPolicy {
				for k := range policy {
					policies = append(policies, muxgo.PlaybackPolicy(k))
				}
			}
			createAssetRequest.PlaybackPolicy = policies
		}

		// Set MP4 support
		if options.MP4Support != "" {
			createAssetRequest.Mp4Support = options.MP4Support
		}

		// Set other options
		if options.PassThrough != "" {
			createAssetRequest.Passthrough = options.PassThrough
		}

		createAssetRequest.PerTitleEncode = options.PerTitleEncode
		createAssetRequest.NormalizeAudio = options.NormalizaAudio
		createAssetRequest.Test = options.Test
	}

	// Make the API request
	var result muxgo.AssetResponse
	var err error

	err = c.RetryOperation(func() error {
		result, err = c.muxClient.AssetsApi.CreateAsset(createAssetRequest, muxgo.WithContext(ctx))
		if err != nil {
			return err
		}
		return nil
	})

	if err != nil {
		log.Error().Err(err).Str("sourceURL", sourceURL).Msg("Failed to create asset from URL")
		return nil, NewMuxAssetError(fmt.Sprintf("failed to create asset from URL: %v", err))
	}

	// Map the result to our Asset struct
	return mapAssetResponse(&result.Data), nil
}

// Helper function to map Mux API response to our Asset struct
func mapAssetResponse(data *muxgo.Asset) *Asset {
	asset := &Asset{
		ID:       data.Id,
		Status:   data.Status,
		Duration: 0, // Default value
	}

	if data.Duration != 0 {
		asset.Duration = data.Duration
	}

	if data.AspectRatio != "" {
		asset.AspectRatio = data.AspectRatio
	}

	if data.MaxStoredResolution != "" {
		asset.MaxStoredResolution = data.MaxStoredResolution
	}

	if data.MaxStoredFrameRate != 0 {
		asset.MaxResolutionTier = strconv.FormatFloat(data.MaxStoredFrameRate, 'f', 2, 64)
	}

	if data.CreatedAt != "" {
		createdAt, _ := time.Parse(time.RFC3339, data.CreatedAt)
		asset.CreatedAt = createdAt
	}

	// Use current time if UpdatedAt is not available
	asset.UpdatedAt = time.Now()

	if data.Mp4Support != "" {
		asset.MP4Support = data.Mp4Support
	}

	// Map playback IDs
	if len(data.PlaybackIds) > 0 {
		asset.PlaybackIDs = make([]PlaybackID, len(data.PlaybackIds))
		for i, pid := range data.PlaybackIds {
			asset.PlaybackIDs[i] = PlaybackID{
				ID:     pid.Id,
				Policy: string(pid.Policy),
			}
		}
	}

	// Map tracks
	if len(data.Tracks) > 0 {
		asset.Tracks = make([]AssetTrack, len(data.Tracks))
		for i, track := range data.Tracks {
			assetTrack := AssetTrack{
				ID:   track.Id,
				Type: track.Type,
			}

			if track.Duration != 0 {
				assetTrack.Duration = track.Duration
			}

			if track.MaxWidth != 0 {
				assetTrack.MaxWidth = int(track.MaxWidth)
			}

			if track.MaxHeight != 0 {
				assetTrack.MaxHeight = int(track.MaxHeight)
			}

			// Note: muxgo.Track doesn't have a Codec field, so we skip this assignment

			asset.Tracks[i] = assetTrack
		}
	}

	// Map errors
	if len(data.Errors.Messages) > 0 {
		asset.Errors = make([]AssetError, len(data.Errors.Messages))
		for i, msg := range data.Errors.Messages {
			asset.Errors[i] = AssetError{
				Type:    "processing",
				Message: msg,
			}
		}
	}

	return asset
}
