package publicServices

import (
	"context"
	"regexp"
	"strings"
	"vibico-education-api/pkg/sms"
	translator "vibico-education-api/pkg/translators"
	"vibico-education-api/setup/grpc_clients"

	pb "github.com/BehemothLtd/vibico-auth/proto/auth"

	"github.com/BehemothLtd/behemoth-pkg/golang/constants"
	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/rs/zerolog/log"
)

type SignUpVerifyResend struct {
	ctx         context.Context
	phoneNumber *string
}

func NewSignUpVerifyResendService(
	ctx context.Context,
	phoneNumber *string,
) *SignUpVerifyResend {
	log.Debug().Ctx(ctx).Msg("publicServices.NewSignUpVerifyResendService")

	return &SignUpVerifyResend{
		ctx:         ctx,
		phoneNumber: phoneNumber,
	}
}

func (service *SignUpVerifyResend) Execute() error {
	log.Debug().Ctx(service.ctx).Msg("SignUpVerifyResend.Execute")

	if err := service.validate(); err != nil {
		return err
	}

	smsService := sms.NewSMSService(service.ctx, utils.GetEnv("TWILIO_SERVICE_SID", ""))
	if err := smsService.SendVerificationCode(*service.phoneNumber, nil, nil); err != nil {
		return err
	}

	return nil
}

func (service *SignUpVerifyResend) validate() error {
	log.Debug().Ctx(service.ctx).Msg("SignUpVerifyResend.validate")

	exception := exceptions.NewUnprocessableContentError(translator.Translate(nil, "general_pleaseInputCorrect"), nil)

	if service.phoneNumber == nil || strings.TrimSpace(*service.phoneNumber) == "" {
		exception.AddError("phoneNumber", []any{translator.Translate(nil, "errValidationMsg_required")})

		return exception
	}

	re := regexp.MustCompile(constants.PhoneNumberFormat)
	if !re.MatchString(*service.phoneNumber) {
		exception.AddError("phoneNumber", []any{translator.Translate(nil, "errValidation_wrongFormat")})

		return exception
	}

	res, err := grpc_clients.AuthClient().GetUserByPhoneNumber(
		grpc_clients.NewCtx(service.ctx),
		&pb.UserPhoneNumberRequest{
			IdentityPoolId: grpc_clients.PoolId(),
			PhoneNumber:    *service.phoneNumber,
		},
	)

	if err != nil {
		exception.AddError("phoneNumber", []any{err.Error()})

		return exception
	}

	if res.User.IsPhoneVerified {
		return exceptions.NewUnprocessableContentError(translator.Translate(nil, "errValidationMsg_phoneNumberHasBeenVerified"), nil)
	}

	return nil
}
