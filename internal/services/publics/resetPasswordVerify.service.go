package publicServices

import (
	"context"
	publicInputs "vibico-education-api/internal/gqls/inputs/publics"
	"vibico-education-api/internal/repository"
	"vibico-education-api/pkg/helpers"
	"vibico-education-api/pkg/sms"
	translator "vibico-education-api/pkg/translators"
	"vibico-education-api/setup/grpc_clients"

	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	pb "github.com/BehemothLtd/vibico-auth/proto/auth"
)

type ResetPasswordVerifyService struct {
	ctx              context.Context
	input            publicInputs.PasswordResetVerifyInput
	repos            repository.IRepositories
	ResetPasswordUrl string
}

func NewResetPasswordVerifyService(
	ctx context.Context,
	input publicInputs.PasswordResetVerifyInput,
	repos repository.IRepositories,
) *ResetPasswordVerifyService {
	return &ResetPasswordVerifyService{
		ctx:   ctx,
		input: input,
		repos: repos,
	}
}

func (s *ResetPasswordVerifyService) Execute() error {
	if err := s.validate(); err != nil {
		return err
	}

	identityPoolId := grpc_clients.PoolId()
	grpcResponse, err := grpc_clients.AuthClient().UpdateResetPasswordCode(
		grpc_clients.NewCtx(s.ctx),
		&pb.UpdateResetPasswordCodeRequest{
			IdentityPoolId: identityPoolId,
			PhoneNumber:    *s.input.PhoneNumber,
		},
	)
	if err != nil {
		return err
	}

	if grpcResponse.User != nil && grpcResponse.User.PasswordResetCode != nil && grpcResponse.User.PhoneNumber != nil {
		s.ResetPasswordUrl = helpers.MakeUrlWithParams(utils.GetEnv("WEB_URL", "http://localhost:8080")+"/reset_password", map[string]string{
			"phoneNumber": *grpcResponse.User.PhoneNumber,
			"code":        *grpcResponse.User.PasswordResetCode,
		})
	}

	return nil
}

func (service *ResetPasswordVerifyService) validate() error {
	if service.input.Code == nil ||
		*service.input.Code == "" ||
		service.input.PhoneNumber == nil ||
		*service.input.PhoneNumber == "" {
		return exceptions.NewUnprocessableContentError(translator.Translate(nil, "GeneralPleaseInputCorrect"), exceptions.ResourceModificationError{
			"base": []any{translator.Translate(nil, "GeneralPleaseInputCorrect")},
		})
	}

	smsService := sms.NewSMSService(service.ctx, utils.GetEnv("TWILIO_SERVICE_SID", ""))
	status, err := smsService.VerifyCode(*service.input.PhoneNumber, *service.input.Code)

	if err != nil {
		return exceptions.NewUnprocessableContentError(
			translator.Translate(nil, "GeneralError"),
			exceptions.ResourceModificationError{
				"code": []any{translator.Translate(nil, "errValidationMsg_verifyCode_invalid")},
			})
	}

	if status != sms.TwilioStatusApproved {
		return exceptions.NewUnprocessableContentError(translator.Translate(nil, "errValidationMsg_verifyCode_invalid"), nil)
	}

	return nil
}
