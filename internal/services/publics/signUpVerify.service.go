package publicServices

import (
	"context"
	"strings"

	publicInputs "vibico-education-api/internal/gqls/inputs/publics"
	"vibico-education-api/pkg/sms"
	translator "vibico-education-api/pkg/translators"
	"vibico-education-api/setup/grpc_clients"

	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/BehemothLtd/vibico-auth/proto/auth"
	"github.com/rs/zerolog/log"
)

type SignUpVerifyService struct {
	ctx   context.Context
	input *publicInputs.SignUpVerifyInput
}

func NewSignUpVerifyService(
	ctx context.Context,
	input *publicInputs.SignUpVerifyInput,
) *SignUpVerifyService {
	log.Debug().Ctx(ctx).Msg("publicServices.NewSignUpVerifyService")

	return &SignUpVerifyService{
		ctx:   ctx,
		input: input,
	}
}

func (service *SignUpVerifyService) Execute() error {
	log.Debug().Ctx(service.ctx).Msg("SignUpVerifyService.Execute")

	if err := service.validate(); err != nil {
		return err
	}

	_, err := grpc_clients.AuthClient().MarkPhoneVerified(
		grpc_clients.NewCtx(service.ctx),
		&auth.MarkPhoneVerifiedRequest{IdentityPoolId: grpc_clients.PoolId(), PhoneNumber: *service.input.PhoneNumber},
	)
	if err != nil {
		return err
	}

	return nil
}

func (service *SignUpVerifyService) validate() error {
	log.Debug().Ctx(service.ctx).Msg("SignUpVerifyService.validate")

	if err := service.validateInput(); err != nil {
		return err
	}

	if err := service.validateCode(); err != nil {
		return err
	}

	return nil
}

func (service *SignUpVerifyService) validateInput() error {
	log.Debug().Ctx(service.ctx).Msg("SignUpVerifyService.validateInput")

	exception := exceptions.NewUnprocessableContentError(translator.Translate(nil, "general_pleaseInputCorrect"), nil)

	if service.input == nil {
		return exception
	}

	if service.input.PhoneNumber == nil || strings.TrimSpace(*service.input.PhoneNumber) == "" {
		exception.AddError("phoneNumber", []any{translator.Translate(nil, "errValidationMsg_required")})

		return exception
	}

	if service.input.Code == nil || strings.TrimSpace(*service.input.Code) == "" {
		exception.AddError("code", []any{translator.Translate(nil, "errValidationMsg_required")})

		return exception
	}

	return nil
}

func (service *SignUpVerifyService) validateCode() error {
	log.Debug().Ctx(service.ctx).Msg("SignUpVerifyService.validateCode")

	smsService := sms.NewSMSService(service.ctx, utils.GetEnv("TWILIO_SERVICE_SID", ""))
	status, err := smsService.VerifyCode(*service.input.PhoneNumber, *service.input.Code)

	if err != nil {
		return exceptions.NewUnprocessableContentError(
			translator.Translate(nil, "general_error"),
			exceptions.ResourceModificationError{
				"code": []any{err.Error()},
			})
	}

	if status != sms.TwilioStatusApproved {
		return exceptions.NewUnprocessableContentError(translator.Translate(nil, "errValidationMsg_verifyCode_invalid"), nil)
	}

	return nil
}
