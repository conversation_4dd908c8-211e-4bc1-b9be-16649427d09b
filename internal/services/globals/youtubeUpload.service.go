package globalServices

import (
	"context"
	"vibico-education-api/internal/asyncq"
	videoTranscoderTasks "vibico-education-api/internal/asyncq/tasks/videoTranscoder"
	"vibico-education-api/internal/models"

	"github.com/rs/zerolog/log"
)

const tags = "education,vibico,course"

type YouTubeUploadService struct {
	ctx    context.Context
	videos []*models.Video
}

func NewYouTubeUploadService(ctx context.Context, videos []*models.Video) *YouTubeUploadService {
	return &YouTubeUploadService{
		ctx:    ctx,
		videos: videos,
	}
}

func (s *YouTubeUploadService) Execute() error {
	for _, video := range s.videos {
		description := ""
		if video.Description != nil {
			description = *video.Description
		}

		youtubePayload := videoTranscoderTasks.YouTubeUploadPayload{
			VideoID:     video.ID,
			Title:       video.Title,
			Description: description,
			Tags:        tags,
		}

		task, err := videoTranscoderTasks.NewYouTubeUploadTask(youtubePayload)
		if err != nil {
			log.Error().Ctx(s.ctx).Err(err).Uint32("videoID", video.ID).Msg("failed to create YouTube upload task")

			continue
		}

		if _, err := asyncq.GetManager().EnqueueTask(s.ctx, task); err != nil {
			log.Error().Ctx(s.ctx).Err(err).Uint32("videoID", video.ID).Msg("failed to enqueue YouTube upload task")
		}
	}

	return nil
}
