package globalServices

import (
	"context"
	"fmt"
	"os"
	"time"
	"vibico-education-api/constants"
	"vibico-education-api/internal/asyncq"
	videoTranscoderTasks "vibico-education-api/internal/asyncq/tasks/videoTranscoder"
	"vibico-education-api/internal/models"

	"cloud.google.com/go/storage"
)

type MuxUploadService struct {
	ctx    context.Context
	videos []*models.Video
}

func NewMuxUploadService(ctx context.Context, videos []*models.Video) *MuxUploadService {
	return &MuxUploadService{
		ctx:    ctx,
		videos: videos,
	}
}

func (s *MuxUploadService) Execute() error {
	for _, video := range s.videos {
		// Initialize GCS client using default credentials
		client, err := storage.NewClient(s.ctx)
		if err != nil {
			return fmt.Errorf("failed to initialize GCS client: %v", err)
		}
		defer client.Close()

		// Get bucket and object
		bucketName := os.Getenv("GCS_BUCKET_NAME")
		videoPath := fmt.Sprintf(constants.CourseVideoPath, video.Title, video.Title)

		// Generate signed URL valid for 70 minutes
		url, err := client.Bucket(bucketName).SignedURL(videoPath, &storage.SignedURLOptions{
			Scheme:  storage.SigningSchemeV4,
			Method:  "GET",
			Expires: time.Now().Add(70 * time.Minute),
		})

		if err != nil {
			return fmt.Errorf("failed to generate signed URL: %v", err)
		}

		// Create description pointer safety
		description := ""
		if video.Description != nil {
			description = *video.Description
		}

		// Create Mux upload payload
		muxPayload := videoTranscoderTasks.MuxUploadPayload{
			VideoID:     video.ID,
			FileURL:     url,
			Title:       video.Title,
			Description: description,
			IsFree:      video.IsFree,
			PassThrough: fmt.Sprintf("video-%d", video.ID),
		}

		// Create and enqueue Mux upload task
		task, err := videoTranscoderTasks.NewMuxUploadTask(muxPayload)
		if err != nil {
			return fmt.Errorf("failed to create Mux upload task: %w", err)
		}

		if _, err := asyncq.GetManager().EnqueueTask(context.Background(), task); err != nil {
			return fmt.Errorf("failed to enqueue Mux upload task: %w", err)
		}
	}

	return nil
}
