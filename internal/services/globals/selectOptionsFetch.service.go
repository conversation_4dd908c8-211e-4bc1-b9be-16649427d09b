package globalServices

import (
	"context"
	"slices"
	"strings"
	"vibico-education-api/internal/enums"
	globalInputs "vibico-education-api/internal/gqls/inputs/global"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	"vibico-education-api/internal/repository"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/gql"
	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/rs/zerolog/log"
)

type SelectOptionsFetchService struct {
	ctx    context.Context
	keys   *[]string
	params *globalInputs.SelectOptionsParamsType
	Result globalPayloads.SelectOptionsPayloadType
	repos  repository.IRepositories
}

func NewSelectOptionsFetchService(
	ctx context.Context,
	keys *[]string,
	params *globalInputs.SelectOptionsParamsType,
	repos repository.IRepositories,
) *SelectOptionsFetchService {
	return &SelectOptionsFetchService{
		ctx:    ctx,
		keys:   keys,
		params: params,
		repos:  repos,
	}
}

func (s *SelectOptionsFetchService) Execute() error {
	log.Debug().Ctx(s.ctx).Msg("SelectOptionsFetchService.Execute")

	if s.keys == nil {
		return nil
	}

	handlerMap := s.getHandlerMap()

	for _, key := range *s.keys {
		if handler, ok := handlerMap[key]; ok {
			if err := handler(); err != nil {
				return err
			}
		}
	}

	return nil
}

func (s *SelectOptionsFetchService) getHandlerMap() map[string]func() error {
	return map[string]func() error{
		"skillOptions":                    s.handleSkillOptions,
		"levelOptions":                    s.handleDrillLevels,
		"courseStatusOptions":             s.handleCourseStatus,
		"teacherOptions":                  s.handleTeacherOptions,
		"courseInstructionalLevelOptions": s.handleCourseInstructionalLevelOptions,
	}
}

func (s *SelectOptionsFetchService) handleSkillOptions() error {
	skills, err := s.repos.SkillRepo().All(s.ctx)
	if err != nil {
		return err
	}

	for _, skill := range *skills {
		skillKey := "Skill" + strings.ReplaceAll(skill.Name, " ", "")

		s.Result.SkillOptions = append(s.Result.SkillOptions, globalPayloads.GeneralOptionType{
			Value: gql.Uint32(skill.ID),
			Label: translator.Translate(nil, skillKey),
		})
	}
	return nil
}

func (s *SelectOptionsFetchService) handleDrillLevels() error {
	s.Result.LevelOptions = []globalPayloads.EnumValueOptionType{
		{Value: gql.Uint32(enums.DrillLevelBeginner.ToInt64()), Label: enums.DrillLevelBeginner.String(), Description: translator.Translate(nil, "DrillLevelBeginner")},
		{Value: gql.Uint32(enums.DrillLevelIntermediate.ToInt64()), Label: enums.DrillLevelIntermediate.String(), Description: translator.Translate(nil, "DrillLevelIntermediate")},
		{Value: gql.Uint32(enums.DrillLevelAdvanced.ToInt64()), Label: enums.DrillLevelAdvanced.String(), Description: translator.Translate(nil, "DrillLevelAdvanced")},
		{Value: gql.Uint32(enums.DrillLevelExpert.ToInt64()), Label: enums.DrillLevelExpert.String(), Description: translator.Translate(nil, "DrillLevelExpert")},
	}

	return nil
}

func (s *SelectOptionsFetchService) handleCourseStatus() error {
	hasCourseOptionExcludes := s.params != nil && s.params.CourseOptionExcludes != nil && len(*s.params.CourseOptionExcludes) > 0

	for _, courseStatusName := range enums.CourseStatusNames() {
		if hasCourseOptionExcludes && slices.Contains(*s.params.CourseOptionExcludes, courseStatusName) {
			continue
		}

		s.Result.CourseStatusOptions = append(s.Result.CourseStatusOptions, globalPayloads.StringValueOptionType{
			Value: courseStatusName,
			Label: translator.Translate(nil, "CourseStatus"+utils.CamelToPascalCase(courseStatusName)),
		})
	}

	return nil
}

func (s *SelectOptionsFetchService) handleTeacherOptions() error {
	teachers, err := s.repos.TeacherRepo().FindByCondition(s.ctx, true)
	if err != nil {
		return err
	}

	s.Result.TeacherOptions = make([]globalPayloads.GeneralOptionType, 0, len(*teachers))

	for _, teacher := range *teachers {
		s.Result.TeacherOptions = append(s.Result.TeacherOptions, globalPayloads.GeneralOptionType{
			Value: gql.Uint32(teacher.ID),
			Label: teacher.Name,
		})
	}
	return nil
}

func (s *SelectOptionsFetchService) handleCourseInstructionalLevelOptions() error {
	for _, courseInstructionalLevelName := range enums.CourseInstructionalLevelNames() {
		s.Result.CourseInstructionalLevelOptions = append(s.Result.CourseInstructionalLevelOptions, globalPayloads.StringValueOptionType{
			Value: courseInstructionalLevelName,
			Label: translator.Translate(nil, "CourseInstructionalLevel"+utils.CamelToPascalCase(courseInstructionalLevelName)),
		})
	}

	return nil
}
