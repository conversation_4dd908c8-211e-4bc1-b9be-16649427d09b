package globalServices

import (
	"context"
	"encoding/json"
	"fmt"
	"vibico-education-api/internal/asyncq"
	videoTranscoderTasks "vibico-education-api/internal/asyncq/tasks/videoTranscoder"
	"vibico-education-api/internal/models"

	"github.com/hibiken/asynq"
)

type VideoAfterDestroyService struct {
	ctx   context.Context
	video *models.Video
}

func NewVideoAfterDestroyService(ctx context.Context, video *models.Video) *VideoAfterDestroyService {
	return &VideoAfterDestroyService{
		ctx:   ctx,
		video: video,
	}
}

func (s *VideoAfterDestroyService) Execute() error {
	err := s.killAsyncTasks()
	if err != nil {
		return fmt.Errorf("failed to kill async tasks: %w", err)
	}

	platforms := make([]videoTranscoderTasks.PlatformInfo, 0, len(s.video.VideoPlatforms))
	for _, p := range s.video.VideoPlatforms {
		platforms = append(platforms, videoTranscoderTasks.PlatformInfo{
			Platform:        p.Platform,
			Status:          p.Status,
			PlatformVideoID: p.PlatformVideoID,
			YoutubeTokenID:  p.TokenID,
		})
	}

	cleanupPayload := videoTranscoderTasks.VideoCleanupPayload{
		VideoID:   s.video.ID,
		Filename:  s.video.VideoUpload.Filename,
		Platforms: platforms,
	}

	cleanupTask, err := videoTranscoderTasks.NewVideoCleanupTask(cleanupPayload)
	if err != nil {
		return fmt.Errorf("failed to create cleanup task: %w", err)
	}

	if _, err := asyncq.GetManager().EnqueueTask(s.ctx, cleanupTask); err != nil {
		return fmt.Errorf("failed to enqueue cleanup task: %w", err)
	}

	return nil
}

func (s *VideoAfterDestroyService) killAsyncTasks() error {
	inspector := asynq.NewInspector(asyncq.GetManager().Opt)
	defer inspector.Close()

	// Kill video processing tasks
	if err := s.killTasksByType(inspector, videoTranscoderTasks.GCSChunksComposeTaskType, func(payload []byte) (bool, error) {
		var p videoTranscoderTasks.GCSChunksComposeTaskPayload
		if err := json.Unmarshal(payload, &p); err != nil {
			return false, nil // Skip invalid payloads
		}
		return p.FileID == s.video.VideoUpload.Filename, nil
	}); err != nil {
		return fmt.Errorf("failed to kill video processing tasks: %w", err)
	}

	// Kill Mux upload tasks
	if err := s.killTasksByType(inspector, videoTranscoderTasks.MuxUploadTaskType, func(payload []byte) (bool, error) {
		var p videoTranscoderTasks.MuxUploadPayload
		if err := json.Unmarshal(payload, &p); err != nil {
			return false, nil // Skip invalid payloads
		}
		return p.VideoID == s.video.ID, nil
	}); err != nil {
		return fmt.Errorf("failed to kill Mux upload tasks: %w", err)
	}

	// Kill YouTube upload tasks
	if err := s.killTasksByType(inspector, videoTranscoderTasks.YouTubeUploadTaskType, func(payload []byte) (bool, error) {
		var p videoTranscoderTasks.YouTubeUploadPayload
		if err := json.Unmarshal(payload, &p); err != nil {
			return false, nil // Skip invalid payloads
		}
		return p.VideoID == s.video.ID, nil
	}); err != nil {
		return fmt.Errorf("failed to kill YouTube upload tasks: %w", err)
	}

	// run scheduled cleanup tasks
	scheduledTasks, err := inspector.ListScheduledTasks(asyncq.QueueVideoTranscoder, videoTranscoderTasks.CleanupOrphanChunksTaskType)
	if err != nil {
		return fmt.Errorf("failed to list scheduled cleanup tasks: %w", err)
	}

	for _, task := range scheduledTasks {
		var p videoTranscoderTasks.CleanUpOrphanChunksTaskPayload
		if err := json.Unmarshal(task.Payload, &p); err != nil {
			continue
		}

		if p.FileID == s.video.VideoUpload.Filename || p.VideoID == s.video.ID {
			if err := inspector.RunTask(asyncq.QueueVideoTranscoder, task.ID); err != nil {
				return fmt.Errorf("failed to run scheduled cleanup task %s: %w", task.ID, err)
			}
		}
	}

	return nil
}

func (s *VideoAfterDestroyService) killTasksByType(inspector *asynq.Inspector, taskType string, shouldKill func([]byte) (bool, error)) error {
	// 1. Cancel active tasks
	activeTasks, err := inspector.ListActiveTasks(asyncq.QueueVideoTranscoder, taskType)
	if err != nil {
		return fmt.Errorf("failed to list active tasks for type %s: %w", taskType, err)
	}

	for _, task := range activeTasks {
		if shouldKill, err := shouldKill(task.Payload); err != nil {
			continue
		} else if shouldKill {
			if err := inspector.CancelProcessing(task.ID); err != nil {
				return fmt.Errorf("failed to cancel active task %s: %w", task.ID, err)
			}
		}
	}

	// 2. Delete pending tasks
	pendingTasks, err := inspector.ListPendingTasks(asyncq.QueueVideoTranscoder, taskType)
	if err != nil {
		return fmt.Errorf("failed to list pending tasks for type %s: %w", taskType, err)
	}

	for _, task := range pendingTasks {
		if shouldKill, err := shouldKill(task.Payload); err != nil {
			continue
		} else if shouldKill {
			if err := inspector.DeleteTask(asyncq.QueueVideoTranscoder, task.ID); err != nil {
				return fmt.Errorf("failed to delete pending task %s: %w", task.ID, err)
			}
		}
	}

	// 3. Delete retry tasks
	retryTasks, err := inspector.ListRetryTasks(asyncq.QueueVideoTranscoder, taskType)
	if err != nil {
		return fmt.Errorf("failed to list retry tasks for type %s: %w", taskType, err)
	}

	for _, task := range retryTasks {
		if shouldKill, err := shouldKill(task.Payload); err != nil {
			continue
		} else if shouldKill {
			if err := inspector.DeleteTask(asyncq.QueueVideoTranscoder, task.ID); err != nil {
				return fmt.Errorf("failed to delete retry task %s: %w", task.ID, err)
			}
		}
	}

	return nil
}
