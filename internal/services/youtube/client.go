package youtube

import (
	"context"
	"errors"
	"fmt"
	"os"
	"strings"
	"sync"
	"time"

	"github.com/cenkalti/backoff/v5"
	"github.com/rs/zerolog/log"
	"google.golang.org/api/option"
	"google.golang.org/api/youtube/v3"
)

// Client for interacting with the YouTube API
type Client struct {
	service         *youtube.Service
	authManager     *AuthManager
	quota           *QuotaTracker
	credentialsFile string
	tokenFile       string
	mu              sync.Mutex
}

// QuotaTracker tracks YouTube API quota usage
type QuotaTracker struct {
	usage     int
	limit     int
	resetTime time.Time
	mu        sync.Mutex
}

// NewQuotaTracker creates a new QuotaTracker
func NewQuotaTracker(quotaLimit int) *QuotaTracker {
	return &QuotaTracker{
		usage:     0,
		limit:     quotaLimit,
		resetTime: time.Now().Add(24 * time.Hour),
	}
}

// TrackQuotaUsage tracks YouTube API quota usage
func (q *QuotaTracker) TrackQuotaUsage(cost int) {
	q.mu.Lock()
	defer q.mu.Unlock()

	// Reset quota if it's a new day
	currentTime := time.Now()
	if currentTime.After(q.resetTime) {
		q.usage = 0
		q.resetTime = currentTime.Add(24 * time.Hour)
	}

	// Update quota usage
	q.usage += cost

	// Log quota usage
	log.Debug().Msgf("YouTube API quota usage: %d/%d", q.usage, q.limit)
}

// CheckQuota checks if there's enough quota for an API call
func (q *QuotaTracker) CheckQuota(cost int) bool {
	q.mu.Lock()
	defer q.mu.Unlock()

	// Reset quota if it's a new day
	currentTime := time.Now()
	if currentTime.After(q.resetTime) {
		q.usage = 0
		q.resetTime = currentTime.Add(24 * time.Hour)
	}

	return q.usage+cost <= q.limit
}

// GetQuotaUsage returns current quota usage
func (q *QuotaTracker) GetQuotaUsage() int {
	q.mu.Lock()
	defer q.mu.Unlock()
	return q.usage
}

// GetQuotaLimit returns quota limit
func (q *QuotaTracker) GetQuotaLimit() int {
	return q.limit
}

// ResetQuota resets quota usage
func (q *QuotaTracker) ResetQuota() {
	q.mu.Lock()
	defer q.mu.Unlock()
	q.usage = 0
	q.resetTime = time.Now().Add(24 * time.Hour)
}

// NewClientFromEnv creates a new YouTube client using environment variables
func NewClientFromEnv(ctx context.Context) (*Client, error) {
	credentialsFile := os.Getenv("YOUTUBE_API_CREDENTIALS_FILE")
	tokenFile := os.Getenv("YOUTUBE_API_TOKEN_FILE")

	if credentialsFile == "" {
		return nil, NewYouTubeClientError("YOUTUBE_API_CREDENTIALS_FILE environment variable not set")
	}

	if tokenFile == "" {
		return nil, NewYouTubeClientError("YOUTUBE_API_TOKEN_FILE environment variable not set")
	}

	return NewClient(ctx, &ClientConfig{
		CredentialsFile: credentialsFile,
		TokenFile:       tokenFile,
		QuotaLimit:      DefaultQuotaLimit,
	})
}

// NewClient creates a new YouTube client
func NewClient(ctx context.Context, config *ClientConfig) (*Client, error) {
	if config == nil {
		return nil, NewYouTubeClientError("config cannot be nil")
	}

	if config.CredentialsFile == "" {
		return nil, NewYouTubeClientError("credentials file not specified")
	}

	if config.TokenFile == "" {
		return nil, NewYouTubeClientError("token file not specified")
	}

	quotaLimit := config.QuotaLimit
	if quotaLimit <= 0 {
		quotaLimit = DefaultQuotaLimit
	}

	client := &Client{
		credentialsFile: config.CredentialsFile,
		tokenFile:       config.TokenFile,
		quota:           NewQuotaTracker(quotaLimit),
	}

	// Initialize auth manager
	authManager, err := NewAuthManager(ctx, config.CredentialsFile, config.TokenFile)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize auth manager: %w", err)
	}
	client.authManager = authManager

	// Initialize YouTube service
	if err := client.initService(ctx); err != nil {
		return nil, fmt.Errorf("failed to initialize YouTube service: %w", err)
	}

	return client, nil
}

// initService initializes the YouTube service
func (c *Client) initService(ctx context.Context) error {
	c.mu.Lock()
	defer c.mu.Unlock()

	// Get HTTP client
	httpClient, err := c.authManager.GetHTTPClient(ctx)
	if err != nil {
		return fmt.Errorf("failed to get HTTP client: %w", err)
	}

	// Create YouTube service
	service, err := youtube.NewService(ctx, option.WithHTTPClient(httpClient))
	if err != nil {
		return fmt.Errorf("failed to create YouTube service: %w", err)
	}

	c.service = service
	return nil
}

// GetService returns the YouTube service
func (c *Client) GetService() *youtube.Service {
	return c.service
}

// RetryOperation retries an operation with exponential backoff
func (c *Client) RetryOperation(operation func() error) error {
	b := backoff.NewExponentialBackOff()
	b.InitialInterval = 1 * time.Second
	b.MaxInterval = 60 * time.Second

	// Create a context with timeout for overall retry duration
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()

	// Adapt our operation to match the Operation[T] type signature
	// We use bool as our T type since we only care about the error
	op := func() (bool, error) {
		err := operation()
		if err != nil {
			// Check if this is a permanent error that shouldn't be retried
			if errors.Is(err, context.Canceled) || errors.Is(err, context.DeadlineExceeded) {
				return false, err // Stop retrying on context errors
			}
			return false, nil // Temporary error, will retry
		}
		return true, nil // Success
	}

	success, err := backoff.Retry(ctx, op, backoff.WithBackOff(b))
	if err != nil {
		return err // Return permanent error
	}

	if !success {
		return errors.New("operation failed after maximum retries")
	}

	return nil
}

// IsQuotaExceededError checks if an error is due to quota exceeded
func IsQuotaExceededError(err error) bool {
	if err == nil {
		return false
	}

	// Check if it's our own QuotaExceededError
	var quotaErr *QuotaExceededError
	if errors.As(err, &quotaErr) {
		return true
	}

	// Check error message for quota exceeded
	errorMsg := err.Error()
	return strings.Contains(errorMsg, "quotaExceeded") || strings.Contains(errorMsg, "quota exceeded")
}
