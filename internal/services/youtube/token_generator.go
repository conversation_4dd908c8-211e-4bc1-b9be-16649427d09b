package youtube

import (
	"context"
	"encoding/json"
	"fmt"
	"os"

	"github.com/rs/zerolog/log"
	"golang.org/x/oauth2"
	"golang.org/x/oauth2/google"
)

// TokenGenerator handles generation of YouTube API tokens
type TokenGenerator struct {
	config    *oauth2.Config
	tokenFile string
}

// NewTokenGenerator creates a new TokenGenerator
func NewTokenGenerator(credentialsFile, tokenFile string) (*TokenGenerator, error) {
	// Check if credentials file exists
	if _, err := os.Stat(credentialsFile); os.IsNotExist(err) {
		return nil, fmt.Errorf("credentials file does not exist: %s", credentialsFile)
	}

	// Read OAuth2 credentials
	credBytes, err := os.ReadFile(credentialsFile)
	if err != nil {
		return nil, fmt.Errorf("failed to read credentials file: %v", err)
	}

	// Parse credentials map first
	var credMap map[string]interface{}
	if err := json.Unmarshal(credBytes, &credMap); err != nil {
		return nil, fmt.Errorf("failed to parse credentials file: %v", err)
	}

	// Handle case where redirect_uris is missing
	if webConfig, ok := credMap["web"].(map[string]interface{}); ok {
		// Set redirect_uris regardless of whether it exists or not
		webConfig["redirect_uris"] = []string{"http://localhost:8080/"}
		// Update credMap
		credMap["web"] = webConfig
		// Convert back to bytes
		updatedCredBytes, err := json.Marshal(credMap)
		if err != nil {
			return nil, fmt.Errorf("failed to serialize updated credentials: %v", err)
		}
		credBytes = updatedCredBytes
	}

	// Parse OAuth2 config
	config, err := google.ConfigFromJSON(credBytes, Scopes...)
	if err != nil {
		return nil, fmt.Errorf("failed to parse client secret file: %v", err)
	}

	// Ensure redirect URI is set correctly
	config.RedirectURL = "http://localhost:8080/"

	return &TokenGenerator{
		config:    config,
		tokenFile: tokenFile,
	}, nil
}

// GetAuthURL returns the authorization URL for the OAuth2 flow
func (g *TokenGenerator) GetAuthURL() (string, error) {
	// Generate auth URL with offline access for refresh token
	authURL := g.config.AuthCodeURL("state-token", oauth2.AccessTypeOffline, oauth2.ApprovalForce)
	return authURL, nil
}

// ExchangeToken exchanges an authorization code for a token
func (g *TokenGenerator) ExchangeToken(ctx context.Context, authCode string) (*oauth2.Token, error) {
	// Exchange auth code for token
	token, err := g.config.Exchange(ctx, authCode)
	if err != nil {
		return nil, fmt.Errorf("failed to exchange authorization code for token: %v", err)
	}

	return token, nil
}

// SaveToken saves the OAuth2 token to the token file
func (g *TokenGenerator) SaveToken(token *oauth2.Token) error {
	// Convert OAuth2 token to token data
	tokenData := TokenData{
		Token:        token.AccessToken,
		RefreshToken: token.RefreshToken,
		TokenURI:     "Bearer",
		ClientID:     g.config.ClientID,
		ClientSecret: g.config.ClientSecret,
		Scopes:       Scopes,
	}

	// Set expiry time if available
	if !token.Expiry.IsZero() {
		tokenData.ExpiryTime = token.Expiry.Unix()
	}

	// Serialize token data
	tokenBytes, err := json.MarshalIndent(tokenData, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to serialize token: %v", err)
	}

	// Write token data to file
	if err := os.WriteFile(g.tokenFile, tokenBytes, 0600); err != nil {
		return fmt.Errorf("failed to save token file: %v", err)
	}

	log.Info().Str("tokenFile", g.tokenFile).Msg("Token saved successfully")
	return nil
}
