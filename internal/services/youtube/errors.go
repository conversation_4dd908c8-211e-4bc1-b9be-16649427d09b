package youtube

import "fmt"

// YouTubeClientError is the base error for YouTube client errors
type YouTubeClientError struct {
	Message string
}

func (e YouTubeClientError) Error() string {
	return fmt.Sprintf("YouTube client error: %s", e.Message)
}

// NewYouTubeClientError creates a new YouTubeClientError
func NewYouTubeClientError(message string) error {
	return &YouTubeClientError{Message: message}
}

// YouTubeUploadError is an error raised during YouTube uploads
type YouTubeUploadError struct {
	Message string
}

func (e YouTubeUploadError) Error() string {
	return fmt.Sprintf("YouTube upload error: %s", e.Message)
}

// NewYouTubeUploadError creates a new YouTubeUploadError
func NewYouTubeUploadError(message string) error {
	return &YouTubeUploadError{Message: message}
}

// QuotaExceededError is raised when YouTube API quota is exceeded
type QuotaExceededError struct {
	Message string
}

func (e QuotaExceededError) Error() string {
	if e.Message == "" {
		return "YouTube API quota exceeded"
	}
	return fmt.Sprintf("YouTube API quota exceeded: %s", e.Message)
}

// NewQuotaExceededError creates a new QuotaExceededError
func NewQuotaExceededError(message string) error {
	return &QuotaExceededError{Message: message}
}

// AuthenticationError is raised for authentication errors
type AuthenticationError struct {
	Message string
}

func (e AuthenticationError) Error() string {
	return fmt.Sprintf("YouTube authentication error: %s", e.Message)
}

// NewAuthenticationError creates a new AuthenticationError
func NewAuthenticationError(message string) error {
	return &AuthenticationError{Message: message}
}
