package youtube

import (
	"context"
	"fmt"
	"time"

	"github.com/rs/zerolog/log"
	"google.golang.org/api/youtube/v3"
)

// VideoDetailsService handles getting details for a YouTube video
type VideoDetailsService struct {
	ctx     context.Context
	client  *Client
	videoID string
	video   *Video
}

// NewVideoDetailsService creates a new VideoDetailsService
func NewVideoDetailsService(
	ctx context.Context,
	client *Client,
	videoID string,
) *VideoDetailsService {
	return &VideoDetailsService{
		ctx:     ctx,
		client:  client,
		videoID: videoID,
		video:   &Video{},
	}
}

// Execute gets details for a video
func (s *VideoDetailsService) Execute() error {
	// Validate inputs
	if err := s.validateInputs(); err != nil {
		return err
	}

	// Check quota
	if !s.client.quota.CheckQuota(QuotaCostVideoDetails) {
		return NewQuotaExceededError("")
	}

	// Get YouTube service
	youtubeService := s.client.GetService()

	// Execute the request with retry
	var response *youtube.VideoListResponse
	err := s.client.RetryOperation(func() error {
		resp, err := youtubeService.Videos.
			List([]string{"snippet", "status", "contentDetails", "statistics"}).
			Id(s.videoID).
			Do()
		if err != nil {
			if IsQuotaExceededError(err) {
				return NewQuotaExceededError(err.Error())
			}
			return err
		}
		response = resp
		return nil
	})

	if err != nil {
		log.Error().Err(err).Str("videoId", s.videoID).Msg("Failed to get video details")
		return NewYouTubeClientError(fmt.Sprintf("failed to get video details: %v", err))
	}

	// Track quota usage
	s.client.quota.TrackQuotaUsage(QuotaCostVideoDetails)

	// Check if video was found
	if len(response.Items) == 0 {
		return NewYouTubeClientError(fmt.Sprintf("video not found: %s", s.videoID))
	}

	video := response.Items[0]

	// Map the response to our Video struct
	publishedAt, err := time.Parse(time.RFC3339, video.Snippet.PublishedAt)
	if err != nil {
		publishedAt = time.Now()
	}

	// Convert statistics
	var viewCount, likeCount, commentCount int64
	if video.Statistics != nil {
		// Convert uint64 to int64, we assume none of these will ever exceed int64 max value
		if video.Statistics.ViewCount > 0 {
			viewCount = int64(video.Statistics.ViewCount)
		}
		if video.Statistics.LikeCount > 0 {
			likeCount = int64(video.Statistics.LikeCount)
		}
		if video.Statistics.CommentCount > 0 {
			commentCount = int64(video.Statistics.CommentCount)
		}
	}

	s.video = &Video{
		ID:            video.Id,
		Title:         video.Snippet.Title,
		Description:   video.Snippet.Description,
		Tags:          video.Snippet.Tags,
		CategoryID:    video.Snippet.CategoryId,
		PrivacyStatus: PrivacyStatus(video.Status.PrivacyStatus),
		URL:           fmt.Sprintf("https://www.youtube.com/watch?v=%s", video.Id),
		Duration:      video.ContentDetails.Duration,
		ViewCount:     viewCount,
		LikeCount:     likeCount,
		CommentCount:  commentCount,
		PublishedAt:   publishedAt,
	}

	return nil
}

// GetVideo returns the retrieved video
func (s *VideoDetailsService) GetVideo() *Video {
	return s.video
}

// validateInputs validates the service inputs
func (s *VideoDetailsService) validateInputs() error {
	if s.client == nil {
		return NewYouTubeClientError("client cannot be nil")
	}

	if s.videoID == "" {
		return NewYouTubeClientError("video ID cannot be empty")
	}

	return nil
}

// VideoUpdateService handles updating a YouTube video
type VideoUpdateService struct {
	ctx           context.Context
	client        *Client
	videoID       string
	title         *string
	description   *string
	tags          *[]string
	categoryID    *string
	privacyStatus *PrivacyStatus
	video         *Video
}

// NewVideoUpdateService creates a new VideoUpdateService
func NewVideoUpdateService(
	ctx context.Context,
	client *Client,
	videoID string,
) *VideoUpdateService {
	return &VideoUpdateService{
		ctx:     ctx,
		client:  client,
		videoID: videoID,
		video:   &Video{},
	}
}

// WithTitle sets the title to update
func (s *VideoUpdateService) WithTitle(title string) *VideoUpdateService {
	s.title = &title
	return s
}

// WithDescription sets the description to update
func (s *VideoUpdateService) WithDescription(description string) *VideoUpdateService {
	s.description = &description
	return s
}

// WithTags sets the tags to update
func (s *VideoUpdateService) WithTags(tags []string) *VideoUpdateService {
	s.tags = &tags
	return s
}

// WithCategoryID sets the category ID to update
func (s *VideoUpdateService) WithCategoryID(categoryID string) *VideoUpdateService {
	s.categoryID = &categoryID
	return s
}

// WithPrivacyStatus sets the privacy status to update
func (s *VideoUpdateService) WithPrivacyStatus(privacyStatus PrivacyStatus) *VideoUpdateService {
	s.privacyStatus = &privacyStatus
	return s
}

// Execute updates a video
func (s *VideoUpdateService) Execute() error {
	// Validate inputs
	if err := s.validateInputs(); err != nil {
		return err
	}

	// Check quota
	if !s.client.quota.CheckQuota(QuotaCostVideoUpdate) {
		return NewQuotaExceededError("")
	}

	// Get YouTube service
	youtubeService := s.client.GetService()

	// Get current video details to update only specified fields
	detailsService := NewVideoDetailsService(s.ctx, s.client, s.videoID)
	if err := detailsService.Execute(); err != nil {
		return err
	}
	currentVideo := detailsService.GetVideo()

	// Create update request
	updateRequest := &youtube.Video{
		Id: s.videoID,
		Snippet: &youtube.VideoSnippet{
			Title:       currentVideo.Title,
			Description: currentVideo.Description,
			CategoryId:  currentVideo.CategoryID,
		},
		Status: &youtube.VideoStatus{
			PrivacyStatus: string(currentVideo.PrivacyStatus),
		},
	}

	// Apply updates if specified
	if s.title != nil {
		updateRequest.Snippet.Title = *s.title
	}
	if s.description != nil {
		updateRequest.Snippet.Description = *s.description
	}
	if s.tags != nil {
		updateRequest.Snippet.Tags = *s.tags
	}
	if s.categoryID != nil {
		updateRequest.Snippet.CategoryId = *s.categoryID
	}
	if s.privacyStatus != nil {
		updateRequest.Status.PrivacyStatus = string(*s.privacyStatus)
	}

	// Execute the request with retry
	var response *youtube.Video
	err := s.client.RetryOperation(func() error {
		resp, err := youtubeService.Videos.Update(
			[]string{"snippet", "status"},
			updateRequest,
		).Do()
		if err != nil {
			if IsQuotaExceededError(err) {
				return NewQuotaExceededError(err.Error())
			}
			return err
		}
		response = resp
		return nil
	})

	if err != nil {
		log.Error().Err(err).Str("videoId", s.videoID).Msg("Failed to update video")
		return NewYouTubeClientError(fmt.Sprintf("failed to update video: %v", err))
	}

	// Track quota usage
	s.client.quota.TrackQuotaUsage(QuotaCostVideoUpdate)

	// Map the response to our Video struct
	publishedAt, err := time.Parse(time.RFC3339, response.Snippet.PublishedAt)
	if err != nil {
		publishedAt = time.Now()
	}

	s.video = &Video{
		ID:            response.Id,
		Title:         response.Snippet.Title,
		Description:   response.Snippet.Description,
		Tags:          response.Snippet.Tags,
		CategoryID:    response.Snippet.CategoryId,
		PrivacyStatus: PrivacyStatus(response.Status.PrivacyStatus),
		URL:           fmt.Sprintf("https://www.youtube.com/watch?v=%s", response.Id),
		PublishedAt:   publishedAt,
	}

	log.Info().Str("videoId", s.video.ID).Msg("Video updated successfully")

	return nil
}

// GetVideo returns the updated video
func (s *VideoUpdateService) GetVideo() *Video {
	return s.video
}

// validateInputs validates the service inputs
func (s *VideoUpdateService) validateInputs() error {
	if s.client == nil {
		return NewYouTubeClientError("client cannot be nil")
	}

	if s.videoID == "" {
		return NewYouTubeClientError("video ID cannot be empty")
	}

	// At least one update field must be specified
	if s.title == nil && s.description == nil && s.tags == nil && s.categoryID == nil && s.privacyStatus == nil {
		return NewYouTubeClientError("at least one field to update must be specified")
	}

	return nil
}
