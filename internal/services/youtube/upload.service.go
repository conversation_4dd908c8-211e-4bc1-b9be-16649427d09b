package youtube

import (
	"context"
	"fmt"
	"io"
	"os"

	"github.com/rs/zerolog/log"
	"google.golang.org/api/youtube/v3"
)

// UploadService handles uploading videos to YouTube
type UploadService struct {
	ctx              context.Context
	client           *Client
	videoPath        string
	metadata         VideoMetadata
	progressCallback ProgressCallback
	video            *Video
}

// NewUploadService creates a new UploadService
func NewUploadService(
	ctx context.Context,
	client *Client,
	videoPath string,
	metadata VideoMetadata,
	progressCallback ProgressCallback,
) *UploadService {
	return &UploadService{
		ctx:              ctx,
		client:           client,
		videoPath:        videoPath,
		metadata:         metadata,
		progressCallback: progressCallback,
		video:            &Video{},
	}
}

// Execute uploads a video to YouTube
func (s *UploadService) Execute() error {
	// Validate inputs
	if err := s.validateInputs(); err != nil {
		return err
	}

	// Check quota
	if !s.client.quota.CheckQuota(QuotaCostUpload) {
		return NewQuotaExceededError("")
	}

	// Create YouTube service
	youtubeService := s.client.GetService()

	// Validate video path
	fileInfo, err := os.Stat(s.videoPath)
	if err != nil {
		return NewYouTubeUploadError(fmt.Sprintf("failed to access video file: %v", err))
	}

	if fileInfo.IsDir() {
		return NewYouTubeUploadError("provided path is a directory, not a file")
	}

	// Prepare video metadata
	videoSnippet := &youtube.VideoSnippet{
		Title:       s.metadata.Title,
		Description: s.metadata.Description,
		CategoryId:  s.metadata.CategoryID,
	}

	// Add tags if provided
	if len(s.metadata.Tags) > 0 {
		videoSnippet.Tags = s.metadata.Tags
	}

	// Set privacy status
	status := &youtube.VideoStatus{
		PrivacyStatus:           string(s.metadata.PrivacyStatus),
		SelfDeclaredMadeForKids: false,
	}

	// Create video insert request
	insert := youtubeService.Videos.Insert(
		[]string{"snippet", "status"},
		&youtube.Video{
			Snippet: videoSnippet,
			Status:  status,
		},
	)

	// Open the video file for uploading
	file, err := os.Open(s.videoPath)
	if err != nil {
		return NewYouTubeUploadError(fmt.Sprintf("failed to open video file: %v", err))
	}
	defer file.Close()

	// Create a progress reader that wraps the file
	progressReader := &ProgressReader{
		Reader:    file,
		Size:      fileInfo.Size(),
		Progress:  s.progressCallback,
		BytesRead: 0,
	}

	// Set the media content
	insert = insert.Media(progressReader)

	// Execute the request through the client's retry operation
	var response *youtube.Video
	err = s.client.RetryOperation(func() error {
		resp, err := insert.Do()
		if err != nil {
			// Check for quota exceeded error
			if IsQuotaExceededError(err) {
				return NewQuotaExceededError(err.Error())
			}
			return err
		}
		response = resp
		return nil
	})

	if err != nil {
		log.Error().Err(err).Msg("Failed to upload video")
		return NewYouTubeUploadError(fmt.Sprintf("failed to upload video: %v", err))
	}

	// Track quota usage
	s.client.quota.TrackQuotaUsage(QuotaCostUpload)

	// Map the response to our Video struct
	s.video = &Video{
		ID:            response.Id,
		Title:         response.Snippet.Title,
		Description:   response.Snippet.Description,
		Tags:          response.Snippet.Tags,
		CategoryID:    response.Snippet.CategoryId,
		PrivacyStatus: PrivacyStatus(response.Status.PrivacyStatus),
		URL:           fmt.Sprintf("https://www.youtube.com/watch?v=%s", response.Id),
	}

	log.Info().Str("videoId", s.video.ID).Str("url", s.video.URL).Msg("Video uploaded successfully")

	return nil
}

// GetVideo returns the uploaded video information
func (s *UploadService) GetVideo() *Video {
	return s.video
}

// validateInputs validates the service inputs
func (s *UploadService) validateInputs() error {
	if s.client == nil {
		return NewYouTubeUploadError("client cannot be nil")
	}

	if s.videoPath == "" {
		return NewYouTubeUploadError("video path cannot be empty")
	}

	if _, err := os.Stat(s.videoPath); os.IsNotExist(err) {
		return NewYouTubeUploadError(fmt.Sprintf("video file not found: %s", s.videoPath))
	}

	if s.metadata.Title == "" {
		return NewYouTubeUploadError("video title cannot be empty")
	}

	if s.metadata.PrivacyStatus == "" {
		s.metadata.PrivacyStatus = PrivacyStatusPrivate // Default to private
	}

	if s.metadata.CategoryID == "" {
		s.metadata.CategoryID = "22" // Default to "People & Blogs"
	}

	return nil
}

// ProgressReader wraps an io.Reader to track read progress
type ProgressReader struct {
	Reader    io.Reader
	Size      int64
	BytesRead int64
	Progress  ProgressCallback
}

// Read implements io.Reader
func (r *ProgressReader) Read(p []byte) (n int, err error) {
	n, err = r.Reader.Read(p)

	if n > 0 {
		r.BytesRead += int64(n)

		// Report progress if callback is provided
		if r.Progress != nil && r.Size > 0 {
			progress := float64(r.BytesRead) / float64(r.Size)
			r.Progress(progress)
		}
	}

	return n, err
}
