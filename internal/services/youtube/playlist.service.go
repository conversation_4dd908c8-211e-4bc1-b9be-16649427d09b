package youtube

import (
	"context"
	"fmt"
	"time"

	"github.com/rs/zerolog/log"
	"google.golang.org/api/youtube/v3"
)

// PlaylistCreateService handles creating a new YouTube playlist
type PlaylistCreateService struct {
	ctx           context.Context
	client        *Client
	title         string
	description   string
	privacyStatus PrivacyStatus
	playlist      *Playlist
}

// NewPlaylistCreateService creates a new PlaylistCreateService
func NewPlaylistCreateService(
	ctx context.Context,
	client *Client,
	title string,
	description string,
	privacyStatus PrivacyStatus,
) *PlaylistCreateService {
	return &PlaylistCreateService{
		ctx:           ctx,
		client:        client,
		title:         title,
		description:   description,
		privacyStatus: privacyStatus,
		playlist:      &Playlist{},
	}
}

// Execute creates a new playlist
func (s *PlaylistCreateService) Execute() error {
	// Validate inputs
	if err := s.validateInputs(); err != nil {
		return err
	}

	// Check quota
	if !s.client.quota.CheckQuota(QuotaCostPlaylistCreate) {
		return NewQuotaExceededError("")
	}

	// Create playlist request
	request := &youtube.Playlist{
		Snippet: &youtube.PlaylistSnippet{
			Title:       s.title,
			Description: s.description,
		},
		Status: &youtube.PlaylistStatus{
			PrivacyStatus: string(s.privacyStatus),
		},
	}

	// Get YouTube service
	youtubeService := s.client.GetService()

	// Execute the request with retry
	var response *youtube.Playlist
	err := s.client.RetryOperation(func() error {
		resp, err := youtubeService.Playlists.Insert([]string{"snippet", "status"}, request).Do()
		if err != nil {
			if IsQuotaExceededError(err) {
				return NewQuotaExceededError(err.Error())
			}
			return err
		}
		response = resp
		return nil
	})

	if err != nil {
		log.Error().Err(err).Msg("Failed to create playlist")
		return NewYouTubeClientError(fmt.Sprintf("failed to create playlist: %v", err))
	}

	// Track quota usage
	s.client.quota.TrackQuotaUsage(QuotaCostPlaylistCreate)

	// Map the response to our Playlist struct
	publishedAt, err := time.Parse(time.RFC3339, response.Snippet.PublishedAt)
	if err != nil {
		publishedAt = time.Now()
	}

	s.playlist = &Playlist{
		ID:            response.Id,
		Title:         response.Snippet.Title,
		Description:   response.Snippet.Description,
		PrivacyStatus: PrivacyStatus(response.Status.PrivacyStatus),
		URL:           fmt.Sprintf("https://www.youtube.com/playlist?list=%s", response.Id),
		ItemCount:     int64(response.ContentDetails.ItemCount),
		PublishedAt:   publishedAt,
		ChannelID:     response.Snippet.ChannelId,
		ChannelTitle:  response.Snippet.ChannelTitle,
	}

	log.Info().Str("playlistId", s.playlist.ID).Str("url", s.playlist.URL).Msg("Playlist created successfully")

	return nil
}

// GetPlaylist returns the created playlist
func (s *PlaylistCreateService) GetPlaylist() *Playlist {
	return s.playlist
}

// validateInputs validates the service inputs
func (s *PlaylistCreateService) validateInputs() error {
	if s.client == nil {
		return NewYouTubeClientError("client cannot be nil")
	}

	if s.title == "" {
		return NewYouTubeClientError("playlist title cannot be empty")
	}

	if s.privacyStatus == "" {
		s.privacyStatus = PrivacyStatusPrivate // Default to private
	}

	return nil
}

// PlaylistAddVideoService handles adding a video to a YouTube playlist
type PlaylistAddVideoService struct {
	ctx        context.Context
	client     *Client
	playlistID string
	videoID    string
	item       *PlaylistItem
}

// NewPlaylistAddVideoService creates a new PlaylistAddVideoService
func NewPlaylistAddVideoService(
	ctx context.Context,
	client *Client,
	playlistID string,
	videoID string,
) *PlaylistAddVideoService {
	return &PlaylistAddVideoService{
		ctx:        ctx,
		client:     client,
		playlistID: playlistID,
		videoID:    videoID,
		item:       &PlaylistItem{},
	}
}

// Execute adds a video to a playlist
func (s *PlaylistAddVideoService) Execute() error {
	// Validate inputs
	if err := s.validateInputs(); err != nil {
		return err
	}

	// Check quota
	if !s.client.quota.CheckQuota(QuotaCostAddToPlaylist) {
		return NewQuotaExceededError("")
	}

	// Create request
	request := &youtube.PlaylistItem{
		Snippet: &youtube.PlaylistItemSnippet{
			PlaylistId: s.playlistID,
			ResourceId: &youtube.ResourceId{
				Kind:    "youtube#video",
				VideoId: s.videoID,
			},
		},
	}

	// Get YouTube service
	youtubeService := s.client.GetService()

	// Execute the request with retry
	var response *youtube.PlaylistItem
	err := s.client.RetryOperation(func() error {
		resp, err := youtubeService.PlaylistItems.Insert([]string{"snippet"}, request).Do()
		if err != nil {
			if IsQuotaExceededError(err) {
				return NewQuotaExceededError(err.Error())
			}
			return err
		}
		response = resp
		return nil
	})

	if err != nil {
		log.Error().Err(err).Msg("Failed to add video to playlist")
		return NewYouTubeClientError(fmt.Sprintf("failed to add video to playlist: %v", err))
	}

	// Track quota usage
	s.client.quota.TrackQuotaUsage(QuotaCostAddToPlaylist)

	// Map the response to our PlaylistItem struct
	publishedAt, err := time.Parse(time.RFC3339, response.Snippet.PublishedAt)
	if err != nil {
		publishedAt = time.Now()
	}

	s.item = &PlaylistItem{
		ID:           response.Id,
		Title:        response.Snippet.Title,
		VideoID:      response.Snippet.ResourceId.VideoId,
		Position:     int64(response.Snippet.Position),
		PublishedAt:  publishedAt,
		ChannelID:    response.Snippet.ChannelId,
		ChannelTitle: response.Snippet.ChannelTitle,
		VideoURL:     fmt.Sprintf("https://www.youtube.com/watch?v=%s", response.Snippet.ResourceId.VideoId),
	}

	log.Info().
		Str("playlistItemId", s.item.ID).
		Str("playlistId", s.playlistID).
		Str("videoId", s.videoID).
		Msg("Video added to playlist successfully")

	return nil
}

// GetPlaylistItem returns the added playlist item
func (s *PlaylistAddVideoService) GetPlaylistItem() *PlaylistItem {
	return s.item
}

// validateInputs validates the service inputs
func (s *PlaylistAddVideoService) validateInputs() error {
	if s.client == nil {
		return NewYouTubeClientError("client cannot be nil")
	}

	if s.playlistID == "" {
		return NewYouTubeClientError("playlist ID cannot be empty")
	}

	if s.videoID == "" {
		return NewYouTubeClientError("video ID cannot be empty")
	}

	return nil
}

// PlaylistDetailsService handles getting details for a YouTube playlist
type PlaylistDetailsService struct {
	ctx        context.Context
	client     *Client
	playlistID string
	playlist   *Playlist
}

// NewPlaylistDetailsService creates a new PlaylistDetailsService
func NewPlaylistDetailsService(
	ctx context.Context,
	client *Client,
	playlistID string,
) *PlaylistDetailsService {
	return &PlaylistDetailsService{
		ctx:        ctx,
		client:     client,
		playlistID: playlistID,
		playlist:   &Playlist{},
	}
}

// Execute gets details for a playlist
func (s *PlaylistDetailsService) Execute() error {
	// Validate inputs
	if err := s.validateInputs(); err != nil {
		return err
	}

	// Check quota
	if !s.client.quota.CheckQuota(QuotaCostPlaylistDetails) {
		return NewQuotaExceededError("")
	}

	// Get YouTube service
	youtubeService := s.client.GetService()

	// Execute the request with retry
	var response *youtube.PlaylistListResponse
	err := s.client.RetryOperation(func() error {
		resp, err := youtubeService.Playlists.
			List([]string{"snippet", "status", "contentDetails"}).
			Id(s.playlistID).
			MaxResults(1).
			Do()
		if err != nil {
			if IsQuotaExceededError(err) {
				return NewQuotaExceededError(err.Error())
			}
			return err
		}
		response = resp
		return nil
	})

	if err != nil {
		log.Error().Err(err).Str("playlistId", s.playlistID).Msg("Failed to get playlist details")
		return NewYouTubeClientError(fmt.Sprintf("failed to get playlist details: %v", err))
	}

	// Track quota usage
	s.client.quota.TrackQuotaUsage(QuotaCostPlaylistDetails)

	// Check if playlist was found
	if len(response.Items) == 0 {
		return NewYouTubeClientError(fmt.Sprintf("playlist not found: %s", s.playlistID))
	}

	playlist := response.Items[0]

	// Map the response to our Playlist struct
	publishedAt, err := time.Parse(time.RFC3339, playlist.Snippet.PublishedAt)
	if err != nil {
		publishedAt = time.Now()
	}

	s.playlist = &Playlist{
		ID:            playlist.Id,
		Title:         playlist.Snippet.Title,
		Description:   playlist.Snippet.Description,
		PrivacyStatus: PrivacyStatus(playlist.Status.PrivacyStatus),
		URL:           fmt.Sprintf("https://www.youtube.com/playlist?list=%s", playlist.Id),
		ItemCount:     int64(playlist.ContentDetails.ItemCount),
		PublishedAt:   publishedAt,
		ChannelID:     playlist.Snippet.ChannelId,
		ChannelTitle:  playlist.Snippet.ChannelTitle,
	}

	return nil
}

// GetPlaylist returns the retrieved playlist
func (s *PlaylistDetailsService) GetPlaylist() *Playlist {
	return s.playlist
}

// validateInputs validates the service inputs
func (s *PlaylistDetailsService) validateInputs() error {
	if s.client == nil {
		return NewYouTubeClientError("client cannot be nil")
	}

	if s.playlistID == "" {
		return NewYouTubeClientError("playlist ID cannot be empty")
	}

	return nil
}

// PlaylistItemsService handles listing items in a YouTube playlist
type PlaylistItemsService struct {
	ctx        context.Context
	client     *Client
	playlistID string
	maxResults int64
	items      []*PlaylistItem
}

// NewPlaylistItemsService creates a new PlaylistItemsService
func NewPlaylistItemsService(
	ctx context.Context,
	client *Client,
	playlistID string,
	maxResults int64,
) *PlaylistItemsService {
	if maxResults <= 0 {
		maxResults = 50 // Default to 50 results
	}

	return &PlaylistItemsService{
		ctx:        ctx,
		client:     client,
		playlistID: playlistID,
		maxResults: maxResults,
		items:      []*PlaylistItem{},
	}
}

// Execute lists items in a playlist
func (s *PlaylistItemsService) Execute() error {
	// Validate inputs
	if err := s.validateInputs(); err != nil {
		return err
	}

	// Check quota
	if !s.client.quota.CheckQuota(QuotaCostPlaylistItems) {
		return NewQuotaExceededError("")
	}

	// Get YouTube service
	youtubeService := s.client.GetService()

	// Execute the request with retry
	var response *youtube.PlaylistItemListResponse
	err := s.client.RetryOperation(func() error {
		resp, err := youtubeService.PlaylistItems.
			List([]string{"snippet", "contentDetails", "status"}).
			PlaylistId(s.playlistID).
			MaxResults(s.maxResults).
			Do()
		if err != nil {
			if IsQuotaExceededError(err) {
				return NewQuotaExceededError(err.Error())
			}
			return err
		}
		response = resp
		return nil
	})

	if err != nil {
		log.Error().Err(err).Str("playlistId", s.playlistID).Msg("Failed to list playlist items")
		return NewYouTubeClientError(fmt.Sprintf("failed to list playlist items: %v", err))
	}

	// Track quota usage
	s.client.quota.TrackQuotaUsage(QuotaCostPlaylistItems)

	// Map the response items to our PlaylistItem structs
	s.items = make([]*PlaylistItem, 0, len(response.Items))
	for _, item := range response.Items {
		publishedAt, err := time.Parse(time.RFC3339, item.Snippet.PublishedAt)
		if err != nil {
			publishedAt = time.Now()
		}

		s.items = append(s.items, &PlaylistItem{
			ID:            item.Id,
			Title:         item.Snippet.Title,
			VideoID:       item.ContentDetails.VideoId,
			Position:      int64(item.Snippet.Position),
			PublishedAt:   publishedAt,
			ChannelID:     item.Snippet.ChannelId,
			ChannelTitle:  item.Snippet.ChannelTitle,
			PrivacyStatus: PrivacyStatus(item.Status.PrivacyStatus),
			VideoURL:      fmt.Sprintf("https://www.youtube.com/watch?v=%s", item.ContentDetails.VideoId),
		})
	}

	log.Info().
		Str("playlistId", s.playlistID).
		Int("itemCount", len(s.items)).
		Msg("Listed playlist items successfully")

	return nil
}

// GetItems returns the retrieved playlist items
func (s *PlaylistItemsService) GetItems() []*PlaylistItem {
	return s.items
}

// validateInputs validates the service inputs
func (s *PlaylistItemsService) validateInputs() error {
	if s.client == nil {
		return NewYouTubeClientError("client cannot be nil")
	}

	if s.playlistID == "" {
		return NewYouTubeClientError("playlist ID cannot be empty")
	}

	return nil
}
