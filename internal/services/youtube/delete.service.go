package youtube

import (
	"context"
	"fmt"

	"github.com/rs/zerolog/log"
)

// VideoDeleteService handles deleting a YouTube video
type VideoDeleteService struct {
	ctx     context.Context
	client  *Client
	videoID string
}

// NewVideoDeleteService creates a new VideoDeleteService
func NewVideoDeleteService(
	ctx context.Context,
	client *Client,
	videoID string,
) *VideoDeleteService {
	return &VideoDeleteService{
		ctx:     ctx,
		client:  client,
		videoID: videoID,
	}
}

// NewVideoDeleteServiceFromEnv creates a new VideoDeleteService using environment variables for client configuration
func NewVideoDeleteServiceFromEnv(
	ctx context.Context,
	videoID string,
) (*VideoDeleteService, error) {
	client, err := NewClientFromEnv(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to create YouTube client: %w", err)
	}
	return NewVideoDeleteService(ctx, client, videoID), nil
}

// Execute deletes a video from YouTube
func (s *VideoDeleteService) Execute() error {
	// Validate inputs
	if err := s.validateInputs(); err != nil {
		return err
	}

	// Check quota
	if !s.client.quota.CheckQuota(QuotaCostVideoUpdate) {
		return NewQuotaExceededError("")
	}

	// Get YouTube service
	youtubeService := s.client.GetService()

	// Execute the request with retry
	err := s.client.RetryOperation(func() error {
		err := youtubeService.Videos.Delete(s.videoID).Do()
		if err != nil {
			if IsQuotaExceededError(err) {
				return NewQuotaExceededError(err.Error())
			}
			return err
		}
		return nil
	})

	if err != nil {
		log.Error().Err(err).Str("videoId", s.videoID).Msg("Failed to delete video")
		return NewYouTubeClientError(fmt.Sprintf("failed to delete video: %v", err))
	}

	// Track quota usage
	s.client.quota.TrackQuotaUsage(QuotaCostVideoUpdate)

	log.Info().Str("videoId", s.videoID).Msg("Video deleted successfully")

	return nil
}

// validateInputs validates the service inputs
func (s *VideoDeleteService) validateInputs() error {
	if s.client == nil {
		return NewYouTubeClientError("client cannot be nil")
	}

	if s.videoID == "" {
		return NewYouTubeClientError("video ID cannot be empty")
	}

	return nil
}
