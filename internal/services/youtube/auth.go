package youtube

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"time"

	"github.com/rs/zerolog/log"
	"golang.org/x/oauth2"
	"golang.org/x/oauth2/google"
)

// AuthManager handles YouTube OAuth2 authentication
type AuthManager struct {
	config     *oauth2.Config
	tokenFile  string
	token      *oauth2.Token
	httpClient *http.Client
}

// NewAuthManager creates a new AuthManager
func NewAuthManager(ctx context.Context, credentialsFile, tokenFile string) (*AuthManager, error) {
	// Check if credentials file exists
	if _, err := os.Stat(credentialsFile); os.IsNotExist(err) {
		return nil, NewAuthenticationError(fmt.Sprintf("credentials file does not exist: %s", credentialsFile))
	}

	// Read OAuth2 credentials
	credBytes, err := os.ReadFile(credentialsFile)
	if err != nil {
		return nil, NewAuthenticationError(fmt.Sprintf("failed to read credentials file: %v", err))
	}

	// Parse OAuth2 config
	var config *oauth2.Config
	var credMap map[string]interface{}
	if err := json.Unmarshal(credBytes, &credMap); err != nil {
		return nil, NewAuthenticationError(fmt.Sprintf("failed to parse credentials file: %v", err))
	}

	// Handle case where redirect_uris is missing
	if webConfig, ok := credMap["web"].(map[string]interface{}); ok {
		// Set redirect_uris regardless of whether it exists or not
		webConfig["redirect_uris"] = []string{"http://localhost:8080/"}
		// Update credMap
		credMap["web"] = webConfig
		// Convert back to bytes
		updatedCredBytes, err := json.Marshal(credMap)
		if err != nil {
			return nil, NewAuthenticationError(fmt.Sprintf("failed to serialize updated credentials: %v", err))
		}
		credBytes = updatedCredBytes
	}

	// Create config from updated credBytes
	config, err = google.ConfigFromJSON(credBytes, Scopes...)
	if err != nil {
		return nil, NewAuthenticationError(fmt.Sprintf("failed to parse client secret file: %v", err))
	}

	// Ensure redirect URI is set correctly
	config.RedirectURL = "http://localhost:8080/"

	auth := &AuthManager{
		config:    config,
		tokenFile: tokenFile,
	}

	// Load token
	if err := auth.loadToken(); err != nil {
		return nil, err
	}

	// Create HTTP client with token
	auth.httpClient = auth.config.Client(ctx, auth.token)

	return auth, nil
}

// GetHTTPClient returns an HTTP client with valid OAuth2 token
func (a *AuthManager) GetHTTPClient(ctx context.Context) (*http.Client, error) {
	// Check if token is valid and refresh if needed
	if !a.token.Valid() {
		if err := a.refreshToken(ctx); err != nil {
			return nil, err
		}
		// Update HTTP client with new token
		a.httpClient = a.config.Client(ctx, a.token)
	}

	return a.httpClient, nil
}

// loadToken loads the OAuth2 token from the token file
func (a *AuthManager) loadToken() error {
	// Check if token file exists
	if _, err := os.Stat(a.tokenFile); os.IsNotExist(err) {
		return NewAuthenticationError(fmt.Sprintf("token file does not exist: %s. Please generate a token file first", a.tokenFile))
	}

	// Read token data
	tokenBytes, err := os.ReadFile(a.tokenFile)
	if err != nil {
		return NewAuthenticationError(fmt.Sprintf("failed to read token file: %v", err))
	}

	// Parse token data
	var tokenData TokenData
	if err := json.Unmarshal(tokenBytes, &tokenData); err != nil {
		return NewAuthenticationError(fmt.Sprintf("failed to parse token file: %v", err))
	}

	// Log token data for debugging
	log.Debug().
		Str("tokenFile", a.tokenFile).
		Str("refreshToken", maskString(tokenData.RefreshToken)).
		Msg("Loaded token from file")

	// Convert to OAuth2 token
	a.token = &oauth2.Token{
		AccessToken:  tokenData.Token,
		RefreshToken: tokenData.RefreshToken,
		TokenType:    "Bearer",
	}

	// Set expiry time if available
	if tokenData.ExpiryTime > 0 {
		a.token.Expiry = time.Unix(tokenData.ExpiryTime, 0)
	}

	// Validate token
	if !a.isTokenValid() {
		return NewAuthenticationError("token is invalid, please generate a new token")
	}

	return nil
}

// maskString masks a string for logging
func maskString(s string) string {
	if len(s) <= 8 {
		return "****"
	}
	return s[:4] + "..." + s[len(s)-4:]
}

// refreshToken refreshes the OAuth2 token
func (a *AuthManager) refreshToken(ctx context.Context) error {
	log.Info().Msgf("Token expired, refreshing...")

	// Create token source
	tokenSource := a.config.TokenSource(ctx, a.token)

	// Get new token
	newToken, err := tokenSource.Token()
	if err != nil {
		return NewAuthenticationError(fmt.Sprintf("failed to refresh token: %v", err))
	}

	// Update token
	a.token = newToken

	// Save new token
	if err := a.saveToken(); err != nil {
		return err
	}

	log.Info().Msgf("Token refreshed and saved successfully")
	return nil
}

// saveToken saves the OAuth2 token to the token file
func (a *AuthManager) saveToken() error {
	// Convert OAuth2 token to token data
	tokenData := TokenData{
		Token:        a.token.AccessToken,
		RefreshToken: a.token.RefreshToken,
		TokenURI:     a.token.TokenType,
		ClientID:     a.config.ClientID,
		ClientSecret: a.config.ClientSecret,
		Scopes:       Scopes,
	}

	// Set expiry time if available
	if !a.token.Expiry.IsZero() {
		tokenData.ExpiryTime = a.token.Expiry.Unix()
	}

	// Serialize token data
	tokenBytes, err := json.Marshal(tokenData)
	if err != nil {
		return NewAuthenticationError(fmt.Sprintf("failed to serialize token: %v", err))
	}

	// Write token data to file
	if err := os.WriteFile(a.tokenFile, tokenBytes, 0600); err != nil {
		return NewAuthenticationError(fmt.Sprintf("failed to save token file: %v", err))
	}

	return nil
}

// isTokenValid checks if the token is valid
func (a *AuthManager) isTokenValid() bool {
	return a.token != nil && a.token.RefreshToken != ""
}
