package youtube

import (
	"context"
	"time"
)

// YouTube API scopes
var (
	// Scopes required for YouTube API operations
	Scopes = []string{
		"https://www.googleapis.com/auth/youtube.upload",
		"https://www.googleapis.com/auth/youtube",
	}
)

// API quota costs for various operations
const (
	// QuotaCostUpload is the approximate cost for uploading a video
	QuotaCostUpload = 1600
	// QuotaCostPlaylistCreate is the approximate cost for creating a playlist
	QuotaCostPlaylistCreate = 50
	// QuotaCostPlaylistUpdate is the approximate cost for updating a playlist
	QuotaCostPlaylistUpdate = 50
	// QuotaCostAddToPlaylist is the approximate cost for adding a video to a playlist
	QuotaCostAddToPlaylist = 50
	// QuotaCostVideoUpdate is the approximate cost for updating a video
	QuotaCostVideoUpdate = 50
	// QuotaCostVideoDetails is the approximate cost for getting video details
	QuotaCostVideoDetails = 1
	// QuotaCostPlaylistDetails is the approximate cost for getting playlist details
	QuotaCostPlaylistDetails = 1
	// QuotaCostPlaylistItems is the approximate cost for getting playlist items
	QuotaCostPlaylistItems = 1
	// DefaultQuotaLimit is the default daily quota limit
	DefaultQuotaLimit = 10000
)

// PrivacyStatus represents YouTube privacy status types
type PrivacyStatus string

// Privacy status constants
const (
	PrivacyStatusPrivate  PrivacyStatus = "private"
	PrivacyStatusPublic   PrivacyStatus = "public"
	PrivacyStatusUnlisted PrivacyStatus = "unlisted"
)

// ProgressCallback is a function type for upload progress callbacks
type ProgressCallback func(progress float64)

// VideoMetadata contains metadata for a YouTube video
type VideoMetadata struct {
	Title             string
	Description       string
	Tags              []string
	CategoryID        string
	PrivacyStatus     PrivacyStatus
	NotifySubscribers bool
}

// Video represents a YouTube video
type Video struct {
	ID            string
	Title         string
	Description   string
	Tags          []string
	CategoryID    string
	PrivacyStatus PrivacyStatus
	URL           string
	Duration      string
	ViewCount     int64
	LikeCount     int64
	CommentCount  int64
	PublishedAt   time.Time
}

// Playlist represents a YouTube playlist
type Playlist struct {
	ID            string
	Title         string
	Description   string
	PrivacyStatus PrivacyStatus
	URL           string
	ItemCount     int64
	PublishedAt   time.Time
	ChannelID     string
	ChannelTitle  string
}

// PlaylistItem represents an item in a YouTube playlist
type PlaylistItem struct {
	ID            string
	Title         string
	VideoID       string
	Position      int64
	PublishedAt   time.Time
	ChannelID     string
	ChannelTitle  string
	PrivacyStatus PrivacyStatus
	VideoURL      string
}

// ClientConfig contains configuration parameters for the YouTube client
type ClientConfig struct {
	CredentialsFile string
	TokenFile       string
	QuotaLimit      int
}

// TokenData represents the structure of the token file
type TokenData struct {
	Token        string   `json:"token"`
	RefreshToken string   `json:"refresh_token"`
	TokenURI     string   `json:"token_uri"`
	ClientID     string   `json:"client_id"`
	ClientSecret string   `json:"client_secret"`
	Scopes       []string `json:"scopes"`
	ExpiryTime   int64    `json:"expiry_time"`
}

// QuotaManager interface defines methods for tracking YouTube API quota usage
type QuotaManager interface {
	TrackQuotaUsage(cost int)
	CheckQuota(cost int) bool
	GetQuotaUsage() int
	GetQuotaLimit() int
	ResetQuota()
}

// YouTubeService interface defines methods that any YouTube service should implement
type YouTubeService interface {
	Execute(ctx context.Context) error
}
