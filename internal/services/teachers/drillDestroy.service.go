package teacherServices

import (
	"vibico-education-api/internal/models"
	"vibico-education-api/internal/repository/repositories"
	globalServices "vibico-education-api/internal/services/globals"

	"github.com/rs/zerolog/log"
	"golang.org/x/net/context"
	"gorm.io/gorm"
)

type DrillDestroyService struct {
	ctx      context.Context
	db       *gorm.DB
	drill    *models.Drill
	courseId *uint32
}

func NewDrillDestroyService(
	ctx context.Context,
	db *gorm.DB,
	drill *models.Drill,
	courseId *uint32,
) *DrillDestroyService {
	return &DrillDestroyService{
		ctx:      ctx,
		db:       db,
		drill:    drill,
		courseId: courseId,
	}
}

func (s *DrillDestroyService) Execute() error {
	log.Debug().Ctx(s.ctx).Msg("DrillDestroyService.Execute")
	var videoAfterDestroyServices []func() error

	err := repositories.TransactionWithLogging(s.db.WithContext(s.ctx), func(tx *gorm.DB) error {
		var videos []*models.Video

		if err := tx.Model(&models.Video{}).Preload("VideoUpload").Preload("VideoPlatforms").
			Where("videos.parent_id = ? AND parent_type = 'Drill'", s.drill.ID).Find(&videos).Error; err != nil {
			return err
		}

		if len(videos) > 0 {
			videoIds := make([]int32, len(videos))

			for i, video := range videos {
				// Capture video in closure to avoid loop variable issues
				capturedVideo := video
				videoAfterDestroyServices = append(videoAfterDestroyServices, func() error {
					service := globalServices.NewVideoAfterDestroyService(s.ctx, capturedVideo)
					return service.Execute()
				})

				videoIds[i] = int32(video.ID)
			}

			if err := tx.Delete(&videos, videoIds).Error; err != nil {
				return err
			}
		}

		// TODO: handle delete diagrams ImageUrl GCS
		if err := tx.Select("Diagrams", "DrillSkills", "CourseSectionItemDrill").
			Delete(s.drill).Error; err != nil {
			return err
		}

		if s.courseId != nil {
			return tx.Exec("UPDATE courses SET status = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?", s.courseId).Error
		}
		return nil
	})

	// Transaction committed successfully, run the video after destroy services
	if err == nil {
		for _, serviceFunc := range videoAfterDestroyServices {
			serviceFunc()
		}
	}

	return nil
}
