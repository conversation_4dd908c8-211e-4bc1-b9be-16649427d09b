package courseSectionServices

import (
	"context"
	"vibico-education-api/internal/forms/teachers/courseSectionForms"
	teacherInputs "vibico-education-api/internal/gqls/inputs/teachers"
	"vibico-education-api/internal/models"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
)

type ICourseSectionRepositoryForCourseSectionUpdate interface {
	courseSectionForms.ICourseSectionRepositoryForCourseSectionUpdateForm
}

type CourseSectionUpdateService struct {
	ctx           context.Context
	input         *teacherInputs.CourseSectionCreateAndUpdateInput
	repo          ICourseSectionRepositoryForCourseSectionUpdate
	form          *courseSectionForms.CourseSectionUpdateForm
	CourseSection *models.CourseSection
}

func NewCourseSectionUpdateService(
	ctx context.Context,
	input *teacherInputs.CourseSectionCreateAndUpdateInput,
	repo ICourseSectionRepositoryForCourseSectionUpdate,
	CourseSection *models.CourseSection,
) *CourseSectionUpdateService {

	return &CourseSectionUpdateService{
		ctx:           ctx,
		input:         input,
		repo:          repo,
		form:          courseSectionForms.NewCourseSectionUpdateForm(ctx, input, repo, CourseSection),
		CourseSection: CourseSection,
	}
}

func (service *CourseSectionUpdateService) Execute() error {
	if service.input == nil {
		return exceptions.NewUnprocessableContentError(translator.Translate(nil, "general_pleaseInputCorrect"), exceptions.ResourceModificationError{
			"base": []interface{}{translator.Translate(nil, "general_pleaseInputCorrect")},
		})
	}

	if err := service.form.Save(); err != nil {
		return err
	} else {
		service.CourseSection = service.form.CourseSection

	}

	return nil
}
