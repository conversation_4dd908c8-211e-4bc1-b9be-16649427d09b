package courseSectionServices

import (
	"context"
	"vibico-education-api/internal/forms/teachers/courseSectionForms"
	teacherInputs "vibico-education-api/internal/gqls/inputs/teachers"
	"vibico-education-api/internal/models"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
)

type ICourseSectionRepositoryForCourseSectionCreate interface {
	courseSectionForms.ICourseSectionRepositoryForCourseSectionCreateForm
}

type CourseSectionCreateService struct {
	ctx           context.Context
	input         *teacherInputs.CourseSectionCreateAndUpdateInput
	repo          ICourseSectionRepositoryForCourseSectionCreate
	form          *courseSectionForms.CourseSectionCreateForm
	CourseSection *models.CourseSection
	CourseId      uint32
}

func NewCourseSectionCreateService(
	ctx context.Context,
	input *teacherInputs.CourseSectionCreateAndUpdateInput,
	repo ICourseSectionRepositoryForCourseSectionCreate,
	CourseId uint32,
) *CourseSectionCreateService {

	return &CourseSectionCreateService{
		ctx:           ctx,
		input:         input,
		repo:          repo,
		form:          courseSectionForms.NewCourseSectionCreateForm(ctx, input, repo, CourseId),
		CourseSection: &models.CourseSection{},
	}
}

func (service *CourseSectionCreateService) Execute() error {
	if service.input == nil {
		return exceptions.NewUnprocessableContentError(translator.Translate(nil, "general_pleaseInputCorrect"), exceptions.ResourceModificationError{
			"base": []interface{}{translator.Translate(nil, "general_pleaseInputCorrect")},
		})
	}

	if err := service.form.Save(); err != nil {
		return err
	} else {
		service.CourseSection = service.form.CourseSection

	}

	return nil
}
