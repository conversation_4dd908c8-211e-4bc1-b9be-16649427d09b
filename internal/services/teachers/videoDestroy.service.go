package teacherServices

import (
	"context"
	"vibico-education-api/internal/models"
	globalServices "vibico-education-api/internal/services/globals"
)

type IVideoRepo interface {
	DeleteWithCourse(ctx context.Context, video *models.Video, courseId uint32) error
}

type VideoDestroyService struct {
	ctx      context.Context
	video    *models.Video
	repo     IVideoRepo
	courseId uint32
}

func NewVideoDestroyService(
	ctx context.Context, video *models.Video, repo IVideoRepo, courseId uint32,
) *VideoDestroyService {
	return &VideoDestroyService{
		ctx:      ctx,
		video:    video,
		repo:     repo,
		courseId: courseId,
	}
}

func (s *VideoDestroyService) Execute() error {
	if err := s.repo.DeleteWithCourse(s.ctx, s.video, s.courseId); err != nil {
		return err
	}

	globalServices.NewVideoAfterDestroyService(s.ctx, s.video).Execute()

	return nil
}
