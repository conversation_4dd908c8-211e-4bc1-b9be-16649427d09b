package courseSectionItemServices

import (
	"context"
	teacherForms "vibico-education-api/internal/forms/teachers"
	teacherInputs "vibico-education-api/internal/gqls/inputs/teachers"
	"vibico-education-api/internal/models"
	"vibico-education-api/internal/repository/repositories"

	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

type ICourseSectionRepository interface {
	FindBySectionIdCourseIdAndTeacher(ctx context.Context, id uint32, courseId uint32, teacherId uint32, preloads ...repositories.CustomPreload) (*models.CourseSection, error)
}

type CreateService struct {
	ctx                   context.Context
	courseSectionRepo     ICourseSectionRepository
	courseSectionItemRepo teacherForms.ICourseSectionItemRepository
	drillRepo             teacherForms.IDrillRepository

	CourseSectionItem *models.CourseSectionItem
	currentTeacher    *models.Teacher
	input             *teacherInputs.CourseSectionItemCreateInput
	courseId          uint32
}

func NewCreateService(
	ctx context.Context,
	courseSectionRepo ICourseSectionRepository,
	courseSectionItemRepo teacherForms.ICourseSectionItemRepository,
	drillRepo teacherForms.IDrillRepository,

	currentTeacher *models.Teacher,
	input *teacherInputs.CourseSectionItemCreateInput,
) *CreateService {
	log.Debug().Ctx(ctx).Msg("CourseSectionItemService.NewCreateService")

	return &CreateService{
		ctx:                   ctx,
		courseSectionRepo:     courseSectionRepo,
		courseSectionItemRepo: courseSectionItemRepo,
		drillRepo:             drillRepo,
		currentTeacher:        currentTeacher,
		input:                 input,
		CourseSectionItem:     &models.CourseSectionItem{},
	}
}

func (s CreateService) Execute() error {
	if err := s.validate(); err != nil {
		return err
	}

	form := teacherForms.NewCourseSectionItemMutateForm(
		s.ctx,
		s.input.Input,
		s.courseSectionItemRepo,
		s.drillRepo,
		s.CourseSectionItem,
		s.currentTeacher,
		s.courseId,
	)

	if err := form.Create(); err != nil {
		return err
	}

	return nil
}

func (s *CreateService) validate() error {
	var err error
	s.courseId, err = utils.GqlIdToUint32(s.input.CourseId)
	if err != nil {
		return err
	}

	courseSectionId, err := utils.GqlIdToUint32(s.input.CourseSectionId)
	if err != nil {
		return err
	}

	courseSection, err := s.courseSectionRepo.FindBySectionIdCourseIdAndTeacher(
		s.ctx,
		courseSectionId,
		s.courseId,
		s.currentTeacher.ID,
	)

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return exceptions.NewRecordNotFoundError()
		}
		return err
	}

	s.CourseSectionItem.CourseSectionId = courseSectionId
	s.CourseSectionItem.CourseSection = courseSection
	s.CourseSectionItem.CourseId = courseSection.CourseID

	return nil
}
