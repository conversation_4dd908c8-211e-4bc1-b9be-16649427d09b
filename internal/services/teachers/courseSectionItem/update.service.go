package courseSectionItemServices

import (
	"context"
	teacherForms "vibico-education-api/internal/forms/teachers"
	teacherInputs "vibico-education-api/internal/gqls/inputs/teachers"
	"vibico-education-api/internal/models"

	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"gorm.io/gorm"
)

type UpdateService struct {
	ctx                   context.Context
	courseSectionRepo     ICourseSectionRepository
	courseSectionItemRepo teacherForms.ICourseSectionItemRepository
	drillRepo             teacherForms.IDrillRepository

	CourseSectionItem *models.CourseSectionItem
	currentTeacher    *models.Teacher
	input             *teacherInputs.CourseSectionItemUpdateInput
	courseId          uint32
}

func NewUpdateService(
	ctx context.Context,
	courseSectionRepo ICourseSectionRepository,
	courseSectionItemRepo teacherForms.ICourseSectionItemRepository,
	drillRepo teacherForms.IDrillRepository,

	teacher *models.Teacher,
	input *teacherInputs.CourseSectionItemUpdateInput,
) *UpdateService {
	return &UpdateService{
		ctx:                   ctx,
		courseSectionRepo:     courseSectionRepo,
		courseSectionItemRepo: courseSectionItemRepo,
		drillRepo:             drillRepo,
		currentTeacher:        teacher,
		input:                 input,
	}
}

func (s *UpdateService) Execute() error {
	if err := s.validate(); err != nil {
		return err
	}

	form := teacherForms.NewCourseSectionItemMutateForm(
		s.ctx,
		s.input.Input,
		s.courseSectionItemRepo,
		s.drillRepo,
		s.CourseSectionItem,
		s.currentTeacher,
		s.courseId,
	)

	if err := form.Update(); err != nil {
		return err
	}
	return nil
}

func (s *UpdateService) validate() error {
	var err error
	s.courseId, err = utils.GqlIdToUint32(s.input.CourseId)
	if err != nil {
		return err
	}

	courseSectionId, err := utils.GqlIdToUint32(s.input.CourseSectionId)
	if err != nil {
		return err
	}

	id, err := utils.GqlIdToUint32(s.input.Id)
	if err != nil {
		return err
	}

	s.CourseSectionItem, err = s.courseSectionItemRepo.FindByIdAndTeacherCourse(
		s.ctx,
		id,
		courseSectionId,
		s.courseId,
		s.currentTeacher.ID,
	)

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return exceptions.NewRecordNotFoundError()
		}
		return err
	}

	return nil
}
