package teacherServices

import (
	"context"
	"errors"
	"fmt"
	"time"
	"vibico-education-api/constants"
	"vibico-education-api/internal/models"
	"vibico-education-api/internal/repository/repositories"
	"vibico-education-api/pkg/helpers"
	"vibico-education-api/pkg/sms"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	pb "github.com/BehemothLtd/vibico-auth/proto/auth"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

type ICourseRepository interface {
	FindByIDAndTeacher(ctx context.Context, courseId, teacherId uint32, preloads ...repositories.CustomPreload) (*models.Course, error)
}

type IUserRepository interface {
	InviteUser(ctx context.Context, courseID, coursePackageID uint32, phoneNumber string, pbUserParams *pb.UserParams, teacherID uint32) error
}

type InviteUserService struct {
	ctx             context.Context
	courseID        uint32
	coursePackageID uint32
	teacher         *models.Teacher
	phoneNumber     string
	courseRepo      ICourseRepository
	userRepo        IUserRepository
}

func NewInviteUserService(
	ctx context.Context,
	courseID uint32,
	coursePackageID uint32,
	teacher *models.Teacher,
	phoneNumber string,
	courseRepo ICourseRepository,
	userRepo IUserRepository,
) *InviteUserService {
	return &InviteUserService{
		ctx:             ctx,
		courseID:        courseID,
		coursePackageID: coursePackageID,
		teacher:         teacher,
		phoneNumber:     phoneNumber,
		courseRepo:      courseRepo,
		userRepo:        userRepo,
	}
}

func (s *InviteUserService) Execute() error {
	log.Debug().Ctx(s.ctx).Msg("InviteUserService.Execute")

	course, err := s.courseRepo.FindByIDAndTeacher(s.ctx, s.courseID, s.teacher.ID)
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		return errors.New(translator.Translate(nil, "errExceptionMsg_unauthorized"))
	}

	pbUserParams := &pb.UserParams{
		Username:        &s.phoneNumber,
		PhoneNumber:     &s.phoneNumber,
		RawPassword:     s.phoneNumber,
		IsPhoneVerified: true,
		Status:          "ACTIVE",
		Role:            utils.PointerString(constants.RoleUser),
	}

	err = s.userRepo.InviteUser(s.ctx, s.courseID, s.coursePackageID, s.phoneNumber, pbUserParams, s.teacher.ID)
	if err != nil {
		return err
	}
	go s.sendSMS(course)

	return nil
}

func (s *InviteUserService) sendSMS(course *models.Course) {
	timeDuration := 24 * time.Hour
	courseUrl := fmt.Sprintf("%s/courses/%s?invited=true", utils.GetEnv("WEB_URL", "http://localhost:8080"), course.Slug)
	verifyCode := helpers.RandomDigits(6)
	message := translator.Translate(nil, "VerifyTeacherInviteUser_Msg",
		course.Title,
		s.teacher.Name,
		courseUrl,
		verifyCode,
	)

	smsService := sms.NewSMSService(s.ctx, utils.GetEnv("TWILIO_SERVICE_SID", ""))
	smsService.SendVerificationCode(s.phoneNumber, &timeDuration, &message)
}
