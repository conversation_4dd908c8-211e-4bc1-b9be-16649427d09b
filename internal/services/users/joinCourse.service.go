package userServices

import (
	"context"
	"errors"
	"strings"
	"time"
	"vibico-education-api/internal/enums"
	userForms "vibico-education-api/internal/forms/users"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	"vibico-education-api/internal/models"
	"vibico-education-api/internal/repository/repositories"
	"vibico-education-api/pkg/sms"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/rs/zerolog/log"
)

type ICourseRepository interface {
	FindByID(ctx context.Context, id uint32, preloads ...repositories.CustomPreload) (*models.Course, error)
}

type ICourseUserRepository interface {
	FindOrInit(ctx context.Context, courseId, userId uint32) *models.CourseUser
	Create(ctx context.Context, courseUser *models.CourseUser) error
	Update(ctx context.Context, courseUser *models.CourseUser, isIncrease bool, columns ...string) error
}

type ICoursePackageRepository interface {
	FindByIdAndCourseId(ctx context.Context, id, courseId uint32) (*models.CoursePackage, error)
	FindBasicByCourseId(ctx context.Context, courseId uint32) (*models.CoursePackage, error)
}

type JoinCourseService struct {
	courseRepo        ICourseRepository
	courseUserRepo    ICourseUserRepository
	coursePackageRepo ICoursePackageRepository

	currentUser     *models.User
	ctx             context.Context
	courseId        uint32
	verifyCode      *string
	coursePackageId uint32
}

func NewJoinCourseService(
	ctx context.Context,
	courseRepo ICourseRepository,
	courseUserRepo ICourseUserRepository,
	coursePackageRepo ICoursePackageRepository,

	currentUser *models.User,
	courseId uint32,
	verifyCode *string,
	coursePackageId uint32,
) *JoinCourseService {
	return &JoinCourseService{
		courseRepo:        courseRepo,
		courseUserRepo:    courseUserRepo,
		coursePackageRepo: coursePackageRepo,

		currentUser:     currentUser,
		ctx:             ctx,
		courseId:        courseId,
		verifyCode:      verifyCode,
		coursePackageId: coursePackageId,
	}
}

func (s *JoinCourseService) Execute() (*globalPayloads.MessageInfoPayload, error) {
	course, err := s.courseRepo.FindByID(
		s.ctx,
		s.courseId,
		repositories.CustomPreload{Key: "Teacher"},
	)
	if err != nil {
		return nil, err
	}

	if !course.Teacher.Active {
		return nil, errors.New("teacher has ceased to be active")
	}

	if course.Teacher.AuthId == s.currentUser.AuthId {
		return nil, errors.New(translator.Translate(nil, "errDbMsg_CanNotJoinYourOwnCourse"))
	}

	courseUser := s.courseUserRepo.FindOrInit(s.ctx, s.courseId, s.currentUser.ID)

	if courseUser.ID != 0 {
		if courseUser.Status == enums.CourseUserStatusInvited {
			if s.verifyCode != nil && strings.TrimSpace(*s.verifyCode) != "" {
				form := userForms.NewJoinCourseForm(s.ctx, s.verifyCode)
				if err := form.Validate(); err != nil {
					return nil, err
				}

				result, err := s.enrollCourseUserSuccess(courseUser)
				if err != nil {
					return nil, err
				}
				smsService := sms.NewSMSService(s.ctx, utils.GetEnv("TWILIO_SERVICE_SID", ""))
				status, err := smsService.VerifyCode(*s.currentUser.PhoneNumber, *s.verifyCode)

				if err != nil || status != sms.TwilioStatusApproved {
					return nil, errors.New(translator.Translate(nil, "errValidationMsg_verifyCode_invalid"))
				}

				return result, nil
			} else {
				// TODO: waiting for billing spec to expand logic
				return s.enrollCourseUserSuccess(courseUser)
			}
		} else {
			return nil, errors.New(translator.Translate(nil, "errDbMsg_AlreadyJoinCourse"))
		}
	}

	courseUser.Status = enums.CourseUserStatusEnrolled

	basicPackage, err := s.getCoursePackage()
	if err != nil {
		return nil, err
	}

	if basicPackage.ID != 0 {
		courseUser.CoursePackageID = &basicPackage.ID
	}

	if err := s.courseUserRepo.Create(s.ctx, courseUser); err != nil {
		return nil, err
	}

	return &globalPayloads.MessageInfoPayload{
		Message: translator.Translate(nil, "infoMsg_joinCourseSuccess"),
	}, nil
}

func (s *JoinCourseService) enrollCourseUserSuccess(
	courseUser *models.CourseUser) (*globalPayloads.MessageInfoPayload, error) {
	log.Debug().Ctx(s.ctx).Msg("JoinCourseService.enrollCourseUserSuccess")

	courseUser.Status = enums.CourseUserStatusEnrolled
	timeNow := time.Now()
	courseUser.JoinedAt = &timeNow

	if err := s.courseUserRepo.Update(s.ctx, courseUser, true, "status", "joined_at"); err != nil {
		return nil, err
	}
	return &globalPayloads.MessageInfoPayload{
		Message: translator.Translate(nil, "infoMsg_joinCourseSuccess"),
	}, nil
}

func (s *JoinCourseService) getCoursePackage() (*models.CoursePackage, error) {
	var basicPackage *models.CoursePackage
	var err error

	if s.coursePackageId != 0 {
		basicPackage, err = s.coursePackageRepo.FindByIdAndCourseId(s.ctx, s.coursePackageId, s.courseId)
		if err != nil {
			return nil, err
		}
	} else {
		basicPackage, err = s.coursePackageRepo.FindBasicByCourseId(s.ctx, s.courseId)
		if err != nil {
			return nil, err
		}
	}

	return basicPackage, nil
}
