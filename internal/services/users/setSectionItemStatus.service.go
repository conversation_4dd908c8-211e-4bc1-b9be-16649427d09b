package userServices

import (
	"context"
	"vibico-education-api/internal/enums"
	userInputs "vibico-education-api/internal/gqls/inputs/users"
	"vibico-education-api/internal/models"
	"vibico-education-api/internal/repository"
	"vibico-education-api/internal/repository/repositories"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
)

type SetSectionItemStatusService struct {
	ctx         context.Context
	repos       repository.IRepositories
	currentUser *models.User
	input       userInputs.SetSectionItemStatusInput

	courseID         uint32
	itemID           uint32
	status           enums.UserCourseSectionItemStatus
	courseUserStatus enums.CourseUserStatus
}

func NewSetSectionItemStatusService(
	ctx context.Context,
	repos repository.IRepositories,
	currentUser *models.User,
	input userInputs.SetSectionItemStatusInput,
) *SetSectionItemStatusService {
	return &SetSectionItemStatusService{
		ctx:         ctx,
		repos:       repos,
		currentUser: currentUser,
		input:       input,
	}
}

func (s *SetSectionItemStatusService) Execute() error {
	if err := s.validate(); err != nil {
		return err
	}

	userItemRepo := s.repos.UserCourseSectionItemRepo()
	userItem, err := userItemRepo.FindOrInit(s.ctx, s.currentUser.ID, s.itemID)
	if err != nil {
		return err
	}

	userItem.Status = s.status

	return userItemRepo.SetStatus(s.ctx, userItem, s.courseID, s.courseUserStatus)
}

func (s *SetSectionItemStatusService) validate() (err error) {
	s.itemID, err = utils.ParseGraphqlID[uint32](s.input.ID)
	if err != nil {
		return err
	}

	s.courseID, err = utils.ParseGraphqlID[uint32](s.input.CourseID)
	if err != nil {
		return err
	}

	s.status, err = enums.ParseUserCourseSectionItemStatus(s.input.Status)
	if err != nil {
		return err
	}

	courseUser, err := s.repos.CourseUserRepo().FindByUserEnrolledAndCourse(
		s.ctx,
		s.courseID, s.currentUser.ID,
		repositories.CustomPreload{Key: "Course"},
	)
	if err != nil {
		return err
	}

	s.courseUserStatus = enums.CourseUserStatusInProgress
	courseUserMetadata := courseUser.CourseUserMetadata

	if s.status == enums.UserCourseSectionItemStatusCompleted &&
		courseUserMetadata != nil &&
		courseUserMetadata.CompletedSectionItemCount != nil &&
		*courseUserMetadata.CompletedSectionItemCount == int32(courseUser.Course.SectionItemCount)-1 {
		s.courseUserStatus = enums.CourseUserStatusCompleted
	}

	return
}
