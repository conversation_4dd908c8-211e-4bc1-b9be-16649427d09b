package userServices

import (
	"context"
	"errors"
	"vibico-education-api/internal/models"
	"vibico-education-api/internal/repository"
	translator "vibico-education-api/pkg/translators"
)

type BuyDrillService struct {
	ctx   context.Context
	repos repository.IRepositories

	currentUser *models.User
	drillId     uint32
}

func NewBuyDrillService(
	ctx context.Context,
	repos repository.IRepositories,
	currentUser *models.User,
	drillId uint32,
) *BuyDrillService {
	return &BuyDrillService{
		ctx:         ctx,
		repos:       repos,
		currentUser: currentUser,
		drillId:     drillId,
	}
}

func (s *BuyDrillService) Execute() error {
	drill, err := s.repos.DrillRepo().FindApprovedByID(s.ctx, s.drillId)
	if err != nil {
		return err
	}

	if drill.OwnerType == "Teacher" {
		owner, err := s.repos.TeacherRepo().FindById(s.ctx, drill.OwnerID)
		if err != nil {
			return err
		}

		if owner.AuthId == s.currentUser.AuthId {
			return errors.New(translator.Translate(nil, "errDbMsg_CanNotBuyYourOwnDrill"))
		}
	}

	repo := s.repos.UserDrillRepo()

	userDrill := repo.FindOrInit(s.ctx, drill.ID, s.currentUser.ID)
	if userDrill.ID != 0 {
		return errors.New(translator.Translate(nil, "errDbMsg_AlreadyBoughtDrill"))
	}

	return repo.Create(s.ctx, userDrill)
}
