package routes

import (
	"strings"
	"vibico-education-api/constants"
	vibicoMiddlewares "vibico-education-api/internal/middlewares"
	"vibico-education-api/internal/repository"
	translator "vibico-education-api/pkg/translators"

	"fmt"
	"net/http"
	"net/url"

	"github.com/rs/zerolog/log"

	"github.com/BehemothLtd/behemoth-pkg/golang/cookies"
	"github.com/BehemothLtd/behemoth-pkg/golang/middlewares"
	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

// RouterConfig encapsulates the configuration for the router
type RouterConfig struct {
	engine *gin.Engine
	repos  repository.IRepositories
}

// NewRouter initializes a new router configuration and returns a configured Gin engine
func NewRouter(routerType string, repos repository.IRepositories) *gin.Engine {
	config := &RouterConfig{
		engine: gin.New(),
		repos:  repos,
	}
	if err := config.initialize(routerType); err != nil {
		log.Fatal().Msgf("Failed to initialize router: %v", err)
	}
	return config.engine
}

func InitServer(repos repository.IRepositories) *http.Server {
	appPort := utils.GetEnv("APP_PORT", "3000")
	appPort = fmt.Sprintf(":%s", appPort)
	handler := NewRouter("api", repos)
	log.Debug().Msgf("🚀 Server running at http://localhost" + appPort)

	return &http.Server{
		Addr:    appPort,
		Handler: handler,
	}
}

// initialize sets up routes, and returns the engine
func (c *RouterConfig) initialize(routerType string) error {
	c.setupMiddleware()
	c.setupUploadFiles()

	switch routerType {
	case "api":
		RegisterApiRoutes(c.engine, c.repos)
	default:
		return fmt.Errorf("invalid router type: %s", routerType)
	}

	return nil
}

// setupUploadFiles sets up the upload file directories for dev
func (c *RouterConfig) setupUploadFiles() {
	if utils.IsDevelopmentEnv() || utils.IsLocalEnv() {
		c.engine.Static("/uploads", "./tmp/uploads/")
	}
}

// setupMiddleware attaches global middleware to the Gin engine
func (c *RouterConfig) setupMiddleware() {
	c.engine.Use(CorsConfig())
	c.engine.Use(middlewares.IncludeGinCtxIntoCtx())
	c.engine.Use(middlewares.TraceRequest())
	c.engine.Use(vibicoMiddlewares.RequestLogger())
	c.engine.Use(SetClientLanguage())
}

func SetClientLanguage() gin.HandlerFunc {
	return func(c *gin.Context) {
		lang, _ := cookies.ExtractTokenFromCookie(c, "language")
		translator.ClientLanguage = lang
		c.Next()
	}
}

func getAllowedDomains() []string {
	domains := []string{"billiardspro.xyz", "vibico.co"}
	if customDomains := utils.GetEnv("ALLOWED_DOMAINS", ""); customDomains != "" {
		domains = append(domains, strings.Split(customDomains, ",")...)
	}
	return domains
}

func isAllowedOrigin(origin string, allowedDomains []string) bool {
	if (utils.IsDevelopmentEnv() || utils.IsLocalEnv()) && strings.HasPrefix(origin, "http://localhost") {
		return true
	}

	u, err := url.Parse(origin)
	if err != nil {
		return false
	}

	for _, domain := range allowedDomains {
		if u.Host == domain || strings.HasSuffix(u.Host, "."+domain) {
			return true
		}
	}
	return false
}

func CorsConfig() gin.HandlerFunc {
	allowedDomains := getAllowedDomains()
	log.Debug().Msgf("Allowed domains: " + strings.Join(allowedDomains, ", "))

	config := cors.Config{
		AllowMethods:     []string{"POST", "GET", "OPTIONS", "PUT", "DELETE"},
		AllowHeaders:     []string{"Content-Type", "Authorization", constants.AuthorizationHeader},
		ExposeHeaders:    []string{"Content-Length"},
		AllowCredentials: true,
		AllowOriginFunc: func(origin string) bool {
			return isAllowedOrigin(origin, allowedDomains)
		},
	}

	return cors.New(config)
}
