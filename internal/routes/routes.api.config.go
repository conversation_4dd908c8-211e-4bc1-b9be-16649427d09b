package routes

import (
	"vibico-education-api/constants"
	"vibico-education-api/internal/asyncq"
	"vibico-education-api/internal/controllers"
	gqls "vibico-education-api/internal/gqls/handlers"
	"vibico-education-api/internal/middlewares"
	"vibico-education-api/internal/repository"

	"github.com/gin-gonic/gin"
	"github.com/hibiken/asynqmon"
)

func RegisterApiRoutes(r *gin.Engine, repos repository.IRepositories) {
	registerWebRoutes(r)
	registerGraphqlRoutes(r, repos)
	registerRestRoutes(r, repos)
	registerMonitoringRoutes(r)
}

func registerMonitoringRoutes(r *gin.Engine) {
	h := asynqmon.New(asynqmon.Options{
		RootPath:     "/server/asynq",
		RedisConnOpt: asyncq.GetManager().Opt,
	})

	r.GET(h.RootPath()+"/*any", gin.WrapH(h))
	r.DELETE(h.RootPath()+"/*any", gin.WrapH(h))
	r.POST(h.RootPath()+"/*any", gin.WrapH(h))
	r.PUT(h.RootPath()+"/*any", gin.WrapH(h))
	r.PATCH(h.RootPath()+"/*any", gin.WrapH(h))
}

func registerGraphqlRoutes(r *gin.Engine, repos repository.IRepositories) {
	graphqlGroup := r.Group(constants.GraphQLEndpoint)

	graphqlGroup.POST(
		"/user",
		gqls.UserGqlHandler(repos),
	)
	graphqlGroup.POST(
		"/teacher",
		gqls.TeacherGqlHandler(repos),
	)
	graphqlGroup.POST(
		"/admin",
		gqls.AdminGqlHandler(repos),
	)
	graphqlGroup.POST(
		"/public",
		gqls.PublicGqlHandler(repos),
	)
}

func registerRestRoutes(r *gin.Engine, repos repository.IRepositories) {
	restGroup := r.Group(constants.RestEndpoint)
	registerAdminRoutes(restGroup, repos)
	registerTeacherRoutes(restGroup, repos)
	registerUserRoutes(restGroup, repos)
}

func registerAdminRoutes(r *gin.RouterGroup, repos repository.IRepositories) {
	adminRoutes := r.Group("/admin")
	adminRoutes.Use(middlewares.AdminAuthMiddleware(repos))
	{
		adminRoutes.POST("/uploads", controllers.HandleUpload)
		adminRoutes.POST("/drill_uploads", controllers.HandleDrillUpload)
		adminRoutes.POST("/chunking_uploads", func(c *gin.Context) {
			controllers.HandleChunksUpload(c, repos)
		})
		adminRoutes.POST("/chunking_uploads/finish", func(c *gin.Context) {
			controllers.HandleFinishUpload(c, repos)
		})
		adminRoutes.GET("/stream/:playbackId", func(c *gin.Context) {
			controllers.HandleVideoStream(c, repos)
		})
	}
}

func registerTeacherRoutes(r *gin.RouterGroup, repos repository.IRepositories) {
	teacherRoutes := r.Group("/teacher")
	teacherRoutes.Use(middlewares.TeacherAuthMiddleware(repos))
	{
		teacherRoutes.POST("/uploads", controllers.HandleUpload)
		teacherRoutes.POST("/drill_uploads", controllers.HandleDrillUpload)
		teacherRoutes.POST("/chunking_uploads", func(c *gin.Context) {
			controllers.HandleChunksUpload(c, repos)
		})
		teacherRoutes.POST("/chunking_uploads/finish", func(c *gin.Context) {
			controllers.HandleFinishUpload(c, repos)
		})
		teacherRoutes.GET("/stream/:playbackId", func(c *gin.Context) {
			controllers.HandleVideoStream(c, repos)
		})
	}
}

func registerUserRoutes(r *gin.RouterGroup, repos repository.IRepositories) {
	userRoutes := r.Group("/user")
	userRoutes.Use(middlewares.UserAuthMiddleware(repos))
	{
		userRoutes.POST("/uploads", controllers.HandleUpload)
		userRoutes.POST("/chunking_uploads", func(c *gin.Context) {
			controllers.HandleChunksUpload(c, repos)
		})
		userRoutes.POST("/chunking_uploads/finish", func(c *gin.Context) {
			controllers.HandleFinishUpload(c, repos)
		})
		userRoutes.GET("/stream/:playbackId", func(c *gin.Context) {
			controllers.HandleVideoStream(c, repos)
		})
	}
}
