package asyncq

import (
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
)

type AsynqLogger struct {
	logger zerolog.Logger
}

func NewLogger() *AsynqLogger {
	return &AsynqLogger{
		logger: log.Logger,
	}
}

func (l *AsynqLogger) Debug(args ...any) {
	l.logger.Debug().Msgf("%v", args...)
}

func (l *AsynqLogger) Info(args ...any) {
	l.logger.Info().Msgf("%v", args...)
}

func (l *AsynqLogger) Warn(args ...any) {
	l.logger.Warn().Msgf("%v", args...)
}

func (l *AsynqLogger) Error(args ...any) {
	l.logger.Error().Msgf("%v", args...)
}

func (l *AsynqLogger) Fatal(args ...any) {
	l.logger.Fatal().Msgf("%v", args...)
}
