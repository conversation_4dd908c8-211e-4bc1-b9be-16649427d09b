package generalTasks

import (
	"context"
	"encoding/json"
	"vibico-education-api/internal/repository"

	"github.com/hibiken/asynq"
	"github.com/rs/zerolog/log"
)

func NewExampleTask() (*asynq.Task, error) {
	payload, err := json.Marshal(nil)
	if err != nil {
		return nil, err
	}
	return asynq.NewTask(ExampleTaskType, payload), nil
}

func HandleExampleTask(repos repository.IRepositories) func(context.Context, *asynq.Task) error {
	return func(ctx context.Context, t *asynq.Task) error {
		log.Ctx(ctx).Info().Msg("Example task start")
		log.Ctx(ctx).Info().Msg("Example task end")
		return nil
	}
}
