package videoTranscoderTasks

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"strings"

	"vibico-education-api/internal/asyncq"

	"cloud.google.com/go/storage"
	"github.com/hibiken/asynq"
	"google.golang.org/api/iterator"

	"vibico-education-api/internal/enums"
	"vibico-education-api/internal/repository"
)

// Payload for cleaning up orphan chunks
type CleanUpOrphanChunksTaskPayload struct {
	FileID    string `json:"fileId"`
	ChunkPath string `json:"chunkPath"` // prefix in bucket
	Bucket    string `json:"bucket"`
	VideoID   uint32 `json:"videoId"`
}

// NewCleanUpOrphanChunksTask creates a new asynq task
func NewCleanUpOrphanChunksTask(payload CleanUpOrphanChunksTaskPayload) (*asynq.Task, error) {
	b, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("marshal payload: %w", err)
	}
	return asynq.NewTask(
		CleanupOrphanChunksTaskType,
		b,
		asynq.Queue(asyncq.QueueVideoTranscoder),
	), nil
}

// HandleCleanUpOrphanChunksTask processes the cleanup task
func HandleCleanUpOrphanChunksTask(repos repository.IRepositories) func(ctx context.Context, t *asynq.Task) error {
	return func(ctx context.Context, t *asynq.Task) error {
		var p CleanUpOrphanChunksTaskPayload
		if err := json.Unmarshal(t.Payload(), &p); err != nil {
			return fmt.Errorf("unmarshal payload: %w", err)
		}
		log.Printf("[Cleanup] fileID=%s, videoID=%d", p.FileID, p.VideoID)

		// Init a single storage client
		client, err := storage.NewClient(ctx)
		if err != nil {
			return fmt.Errorf("storage.NewClient: %w", err)
		}
		defer client.Close()

		// Perform cleanup
		count, err := cleanupChunks(ctx, client, p)
		if err != nil {
			return fmt.Errorf("cleanupChunks: %w", err)
		}

		// If chunks were deleted, remove db record if draft
		if count > 0 {
			if err := cleanupDatabaseRecord(ctx, repos, p.VideoID); err != nil {
				log.Printf("cleanup DB record error videoID=%d: %v", p.VideoID, err)
			}
		}

		log.Printf("Deleted %d chunks for fileID=%s", count, p.FileID)
		return nil
	}
}

// cleanupChunks deletes all chunk files for the given payload
func cleanupChunks(ctx context.Context, client *storage.Client, p CleanUpOrphanChunksTaskPayload) (int, error) {
	bucket := client.Bucket(p.Bucket)
	prefix := fmt.Sprintf("%s/%s/", p.ChunkPath, p.FileID)
	it := bucket.Objects(ctx, &storage.Query{Prefix: prefix})
	deleted := 0

	for {
		attr, err := it.Next()
		if err == iterator.Done {
			break
		}
		if err != nil {
			return deleted, fmt.Errorf("list chunks: %w", err)
		}
		// skip directories
		if strings.HasSuffix(attr.Name, "/") {
			continue
		}
		if err := bucket.Object(attr.Name).Delete(ctx); err != nil {
			log.Printf("delete %s: %v", attr.Name, err)
		} else {
			deleted++
			log.Printf("deleted chunk %s", attr.Name)
		}
	}
	return deleted, nil
}

// cleanupDatabaseRecord removes the draft video record
func cleanupDatabaseRecord(ctx context.Context, repos repository.IRepositories, videoID uint32) error {
	video, err := repos.VideoRepo().FindByID(ctx, videoID)
	if err != nil {
		return fmt.Errorf("find video: %w", err)
	}
	if video.Status == enums.VideoStatusDraft {
		if err := repos.VideoRepo().DeleteDraftByID(ctx, videoID); err != nil {
			return fmt.Errorf("delete draft: %w", err)
		}
		log.Printf("deleted draft video %d", videoID)
	} else {
		log.Printf("video %d not draft, skip delete", videoID)
	}
	return nil
}
