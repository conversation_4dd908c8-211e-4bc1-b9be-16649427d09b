package videoTranscoderTasks

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"time"
	"vibico-education-api/internal/asyncq"
	"vibico-education-api/internal/enums"
	"vibico-education-api/internal/repository"
	"vibico-education-api/internal/services/mux"
	"vibico-education-api/internal/services/youtube"
	"vibico-education-api/pkg/helpers"

	"cloud.google.com/go/storage"
	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/hibiken/asynq"
	"github.com/rs/zerolog/log"
	"google.golang.org/api/iterator"
)

type PlatformInfo struct {
	Platform        enums.VideoPlatform       `json:"platform"`
	Status          enums.VideoPlatformStatus `json:"status"`
	PlatformVideoID string                    `json:"platformVideoId"`
	YoutubeTokenID  *uint32                   `json:"youtubeTokenId"`
}

type VideoCleanupPayload struct {
	VideoID   uint32         `json:"videoId"`
	Filename  string         `json:"filename"`
	Platforms []PlatformInfo `json:"platforms"`
}

func NewVideoCleanupTask(payload VideoCleanupPayload) (*asynq.Task, error) {
	data, err := json.Marshal(payload)
	if err != nil {
		log.Error().
			Err(err).
			Uint32("video_id", payload.VideoID).
			Msg("Failed to marshal video cleanup payload")
		return nil, err
	}

	return asynq.NewTask(VideoCleanupTaskType, data, asynq.Queue(asyncq.QueueVideoTranscoder)), nil
}

func HandleVideoCleanupTask(repos repository.IRepositories) func(context.Context, *asynq.Task) error {
	return func(ctx context.Context, t *asynq.Task) error {
		log.Info().
			Str("task_id", t.ResultWriter().TaskID()).
			Msg("Starting video cleanup task")

		var payload VideoCleanupPayload
		if err := json.Unmarshal(t.Payload(), &payload); err != nil {
			log.Error().
				Err(err).
				Str("task_id", t.ResultWriter().TaskID()).
				Msg("Failed to unmarshal video cleanup payload")
			return fmt.Errorf("json.Unmarshal failed: %v: %w", err, asynq.SkipRetry)
		}

		err := executeVideoCleanup(ctx, payload, repos)
		if err != nil {
			log.Error().
				Err(err).
				Uint32("video_id", payload.VideoID).
				Str("task_id", t.ResultWriter().TaskID()).
				Msg("Video cleanup task failed")
		} else {
			log.Info().
				Uint32("video_id", payload.VideoID).
				Str("task_id", t.ResultWriter().TaskID()).
				Msg("Video cleanup completed successfully")
		}

		return err
	}
}

func executeVideoCleanup(ctx context.Context, payload VideoCleanupPayload, repos repository.IRepositories) error {
	stateKey := fmt.Sprintf("video_cleanup_state:%d", payload.VideoID)

	completed, _ := helpers.GetFromRedis[map[string]bool](stateKey)
	if completed == nil {
		completed = &map[string]bool{}
	}

	var combinedErrs []error

	if !(*completed)["gcs"] {
		if err := gcsDelete(payload); err != nil {
			log.Error().
				Err(err).
				Uint32("video_id", payload.VideoID).
				Msg("GCS deletion failed")
			combinedErrs = append(combinedErrs, fmt.Errorf("gcs delete: %w", err))
		} else {
			(*completed)["gcs"] = true
			helpers.StoreInRedis(stateKey, *completed, 24*time.Hour)
		}
	}

	if !(*completed)["mux"] {
		if err := muxDelete(payload); err != nil {
			log.Error().
				Err(err).
				Uint32("video_id", payload.VideoID).
				Msg("Mux deletion failed")
			combinedErrs = append(combinedErrs, fmt.Errorf("mux delete: %w", err))
		} else {
			(*completed)["mux"] = true
			helpers.StoreInRedis(stateKey, *completed, 24*time.Hour)
		}
	}

	if !(*completed)["youtube"] {
		if err := youtubeDelete(payload, repos); err != nil {
			log.Error().
				Err(err).
				Uint32("video_id", payload.VideoID).
				Msg("YouTube deletion failed")
			combinedErrs = append(combinedErrs, fmt.Errorf("youtube delete: %w", err))
		} else {
			(*completed)["youtube"] = true
			helpers.StoreInRedis(stateKey, *completed, 24*time.Hour)
		}
	}

	// If all steps succeeded, remove the Redis key
	if len(combinedErrs) == 0 {
		helpers.GetRedisClient().Del(ctx, stateKey)
		return nil
	}

	log.Error().
		Uint32("video_id", payload.VideoID).
		Int("error_count", len(combinedErrs)).
		Msg("Video cleanup completed with errors")

	return errors.Join(combinedErrs...)
}

func gcsDelete(payload VideoCleanupPayload) error {
	ctx, cancel := context.WithTimeout(context.Background(), time.Minute*10)
	defer cancel()

	client, err := storage.NewClient(ctx)
	if err != nil {
		log.Error().
			Err(err).
			Uint32("video_id", payload.VideoID).
			Msg("Failed to create GCS storage client")
		return fmt.Errorf("failed to create storage client: %w", err)
	}
	defer client.Close()

	bucketName := utils.GetEnv("GCS_BUCKET_NAME", "")
	if bucketName == "" {
		return errors.New("GCS_BUCKET_NAME is required")
	}

	prefix := fmt.Sprintf("courses/%s/", payload.Filename)
	query := &storage.Query{Prefix: prefix}
	it := client.Bucket(bucketName).Objects(ctx, query)

	var deletedCount int
	for {
		attrs, err := it.Next()
		if err == iterator.Done {
			break
		}
		if err != nil {
			log.Error().
				Err(err).
				Uint32("video_id", payload.VideoID).
				Str("prefix", prefix).
				Msg("Error iterating over GCS objects")
			return fmt.Errorf("error iterating over objects: %w", err)
		}

		if delErr := client.Bucket(bucketName).Object(attrs.Name).Delete(ctx); delErr != nil {
			log.Error().
				Err(delErr).
				Uint32("video_id", payload.VideoID).
				Str("object_name", attrs.Name).
				Msg("Failed to delete GCS object")
			return fmt.Errorf("failed to delete %q: %w", attrs.Name, delErr)
		}
		deletedCount++
	}

	if deletedCount > 0 {
		log.Info().
			Uint32("video_id", payload.VideoID).
			Int("deleted_count", deletedCount).
			Msg("GCS objects deleted")
	}

	return nil
}

func muxDelete(payload VideoCleanupPayload) error {
	ctx, cancel := context.WithTimeout(context.Background(), time.Minute*10)
	defer cancel()

	for _, platform := range payload.Platforms {
		if platform.Platform == enums.VideoPlatformMux && platform.Status == enums.VideoPlatformStatusAvailable {
			deleteService, err := mux.NewDeleteServiceFromEnv(ctx, platform.PlatformVideoID)
			if err != nil {
				log.Error().
					Err(err).
					Uint32("video_id", payload.VideoID).
					Str("platform_video_id", platform.PlatformVideoID).
					Msg("Failed to create Mux delete service")
				return fmt.Errorf("failed to create Mux delete service: %v", err)
			}

			if err := deleteService.Execute(); err != nil {
				// TODO: Handle Mux "not found" error here - return nil if video doesn't exist
				log.Error().
					Err(err).
					Uint32("video_id", payload.VideoID).
					Str("platform_video_id", platform.PlatformVideoID).
					Msg("Failed to delete Mux video")
				return fmt.Errorf("failed to delete Mux video: %v", err)
			}

			log.Info().
				Uint32("video_id", payload.VideoID).
				Str("platform_video_id", platform.PlatformVideoID).
				Msg("Mux video deleted")
			break
		}
	}
	return nil
}

func youtubeDelete(payload VideoCleanupPayload, repos repository.IRepositories) error {
	ctx, cancel := context.WithTimeout(context.Background(), time.Minute*10)
	defer cancel()

	for _, platform := range payload.Platforms {
		if platform.Platform == enums.VideoPlatformYoutube && platform.Status == enums.VideoPlatformStatusAvailable {
			if platform.YoutubeTokenID != nil {
				usedToken, err := repos.YoutubeTokenRepo().FindByID(ctx, *platform.YoutubeTokenID)
				if err != nil {
					log.Error().
						Err(err).
						Uint32("video_id", payload.VideoID).
						Str("platform_video_id", platform.PlatformVideoID).
						Msg("Failed to find YouTube token")
					return fmt.Errorf("failed to find YouTube token: %v", err)
				}
				tokenFilePath := os.Getenv("YOUTUBE_API_TOKEN_FILE")
				if err := writeTokenToFile(ctx, usedToken.Token, tokenFilePath); err != nil {
					log.Error().
						Err(err).
						Str("tokenFile", tokenFilePath).
						Msg("Failed to write token to file")
					return fmt.Errorf("failed to write token to file: %w", err)
				}
				log.Info().
					Str("tokenFile", tokenFilePath).
					Uint32("tokenId", usedToken.ID).
					Msg("Token written successfully")
			}

			deleteService, err := youtube.NewVideoDeleteServiceFromEnv(ctx, platform.PlatformVideoID)
			if err != nil {
				log.Error().
					Err(err).
					Uint32("video_id", payload.VideoID).
					Str("platform_video_id", platform.PlatformVideoID).
					Msg("Failed to create YouTube delete service")
				return fmt.Errorf("failed to create YouTube delete service: %v", err)
			}

			if err := deleteService.Execute(); err != nil {
				// TODO: Handle YouTube "not found" error here - return nil if video doesn't exist
				log.Error().
					Err(err).
					Uint32("video_id", payload.VideoID).
					Str("platform_video_id", platform.PlatformVideoID).
					Msg("Failed to delete YouTube video")
				return fmt.Errorf("failed to delete YouTube video: %v", err)
			}

			log.Info().
				Uint32("video_id", payload.VideoID).
				Str("platform_video_id", platform.PlatformVideoID).
				Msg("YouTube video deleted")
			break
		}
	}
	return nil
}
