package videoTranscoderTasks

import (
	"vibico-education-api/internal/repository"

	"github.com/hibiken/asynq"
)

const (
	GCSChunksComposeTaskType    = "videoTranscoder.GCSChunksComposeTaskType"
	MuxUploadTaskType           = "videoTranscoder.MuxUploadTaskType"
	YouTubeUploadTaskType       = "videoTranscoder.YouTubeUploadTaskType"
	CleanupOrphanChunksTaskType = "videoTranscoder.CleanupOrphanChunksTaskType"
	VideoCleanupTaskType        = "videoTranscoder.VideoCleanupTaskType"
)

func RegisterHandlers(mux *asynq.ServeMux, repos repository.IRepositories) {
	mux.HandleFunc(MuxUploadTaskType, HandleMuxUploadTask(repos))
	mux.HandleFunc(YouTubeUploadTaskType, HandleYouTubeUploadTask(repos))
	mux.HandleFunc(GCSChunksComposeTaskType, HandleGCSChunksComposeTask(repos))
	mux.HandleFunc(CleanupOrphanChunksTaskType, HandleCleanUpOrphanChunksTask(repos))
	mux.HandleFunc(VideoCleanupTaskType, HandleVideoCleanupTask(repos))
}
