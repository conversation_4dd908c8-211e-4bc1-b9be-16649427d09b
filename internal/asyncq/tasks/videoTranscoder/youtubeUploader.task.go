package videoTranscoderTasks

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/hibiken/asynq"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"

	"vibico-education-api/internal/asyncq"
	"vibico-education-api/internal/enums"
	"vibico-education-api/internal/models"
	"vibico-education-api/internal/repository"
	"vibico-education-api/internal/repository/repositories"
	"vibico-education-api/internal/services/youtube"
	"vibico-education-api/pkg/gcs"
	"vibico-education-api/pkg/helpers"
)

const (
	uploadTimeout = 1 * time.Hour
)

// YouTubeUploadPayload contains all necessary data for uploading video to YouTube
type YouTubeUploadPayload struct {
	VideoID     uint32 `json:"videoId"`
	FileURL     string `json:"fileUrl"`
	Title       string `json:"title"`
	Description string `json:"description"`
	Tags        string `json:"tags"`
	CategoryID  string `json:"categoryId"`
}

// NewYouTubeUploadTask creates a new task for uploading a video to YouTube
func NewYouTubeUploadTask(payload YouTubeUploadPayload) (*asynq.Task, error) {
	data, err := json.Marshal(payload)
	if err != nil {
		return nil, err
	}
	return asynq.NewTask(
		YouTubeUploadTaskType,
		data,
		asynq.Queue(asyncq.QueueVideoTranscoder),
	), nil
}

// HandleYouTubeUploadTask returns a handler function for the YouTube upload task
func HandleYouTubeUploadTask(repos repository.IRepositories) func(context.Context, *asynq.Task) error {
	return func(ctx context.Context, task *asynq.Task) error {
		var payload YouTubeUploadPayload
		if err := json.Unmarshal(task.Payload(), &payload); err != nil {
			log.Ctx(ctx).Error().
				Err(err).
				Bytes("payload", task.Payload()).
				Msg("Failed to unmarshal YouTube upload task payload")
			return fmt.Errorf("failed to unmarshal payload: %w", asynq.SkipRetry)
		}

		ctx, cancel := context.WithTimeout(ctx, uploadTimeout)
		defer cancel()

		logger := log.Ctx(ctx).With().
			Uint32("videoId", payload.VideoID).
			Str("title", payload.Title).
			Logger()

		logger.Info().Msg("Starting YouTube upload task")

		progressCallback := func(progress float64) {
			logger.Info().
				Float64("progress", progress).
				Msg("YouTube upload progress")
		}

		logger.Info().Msg("Fetching video details from database")
		video, err := repos.VideoRepo().FindByID(ctx, payload.VideoID, repositories.CustomPreload{
			Key: "VideoUpload",
		})
		if err != nil {
			logger.Error().Err(err).Msg("Failed to find video in database")
			return fmt.Errorf("failed to find video: %w", err)
		}

		logger.Info().Msg("Downloading video from Google Cloud Storage")
		tmpFile, err := gcs.DownloadVideoFromGCS(ctx, video.VideoUpload.Filename, nil)
		if err != nil {
			logger.Error().
				Err(err).
				Str("videoTitle", video.VideoUpload.Filename).
				Msg("Failed to download video from GCS")
			return fmt.Errorf("failed to download video: %w", err)
		}
		defer os.Remove(tmpFile.Name())
		logger.Info().Str("tempFile", tmpFile.Name()).Msg("Video downloaded successfully")

		// Add watermark to the downloaded video
		logger.Info().Msg("Adding watermark to video")
		watermarkedFile, err := AddWatermarkToVideo(ctx, tmpFile.Name())
		if err != nil {
			logger.Error().Err(err).Msg("Failed to add watermark to video")
			return fmt.Errorf("failed to add watermark: %w", err)
		}
		defer os.Remove(watermarkedFile.Name())
		logger.Info().Str("watermarkedFile", watermarkedFile.Name()).Msg("Watermark added successfully")

		// Get YouTube token
		logger.Info().Msg("Fetching YouTube token from database")
		token, err := repos.YoutubeTokenRepo().GetLeastUsed(ctx)
		if err != nil {
			logger.Error().Err(err).Msg("Failed to fetch YouTube token")
			return fmt.Errorf("failed to fetch YouTube token: %w", err)
		}

		// Decode and write the token to file
		logger.Info().Msg("Decoding and writing YouTube token to file")
		tokenFilePath := os.Getenv("YOUTUBE_API_TOKEN_FILE")
		if err := writeTokenToFile(ctx, token.Token, tokenFilePath); err != nil {
			logger.Error().
				Err(err).
				Str("tokenFile", tokenFilePath).
				Msg("Failed to write token to file")
			return fmt.Errorf("failed to write token to file: %w", err)
		}
		logger.Info().
			Str("tokenFile", tokenFilePath).
			Uint32("tokenId", token.ID).
			Msg("Token written successfully")

		logger.Info().Msg("Initializing YouTube client")
		youtubeClient, err := youtube.NewClient(ctx, &youtube.ClientConfig{
			CredentialsFile: os.Getenv("YOUTUBE_API_CREDENTIALS_FILE"),
			TokenFile:       tokenFilePath,
			QuotaLimit:      youtube.DefaultQuotaLimit,
		})
		if err != nil {
			logger.Error().
				Err(err).
				Str("credentialsFile", os.Getenv("YOUTUBE_API_CREDENTIALS_FILE")).
				Str("tokenFile", tokenFilePath).
				Msg("Failed to create YouTube client")
			return fmt.Errorf("failed to create YouTube client: %w", err)
		}
		logger.Info().Msg("YouTube client initialized successfully")

		tags := strings.Split(strings.TrimSpace(payload.Tags), ",")
		metadata := youtube.VideoMetadata{
			Title:         payload.Title,
			Description:   payload.Description,
			Tags:          tags,
			CategoryID:    "27", // Education category
			PrivacyStatus: youtube.PrivacyStatusUnlisted,
		}
		logger.Info().
			Interface("metadata", metadata).
			Msg("Prepared video metadata")

		logger.Info().Msg("Creating/Updating video platform record")
		platformRecord, err := handleVideoPlatformRecord(ctx, repos.VideoPlatformRepo(), payload.VideoID, enums.VideoPlatformYoutube, enums.VideoPlatformStatusSyncing, &token.ID)
		if err != nil {
			logger.Error().
				Err(err).
				Msg("Failed to handle video platform record")
			return fmt.Errorf("failed to handle video platform record: %w", err)
		}
		logger.Info().
			Uint32("platformRecordId", platformRecord.ID).
			Uint32("tokenId", token.ID).
			Msg("Video platform record prepared")

		logger.Info().Msg("Starting YouTube upload process")
		uploadService := youtube.NewUploadService(
			ctx,
			youtubeClient,
			watermarkedFile.Name(),
			metadata,
			progressCallback,
		)

		if err := uploadService.Execute(); err != nil {
			logger.Error().
				Err(err).
				Msg("YouTube upload failed")
			if platformRecord != nil {
				updateVideoPlatformStatus(ctx, repos.VideoPlatformRepo(), platformRecord.ID, enums.VideoPlatformStatusError)
			}
			return fmt.Errorf("failed to upload video: %w", err)
		}

		uploadedVideo := uploadService.GetVideo()
		platformRecord.URL = uploadedVideo.URL
		logger.Info().
			Str("youtubeId", uploadedVideo.ID).
			Str("url", uploadedVideo.URL).
			Msg("Video uploaded successfully to YouTube")

		logger.Info().Msg("Updating video platform record with YouTube details")

		if err := updateVideoPlatformRecord(ctx, repos.VideoPlatformRepo(), platformRecord, uploadedVideo.ID, enums.VideoPlatformStatusAvailable); err != nil {
			logger.Error().
				Err(err).
				Uint32("platformRecordId", platformRecord.ID).
				Str("youtubeId", uploadedVideo.ID).
				Msg("Failed to update video platform record")

			deleteYoutubeVideoService := youtube.NewVideoDeleteService(ctx, youtubeClient, uploadedVideo.ID)
			if err := deleteYoutubeVideoService.Execute(); err != nil {
				logger.Error().Err(err).
					Str("youtubeId", uploadedVideo.ID).
					Msg("Delete Youtube video failed")

				return fmt.Errorf("failed to delete youtube video: %w", err)
			}

			return fmt.Errorf("failed to update video platform record: %w", err)
		}

		logger.Info().
			Str("youtubeId", uploadedVideo.ID).
			Str("url", uploadedVideo.URL).
			Msg("YouTube upload task completed successfully")

		return nil
	}
}

// writeTokenToFile decodes the base64 token and writes it to the specified file
func writeTokenToFile(ctx context.Context, base64Token, tokenFilePath string) error {
	logger := log.Ctx(ctx)

	// Decode the base64 token
	tokenBytes, err := helpers.DecodeBase64(base64Token)
	if err != nil {
		logger.Error().
			Err(err).
			Msg("Failed to decode base64 token")
		return fmt.Errorf("failed to decode base64 token: %w", err)
	}

	// Validate that the decoded token is valid JSON
	var tokenData map[string]interface{}
	if err := json.Unmarshal(tokenBytes, &tokenData); err != nil {
		logger.Error().
			Err(err).
			Msg("Decoded token is not valid JSON")
		return fmt.Errorf("decoded token is not valid JSON: %w", err)
	}

	// Write the decoded token to file
	if err := os.WriteFile(tokenFilePath, tokenBytes, 0600); err != nil {
		logger.Error().
			Err(err).
			Str("tokenFile", tokenFilePath).
			Msg("Failed to write token to file")
		return fmt.Errorf("failed to write token to file: %w", err)
	}

	logger.Debug().
		Str("tokenFile", tokenFilePath).
		Int("tokenSize", len(tokenBytes)).
		Msg("Token decoded and written to file successfully")

	return nil
}

func handleVideoPlatformRecord(ctx context.Context, repo *repositories.VideoPlatformRepository, videoID uint32, platform enums.VideoPlatform, status enums.VideoPlatformStatus, tokenID *uint32) (*models.VideoPlatform, error) {
	existingRecord, err := repo.FindByVideoIDAndPlatform(ctx, videoID, platform)
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("failed to find video platform record: %w", err)
	}

	if existingRecord != nil {
		// Update existing record with new token ID and status
		existingRecord.TokenID = tokenID
		existingRecord.Status = status

		if err := repo.Update(ctx, existingRecord, "token_id", "status"); err != nil {
			log.Ctx(ctx).Warn().
				Err(err).
				Uint32("videoId", videoID).
				Msg("Failed to update video platform record")
		}
		return existingRecord, nil
	}

	newRecord := &models.VideoPlatform{
		VideoID:  videoID,
		Platform: platform,
		Status:   status,
		TokenID:  tokenID,
	}

	if err := repo.Create(ctx, newRecord); err != nil {
		log.Ctx(ctx).Warn().
			Err(err).
			Uint32("videoId", videoID).
			Msg("Failed to create video platform record")
	}

	return newRecord, nil
}

func updateVideoPlatformStatus(ctx context.Context, repo *repositories.VideoPlatformRepository, recordID uint32, status enums.VideoPlatformStatus) {
	if err := repo.UpdateStatus(ctx, recordID, status); err != nil {
		log.Ctx(ctx).Error().
			Err(err).
			Uint32("recordId", recordID).
			Msg("Failed to update video platform status")
	}
}

func updateVideoPlatformRecord(ctx context.Context, repo *repositories.VideoPlatformRepository, record *models.VideoPlatform, platformVideoID string, status enums.VideoPlatformStatus) error {
	if record == nil || record.ID == 0 {
		return nil
	}

	record.PlatformVideoID = platformVideoID
	record.Status = status
	now := time.Now()
	record.SyncAt = &now

	if err := repo.Update(ctx, record, "platform_video_id", "status", "sync_at", "url"); err != nil {
		log.Ctx(ctx).Error().
			Err(err).
			Uint32("recordId", record.ID).
			Str("platformVideoId", platformVideoID).
			Msg("Failed to update video platform record")
		return fmt.Errorf("failed to update video platform record: %w", err)
	}

	return nil
}
