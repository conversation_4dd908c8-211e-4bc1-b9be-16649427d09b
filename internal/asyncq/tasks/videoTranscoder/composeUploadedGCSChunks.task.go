package videoTranscoderTasks

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"math"
	"os"
	"os/exec"
	"path/filepath"
	"strconv"
	"strings"
	"time"
	"vibico-education-api/constants"
	"vibico-education-api/internal/asyncq"
	"vibico-education-api/internal/enums"
	"vibico-education-api/internal/models"
	"vibico-education-api/internal/repository"
	"vibico-education-api/pkg/helpers"

	"cloud.google.com/go/storage"
	"github.com/hibiken/asynq"
	"github.com/redis/go-redis/v9"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
)

// GCSChunksComposeTaskPayload contains all necessary data for composing chunks
type GCSChunksComposeTaskPayload struct {
	FileID      string   `json:"fileId"`
	TotalChunks int      `json:"totalChunks"`
	Paths       []string `json:"paths"`
	ContentType string   `json:"contentType"`
	UploadID    uint32   `json:"uploadId"`
	BucketName  string   `json:"bucketName"`
	ChunkPath   string   `json:"chunkPath"`
	UploadPath  string   `json:"uploadPath"`
	ParentType  string   `json:"parentType"`
	ParentID    uint32   `json:"parentId"`
}

// NewGCSChunksComposeTask creates a new task for composing chunks
func NewGCSChunksComposeTask(composePayload GCSChunksComposeTaskPayload) (*asynq.Task, error) {
	payload, err := json.Marshal(composePayload)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal payload: %w", err)
	}
	return asynq.NewTask(
		GCSChunksComposeTaskType,
		payload,
		asynq.Queue(asyncq.QueueVideoTranscoder),
	), nil
}

// HandleGCSChunksComposeTask returns a handler function for the chunks compose task
func HandleGCSChunksComposeTask(repos repository.IRepositories) func(context.Context, *asynq.Task) error {
	return func(ctx context.Context, t *asynq.Task) error {
		var payload GCSChunksComposeTaskPayload
		if err := json.Unmarshal(t.Payload(), &payload); err != nil {
			return fmt.Errorf("json.Unmarshal failed: %v: %w", err, asynq.SkipRetry)
		}

		logger := log.Ctx(ctx).With().
			Str("fileID", payload.FileID).
			Int("totalChunks", payload.TotalChunks).
			Uint32("uploadID", payload.UploadID).
			Logger()

		logger.Info().Msg("Starting GCS chunks compose task")

		// Initialize GCS client
		client, err := initializeGCSClient(ctx)
		if err != nil {
			updateUploadStatus(ctx, repos, payload.UploadID, enums.VideoUploadStatusFailed)
			return fmt.Errorf("failed to create GCS client: %w", err)
		}
		defer client.Close()

		// Proceed with the video processing
		if err := processVideo(ctx, client, payload, repos, logger); err != nil {
			logger.Error().Err(err).Msg("Video processing failed")
			updateUploadStatus(ctx, repos, payload.UploadID, enums.VideoUploadStatusFailed)
			return err
		}

		logger.Info().Msg("GCS chunks compose task completed successfully")
		return nil
	}
}

// initializeGCSClient creates a new GCS client
func initializeGCSClient(ctx context.Context) (*storage.Client, error) {
	client, err := storage.NewClient(ctx)
	if err != nil {
		return nil, err
	}
	return client, nil
}

// updateUploadStatus updates the status of a video upload
func updateUploadStatus(ctx context.Context, repos repository.IRepositories, uploadID uint32, status enums.VideoUploadStatus) {
	if err := repos.VideoUploadRepo().UpdateStatus(ctx, uploadID, status, 0); err != nil {
		log.Ctx(ctx).Error().
			Uint32("uploadID", uploadID).
			Err(err).
			Msg("Failed to update upload status")
	}
}

// processVideo handles the entire video processing workflow
func processVideo(ctx context.Context, client *storage.Client, payload GCSChunksComposeTaskPayload,
	repos repository.IRepositories, logger zerolog.Logger) error {
	// Compose chunks into final file
	finalPath, tempPaths, err := composeChunksToFinal(ctx, client, payload)
	if err != nil {
		handleMaxRetryCleanup(ctx, client, payload, err)
		return fmt.Errorf("compose chunks failed: %w", err)
	}

	// Always clean up temporary files
	defer cleanupTempFiles(context.Background(), client.Bucket(payload.BucketName), tempPaths)

	// Generate and upload thumbnail
	thumbnailURL, duration, err := generateAndUploadThumbnail(ctx, client, payload.BucketName, finalPath, payload.FileID)
	if err != nil {
		logger.Error().Err(err).Msg("Thumbnail generation failed")
		return fmt.Errorf("thumbnail generation failed: %w", err)
	}

	// Finalize the upload in the database
	if err := finalizeUpload(ctx, repos, payload, thumbnailURL, duration); err != nil {
		logger.Error().Err(err).Msg("Failed to finalize upload in database")
		return err
	}

	// Clean up chunks after successful completion
	go cleanup(context.Background(), client.Bucket(payload.BucketName), payload.FileID, payload.Paths)

	logger.Info().
		Str("finalPath", finalPath).
		Str("thumbnailURL", *thumbnailURL).
		Msg("Video processing completed successfully")

	return nil
}

// handleMaxRetryCleanup handles cleanup when max retries are reached
func handleMaxRetryCleanup(ctx context.Context, client *storage.Client, payload GCSChunksComposeTaskPayload, err error) {
	retryCount, _ := asynq.GetRetryCount(ctx)
	maxRetry, _ := asynq.GetMaxRetry(ctx)

	if retryCount >= maxRetry {
		log.Ctx(ctx).Info().
			Str("fileID", payload.FileID).
			Msg("Max retries reached, cleaning up chunks")
		cleanup(ctx, client.Bucket(payload.BucketName), payload.FileID, payload.Paths)
	}
}

// cleanupTempFiles removes temporary files created during composition
func cleanupTempFiles(ctx context.Context, bucket *storage.BucketHandle, tempPaths []string) {
	for _, tp := range tempPaths {
		if err := bucket.Object(tp).Delete(ctx); err != nil {
			log.Ctx(ctx).Error().
				Str("path", tp).
				Err(err).
				Msg("Failed to delete temporary file")
		}
	}
}

// finalizeUpload updates the database with the completed upload information
func finalizeUpload(ctx context.Context, repos repository.IRepositories,
	payload GCSChunksComposeTaskPayload, thumbnailURL *string, duration *int32) error {
	// Get the upload record
	videoUpload, err := repos.VideoUploadRepo().FindByID(ctx, payload.UploadID)
	if err != nil || videoUpload == nil {
		return fmt.Errorf("upload record not found: ID=%d, error=%v", payload.UploadID, err)
	}
	videoUpload.Status = enums.VideoUploadStatusCompleted

	// Get the video record
	video, err := repos.VideoRepo().FindByID(ctx, videoUpload.VideoID)
	if err != nil || video == nil {
		return fmt.Errorf("video record not found: ID=%d, error=%v", videoUpload.VideoID, err)
	}

	// Update video metadata
	updateVideoMetadata(video, thumbnailURL, duration, payload)

	// Save changes to database
	if err := repos.VideoUploadRepo().FinishUpload(ctx, videoUpload, video); err != nil {
		return fmt.Errorf("failed to update database: %w", err)
	}

	// Publish to Redis
	now := time.Now()
	cutoff := fmt.Sprintf("%d-0", now.Add(-90*time.Minute).UnixMilli())

	red := helpers.GetRedisClient()
	args := &redis.XAddArgs{
		Stream: "video_events",
		MaxLen: 1000,   // keep maximum 1000 entries
		Approx: true,   // approximate trimming, faster [[1](https://redis.io/docs/latest/commands/xadd/)]
		MinID:  cutoff, // remove entries older than 90 minutes
		Limit:  100,    // trim maximum 100 entries at a time
		Values: map[string]any{
			"video_id": video.ID,
			"event":    "video_uploaded",
		},
	}

	if _, err := red.XAdd(ctx, args).Result(); err != nil {
		log.Error().Ctx(ctx).Err(err).Msg("Redis Stream XAdd err")
	}

	return nil
}

// updateVideoMetadata updates the video object with metadata
func updateVideoMetadata(video *models.Video,
	thumbnailURL *string, duration *int32, payload GCSChunksComposeTaskPayload) {
	video.ThumbnailURL = thumbnailURL
	video.UpdatedAt = time.Now()
	video.ParentID = payload.ParentID
	video.Duration = *duration

	parentType, err := enums.ParseVideoParentType(payload.ParentType)
	if err != nil {
		log.Error().Err(err).Str("parentType", payload.ParentType).
			Msg("Failed to parse parent type, using default")
	}
	video.ParentType = parentType

	video.Status = enums.VideoStatusApproved // TODO: change when have proper logic
}

// composeChunksToFinal composes all chunks into a final video file
func composeChunksToFinal(ctx context.Context, client *storage.Client,
	payload GCSChunksComposeTaskPayload) (string, []string, error) {

	bucket := client.Bucket(payload.BucketName)
	var tempPaths []string
	var composedObject *storage.ObjectHandle

	batchSize := constants.GCSMaxComposePerBatch

	// Process chunks in batches due to GCS composer limitations
	for i := 0; i < payload.TotalChunks; i += batchSize {
		end := i + batchSize
		if end > payload.TotalChunks {
			end = payload.TotalChunks
		}

		// Calculate actual batch size considering the composed object
		actualBatchSize := end - i
		if composedObject != nil {
			// If we already have a composed object, reserve one slot for it
			actualBatchSize = constants.GCSMaxComposePerBatch - 1
			if i+actualBatchSize > payload.TotalChunks {
				actualBatchSize = payload.TotalChunks - i
			}
		}

		// Create array of source objects for this batch
		sources := make([]*storage.ObjectHandle, 0, actualBatchSize)
		for j := i; j < i+actualBatchSize; j++ {
			path := fmt.Sprintf("%s/%s/chunk_%d", payload.ChunkPath, payload.FileID, j)
			sources = append(sources, bucket.Object(path))
		}

		// Create temporary path for this batch composition
		tempPath := fmt.Sprintf("%s/%s/temp_%d", payload.ChunkPath, payload.FileID, i/batchSize)
		tempPaths = append(tempPaths, tempPath)
		destObj := bucket.Object(tempPath)

		// Create composer with appropriate sources
		var composer *storage.Composer
		if composedObject != nil {
			composer = destObj.ComposerFrom(append([]*storage.ObjectHandle{composedObject}, sources...)...)
		} else {
			composer = destObj.ComposerFrom(sources...)
		}
		composer.ObjectAttrs.ContentType = payload.ContentType

		// Execute composition
		attrs, err := composer.Run(ctx)
		if err != nil {
			cleanupTempFiles(ctx, bucket, tempPaths)
			return "", nil, fmt.Errorf("composition failed at batch %d: %w", i/batchSize, err)
		}

		composedObject = bucket.Object(attrs.Name)
	}

	// Move final composition to desired location
	finalPath := fmt.Sprintf(constants.CourseVideoPath, payload.FileID, payload.FileID)
	destObj := bucket.Object(finalPath)
	if _, err := destObj.CopierFrom(composedObject).Run(ctx); err != nil {
		return "", tempPaths, fmt.Errorf("failed to move final composition: %w", err)
	}

	return finalPath, tempPaths, nil
}

// generateThumbnail creates a thumbnail from a video file using streaming
func generateThumbnail(videoPath, thumbnailPath string) (*int32, error) {
	// Get video duration using ffprobe
	cmd := exec.Command("ffprobe",
		"-v", "error",
		"-show_entries", "format=duration",
		"-of", "default=noprint_wrappers=1:nokey=1",
		videoPath)

	var stdout, stderr bytes.Buffer
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr

	if err := cmd.Run(); err != nil {
		return nil, fmt.Errorf("failed to get video duration: %v, stderr: %s", err, stderr.String())
	}

	duration, err := strconv.ParseFloat(strings.TrimSpace(stdout.String()), 64)
	if err != nil {
		return nil, fmt.Errorf("failed to parse video duration: %v", err)
	}

	// Calculate middle point
	midPoint := duration / 2

	// Use FFmpeg command with optimized streaming options
	cmd = exec.Command("ffmpeg",
		"-loglevel", "error", // Use "error" for less noisy logs in production
		"-ss", fmt.Sprintf("%f", midPoint), // Seek to middle point
		"-i", videoPath,
		"-frames:v", "1",
		"-c:v", "mjpeg",
		"-f", "image2",
		"-q:v", "2", // High quality
		"-an", // Disable audio
		"-y",  // Overwrite output
		thumbnailPath)

	stderr.Reset()
	cmd.Stderr = &stderr

	if err := cmd.Run(); err != nil {
		return nil, fmt.Errorf("failed to generate thumbnail: ffmpeg error: %v, stderr: %s", err, stderr.String())
	}

	roundDuration := int32(math.Round(duration))
	return &roundDuration, nil
}

// generateAndUploadThumbnail creates a thumbnail from the video and uploads it to GCS
func generateAndUploadThumbnail(ctx context.Context, client *storage.Client,
	bucketName, videoPath, fileID string) (*string, *int32, error) {
	logger := log.Ctx(ctx).With().Str("fileID", fileID).Logger()
	logger.Info().Str("videoPath", videoPath).Msg("Generating thumbnail from video stream")

	// Create and manage temp directory
	tempDir, err := os.MkdirTemp("", "video-thumbnail-")
	if err != nil {
		return nil, nil, fmt.Errorf("failed to create temp directory: %w", err)
	}
	defer os.RemoveAll(tempDir)

	// Set up paths
	localThumbnailPath := filepath.Join(tempDir, fileID+".jpg")

	// Generate thumbnail directly from GCS using FFmpeg's HTTP input
	gcsURL := fmt.Sprintf("https://storage.googleapis.com/%s/%s", bucketName, videoPath)
	duration, err := generateThumbnail(gcsURL, localThumbnailPath)
	if err != nil {
		return nil, nil, err
	}

	// Upload thumbnail to GCS
	thumbnailGCSPath := fmt.Sprintf(constants.CourseVideoThumbnailPath, fileID)
	if err := uploadToGCS(ctx, client, bucketName, localThumbnailPath, thumbnailGCSPath, "image/webp"); err != nil {
		return nil, nil, err
	}

	logger.Info().Str("thumbnailURL", thumbnailGCSPath).Msg("Thumbnail processed successfully")
	return &thumbnailGCSPath, duration, nil
}

// uploadToGCS uploads a file to GCS
func uploadToGCS(ctx context.Context, client *storage.Client, bucketName, localPath, gcsPath, contentType string) error {
	bucket := client.Bucket(bucketName)
	obj := bucket.Object(gcsPath)
	writer := obj.NewWriter(ctx)
	writer.ContentType = contentType

	// Open local file
	file, err := os.Open(localPath)
	if err != nil {
		return fmt.Errorf("failed to open local file: %w", err)
	}
	defer file.Close()

	// Copy data
	if _, err = io.Copy(writer, file); err != nil {
		return fmt.Errorf("failed to upload file: %w", err)
	}

	if err = writer.Close(); err != nil {
		return fmt.Errorf("failed to close writer: %w", err)
	}

	return nil
}

// cleanup deletes chunk objects
func cleanup(ctx context.Context, bucket *storage.BucketHandle, fileID string, paths []string) {
	logger := log.Ctx(ctx).With().Str("fileID", fileID).Logger()
	logger.Info().Msg("Cleaning up chunk files")

	for _, p := range paths {
		if err := bucket.Object(p).Delete(ctx); err != nil {
			logger.Error().Str("path", p).Err(err).Msg("Failed to delete chunk")
		}
	}

	// kill cleaning task
	inspector := asynq.NewInspector(asyncq.GetManager().Opt)
	tasks, _ := inspector.ListScheduledTasks(asyncq.QueueVideoTranscoder, CleanupOrphanChunksTaskType)

	for _, task := range tasks {
		var cleanupPayload CleanUpOrphanChunksTaskPayload
		if err := json.Unmarshal(task.Payload, &cleanupPayload); err != nil {
			continue
		}

		if cleanupPayload.FileID == fileID {
			if err := inspector.DeleteTask(asyncq.QueueVideoTranscoder, task.ID); err != nil {
				logger.Error().Err(err).Msg("Failed to delete cleanup task")
			} else {
				logger.Info().Msg("Successfully cancelled cleanup task")
			}
			break
		}
	}
}
