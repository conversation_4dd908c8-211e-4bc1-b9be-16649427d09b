package videoTranscoderTasks

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"vibico-education-api/constants"
	"vibico-education-api/internal/asyncq"
	"vibico-education-api/internal/enums"
	"vibico-education-api/internal/repository"
	"vibico-education-api/internal/repository/repositories"
	"vibico-education-api/internal/services/mux"
	"vibico-education-api/internal/services/video"
	"vibico-education-api/pkg/gcs"

	"github.com/hibiken/asynq"
	"github.com/rs/zerolog/log"
)

// MuxUploadPayload contains all necessary data for uploading video to Mux
type MuxUploadPayload struct {
	VideoID     uint32 `json:"videoId"`     // ID of the video in the database
	FileURL     string `json:"fileUrl"`     // Pre-signed URL to the file
	Title       string `json:"title"`       // Video title
	Description string `json:"description"` // Video description
	IsFree      bool   `json:"isFree"`      // Whether the video is free to watch
	PassThrough string `json:"passThrough"` // Pass-through value for Mux
}

// NewMuxUploadTask creates a new task for uploading a video to Mux
func NewMuxUploadTask(payload MuxUploadPayload) (*asynq.Task, error) {
	data, err := json.Marshal(payload)
	if err != nil {
		return nil, err
	}
	return asynq.NewTask(
		MuxUploadTaskType,
		data,
		asynq.Queue(asyncq.QueueVideoTranscoder),
	), nil
}

// HandleMuxUploadTask returns a handler function for the Mux upload task
func HandleMuxUploadTask(repos repository.IRepositories) func(context.Context, *asynq.Task) error {
	return func(ctx context.Context, t *asynq.Task) error {
		var payload MuxUploadPayload
		if err := json.Unmarshal(t.Payload(), &payload); err != nil {
			return fmt.Errorf("json.Unmarshal failed: %v: %w", err, asynq.SkipRetry)
		}

		ctx, cancel := context.WithTimeout(ctx, uploadTimeout)
		defer cancel()

		logger := log.Ctx(ctx).With().
			Uint32("videoId", payload.VideoID).
			Str("title", payload.Title).
			Logger()

		logger.Info().Msg("Starting Mux upload task")

		progressCallback := func(progress float64) {
			logger.Info().
				Float64("progress", progress).
				Msg("Mux upload progress")
		}

		logger.Info().Msg("Fetching video details from database")
		video, err := repos.VideoRepo().FindByID(ctx, payload.VideoID, repositories.CustomPreload{
			Key: "VideoUpload",
		})
		if err != nil {
			logger.Error().Err(err).Msg("Failed to find video in database")
			return fmt.Errorf("failed to find video: %w", err)
		}

		logger.Info().Msg("Downloading video from Google Cloud Storage")
		tmpFile, err := gcs.DownloadVideoFromGCS(ctx, video.VideoUpload.Filename, nil)
		if err != nil {
			logger.Error().
				Err(err).
				Str("videoTitle", video.Title).
				Msg("Failed to download video from GCS")
			return fmt.Errorf("failed to download video: %w", err)
		}
		defer os.Remove(tmpFile.Name())
		logger.Info().Str("tempFile", tmpFile.Name()).Msg("Video downloaded successfully")

		// Add watermark to the downloaded video
		logger.Info().Msg("Adding watermark to video")
		watermarkedFile, err := AddWatermarkToVideo(ctx, tmpFile.Name())
		if err != nil {
			logger.Error().Err(err).Msg("Failed to add watermark to video")
			return fmt.Errorf("failed to add watermark: %w", err)
		}
		defer os.Remove(watermarkedFile.Name())
		logger.Info().Str("watermarkedFile", watermarkedFile.Name()).Msg("Watermark added successfully")

		// Initialize the video service
		videoService, err := mux.NewVideoService(ctx)
		if err != nil {
			return fmt.Errorf("failed to create video service: %w", err)
		}

		// Create video metadata
		metadata := mux.VideoMetadata{
			Title:       payload.Title,
			Description: payload.Description,
			PassThrough: payload.PassThrough,
		}

		// Check if a Mux platform record already exists for this video
		platformRecord, err := handleVideoPlatformRecord(ctx, repos.VideoPlatformRepo(), payload.VideoID, enums.VideoPlatformMux, enums.VideoPlatformStatusSyncing, nil)
		if err != nil {
			logger.Error().
				Err(err).
				Msg("Failed to handle video platform record")
			return fmt.Errorf("failed to handle video platform record: %w", err)
		}

		// Upload the watermarked video to Mux
		result, err := videoService.UploadVideo(watermarkedFile.Name(), metadata, payload.IsFree, progressCallback)
		if err != nil {
			if platformRecord != nil {
				updateVideoPlatformStatus(ctx, repos.VideoPlatformRepo(), platformRecord.ID, enums.VideoPlatformStatusError)
			}
			return fmt.Errorf("failed to upload video: %w", err)
		}

		// Update platform record with successful upload info
		if err := updateVideoPlatformRecord(ctx, repos.VideoPlatformRepo(), platformRecord, result.Asset.ID, enums.VideoPlatformStatusAvailable); err != nil {
			logger.Error().
				Err(err).
				Uint32("platformRecordId", platformRecord.ID).
				Str("assetId", result.Asset.ID).
				Msg("Failed to update video platform record")
			return fmt.Errorf("failed to update video platform record: %w", err)
		}

		logger.Info().
			Str("assetId", result.Asset.ID).
			Msg("Video upload successful")

		return nil
	}
}

// Helper function to add watermark to video
func AddWatermarkToVideo(ctx context.Context, inputFile string) (*os.File, error) {
	// Create temporary file for watermarked output
	outputFile, err := os.CreateTemp("", "watermarked-*.mp4")
	if err != nil {
		return nil, fmt.Errorf("failed to create temp file: %w", err)
	}

	// Get watermark path from environment or use default
	watermarkPath := os.Getenv("VIDEO_WATERMARK_PATH")
	if watermarkPath == "" {
		// If not set, use the default path relative to the current working directory
		wd, err := os.Getwd()
		if err != nil {
			os.Remove(outputFile.Name())
			return nil, fmt.Errorf("failed to get working directory: %w", err)
		}
		watermarkPath = filepath.Join(wd, constants.WatermarkImagePath)
	}

	// Verify watermark file exists and is readable
	if _, err := os.Stat(watermarkPath); err != nil {
		os.Remove(outputFile.Name())
		return nil, fmt.Errorf("watermark file not found at %s: %w", watermarkPath, err)
	}

	// Initialize video service
	videoSvc, err := video.NewVideoService("")
	if err != nil {
		os.Remove(outputFile.Name())
		return nil, fmt.Errorf("failed to initialize video service: %w", err)
	}

	// Add watermark
	if err := videoSvc.AddWatermark(inputFile, watermarkPath, outputFile.Name(), "top-right"); err != nil {
		os.Remove(outputFile.Name())
		return nil, fmt.Errorf("failed to add watermark: %w", err)
	}

	return outputFile, nil
}
