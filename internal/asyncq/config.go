package asyncq

import (
	"context"
	"strings"
	"time"
	"vibico-education-api/pkg/helpers"

	"github.com/hibiken/asynq"
	"github.com/rs/zerolog/log"
)

var defaultManager = NewManager()

type Manager struct {
	client *asynq.Client
	Opt    asynq.RedisClientOpt
}

func NewManager() *Manager {
	cfg := helpers.GetRedisConfig()

	return &Manager{
		Opt: asynq.RedisClientOpt{
			Addr:     cfg.Addr,
			Password: cfg.Password,
			DB:       cfg.DB,
		},
	}
}

func (m *Manager) InitAsynqClient() {
	if m.client != nil {
		return
	}

	log.Info().Msgf("Initialized Asynq Redis client with address: %s", m.Opt.Addr)
	m.client = asynq.NewClient(m.Opt)
	log.Info().Msg("Asynq client initialized")
}

func (m *Manager) CloseAsynqClient() {
	if m.client != nil {
		m.client.Close()
	}

	m.client = nil
	log.Info().Msg("Asynq client closed")
}

func (m *Manager) EnqueueTask(ctx context.Context, task *asynq.Task, opts ...asynq.Option) (*asynq.TaskInfo, error) {
	if m.client == nil {
		log.Debug().Ctx(ctx).Msg("Async task client not initialized. Initializing...")
		m.InitAsynqClient()
	}

	opts = append(opts,
		asynq.MaxRetry(3),
		asynq.Timeout(1*time.Hour),
		asynq.Retention(24*time.Hour),
	)

	taskInfo, err := m.client.Enqueue(task, opts...)
	if err != nil {
		log.Error().Err(err).Ctx(ctx).Msg("Could not enqueue task")
		return nil, err
	}

	log.Info().Ctx(ctx).Str("taskId", taskInfo.ID).Str("queue", taskInfo.Queue).Msg("Enqueued task")
	return taskInfo, nil
}

func (m *Manager) NewServer(concurrency int, queues map[string]int) *asynq.Server {
	return asynq.NewServer(m.Opt, asynq.Config{
		Concurrency: concurrency,
		Queues:      queues,
		Logger:      NewLogger(),
	})
}

func GetManager() *Manager {
	return defaultManager
}

// Queue config
const (
	QueueCritical = "critical"
	QueueDefault  = "default"
	QueueLow      = "low"

	QueueVideoTranscoder = "video-transcoder"
)

var availableQueues = map[string]int{
	QueueCritical:        6,
	QueueDefault:         3,
	QueueLow:             1,
	QueueVideoTranscoder: 6,
}

func ParseQueues(requested []string) map[string]int {
	if len(requested) == 0 {
		return availableQueues
	}

	queuesConfig := make(map[string]int)
	for _, q := range requested {
		q = strings.TrimSpace(q)
		weight, ok := availableQueues[q]
		if !ok {
			log.Fatal().Msgf("Queue '%s' is not in allowed queues list", q)
		}
		queuesConfig[q] = weight
	}

	return queuesConfig
}
