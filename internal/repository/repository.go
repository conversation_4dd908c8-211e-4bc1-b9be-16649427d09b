package repository

import (
	"vibico-education-api/internal/repository/repositories"

	"gorm.io/gorm"
)

type IRepositories interface {
	GetDb() *gorm.DB
	UserRepo() *repositories.UserRepository
	TeacherRepo() *repositories.TeacherRepository
	DrillRepo() *repositories.DrillRepository
	CourseRepo() *repositories.CourseRepository
	CourseSectionItemRepo() *repositories.CourseSectionItemRepository
	CourseSectionRepo() *repositories.CourseSectionRepository
	SkillRepo() *repositories.SkillRepository
	DiagramRepo() *repositories.DiagramRepository
	TemplateDiagramRepo() *repositories.TemplateDiagramRepository
	DrillSkillRepo() *repositories.DrillSkillRepository
	AdminRepo() *repositories.AdminRepository
	CourseUserRepo() *repositories.CourseUserRepository
	UserCourseSectionRepo() *repositories.UserCourseSectionRepository
	CourseSectionItemDrillRepo() *repositories.CourseSectionItemDrillRepository
	VideoUploadRepo() *repositories.VideoUploadRepository
	VideoRepo() *repositories.VideoRepository
	VideoPlatformRepo() *repositories.VideoPlatformRepository
	VideoProgressRepo() *repositories.VideoProgressRepository
	UserCourseSectionItemRepo() *repositories.UserCourseSectionItemRepository
	CommentRepo() *repositories.CommentRepository
	PracticeSubmissionRepo() *repositories.PracticeSubmissionRepository
	CensorHistoryRepo() *repositories.CensorHistoryRepository
	YoutubeTokenRepo() *repositories.YoutubeTokenRepository
	UserDrillRepo() *repositories.UserDrillRepository
	PackageDealRepo() *repositories.PackageDealRepository
	CoursePackageRepo() *repositories.CoursePackageRepository
	NotificationRepo() *repositories.NotificationRepository
}

type RepositoryRegister struct {
	db                         *gorm.DB
	userRepo                   *repositories.UserRepository
	drillRepo                  *repositories.DrillRepository
	courseRepo                 *repositories.CourseRepository
	teacherRepository          *repositories.TeacherRepository
	courseSectionItemRepo      *repositories.CourseSectionItemRepository
	courseSectionRepo          *repositories.CourseSectionRepository
	skillRepo                  *repositories.SkillRepository
	diagramRepo                *repositories.DiagramRepository
	templateDiagram            *repositories.TemplateDiagramRepository
	drillSkillRepo             *repositories.DrillSkillRepository
	adminRepo                  *repositories.AdminRepository
	courseUserRepo             *repositories.CourseUserRepository
	userCourseSectionRepo      *repositories.UserCourseSectionRepository
	courseSectionItemDrillRepo *repositories.CourseSectionItemDrillRepository
	videoUploadRepo            *repositories.VideoUploadRepository
	videoRepo                  *repositories.VideoRepository
	videoPlatformRepo          *repositories.VideoPlatformRepository
	videoProgressRepo          *repositories.VideoProgressRepository
	userCourseSectionItemRepo  *repositories.UserCourseSectionItemRepository
	commentRepo                *repositories.CommentRepository
	practiceSubmissionRepo     *repositories.PracticeSubmissionRepository
	youtubeTokenRepo           *repositories.YoutubeTokenRepository
	censorHistoryRepo          *repositories.CensorHistoryRepository
	userDrillRepo              *repositories.UserDrillRepository
	packageDealRepo            *repositories.PackageDealRepository
	coursePackageRepo          *repositories.CoursePackageRepository
	notificationRepo           *repositories.NotificationRepository
}

func NewRepositoryRegister(db *gorm.DB) *RepositoryRegister {
	return &RepositoryRegister{
		db: db,
	}
}

func (r *RepositoryRegister) GetDb() *gorm.DB {
	return r.db
}

func (r *RepositoryRegister) UserRepo() *repositories.UserRepository {
	if r.userRepo == nil {
		r.userRepo = repositories.NewUserRepository(r.db)
	}
	return r.userRepo
}

func (r *RepositoryRegister) DrillRepo() *repositories.DrillRepository {
	if r.drillRepo == nil {
		return repositories.NewDrillRepository(r.db)
	}
	return r.drillRepo

}

func (r *RepositoryRegister) CourseSectionRepo() *repositories.CourseSectionRepository {
	if r.courseSectionRepo == nil {
		r.courseSectionRepo = repositories.NewCourseSectionRepository(r.db)
	}
	return r.courseSectionRepo
}

func (r *RepositoryRegister) CourseRepo() *repositories.CourseRepository {
	if r.courseRepo == nil {
		return repositories.NewCourseRepository(r.db)
	}
	return r.courseRepo
}

func (r *RepositoryRegister) CourseSectionItemRepo() *repositories.CourseSectionItemRepository {
	if r.courseSectionItemRepo == nil {
		r.courseSectionItemRepo = repositories.NewCourseSectionItemRepository(r.db)
	}

	return r.courseSectionItemRepo
}

func (r *RepositoryRegister) TeacherRepo() *repositories.TeacherRepository {
	if r.teacherRepository == nil {
		r.teacherRepository = repositories.NewTeacherRepository(r.db)
	}

	return r.teacherRepository
}

func (r *RepositoryRegister) SkillRepo() *repositories.SkillRepository {
	if r.skillRepo == nil {
		r.skillRepo = repositories.NewSkillRepository(r.db)
	}

	return r.skillRepo
}

func (r *RepositoryRegister) DiagramRepo() *repositories.DiagramRepository {
	if r.diagramRepo == nil {
		r.diagramRepo = repositories.NewDiagramRepository(r.db)
	}

	return r.diagramRepo
}

func (r *RepositoryRegister) TemplateDiagramRepo() *repositories.TemplateDiagramRepository {
	if r.templateDiagram == nil {
		r.templateDiagram = repositories.NewTemplateDiagramRepository(r.db)
	}

	return r.templateDiagram
}

func (r *RepositoryRegister) DrillSkillRepo() *repositories.DrillSkillRepository {
	if r.drillSkillRepo == nil {
		r.drillSkillRepo = repositories.NewDrillSkillRepository(r.db)
	}
	return r.drillSkillRepo
}

func (r *RepositoryRegister) AdminRepo() *repositories.AdminRepository {
	if r.adminRepo == nil {
		r.adminRepo = repositories.NewAdminRepository(r.db)
	}
	return r.adminRepo
}

func (r *RepositoryRegister) CourseUserRepo() *repositories.CourseUserRepository {
	if r.courseUserRepo == nil {
		r.courseUserRepo = repositories.NewCourseUserRepository(r.db)
	}
	return r.courseUserRepo
}

func (r *RepositoryRegister) UserCourseSectionRepo() *repositories.UserCourseSectionRepository {
	if r.userCourseSectionRepo == nil {
		r.userCourseSectionRepo = repositories.NewUserCourseSectionRepository(r.db)
	}
	return r.userCourseSectionRepo
}

func (r *RepositoryRegister) CourseSectionItemDrillRepo() *repositories.CourseSectionItemDrillRepository {
	if r.courseSectionItemDrillRepo == nil {
		r.courseSectionItemDrillRepo = repositories.NewCourseSectionItemDrillRepository(r.db)
	}
	return r.courseSectionItemDrillRepo
}

func (r *RepositoryRegister) VideoUploadRepo() *repositories.VideoUploadRepository {
	if r.videoUploadRepo == nil {
		r.videoUploadRepo = repositories.NewVideoUploadRepository(r.db)
	}
	return r.videoUploadRepo
}

func (r *RepositoryRegister) VideoRepo() *repositories.VideoRepository {
	if r.videoRepo == nil {
		r.videoRepo = repositories.NewVideoRepository(r.db)
	}
	return r.videoRepo
}

func (r *RepositoryRegister) VideoPlatformRepo() *repositories.VideoPlatformRepository {
	if r.videoPlatformRepo == nil {
		r.videoPlatformRepo = repositories.NewVideoPlatformRepository(r.db)
	}
	return r.videoPlatformRepo
}

func (r *RepositoryRegister) VideoProgressRepo() *repositories.VideoProgressRepository {
	if r.videoProgressRepo == nil {
		r.videoProgressRepo = repositories.NewVideoProgressRepository(r.db)
	}
	return r.videoProgressRepo
}

func (r *RepositoryRegister) UserCourseSectionItemRepo() *repositories.UserCourseSectionItemRepository {
	if r.userCourseSectionItemRepo == nil {
		r.userCourseSectionItemRepo = repositories.NewUserCourseSectionItemRepository(r.db)
	}
	return r.userCourseSectionItemRepo
}

func (r *RepositoryRegister) CommentRepo() *repositories.CommentRepository {
	if r.commentRepo == nil {
		r.commentRepo = repositories.NewCommentRepository(r.db)
	}

	return r.commentRepo
}

func (r *RepositoryRegister) PracticeSubmissionRepo() *repositories.PracticeSubmissionRepository {
	if r.practiceSubmissionRepo == nil {
		r.practiceSubmissionRepo = repositories.NewPracticeSubmissionRepository(r.db)
	}

	return r.practiceSubmissionRepo
}

func (r *RepositoryRegister) YoutubeTokenRepo() *repositories.YoutubeTokenRepository {
	if r.youtubeTokenRepo == nil {
		r.youtubeTokenRepo = repositories.NewYoutubeTokenRepository(r.db)
	}
	return r.youtubeTokenRepo
}

func (r *RepositoryRegister) CensorHistoryRepo() *repositories.CensorHistoryRepository {
	if r.censorHistoryRepo == nil {
		r.censorHistoryRepo = repositories.NewCensorHistoryRepository(r.db)
	}
	return r.censorHistoryRepo
}

func (r *RepositoryRegister) UserDrillRepo() *repositories.UserDrillRepository {
	if r.userDrillRepo == nil {
		r.userDrillRepo = repositories.NewUserDrillRepository(r.db)
	}
	return r.userDrillRepo
}

func (r *RepositoryRegister) PackageDealRepo() *repositories.PackageDealRepository {
	if r.packageDealRepo == nil {
		r.packageDealRepo = repositories.NewPackageDealRepository(r.db)
	}
	return r.packageDealRepo
}

func (r *RepositoryRegister) CoursePackageRepo() *repositories.CoursePackageRepository {
	if r.coursePackageRepo == nil {
		r.coursePackageRepo = repositories.NewCoursePackageRepository(r.db)
	}
	return r.coursePackageRepo
}

func (r *RepositoryRegister) NotificationRepo() *repositories.NotificationRepository {
	if r.notificationRepo == nil {
		r.notificationRepo = repositories.NewNotificationRepository(r.db)
	}
	return r.notificationRepo
}
