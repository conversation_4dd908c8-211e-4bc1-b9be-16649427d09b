package repositories

import (
	"context"
	"fmt"
	"vibico-education-api/internal/enums"
	"vibico-education-api/internal/models"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

type CourseSectionItemRepository struct {
	Repository
}

func NewCourseSectionItemRepository(db *gorm.DB) *CourseSectionItemRepository {
	return &CourseSectionItemRepository{
		Repository: Repository{
			db:        db,
			tableName: "course_section_items",
		},
	}
}

// IMPORTANT: The selectFields parameter must include the "Position" field
// to ensure the position increment logic works correctly.
// Not including "Position" in selectFields will cause incorrect item ordering.
func (r *CourseSectionItemRepository) Create(
	ctx context.Context, sectionItem *models.CourseSectionItem, courseId uint32, selectFields []string,
) error {
	log.Debug().Ctx(ctx).Msg("CourseSectionItemRepository.Create")

	err := TransactionWithLogging(r.db.WithContext(ctx), func(tx *gorm.DB) error {
		var maxPosition uint32
		err := tx.Table(r.tableName).
			Where("course_section_id = ?", sectionItem.CourseSectionId).
			Order("position DESC").
			Limit(1).
			Pluck("position", &maxPosition).Error

		if err != nil && err != gorm.ErrRecordNotFound {
			return err
		}

		if maxPosition == 0 {
			sectionItem.Position = 1
		} else {
			sectionItem.Position = maxPosition + 1
		}

		if len(selectFields) > 0 {
			tx = tx.Select(selectFields)
		}

		if err := tx.Create(&sectionItem).Error; err != nil {
			return err
		}

		return tx.Exec(courseIncreaseSectionItemCountSQL, courseId).Error
	})

	return err
}

func (r *CourseSectionItemRepository) Update(ctx context.Context, sectionItem *models.CourseSectionItem, courseId uint32, selectFields []string) error {
	log.Debug().Ctx(ctx).Msg("CourseSectionItemRepository.Update")

	err := TransactionWithLogging(r.db.WithContext(ctx), func(tx *gorm.DB) error {
		if len(selectFields) > 0 {
			tx = tx.Select(selectFields)
		}

		if err := tx.Updates(&sectionItem).Error; err != nil {
			return err
		}

		return tx.Exec(courseUpdateToDraft, courseId).Error
	})
	if err != nil {
		return err
	}

	return nil
}

func (r *CourseSectionItemRepository) UpdateWithNestedAttrs(ctx context.Context, item *models.CourseSectionItem, courseId uint32, selectedFields []string) error {
	log.Debug().Ctx(ctx).Msg("CourseSectionItemRepository.UpdateWithNestedAttrs")

	return TransactionWithLogging(r.db.WithContext(ctx), func(tx *gorm.DB) error {
		itemDrillRepo := NewCourseSectionItemDrillRepository(tx)
		if err := itemDrillRepo.DeleteByItemId(ctx, item.ID); err != nil {
			return err
		}

		if err := tx.Select(selectedFields).Updates(item).Error; err != nil {
			return err
		}
		return tx.Exec(courseUpdateToDraft, courseId).Error
	})
}

// TODO: handle related videos and video platforms
func (r *CourseSectionItemRepository) Delete(
	ctx context.Context, sectionItem *models.CourseSectionItem, courseId uint32,
) error {
	log.Debug().Ctx(ctx).Msg("CourseSectionItemRepository.Delete")

	return TransactionWithLogging(r.db.WithContext(ctx), func(tx *gorm.DB) error {
		err := tx.Select("CourseSectionItemDrills").Select("Videos").Delete(&sectionItem).Error
		if err != nil {
			return err
		}

		return tx.Exec(courseDecreaseSectionItemCountSQL, 1, courseId).Error
	})
}

func (r *CourseSectionItemRepository) DeleteById(ctx context.Context, id uint32) error {
	log.Debug().Ctx(ctx).Msg("CourseSectionItemRepository.DeleteById")

	result := r.db.WithContext(ctx).Delete(&models.CourseSectionItem{ID: id})
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("no record found with id: %d", id)
	}

	return nil
}

func (r *CourseSectionItemRepository) FindByIdAndTeacherCourse(
	ctx context.Context, id, courseSectionId, courseId, teacherId uint32, preloads ...CustomPreload,
) (*models.CourseSectionItem, error) {
	log.Debug().Ctx(ctx).Msg("CourseSectionItemRepository.FindByIdAndTeacherCourse")
	var sectionItem models.CourseSectionItem

	query := r.db.WithContext(ctx).Model(sectionItem)
	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	err := query.
		Select(courseSectionItemSelectSQL).
		Joins(courseSectionItemInnerJoinSectionSQL).
		Joins(courseSectionInnerJoinCourseSQL).
		Scopes(r.courseSectionIdEq(&courseSectionId)).
		Where("course_sections.course_id = ? AND courses.teacher_id = ?", courseId, teacherId).
		Take(&sectionItem, id).Error

	return &sectionItem, err
}

func (r *CourseSectionItemRepository) FindById(
	ctx context.Context, id uint32, preloads ...CustomPreload,
) (*models.CourseSectionItem, error) {
	log.Debug().Ctx(ctx).Msg("CourseSectionItemRepository.FindById")
	var sectionItem models.CourseSectionItem

	query := r.db.WithContext(ctx).Model(sectionItem)
	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	err := query.Take(&sectionItem, id).Error

	return &sectionItem, err
}

func (r *CourseSectionItemRepository) FindByIdAndCourseId(
	ctx context.Context, id, courseId uint32, preloads ...CustomPreload,
) (*models.CourseSectionItem, error) {
	log.Debug().Ctx(ctx).Msg("CourseSectionItemRepository.FindByIdAndCourseId")

	var sectionItem models.CourseSectionItem

	query := r.db.WithContext(ctx).Model(sectionItem)
	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	err := query.Joins(courseSectionItemInnerJoinSectionSQL).
		Where("course_sections.course_id = ?", courseId).
		Select(courseSectionItemSelectSQL).
		Take(&sectionItem, id).Error

	return &sectionItem, err
}

func (r *CourseSectionItemRepository) FindByIDAndSectionID(
	ctx context.Context, id, sectionId uint32, preloads ...CustomPreload,
) (*models.CourseSectionItem, error) {
	log.Debug().Ctx(ctx).Msg("CourseSectionItemRepository.FindByIDAndSectionID")

	var sectionItem models.CourseSectionItem

	query := r.db.WithContext(ctx).Model(sectionItem)
	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	err := query.Select(courseSectionItemSelectSQL).
		Scopes(r.courseSectionIdEq(&sectionId)).
		Take(&sectionItem, id).Error

	return &sectionItem, err
}

func (r *CourseSectionItemRepository) FindBySlugAndCourseId(
	ctx context.Context, slug string, courseId uint32, preloads ...CustomPreload,
) (*models.CourseSectionItem, error) {
	log.Debug().Ctx(ctx).Msg("CourseSectionItemRepository.FindBySlugAndCourseId")

	var sectionItem models.CourseSectionItem

	query := r.db.WithContext(ctx).Model(sectionItem)
	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	err := query.Joins(courseSectionItemInnerJoinSectionSQL).
		Where("course_sections.course_id = ?", courseId).
		Where(gorm.Expr("course_section_items.slug = ?", slug)).
		Select(courseSectionItemSelectSQL).
		Take(&sectionItem).Error

	return &sectionItem, err
}

func (r *CourseSectionItemRepository) FindUserCurrentSectionItem(
	ctx context.Context, courseId, userId uint32, preloads ...CustomPreload,
) (*models.CourseSectionItem, error) {
	log.Debug().Ctx(ctx).Msg("CourseSectionItemRepository.FindUserCurrentSectionItem")

	var sectionItem models.CourseSectionItem

	query := r.db.WithContext(ctx).Model(sectionItem)
	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	fetchQuery := query.Session(&gorm.Session{})
	err := fetchQuery.Select(courseSectionItemSelectSQL).
		Joins(courseSectionItemLeftJoinSectionSQL).
		Where(courseSectionItemExistUserSectionItemSQL, userId).
		Where("course_sections.course_id = ?", courseId).
		Order("course_section_items.id desc").
		Take(&sectionItem).Error

	if err != nil || sectionItem.ID == 0 {
		err = query.Select(courseSectionItemSelectSQL).
			Joins(courseSectionItemInnerJoinSectionSQL).
			Where("course_sections.course_id = ?", courseId).
			Not(courseSectionItemExistUserSectionItemSQL, userId).
			Order("course_section_items.id asc").
			Take(&sectionItem).Error
	}

	return &sectionItem, err
}

func (r *CourseSectionItemRepository) FindBySlugAndTeacherWithPractices(
	ctx context.Context,
	courseSlug, sectionItemSlug string,
	teacherId uint32,
) (*models.CourseSectionItem, error) {
	log.Debug().Ctx(ctx).Msg("CourseSectionItemRepository.FindBySlugAndTeacher")

	var sectionItem models.CourseSectionItem

	err := r.db.WithContext(ctx).
		Joins(courseSectionItemInnerJoinSectionSQL).
		Joins(courseSectionInnerJoinCourseSQL).
		Where("courses.slug = ? AND courses.teacher_id = ? AND course_section_items.slug = ?",
			courseSlug, teacherId, sectionItemSlug).
		Take(&sectionItem).Error

	if err != nil {
		return nil, err
	}

	return &sectionItem, nil
}

func (r *CourseSectionItemRepository) ListByTeacherCourse(
	ctx context.Context,
	courseId uint32,
	courseSectionId uint32,
	teacherId uint32,
	preloads ...CustomPreload,
) (*[]*models.CourseSectionItem, error) {
	sectionItems := []*models.CourseSectionItem{}
	query := r.db.WithContext(ctx).Model(&models.CourseSectionItem{})

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	err := query.Select(courseSectionItemSelectSQL).
		Joins(courseSectionItemInnerJoinSectionSQL).
		Joins(courseSectionInnerJoinCourseSQL).
		Scopes(r.courseSectionIdEq(&courseSectionId)).
		Where("course_sections.course_id = ? AND courses.teacher_id = ?", courseId, teacherId).Preload("CourseSection").
		Order(CSIOrderPositionAscSQL).
		Find(&sectionItems).Error

	return &sectionItems, err
}

func (r *CourseSectionItemRepository) ListByCourseSection(
	ctx context.Context,
	courseId uint32,
	courseSectionId uint32,
	preloads ...CustomPreload,
) (*[]*models.CourseSectionItem, error) {
	sectionItems := []*models.CourseSectionItem{}
	query := r.db.WithContext(ctx).Model(&models.CourseSectionItem{})

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	err := query.Joins(courseSectionItemInnerJoinSectionSQL).
		Where("course_sections.course_id = ? AND course_sections.id = ?", courseId, courseSectionId).
		Order(CSIOrderPositionAscSQL).
		Find(&sectionItems).Error

	return &sectionItems, err
}

func (r *CourseSectionItemRepository) ListByCourseSectionSlug(
	ctx context.Context,
	courseId uint32,
	courseSectionSlug string,
	preloads ...CustomPreload,
) (*[]*models.CourseSectionItem, error) {
	log.Debug().Ctx(ctx).Msg("CourseSectionItemRepository.ListByCourseSectionSlug")

	sectionItems := []*models.CourseSectionItem{}
	query := r.db.WithContext(ctx).Model(&models.CourseSectionItem{})

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	err := query.Joins(courseSectionItemInnerJoinSectionSQL).
		Where(gorm.Expr("course_sections.course_id = ? AND course_sections.slug = ?", courseId, courseSectionSlug)).
		Order(CSIOrderPositionAscSQL).
		Find(&sectionItems).Error

	return &sectionItems, err
}

func (r *CourseSectionItemRepository) GetUsersStats(
	ctx context.Context,
	sectionItemId uint32,
) (*models.SectionItemUsersStats, error) {
	log.Debug().Ctx(ctx).Msg("CourseSectionItemRepository.GetUsersStats")

	var stats models.SectionItemUsersStats

	err := r.db.WithContext(ctx).
		Table("course_section_items csi").
		Select(`
			COUNT(DISTINCT cu.user_id) as total_enrolled_users,
			COUNT(DISTINCT CASE WHEN ps.id IS NOT NULL THEN ps.user_id END) as total_submitted_users,
			COUNT(DISTINCT ucsi.user_id) as total_viewed_section_users
		`).
		Joins("JOIN course_sections cs ON cs.id = csi.course_section_id").
		Joins("JOIN course_users cu ON cu.course_id = cs.course_id AND cu.status != ?", enums.CourseUserStatusInvited).
		Joins("LEFT JOIN user_course_section_items ucsi ON ucsi.course_section_item_id = csi.id").
		Joins("LEFT JOIN practice_submissions ps ON ps.user_id = cu.user_id AND ps.practice_id = csi.id AND ps.practice_type = 'CourseSectionItem'").
		Where("csi.id = ?", sectionItemId).
		Scan(&stats).Error

	return &stats, err
}

func (r *CourseSectionItemRepository) SwapPosition(ctx context.Context, courseSectionId, courseSectionItemId uint32, newIndex int32) error {
	log.Debug().Ctx(ctx).Msg("CourseSectionItemRepository.SwapPosition")

	item, err := r.FindByIDAndSectionID(ctx, courseSectionItemId, courseSectionId)
	if err != nil {
		return err
	}

	err = r.switchTo(ctx, item, newIndex)
	if err != nil {
		log.Error().Err(err).Msg("Failed to switch position")
		return err
	}

	return nil
}

func (r *CourseSectionItemRepository) switchTo(ctx context.Context, item *models.CourseSectionItem, newIndex int32) error {
	log.Debug().Ctx(ctx).Msgf("CourseSectionItemRepository.switchTo: sectionID=%d, itemID=%d, newIndex=%d", item.CourseSectionId, item.ID, newIndex)

	err := TransactionWithLogging(r.db.WithContext(ctx), func(tx *gorm.DB) error {
		var newPosition uint32
		if err := tx.Table(r.tableName).
			Where("course_section_id = ?", item.CourseSectionId).
			Order("position ASC").
			Offset(int(newIndex)).
			Limit(1).
			Pluck("position", &newPosition).Error; err != nil {
			return err
		}

		if newPosition == item.Position {
			return nil
		}

		var condition string
		if item.Position > newPosition {
			condition = "? <= position AND position < ?"
			if err := tx.Table(r.tableName).
				Where(condition, newPosition, item.Position).
				Where("course_section_id = ?", item.CourseSectionId).
				Update("position", gorm.Expr("position + 1")).Error; err != nil {
				return err
			}
		} else {
			condition = "? < position AND position <= ?"
			if err := tx.Table(r.tableName).
				Where(condition, item.Position, newPosition).
				Where("course_section_id = ?", item.CourseSectionId).
				Update("position", gorm.Expr("position - 1")).Error; err != nil {
				return err
			}
		}

		item.Position = newPosition
		if err := tx.Table(r.tableName).
			Where("id = ?", item.ID).
			Update("position", newPosition).Error; err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return err
	}

	return nil
}
