package repositories

import (
	"context"
	"vibico-education-api/constants"
	"vibico-education-api/internal/models"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

type PackageDealRepository struct {
	Repository
}

func NewPackageDealRepository(db *gorm.DB) *PackageDealRepository {
	return &PackageDealRepository{
		Repository: Repository{
			db:        db,
			tableName: "package_deals",
		},
	}
}

func (r *PackageDealRepository) FindFundamentalPackage(ctx context.Context) (*[]*models.PackageDeal, error) {
	log.Debug().Ctx(ctx).Msg("PackageDealRepository.FindFundamentalPackage")

	var packageDeals []*models.PackageDeal
	if err := r.db.WithContext(ctx).
		Where("name IN (?)", []string{constants.CourseBasicPackage, constants.CourseAdvancePackage}).
		Find(&packageDeals).Error; err != nil {
		return nil, err
	}

	return &packageDeals, nil
}

func (r *PackageDealRepository) FindByID(ctx context.Context, id uint32) (*models.PackageDeal, error) {
	log.Debug().Ctx(ctx).Msg("PackageDealRepository.FindByID")

	var packageDeal models.PackageDeal

	if err := r.db.WithContext(ctx).
		First(&packageDeal, id).Error; err != nil {
		return nil, err
	}

	return &packageDeal, nil
}

func (r *PackageDealRepository) FindBasicPackage(ctx context.Context) (*models.PackageDeal, error) {
	log.Debug().Ctx(ctx).Msg("PackageDealRepository.FindBasicPackage")

	var packageDeal models.PackageDeal

	if err := r.db.WithContext(ctx).
		Where("name = ?", constants.CourseBasicPackage).
		First(&packageDeal).Error; err != nil {
		return nil, err
	}

	return &packageDeal, nil
}
