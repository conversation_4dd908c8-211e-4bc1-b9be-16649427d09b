package repositories

import (
	"context"
	"vibico-education-api/internal/models"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

type SkillRepository struct {
	Repository
}

func NewSkillRepository(db *gorm.DB) *SkillRepository {
	return &SkillRepository{
		Repository: Repository{
			db:        db,
			tableName: "skills",
		},
	}
}

func (r *SkillRepository) ListByIds(ctx context.Context, ids []uint32) (*[]models.Skill, error) {
	log.Debug().Ctx(ctx).Msg("SkillRepository.ListByIds")
	var skills []models.Skill

	err := r.db.WithContext(ctx).Table(r.tableName).Select(skillSelectSQL).Where("skills.id in (?)", ids).Scan(&skills).Error

	return &skills, err
}

func (r *SkillRepository) All(ctx context.Context) (*[]models.Skill, error) {
	log.Debug().Ctx(ctx).Msg("SkillRepository.All")
	var skills []models.Skill

	err := r.db.WithContext(ctx).Table(r.tableName).Find(&skills).Error

	return &skills, err
}
