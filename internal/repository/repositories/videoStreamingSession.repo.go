package repositories

import (
	"context"
	"time"
	"vibico-education-api/internal/enums"
	"vibico-education-api/internal/models"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

type VideoStreamingSessionRepository struct {
	Repository
}

func NewVideoStreamingSessionRepository(db *gorm.DB) *VideoStreamingSessionRepository {
	return &VideoStreamingSessionRepository{
		Repository: Repository{db: db, tableName: "video_streaming_sessions"},
	}
}

func (r *VideoStreamingSessionRepository) FindByID(ctx context.Context, id uint32, preloads ...CustomPreload) (*models.VideoStreamingSession, error) {
	log.Debug().Ctx(ctx).Msg("VideoStreamingSessionRepository.FindByID")

	var videoStreamingSession models.VideoStreamingSession

	query := r.db.WithContext(ctx).Table(r.tableName)

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	if err := query.Take(&videoStreamingSession, id).Error; err != nil {
		return nil, err
	}

	return &videoStreamingSession, nil
}

func (r *VideoStreamingSessionRepository) FindBySessionToken(ctx context.Context, sessionToken string, preloads ...CustomPreload) (*models.VideoStreamingSession, error) {
	log.Debug().Ctx(ctx).Msg("VideoStreamingSessionRepository.FindBySessionToken")

	var videoStreamingSession models.VideoStreamingSession

	query := r.db.WithContext(ctx).Table(r.tableName).
		Where("session_token = ?", sessionToken)

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	if err := query.Take(&videoStreamingSession).Error; err != nil {
		return nil, err
	}

	return &videoStreamingSession, nil
}

func (r *VideoStreamingSessionRepository) FindActiveByUserAndVideo(ctx context.Context, userID, videoID uint32, preloads ...CustomPreload) (*models.VideoStreamingSession, error) {
	log.Debug().Ctx(ctx).Msg("VideoStreamingSessionRepository.FindActiveByUserAndVideo")

	var videoStreamingSession models.VideoStreamingSession

	query := r.db.WithContext(ctx).Table(r.tableName).
		Where("user_id = ? AND video_id = ? AND status = ?", userID, videoID, enums.VideoStreamingSessionStatusActive)

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	if err := query.Take(&videoStreamingSession).Error; err != nil {
		return nil, err
	}

	return &videoStreamingSession, nil
}

func (r *VideoStreamingSessionRepository) FindByUserID(ctx context.Context, userID uint32, preloads ...CustomPreload) ([]*models.VideoStreamingSession, error) {
	log.Debug().Ctx(ctx).Msg("VideoStreamingSessionRepository.FindByUserID")

	var videoStreamingSessions []*models.VideoStreamingSession

	query := r.db.WithContext(ctx).Table(r.tableName).
		Where("user_id = ?", userID)

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	if err := query.Find(&videoStreamingSessions).Error; err != nil {
		return nil, err
	}

	return videoStreamingSessions, nil
}

func (r *VideoStreamingSessionRepository) Create(ctx context.Context, videoStreamingSession *models.VideoStreamingSession, selectFields ...string) error {
	log.Debug().Ctx(ctx).Msg("VideoStreamingSessionRepository.Create")

	tx := r.db.WithContext(ctx)
	if len(selectFields) > 0 {
		tx = tx.Select(selectFields)
	}

	return tx.Create(videoStreamingSession).Error
}

func (r *VideoStreamingSessionRepository) Update(ctx context.Context, videoStreamingSession *models.VideoStreamingSession, selectFields ...string) error {
	log.Debug().Ctx(ctx).Msg("VideoStreamingSessionRepository.Update")

	tx := r.db.WithContext(ctx)
	if len(selectFields) > 0 {
		tx = tx.Select(selectFields)
	}

	return tx.Save(videoStreamingSession).Error
}

func (r *VideoStreamingSessionRepository) EndSession(ctx context.Context, id uint32) error {
	log.Debug().Ctx(ctx).Msg("VideoStreamingSessionRepository.EndSession")

	now := time.Now()

	updates := map[string]interface{}{
		"status":     enums.VideoStreamingSessionStatusEnded,
		"ended_at":   now,
		"updated_at": now,
	}

	return r.db.WithContext(ctx).Table(r.tableName).
		Where("id = ?", id).
		Updates(updates).Error
}

func (r *VideoStreamingSessionRepository) ExpireSession(ctx context.Context, id uint32) error {
	log.Debug().Ctx(ctx).Msg("VideoStreamingSessionRepository.ExpireSession")

	now := time.Now()

	updates := map[string]interface{}{
		"status":     enums.VideoStreamingSessionStatusExpired,
		"ended_at":   now,
		"updated_at": now,
	}

	return r.db.WithContext(ctx).Table(r.tableName).
		Where("id = ?", id).
		Updates(updates).Error
}

func (r *VideoStreamingSessionRepository) Delete(ctx context.Context, videoStreamingSession *models.VideoStreamingSession) error {
	log.Debug().Ctx(ctx).Msg("VideoStreamingSessionRepository.Delete")

	return r.db.WithContext(ctx).Delete(videoStreamingSession).Error
}
