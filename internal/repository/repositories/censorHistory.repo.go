package repositories

import (
	"context"
	"vibico-education-api/internal/enums"
	"vibico-education-api/internal/models"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

type CensorHistoryRepository struct {
	Repository
}

func NewCensorHistoryRepository(db *gorm.DB) *CensorHistoryRepository {
	return &CensorHistoryRepository{
		Repository: Repository{db: db, tableName: "censor_histories"},
	}
}

func (r *CensorHistoryRepository) CreateWithCourse(
	ctx context.Context,
	censorHistory *models.CensorHistory,
	course *models.Course,
	selectFields ...string,
) error {
	log.Debug().Ctx(ctx).Msg("CensorHistoryRepository.CreateWithCourse")

	return TransactionWithLogging(r.db.WithContext(ctx), func(tx *gorm.DB) error {
		if len(selectFields) > 0 {
			tx = tx.Select(selectFields)
		}

		if err := tx.Model(&models.CensorHistory{}).Create(censorHistory).Error; err != nil {
			return err
		}

		if err := tx.Exec("UPDATE courses SET status = ? WHERE id = ?", enums.CourseStatusRejected, course.ID).Error; err != nil {
			return err
		}

		return tx.Exec(teacherDecreaseCourseCountSQL, course.TeacherId).Error
	})
}

func (r *CensorHistoryRepository) CreateWithDrill(
	ctx context.Context,
	censorHistory *models.CensorHistory,
	drill *models.Drill,
	selectFields ...string,
) error {
	log.Debug().Ctx(ctx).Msg("CensorHistoryRepository.CreateWithDrill")

	return TransactionWithLogging(r.db.WithContext(ctx), func(tx *gorm.DB) error {
		if len(selectFields) > 0 {
			tx = tx.Select(selectFields)
		}

		if err := tx.Model(&models.CensorHistory{}).Create(censorHistory).Error; err != nil {
			return err
		}

		return tx.Exec("UPDATE drills SET censor = ? WHERE id = ?", enums.DrillCensorRejected, drill.ID).Error
	})
}
