package repositories

import (
	"context"
	"errors"
	"fmt"
	"vibico-education-api/internal/enums"
	adminInputs "vibico-education-api/internal/gqls/inputs/admins"
	publicInputs "vibico-education-api/internal/gqls/inputs/publics"
	teacherInputs "vibico-education-api/internal/gqls/inputs/teachers"
	userInputs "vibico-education-api/internal/gqls/inputs/users"
	"vibico-education-api/internal/models"
	translator "vibico-education-api/pkg/translators"

	"github.com/BehemothLtd/behemoth-pkg/golang/pagination"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

type DrillRepository struct {
	Repository
}

func NewDrillRepository(db *gorm.DB) *DrillRepository {
	return &DrillRepository{
		Repository: Repository{db: db, tableName: "drills"},
	}
}

func (r *DrillRepository) AdminList(ctx context.Context, args adminInputs.DrillInput, preloads ...CustomPreload) (*pagination.PaginationData, error) {
	log.Debug().Ctx(ctx).Msg("DrillRepository.AdminList")

	queryInput, paginationData := args.ToPaginationDataAndQuery()
	paginationData.Collection = []*models.Drill{}

	dbTables := r.db.WithContext(ctx).Model(&models.Drill{})

	for _, preload := range preloads {
		dbTables = dbTables.Preload(preload.Key, preload.Args...)
	}

	paginationScope, err := pagination.Paginate(ctx, dbTables.Scopes(
		r.isMaster(),
		r.isNotDraft(),
		r.titleCont(queryInput.TitleCont),
		r.levelIn(queryInput.LevelIn),
		r.skillIDIn(queryInput.SkillIDIn),
	), &paginationData, r.tableName)
	if err != nil {
		return &paginationData, err
	}

	err = dbTables.Distinct().Scopes(paginationScope).Order(drillsOrderByCensorAndIdSql).Find(&paginationData.Collection).Error

	return &paginationData, err
}

func (r *DrillRepository) ListByTeacher(
	ctx context.Context,
	args teacherInputs.DrillInput,
	teacherId uint32,
	preloads ...CustomPreload,
) (*pagination.PaginationData, error) {
	log.Debug().Ctx(ctx).Msg("DrillRepository.ListByTeacher")

	queryInput, paginationData := args.ToPaginationDataAndQuery()
	paginationData.Collection = []*models.Drill{}

	dbTables := r.db.WithContext(ctx).Model(&models.Drill{})
	for _, preload := range preloads {
		dbTables = dbTables.Preload(preload.Key, preload.Args...)
	}

	paginationScope, err := pagination.Paginate(ctx, dbTables.Scopes(
		r.ownerScope(teacherId, "Teacher"),
		r.titleCont(queryInput.TitleCont),
		r.levelIn(queryInput.LevelIn),
		r.skillIDIn(queryInput.SkillIDIn),
		r.idNotIn(queryInput.IdNotIn),
		r.isMaster(),
	), &paginationData, r.tableName)
	if err != nil {
		return &paginationData, err
	}

	err = dbTables.Distinct().Scopes(paginationScope).Order(drillsOrderByIdDescSql).
		Find(&paginationData.Collection).Error

	return &paginationData, err
}

func (r *DrillRepository) ListWithUser(
	ctx context.Context, args userInputs.DrillInput, userId uint32, preloads ...CustomPreload,
) (*pagination.PaginationData, error) {
	log.Debug().Ctx(ctx).Msg("DrillRepository.ListWithUser")

	queryInput, paginationData, orderClause := args.ToPaginationDataAndQuery()
	paginationData.Collection = []*models.Drill{}

	dbTables := r.db.WithContext(ctx).Model(&models.Drill{})

	for _, preload := range preloads {
		dbTables = dbTables.Preload(preload.Key, preload.Args...)
	}

	paginationScope, err := pagination.Paginate(ctx, dbTables.Scopes(
		r.isApproved(),
		r.isMaster(),
		r.titleCont(queryInput.TitleCont),
		r.levelIn(queryInput.LevelIn),
		r.skillIDIn(queryInput.SkillIDIn),
		r.publicOrBoughtDrill(userId),
	), &paginationData, r.tableName)
	if err != nil {
		return &paginationData, err
	}

	err = dbTables.Distinct().Scopes(paginationScope).Order(orderClause).Find(&paginationData.Collection).Error

	return &paginationData, err
}

func (r *DrillRepository) PublicList(ctx context.Context, args publicInputs.DrillInput, preloads ...CustomPreload) (*pagination.PaginationData, error) {
	log.Debug().Ctx(ctx).Msg("DrillRepository.PublicList")

	queryInput, paginationData, orderClause := args.ToPaginationDataAndQuery()
	paginationData.Collection = []*models.Drill{}

	dbTables := r.db.WithContext(ctx).Model(&models.Drill{})

	for _, preload := range preloads {
		dbTables = dbTables.Preload(preload.Key, preload.Args...)
	}

	paginationScope, err := pagination.Paginate(ctx, dbTables.Scopes(
		r.teacherActivated(),
		r.officialDrill(),
		r.isApproved(),
		r.isMaster(),
		r.titleCont(queryInput.TitleCont),
		r.levelIn(queryInput.LevelIn),
		r.skillIDIn(queryInput.SkillIDIn),
	), &paginationData, r.tableName)
	if err != nil {
		return &paginationData, err
	}

	err = dbTables.Distinct().Scopes(paginationScope).Order(orderClause).Find(&paginationData.Collection).Error

	return &paginationData, err
}

func (r *DrillRepository) PublicRelatedDrillList(
	ctx context.Context, args publicInputs.RelatedDrillInput, preloads ...CustomPreload,
) (*pagination.PaginationData, error) {
	log.Debug().Ctx(ctx).Msg("DrillRepository.PublicRelatedDrillList")

	queryInput, paginationData, orderClause := args.ToPaginationDataAndQuery()
	paginationData.Collection = []*models.Drill{}

	dbTables := r.db.WithContext(ctx).Model(&models.Drill{}).
		Where(gorm.Expr("drills.slug != ?", args.DrillSlug))

	for _, preload := range preloads {
		dbTables = dbTables.Preload(preload.Key, preload.Args...)
	}

	paginationScope, err := pagination.Paginate(ctx, dbTables.Scopes(
		r.teacherActivated(),
		r.officialDrill(),
		r.isMaster(),
		r.isApproved(),
		r.relatedDrills(queryInput.SkillIDIn, queryInput.LevelIn),
	), &paginationData, r.tableName)
	if err != nil {
		return &paginationData, err
	}

	err = dbTables.Distinct().Scopes(paginationScope).Order(orderClause).Find(&paginationData.Collection).Error

	return &paginationData, err
}

func (r *DrillRepository) FindByID(ctx context.Context, id uint32, preloads ...CustomPreload) (*models.Drill, error) {
	log.Debug().Ctx(ctx).Msg("DrillRepository.FindByID")

	var drill models.Drill

	query := r.db.WithContext(ctx).Table(r.tableName)

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	if err := query.Take(&drill, id).Error; err != nil {
		return nil, errors.New(translator.Translate(nil, "errDbMsg_notFound"))
	}

	return &drill, nil
}

func (r *DrillRepository) FindBySlug(ctx context.Context, slug string, preloads ...CustomPreload) (*models.Drill, error) {
	log.Debug().Ctx(ctx).Msg("DrillRepository.FindBySlug")

	var drill models.Drill

	query := r.db.WithContext(ctx).Table(r.tableName)

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	if err := query.Take(&drill, "slug = ?", slug).Error; err != nil {
		return nil, errors.New(translator.Translate(nil, "errDbMsg_notFound"))
	}

	return &drill, nil
}

func (r *DrillRepository) FindOfficialByID(
	ctx context.Context, id uint32, preloads ...CustomPreload) (*models.Drill, error,
) {
	log.Debug().Ctx(ctx).Msg("DrillRepository.FindOfficialByID")

	var drill models.Drill

	query := r.db.WithContext(ctx).Table(r.tableName).Scopes(
		r.officialDrill(), r.isMaster(), r.isApproved())

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	if err := query.Take(&drill, id).Error; err != nil {
		return nil, errors.New(translator.Translate(nil, "errDbMsg_notFound"))
	}

	return &drill, nil
}

func (r *DrillRepository) FindOfficialBySlug(
	ctx context.Context, slug string, preloads ...CustomPreload,
) (*models.Drill, error) {
	log.Debug().Ctx(ctx).Msg("DrillRepository.FindOfficialBySlug")

	var drill models.Drill

	query := r.db.WithContext(ctx).Table(r.tableName).Scopes(
		r.officialDrill(), r.isMaster(), r.isApproved())

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	if err := query.Take(&drill, "slug = ?", slug).Error; err != nil {
		return nil, errors.New(translator.Translate(nil, "errDbMsg_notFound"))
	}

	return &drill, nil
}

func (r *DrillRepository) FindByIDAndOwner(
	ctx context.Context, id, ownerId uint32, ownerType string, preloads ...CustomPreload,
) (*models.Drill, error) {
	log.Debug().Ctx(ctx).Msg("DrillRepository.FindByIDAndOwner")

	var drill models.Drill

	query := r.db.WithContext(ctx).Table(r.tableName).
		Scopes(r.ownerScope(ownerId, ownerType))

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	if err := query.Take(&drill, id).Error; err != nil {
		return nil, errors.New(translator.Translate(nil, "errDbMsg_notFound"))
	}

	return &drill, nil
}

// Find approved drill by id
func (r *DrillRepository) FindApprovedByID(ctx context.Context, id uint32, preloads ...CustomPreload) (*models.Drill, error) {
	log.Debug().Ctx(ctx).Msg("DrillRepository.FindApprovedByID")

	var drill models.Drill

	query := r.db.WithContext(ctx).Table(r.tableName).Scopes(r.isMaster(), r.isApproved())

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	if err := query.Take(&drill, id).Error; err != nil {
		return nil, errors.New(translator.Translate(nil, "errDbMsg_notFound"))
	}

	return &drill, nil
}

// Find approved drill by slug
func (r *DrillRepository) FindApprovedBySlug(ctx context.Context, slug string, preloads ...CustomPreload) (*models.Drill, error) {
	log.Debug().Ctx(ctx).Msg("DrillRepository.FindApprovedBySlug")

	var drill models.Drill

	query := r.db.WithContext(ctx).Table(r.tableName).
		Scopes(
			r.isMaster(),
			r.isApproved(),
			r.teacherActivated(),
			whereEq(r.field("slug"), &slug),
		)

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	if err := query.Take(&drill).Error; err != nil {
		return nil, errors.New(translator.Translate(nil, "errDbMsg_notFound"))
	}

	return &drill, nil
}

// Find approved drill with user data by id
func (r *DrillRepository) FindApprovedByIDWithUser(
	ctx context.Context, id, userId uint32, preloads ...CustomPreload,
) (*models.Drill, error) {
	log.Debug().Ctx(ctx).Msg("DrillRepository.FindApprovedByIDWithUser")

	var drill models.Drill

	query := r.db.WithContext(ctx).Table(r.tableName).
		Joins(drillLeftJoinUserDrillSql).
		Scopes(r.isMaster(), r.isApproved())

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	if err := query.Take(&drill, id).Error; err != nil {
		return nil, errors.New(translator.Translate(nil, "errDbMsg_notFound"))
	}

	return &drill, nil
}

// Find approved drill with user data by slug
func (r *DrillRepository) FindApprovedBySlugWithUser(
	ctx context.Context, drillSlug string, userId uint32, preloads ...CustomPreload,
) (*models.Drill, error) {
	log.Debug().Ctx(ctx).Msg("DrillRepository.FindApprovedBySlugWithUser")

	var drill models.Drill

	query := r.db.WithContext(ctx).Table(r.tableName).
		Joins(drillLeftJoinUserDrillSql).
		Scopes(whereEq(r.field("slug"), &drillSlug), r.isMaster(), r.isApproved())

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	if err := query.Take(&drill).Error; err != nil {
		return nil, errors.New(translator.Translate(nil, "errDbMsg_notFound"))
	}

	return &drill, nil
}

func (r *DrillRepository) FindBySlugAndOwner(
	ctx context.Context, slug string, ownerId uint32, ownerType string, preloads ...CustomPreload,
) (*models.Drill, error) {
	log.Debug().Ctx(ctx).Msg("DrillRepository.FindBySlugAndOwner")

	var drill models.Drill

	query := r.db.WithContext(ctx).Table(r.tableName).
		Scopes(r.ownerScope(ownerId, ownerType))

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	if err := query.Where(gorm.Expr("drills.slug = ?", slug)).Take(&drill).Error; err != nil {
		return nil, errors.New(translator.Translate(nil, "errDbMsg_notFound"))
	}

	return &drill, nil
}

func (r *DrillRepository) FindByIdAndItemId(ctx context.Context, id, itemId uint32, preloads ...CustomPreload) (*models.Drill, error) {
	log.Debug().Ctx(ctx).Msg("DrillRepository.FindByIdAndItemId")

	var drill models.Drill
	query := r.db.WithContext(ctx).Table(r.tableName)

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	if err := query.Joins(drillInnerJoinItemSql).
		Where("course_section_item_drills.course_section_item_id = ?", itemId).
		Take(&drill, id).Error; err != nil {
		return nil, errors.New(translator.Translate(nil, "errDbMsg_notFound"))
	}

	return &drill, nil
}

func (r *DrillRepository) AssociationCount(ctx context.Context, drill *models.Drill, association string) (int64, error) {
	log.Debug().Ctx(ctx).Msgf("DrillRepository.AssociationCount %s", association)

	associationHandler := r.db.Model(drill).Association(association)
	if err := associationHandler.Error; err != nil {
		return 0, err
	}

	return associationHandler.Count(), nil
}

func (r *DrillRepository) DeleteByIdAndOwner(ctx context.Context, id, ownerId uint32, ownerType string) error {
	log.Debug().Ctx(ctx).Msg("DrillRepository.DeleteByIdAndOwner")

	result := r.db.WithContext(ctx).Select("DrillSkills", "Diagrams").
		Delete(&models.Drill{ID: id, OwnerID: ownerId, OwnerType: ownerType})
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("no record found with id: %d", id)
	}

	return nil
}

func (r *DrillRepository) DeleteById(ctx context.Context, id uint32) error {
	log.Debug().Ctx(ctx).Msg("DrillRepository.DeleteById")

	result := r.db.WithContext(ctx).Select("DrillSkills", "Diagrams").Delete(&models.Drill{ID: id})
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("no record found with id: %d", id)
	}

	return nil
}

func (r *DrillRepository) Create(ctx context.Context, drill *models.Drill, selectFields []string) error {
	log.Debug().Ctx(ctx).Msg("DrillRepository.Create")

	return r.db.WithContext(ctx).Select(selectFields).Create(drill).Error
}

func (r *DrillRepository) Update(ctx context.Context, drill *models.Drill, selectFields ...string) error {
	log.Debug().Ctx(ctx).Msg("DrillRepository.Update")

	return r.db.WithContext(ctx).Select(selectFields).Updates(drill).Error
}

func (r *DrillRepository) UpdateWithNestedAttrs(ctx context.Context, drill *models.Drill, selectFields []string) error {
	log.Debug().Ctx(ctx).Msg("DrillRepository.UpdateWithNestedAttrs")

	return TransactionWithLogging(r.db.WithContext(ctx), func(tx *gorm.DB) error {
		drillSkillsRepo := NewDrillSkillRepository(tx)
		if err := drillSkillsRepo.DeleteDrillSkills(ctx, drill.ID); err != nil {
			return err
		}

		diagramRepo := NewDiagramRepository(tx)
		if err := diagramRepo.DeleteByParent(ctx, drill.ID, "Drill"); err != nil {
			return err
		}

		if err := tx.Select(selectFields).Updates(drill).Error; err != nil {
			return err
		}

		return nil
	})
}

func (r *DrillRepository) Submit(ctx context.Context, drill *models.Drill) error {
	log.Debug().Ctx(ctx).Msg("DrillRepository.Submit")

	return TransactionWithLogging(r.db.WithContext(ctx), func(tx *gorm.DB) error {
		drill.Censor = enums.DrillCensorSubmitted
		if err := tx.Select("Censor").Updates(&drill).Error; err != nil {
			return err
		}

		if err := tx.Model(&models.CensorHistory{}).
			Where("parent_id = ? AND parent_type = ?", drill.ID, enums.CensorHistoryParentDrill).
			Update("status", enums.CensorStatusSubmitted).Error; err != nil {
			return err
		}

		return nil
	})
}

func (r *DrillRepository) ApprovedDrill(ctx context.Context, drill *models.Drill) error {
	log.Debug().Ctx(ctx).Msg("DrillRepository.ApprovedDrill")

	return TransactionWithLogging(r.db.WithContext(ctx), func(tx *gorm.DB) error {
		if err := tx.Model(drill).Update("censor", enums.DrillCensorApproved).Error; err != nil {
			return err
		}

		// if err := tx.Model(&models.CensorHistory{}).
		// 	Where("parent_id = ? AND parent_type = ?", drill.ID, enums.CensorHistoryParentDrill).
		// 	Update("status", enums.CensorStatusSubmitted).Error; err != nil {
		// 	return err
		// }

		return nil
	})
}

func (r *DrillRepository) GetOwner(ctx context.Context, drill *models.Drill) (interface{}, error) {
	log.Debug().Ctx(ctx).Msg("DrillRepository.GetOwner")

	switch drill.OwnerType {
	case "Teacher":
		var teacher models.Teacher
		if err := r.db.First(&teacher, drill.OwnerID).Error; err != nil {
			return nil, err
		}
		return &teacher, nil
	case "Admin":
		var admin models.Admin
		if err := r.db.First(&admin, drill.OwnerID).Error; err != nil {
			return nil, err
		}
		return &admin, nil
	default:
		return nil, errors.New("unknown owner type")
	}
}
