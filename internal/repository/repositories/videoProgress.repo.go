package repositories

import (
	"context"
	"time"
	"vibico-education-api/internal/models"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

type VideoProgressRepository struct {
	Repository
}

func NewVideoProgressRepository(db *gorm.DB) *VideoProgressRepository {
	return &VideoProgressRepository{
		Repository: Repository{db: db, tableName: "video_progresses"},
	}
}

func (r *VideoProgressRepository) FindByID(ctx context.Context, id uint32, preloads ...CustomPreload) (*models.VideoProgress, error) {
	log.Debug().Ctx(ctx).Msg("VideoProgressRepository.FindByID")

	var videoProgress models.VideoProgress

	query := r.db.WithContext(ctx).Table(r.tableName)

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	if err := query.Take(&videoProgress, id).Error; err != nil {
		return nil, err
	}

	return &videoProgress, nil
}

func (r *VideoProgressRepository) FindByUserIDAndVideoID(ctx context.Context, userID, videoID uint32, preloads ...CustomPreload) (*models.VideoProgress, error) {
	log.Debug().Ctx(ctx).Msg("VideoProgressRepository.FindByUserIDAndVideoID")

	var videoProgress models.VideoProgress

	query := r.db.WithContext(ctx).Table(r.tableName).
		Where("user_id = ? AND video_id = ?", userID, videoID)

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	if err := query.Take(&videoProgress).Error; err != nil {
		return nil, err
	}

	return &videoProgress, nil
}

func (r *VideoProgressRepository) FindOrCreateByUserAndVideo(
	ctx context.Context, userID, videoID uint32,
) (*models.VideoProgress, error) {
	log.Debug().Ctx(ctx).Msg("VideoProgressRepository.FindOrCreateByUserAndVideo")

	vp := models.VideoProgress{UserID: userID, VideoID: videoID}

	if err := r.db.WithContext(ctx).Table(r.tableName).
		FirstOrCreate(&vp, vp).Error; err != nil {
		return nil, err
	}

	return &vp, nil
}

func (r *VideoProgressRepository) FindByUserID(ctx context.Context, userID uint32, preloads ...CustomPreload) ([]*models.VideoProgress, error) {
	log.Debug().Ctx(ctx).Msg("VideoProgressRepository.FindByUserID")

	var videoProgresses []*models.VideoProgress

	query := r.db.WithContext(ctx).Table(r.tableName).
		Where("user_id = ?", userID)

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	if err := query.Find(&videoProgresses).Error; err != nil {
		return nil, err
	}

	return videoProgresses, nil
}

func (r *VideoProgressRepository) Create(ctx context.Context, videoProgress *models.VideoProgress, selectFields ...string) error {
	log.Debug().Ctx(ctx).Msg("VideoProgressRepository.Create")

	tx := r.db.WithContext(ctx)
	if len(selectFields) > 0 {
		tx = tx.Select(selectFields)
	}

	return tx.Create(videoProgress).Error
}

func (r *VideoProgressRepository) Update(ctx context.Context, videoProgress *models.VideoProgress, selectFields ...string) error {
	log.Debug().Ctx(ctx).Msg("VideoProgressRepository.Update")

	tx := r.db.WithContext(ctx)
	if len(selectFields) > 0 {
		tx = tx.Select(selectFields)
	}

	return tx.Save(videoProgress).Error
}

func (r *VideoProgressRepository) UpdateProgress(ctx context.Context, id uint32, lastPosition int32, completed bool) error {
	log.Debug().Ctx(ctx).Msg("VideoProgressRepository.UpdateProgress")

	updates := map[string]interface{}{
		"last_position": lastPosition,
		"updated_at":    time.Now(),
	}

	if completed {
		updates["completed"] = true
		updates["completed_at"] = time.Now()
	}

	return r.db.WithContext(ctx).Table(r.tableName).
		Where("id = ?", id).
		Updates(updates).Error
}

func (r *VideoProgressRepository) Delete(ctx context.Context, videoProgress *models.VideoProgress) error {
	log.Debug().Ctx(ctx).Msg("VideoProgressRepository.Delete")

	return r.db.WithContext(ctx).Delete(videoProgress).Error
}
