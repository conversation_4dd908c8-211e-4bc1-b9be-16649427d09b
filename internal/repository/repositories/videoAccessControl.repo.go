package repositories

import (
	"context"
	"vibico-education-api/internal/models"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

type VideoAccessControlRepository struct {
	Repository
}

func NewVideoAccessControlRepository(db *gorm.DB) *VideoAccessControlRepository {
	return &VideoAccessControlRepository{
		Repository: Repository{db: db, tableName: "video_access_controls"},
	}
}

func (r *VideoAccessControlRepository) FindByID(ctx context.Context, id uint32, preloads ...CustomPreload) (*models.VideoAccessControl, error) {
	log.Debug().Ctx(ctx).Msg("VideoAccessControlRepository.FindByID")

	var videoAccessControl models.VideoAccessControl

	query := r.db.WithContext(ctx).Table(r.tableName)

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	if err := query.Take(&videoAccessControl, id).Error; err != nil {
		return nil, err
	}

	return &videoAccessControl, nil
}

func (r *VideoAccessControlRepository) FindByVideoID(ctx context.Context, videoID uint32, preloads ...CustomPreload) (*models.VideoAccessControl, error) {
	log.Debug().Ctx(ctx).Msg("VideoAccessControlRepository.FindByVideoID")

	var videoAccessControl models.VideoAccessControl

	query := r.db.WithContext(ctx).Table(r.tableName).
		Where("video_id = ?", videoID)

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	if err := query.Take(&videoAccessControl).Error; err != nil {
		return nil, err
	}

	return &videoAccessControl, nil
}

func (r *VideoAccessControlRepository) FindByPlanID(ctx context.Context, planID uint32, preloads ...CustomPreload) ([]*models.VideoAccessControl, error) {
	log.Debug().Ctx(ctx).Msg("VideoAccessControlRepository.FindByPlanID")

	var videoAccessControls []*models.VideoAccessControl

	query := r.db.WithContext(ctx).Table(r.tableName).
		Where("plan_id = ?", planID)

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	if err := query.Find(&videoAccessControls).Error; err != nil {
		return nil, err
	}

	return videoAccessControls, nil
}

func (r *VideoAccessControlRepository) Create(ctx context.Context, videoAccessControl *models.VideoAccessControl, selectFields ...string) error {
	log.Debug().Ctx(ctx).Msg("VideoAccessControlRepository.Create")

	tx := r.db.WithContext(ctx)
	if len(selectFields) > 0 {
		tx = tx.Select(selectFields)
	}

	return tx.Create(videoAccessControl).Error
}

func (r *VideoAccessControlRepository) Update(ctx context.Context, videoAccessControl *models.VideoAccessControl, selectFields ...string) error {
	log.Debug().Ctx(ctx).Msg("VideoAccessControlRepository.Update")

	tx := r.db.WithContext(ctx)
	if len(selectFields) > 0 {
		tx = tx.Select(selectFields)
	}

	return tx.Save(videoAccessControl).Error
}

func (r *VideoAccessControlRepository) Delete(ctx context.Context, videoAccessControl *models.VideoAccessControl) error {
	log.Debug().Ctx(ctx).Msg("VideoAccessControlRepository.Delete")

	return r.db.WithContext(ctx).Delete(videoAccessControl).Error
}
