package repositories

import (
	"fmt"
	"strings"
	"vibico-education-api/internal/enums"

	"gorm.io/gorm"
)

func (r *UserRepository) nameCont(nameCont *string) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if nameCont == nil || strings.TrimSpace(*nameCont) == "" {
			return db
		}
		sqlNameCont := fmt.Sprintf("%%%s%%", *nameCont)

		return db.Where("users.name ILIKE ?", sqlNameCont)
	}
}

func (r *UserRepository) phoneNumberCont(phoneNumberCont *string) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if phoneNumberCont == nil || strings.TrimSpace(*phoneNumberCont) == "" {
			return db
		}
		sqlPhoneNumberCont := fmt.Sprintf("%%%s%%", *phoneNumberCont)

		return db.Where("users.phone_number ILIKE ?", sqlPhoneNumberCont)
	}
}

func (r *UserRepository) practiceSubmissionStatusEq(statusEq *string, sectionItemId uint32) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if statusEq == nil || strings.TrimSpace(*statusEq) == "" {
			return db
		}

		if *statusEq == "notSubmitted" {
			return db.Where(`NOT EXISTS (
				SELECT 1 
				FROM practice_submissions ps 
				WHERE ps.user_id = users.id 
				AND ps.practice_id = ? 
				AND ps.practice_type = 'CourseSectionItem'
			)`, sectionItemId)
		}

		statusEnum, err := enums.ParsePracticeSubmissionStatus(*statusEq)
		if err != nil {
			return db
		}

		return db.Where(`EXISTS (
			SELECT 1 FROM practice_submissions ps 
			WHERE ps.user_id = users.id 
			AND ps.practice_id = ? 
			AND ps.practice_type = 'CourseSectionItem'
			AND ps.id = (
				SELECT ps2.id 
				FROM practice_submissions ps2 
				WHERE ps2.user_id = users.id 
				AND ps2.practice_id = ? 
				AND ps2.practice_type = 'CourseSectionItem'
				ORDER BY ps2.id DESC 
				LIMIT 1
			)
			AND ps.status = ?
		)`, sectionItemId, sectionItemId, statusEnum)
	}
}

func (r *UserRepository) activated() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		return db.Where("users.active = true")
	}
}
