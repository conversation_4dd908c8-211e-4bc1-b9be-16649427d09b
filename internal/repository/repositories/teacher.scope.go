package repositories

import (
	"fmt"
	"strings"

	"gorm.io/gorm"
)

func (r *TeacherRepository) nameCont(nameCont *string) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if nameCont == nil || strings.TrimSpace(*nameCont) == "" {
			return db
		}
		sqlNameCont := fmt.Sprintf("%%%s%%", *nameCont)

		return db.Where("teachers.name ILIKE ?", sqlNameCont)
	}
}

func (r *TeacherRepository) phoneNumberCont(phoneNumberCont *string) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if phoneNumberCont == nil || strings.TrimSpace(*phoneNumberCont) == "" {
			return db
		}
		sqlPhoneNumberCont := fmt.Sprintf("%%%s%%", *phoneNumberCont)

		return db.Where("teachers.phone_number ILIKE ?", sqlPhoneNumberCont)
	}
}

func (r *TeacherRepository) basicEnteredEq(value bool) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		return db.Where("teachers.basic_entered = ?", value)
	}
}

func (r *TeacherRepository) activated() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		return db.Where("teachers.active = true")
	}
}
