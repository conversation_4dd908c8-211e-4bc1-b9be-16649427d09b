package repositories

import (
	"strings"
	"vibico-education-api/internal/enums"
)

// Order SQL
const courseOrderByIdDescSql = "courses.id DESC"
const courseUserOrderByIdDescSql = "course_users.id DESC"
const courseOrderByUserProcessSql = "calculated_user_process DESC"
const courseOrderByStatusSql = "courses.status ASC, courses.id DESC"

// Select SQl
const courseSelectSql = `
	DISTINCT ON (courses.id)
	courses.id,
	courses.teacher_id,
	courses.title,
	courses.slug,
	courses.description,
	courses.status,
	courses.sale_price,
	courses.price,
	courses.bonus_point,
	courses.bonus_point_percent,
	courses.section_count,
	courses.section_item_count,
	courses.joined_user_count,
	courses.instructional_level,
	courses.banner,
	courses.is_public,
	courses.created_at,
	courses.updated_at`

const adminCourseSelectSql = `
	DISTINCT ON (courses.id, courses.status)
	courses.id,
	courses.teacher_id,
	courses.title,
	courses.slug,
	courses.description,
	courses.status,
	courses.sale_price,
	courses.price,
	courses.bonus_point,
	courses.bonus_point_percent,
	courses.section_count,
	courses.section_item_count,
	courses.joined_user_count,
	courses.banner,
	courses.is_public,
	courses.created_at,
	courses.updated_at`

const courseUserSelectSql = `
	DISTINCT ON (courses.id, average_rating)
	courses.id,
	courses.teacher_id,
	courses.title,
	courses.slug,
	courses.description,
	courses.sale_price,
	courses.price,
	courses.bonus_point,
	courses.bonus_point_percent,
	courses.banner,
	courses.is_public,
	courses.section_count,
	courses.section_item_count,
	courses.joined_user_count,
	courses.instructional_level,
	courses.created_at,
	courses.updated_at,
	COALESCE(ROUND(AVG(comments.rating), 1), 0) AS average_rating`

const userMyCourseWithCourseUserSelectSql = `
	DISTINCT ON (courses.id, course_users.id, average_rating)
	courses.id,
	courses.teacher_id,
	courses.title,
	courses.slug,
	courses.description,
	courses.sale_price,
	courses.price,
	courses.bonus_point,
	courses.bonus_point_percent,
	courses.banner,
	courses.is_public,
	courses.section_count,
	courses.section_item_count,
	courses.joined_user_count,
	courses.created_at,
	courses.updated_at,
  COALESCE(ROUND(AVG(comments.rating), 1), 0) AS average_rating`

const userCourseWithBestSellerSelectSql = `
		DISTINCT ON (courses.id, COUNT(course_users.id), average_rating)
		courses.id,
		courses.teacher_id,
		courses.title,
		courses.slug,
		courses.description,
		courses.sale_price,
		courses.price,
		courses.bonus_point,
		courses.bonus_point_percent,
		courses.banner,
		courses.is_public,
		courses.section_count,
		courses.section_item_count,
		courses.joined_user_count,
		courses.instructional_level,
		courses.created_at,
		courses.updated_at,
    COALESCE(ROUND(AVG(comments.rating), 1), 0) AS average_rating`

const userCourseWithBestRatedSelectSql = `
    DISTINCT ON (courses.id, average_rating)
    courses.id,
    courses.teacher_id,
    courses.title,
    courses.slug,
    courses.description,
    courses.sale_price,
    courses.price,
    courses.bonus_point,
    courses.bonus_point_percent,
    courses.banner,
    courses.is_public,
    courses.section_count,
    courses.section_item_count,
    courses.joined_user_count,
		courses.instructional_level,
    courses.created_at,
    courses.updated_at,
    COALESCE(ROUND(AVG(comments.rating), 1), 0) AS average_rating`

const userCourseWithProcessSelectSql = `
		DISTINCT ON (courses.id, calculated_user_process)
		courses.id,
		courses.teacher_id,
		courses.title,
		courses.slug,
		courses.description,
		courses.sale_price,
		courses.price,
		courses.bonus_point,
		courses.bonus_point_percent,
		courses.banner,
		courses.is_public,
		courses.section_count,
		courses.section_item_count,
		courses.joined_user_count,
		courses.created_at,
		courses.updated_at,
		COALESCE((course_users.course_user_metadata->>'completedSectionItemCount')::float / NULLIF(courses.section_item_count, 0), 0) AS calculated_user_process`

// Join SQL
const courseJoinCourseUserSql = "INNER JOIN course_users ON courses.id = course_users.course_id"
const courseLeftJoinCourseUserSql = "LEFT JOIN course_users ON courses.id = course_users.course_id"

// Mutate SQL
const courseIncreaseSectionCountSQL = "UPDATE courses SET section_count = section_count + 1, status = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?"
const courseDecreaseSectionCountSQL = "UPDATE courses SET section_count = section_count - 1, status = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?"

const courseIncreaseSectionItemCountSQL = "UPDATE courses SET section_item_count = section_item_count + 1, status = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?"
const courseDecreaseSectionItemCountSQL = "UPDATE courses SET section_item_count = section_item_count - ?, status = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?"

const courseIncreaseJoinedUseCountSQL = "UPDATE courses SET joined_user_count = joined_user_count + 1, updated_at = CURRENT_TIMESTAMP WHERE id = ?"

const courseUpdateToDraft = "UPDATE courses SET status = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?"

func (c *CourseRepository) selectWithCategory(categoryCont *string) string {
	if categoryCont == nil || strings.TrimSpace(*categoryCont) == "" {
		return courseUserSelectSql
	}

	switch *categoryCont {
	case string(enums.CourseCategoryBestSeller):
		return userCourseWithBestSellerSelectSql
	case string(enums.CourseCategoryRecentJoined):
		return userMyCourseWithCourseUserSelectSql
	case string(enums.CourseCategoryInProgress):
		return userCourseWithProcessSelectSql
	case string(enums.CourseCategoryBestRated):
		return userCourseWithBestRatedSelectSql
	default:
		return courseUserSelectSql
	}
}

func (c *CourseRepository) publicSelectWithCategory(categoryCont *string) string {
	if categoryCont == nil || strings.TrimSpace(*categoryCont) == "" {
		return courseUserSelectSql
	}

	switch *categoryCont {
	case string(enums.CourseCategoryBestSeller):
		return userCourseWithBestSellerSelectSql
	case string(enums.CourseCategoryBestRated):
		return userCourseWithBestRatedSelectSql
	default:
		return courseUserSelectSql
	}
}
