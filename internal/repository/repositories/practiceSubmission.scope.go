package repositories

import (
	"strings"
	"vibico-education-api/internal/enums"

	"gorm.io/gorm"
)

func (r *PracticeSubmissionRepository) teacherIdEq(teacherIdEq *uint32) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if teacherIdEq == nil || *teacherIdEq == 0 {
			return db
		}

		return db.Where("practice_submissions.teacher_id = ?", teacherIdEq)
	}
}

func (r *PracticeSubmissionRepository) userIdEq(userIdEq *uint32) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if userIdEq == nil || *userIdEq == 0 {
			return db
		}

		return db.Where("practice_submissions.user_id = ?", userIdEq)
	}
}

func (r *PracticeSubmissionRepository) statusEq(statusEq *string) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if statusEq == nil || strings.TrimSpace(*statusEq) == "" {
			return db
		}

		statusEnum, err := enums.ParsePracticeSubmissionStatus(*statusEq)
		if err != nil {
			return db
		}

		return db.Where("practice_submissions.status = ?", statusEnum)
	}
}

func (r *PracticeSubmissionRepository) practiceTypeEq(practiceTypeEq *string) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if practiceTypeEq == nil || strings.TrimSpace(*practiceTypeEq) == "" {
			return db
		}

		targetTypeEnum, err := enums.ParsePracticeSubmissionPracticeType(*practiceTypeEq)
		if err != nil {
			return db
		}

		return db.Where("practice_submissions.practice_type = ?", targetTypeEnum)
	}
}

func (r *PracticeSubmissionRepository) practiceIDEq(practiceIDEq *int32) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if practiceIDEq == nil || *practiceIDEq == 0 {
			return db
		}

		return db.Where("practice_submissions.practice_id = ?", practiceIDEq)
	}
}
