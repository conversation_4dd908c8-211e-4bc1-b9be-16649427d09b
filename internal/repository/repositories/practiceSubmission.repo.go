package repositories

import (
	"context"
	teacherInputs "vibico-education-api/internal/gqls/inputs/teachers"
	userInputs "vibico-education-api/internal/gqls/inputs/users"
	"vibico-education-api/internal/models"

	"github.com/BehemothLtd/behemoth-pkg/golang/pagination"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

type PracticeSubmissionRepository struct {
	Repository
}

func NewPracticeSubmissionRepository(db *gorm.DB) *PracticeSubmissionRepository {
	return &PracticeSubmissionRepository{
		Repository: Repository{db: db, tableName: "practice_submissions"},
	}
}

func (r *PracticeSubmissionRepository) FindByIDAndTeacherID(ctx context.Context, id, teacherID uint32, preloads ...CustomPreload) (*models.PracticeSubmission, error) {
	log.Debug().Ctx(ctx).Msg("PracticeSubmissionRepository.FindByIDAndTeacherID")

	var practiceSubmission models.PracticeSubmission

	query := r.db.WithContext(ctx).Scopes(r.teacherIdEq(&teacherID))

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	if err := query.Take(&practiceSubmission, id).Error; err != nil {
		return nil, err
	}

	return &practiceSubmission, nil
}

func (r *PracticeSubmissionRepository) FindByIDAndUserID(ctx context.Context, id, userID uint32, preloads ...CustomPreload) (*models.PracticeSubmission, error) {
	log.Debug().Ctx(ctx).Msg("PracticeSubmissionRepository.FindByIDAndUserID")

	var practiceSubmission models.PracticeSubmission

	query := r.db.WithContext(ctx).Scopes(r.userIdEq(&userID))

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	if err := query.Take(&practiceSubmission, id).Error; err != nil {
		return nil, err
	}

	return &practiceSubmission, nil
}

func (r *PracticeSubmissionRepository) ListByUser(
	ctx context.Context,
	args userInputs.PracticeSubmissionsInput,
	userID uint32,
	preloads ...CustomPreload,
) (*pagination.PaginationData, error) {
	log.Debug().Ctx(ctx).Msg("PracticeSubmissionRepository.ListByUser")

	queryInput, paginationData := args.ToPaginationDataAndQuery()
	paginationData.Collection = []*models.PracticeSubmission{}

	dbTables := r.db.WithContext(ctx).Model(&models.PracticeSubmission{})

	for _, preload := range preloads {
		dbTables = dbTables.Preload(preload.Key, preload.Args...)
	}

	dbTables = dbTables.Scopes(
		r.userIdEq(&userID),
		r.statusEq(queryInput.StatusEq),
		r.practiceTypeEq(queryInput.PracticeTypeEq),
		r.practiceIDEq(queryInput.PracticeIDEq),
	)

	paginationScope, err := pagination.Paginate(ctx, dbTables, &paginationData, r.tableName)
	if err != nil {
		return nil, err
	}

	err = dbTables.Scopes(paginationScope).Order("id desc").Find(&paginationData.Collection).Error

	return &paginationData, err
}

func (r *PracticeSubmissionRepository) ListByTeacher(
	ctx context.Context,
	args teacherInputs.PracticeSubmissionsInput,
	teacherID uint32,
	preloads ...CustomPreload,
) (*pagination.PaginationData, error) {
	log.Debug().Ctx(ctx).Msg("PracticeSubmissionRepository.ListByTeacher")

	queryInput, paginationData := args.ToPaginationDataAndQuery()
	paginationData.Collection = []*models.PracticeSubmission{}

	dbTables := r.db.WithContext(ctx).Model(&models.PracticeSubmission{})

	for _, preload := range preloads {
		dbTables = dbTables.Preload(preload.Key, preload.Args...)
	}

	dbTables = dbTables.Scopes(
		r.teacherIdEq(&teacherID),
		r.statusEq(queryInput.StatusEq),
	)

	paginationScope, err := pagination.Paginate(ctx, dbTables, &paginationData, r.tableName)
	if err != nil {
		return nil, err
	}

	err = dbTables.Scopes(paginationScope).Order("id desc").Find(&paginationData.Collection).Error

	return &paginationData, err
}

func (r *PracticeSubmissionRepository) Create(ctx context.Context, practiceSubmission *models.PracticeSubmission, selectFields ...string) error {
	log.Debug().Ctx(ctx).Msg("PracticeSubmissionRepository.Create")

	sql := r.db.WithContext(ctx)

	if len(selectFields) > 0 {
		sql = sql.Select(selectFields)
	}

	return sql.Create(practiceSubmission).Error
}

func (r *PracticeSubmissionRepository) CreateWithVideo(ctx context.Context, practiceSubmission *models.PracticeSubmission, videoIDs []uint32, selectFields ...string) error {
	log.Debug().Ctx(ctx).Msg("PracticeSubmissionRepository.CreateWithVideo")

	return TransactionWithLogging(r.db.WithContext(ctx), func(tx *gorm.DB) error {
		if len(selectFields) > 0 {
			tx = tx.Select(selectFields)
		}

		if err := tx.Create(practiceSubmission).Error; err != nil {
			return err
		}

		if len(videoIDs) == 0 {
			return nil
		}

		return tx.Exec(
			"UPDATE videos SET parent_id = ?, parent_type = 'PracticeSubmission', is_free = true, updated_at = NOW() WHERE id IN (?)",
			practiceSubmission.ID,
			videoIDs,
		).Error
	})
}

func (r *PracticeSubmissionRepository) Update(ctx context.Context, practiceSubmission *models.PracticeSubmission, selectFields ...string) error {
	log.Debug().Ctx(ctx).Msg("PracticeSubmissionRepository.Update")

	sql := r.db.WithContext(ctx).Model(&practiceSubmission)

	if len(selectFields) > 0 {
		sql = sql.Select(selectFields)
	}

	return sql.Updates(practiceSubmission).Error
}
