package repositories

import (
	"context"
	"time"
	"vibico-education-api/internal/enums"
	"vibico-education-api/internal/models"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

type VideoUploadRepository struct {
	Repository
}

func NewVideoUploadRepository(db *gorm.DB) *VideoUploadRepository {
	return &VideoUploadRepository{
		Repository: Repository{db: db, tableName: "video_uploads"},
	}
}

func (r *VideoUploadRepository) FindByID(ctx context.Context, id uint32, preloads ...CustomPreload) (*models.VideoUpload, error) {
	log.Debug().Ctx(ctx).Msg("VideoUploadRepository.FindByID")

	var videoUpload models.VideoUpload

	query := r.db.WithContext(ctx).Table(r.tableName)

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	if err := query.Take(&videoUpload, id).Error; err != nil {
		return nil, err
	}

	return &videoUpload, nil
}

func (r *VideoUploadRepository) FindByFileName(ctx context.Context, filename string, preloads ...CustomPreload) (*models.VideoUpload, error) {
	log.Debug().Ctx(ctx).Msg("VideoUploadRepository.FindByFileName")
	var videoUpload models.VideoUpload
	query := r.db.WithContext(ctx).Table(r.tableName).
		Where("filename =?", filename)
	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}
	if err := query.Take(&videoUpload).Error; err != nil {
		return nil, err
	}
	return &videoUpload, nil
}

func (r *VideoUploadRepository) FindByVideoID(ctx context.Context, videoID uint32, preloads ...CustomPreload) (*models.VideoUpload, error) {
	log.Debug().Ctx(ctx).Msg("VideoUploadRepository.FindByVideoID")

	var videoUpload models.VideoUpload

	query := r.db.WithContext(ctx).Table(r.tableName).
		Where("video_id = ?", videoID)

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	if err := query.Take(&videoUpload).Error; err != nil {
		return nil, err
	}

	return &videoUpload, nil
}

func (r *VideoUploadRepository) FindByTeacherID(ctx context.Context, teacherID uint32, preloads ...CustomPreload) ([]*models.VideoUpload, error) {
	log.Debug().Ctx(ctx).Msg("VideoUploadRepository.FindByTeacherID")

	var videoUploads []*models.VideoUpload

	query := r.db.WithContext(ctx).Table(r.tableName).
		Where("uploader_id = ?", teacherID)

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	if err := query.Find(&videoUploads).Error; err != nil {
		return nil, err
	}

	return videoUploads, nil
}

func (r *VideoUploadRepository) Create(ctx context.Context, videoUpload *models.VideoUpload, selectFields ...string) error {
	log.Debug().Ctx(ctx).Msg("VideoUploadRepository.Create")

	tx := r.db.WithContext(ctx)
	if len(selectFields) > 0 {
		tx = tx.Select(selectFields)
	}

	return tx.Create(&videoUpload).Error
}

func (r *VideoUploadRepository) Update(ctx context.Context, videoUpload *models.VideoUpload, selectFields ...string) error {
	log.Debug().Ctx(ctx).Msg("VideoUploadRepository.Update")

	tx := r.db.WithContext(ctx)
	if len(selectFields) > 0 {
		tx = tx.Select(selectFields)
	}

	return tx.Save(videoUpload).Error
}

func (r *VideoUploadRepository) UpdateStatus(ctx context.Context, id uint32, status enums.VideoUploadStatus, progress float64) error {
	log.Debug().Ctx(ctx).Msg("VideoUploadRepository.UpdateStatus")

	updates := map[string]interface{}{
		"status":     status,
		"progress":   progress,
		"updated_at": time.Now(),
	}

	if status == enums.VideoUploadStatusCompleted {
		updates["completed_at"] = time.Now()
	} else if status == enums.VideoUploadStatusUploading && progress == 0 {
		updates["started_at"] = time.Now()
	}

	return r.db.WithContext(ctx).Table(r.tableName).
		Where("id = ?", id).
		Updates(updates).Error
}

func (r *VideoUploadRepository) FinishUpload(ctx context.Context, videoUpload *models.VideoUpload, video *models.Video) error {
	log.Debug().Ctx(ctx).Msg("VideoUploadRepository.FinishUpload")

	return TransactionWithLogging(r.db.WithContext(ctx), func(tx *gorm.DB) error {
		// Update video upload status to completed
		now := time.Now()
		updates := map[string]interface{}{
			"status":       enums.VideoUploadStatusCompleted,
			"completed_at": now,
		}

		if err := tx.Table(r.tableName).
			Where("id = ?", videoUpload.ID).
			Updates(updates).Error; err != nil {
			return err
		}

		if err := tx.Table("videos").
			Where("id = ?", video.ID).
			Updates(video).Error; err != nil {
			return err
		}

		return nil
	})
}

func (r *VideoUploadRepository) Delete(ctx context.Context, videoUpload *models.VideoUpload) error {
	log.Debug().Ctx(ctx).Msg("VideoUploadRepository.Delete")

	return r.db.WithContext(ctx).Delete(videoUpload).Error
}
