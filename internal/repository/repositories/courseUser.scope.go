package repositories

import (
	"gorm.io/gorm"
)

func (r *CourseUserRepository) courseIdEq(courseId *uint32) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if courseId == nil {
			return db
		}

		return db.Where("course_users.course_id = ?", courseId)
	}
}

func (r *CourseUserRepository) createdAtGteq(createdAtGteq *string) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if createdAtGteq == nil {
			return db
		}

		return db // TODO: handle createdAtGteq with format datetime and compare
	}
}
