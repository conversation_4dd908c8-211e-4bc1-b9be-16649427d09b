package repositories

const updateCourseCompletedSectionItemCountSQL = `
UPDATE course_users
SET course_user_metadata = jsonb_set(
    COALESCE(course_user_metadata, '{}'::jsonb),
    '{completedSectionItemCount}',
    (COALESCE((course_user_metadata->>'completedSectionItemCount')::int, 0) + 1)::text::jsonb
), status = ?, updated_at = CURRENT_TIMESTAMP
WHERE course_users.user_id = ? AND course_users.course_id = ?`

const updateCourseCancelCompletedSectionItemCountSQL = `
UPDATE course_users
SET course_user_metadata = jsonb_set(
    COALESCE(course_user_metadata, '{}'::jsonb),
    '{completedSectionItemCount}',
    (COALESCE((course_user_metadata->>'completedSectionItemCount')::int, 0) - 1)::text::jsonb
), status = ?, updated_at = CURRENT_TIMESTAMP
WHERE course_users.user_id = ? AND course_users.course_id = ?
AND COALESCE((course_user_metadata->>'completedSectionItemCount')::int, 0) > 0`
