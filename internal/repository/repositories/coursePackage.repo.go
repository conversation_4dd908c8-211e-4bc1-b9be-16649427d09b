package repositories

import (
	"context"
	"vibico-education-api/constants"
	"vibico-education-api/internal/models"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

type CoursePackageRepository struct {
	Repository
}

func NewCoursePackageRepository(db *gorm.DB) *CoursePackageRepository {
	return &CoursePackageRepository{
		Repository: Repository{
			db:        db,
			tableName: "course_packages",
		},
	}
}

func (r *CoursePackageRepository) FindByIdAndCourseId(ctx context.Context, id, courseId uint32) (*models.CoursePackage, error) {
	log.Debug().Ctx(ctx).Msg("CoursePackageRepository.FindByIdAndCourseId")

	var coursePackage models.CoursePackage
	err := r.db.WithContext(ctx).Where("course_packages.course_id = ?", courseId).
		First(&coursePackage, id).Error
	if err != nil {
		return nil, err
	}

	return &coursePackage, nil
}

func (r *CoursePackageRepository) FindBasicByCourseId(ctx context.Context, courseId uint32) (*models.CoursePackage, error) {
	log.Debug().Ctx(ctx).Msg("CoursePackageRepository.FindBasicByCourseId")

	var coursePackage models.CoursePackage
	err := r.db.WithContext(ctx).Joins(coursePackageInnerJoinPackageSQL).
		Where("course_packages.course_id = ?", courseId).
		Where("package_deals.name = ?", constants.CourseBasicPackage).
		First(&coursePackage).Error
	if err != nil {
		return nil, err
	}

	return &coursePackage, nil
}
