package repositories

import (
	"context"
	"fmt"
	"vibico-education-api/internal/enums"
	"vibico-education-api/internal/models"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

type UserCourseSectionItemRepository struct {
	Repository
}

func NewUserCourseSectionItemRepository(db *gorm.DB) *UserCourseSectionItemRepository {
	return &UserCourseSectionItemRepository{
		Repository: Repository{db: db, tableName: "user_course_section_items"},
	}
}

func (r *UserCourseSectionItemRepository) FindOrInit(
	ctx context.Context, userId, itemId uint32,
) (*models.UserCourseSectionItem, error) {
	log.Debug().Ctx(ctx).Msg("UserCourseSectionItemRepository.FindOrInit")

	userItem := models.UserCourseSectionItem{
		UserID:              userId,
		CourseSectionItemId: itemId,
	}

	err := r.db.WithContext(ctx).Where(userItem).FirstOrInit(&userItem).Error

	return &userItem, err
}

func (r *UserCourseSectionItemRepository) FindByIdAndUserId(
	ctx context.Context, userId, itemId uint32, preloads ...CustomPreload,
) (*models.UserCourseSectionItem, error) {
	log.Debug().Ctx(ctx).Msg("UserCourseSectionItemRepository.FindByIdAndUserId")

	userItem := models.UserCourseSectionItem{
		UserID:              userId,
		CourseSectionItemId: itemId,
	}

	query := r.db.WithContext(ctx).
		Where("user_course_section_items.user_id = ? AND user_course_section_items.course_section_item_id = ?", userId, itemId)

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	if err := query.Take(&userItem).Error; err != nil {
		return nil, err
	}

	return &userItem, nil
}

func (r *UserCourseSectionItemRepository) Create(
	ctx context.Context, userItem *models.UserCourseSectionItem, currentUserId, courseId uint32, courseUserStatus int64,
) error {
	log.Debug().Ctx(ctx).Msg("UserCourseSectionItemRepository.Create")

	return TransactionWithLogging(r.db.WithContext(ctx), func(tx *gorm.DB) error {
		if err := tx.Create(&userItem).Error; err != nil {
			return err
		}

		result := tx.Exec(updateCourseCompletedSectionItemCountSQL, courseUserStatus, currentUserId, courseId)

		if result.Error != nil {
			return result.Error
		}

		return nil
	})
}

func (r *UserCourseSectionItemRepository) DeleteByIdAndItem(
	ctx context.Context, id, userId, courseId uint32,
) error {
	log.Debug().Ctx(ctx).Msg("UserCourseSectionItemRepository.DeleteById")

	return TransactionWithLogging(r.db.WithContext(ctx), func(tx *gorm.DB) error {
		result := tx.Delete(&models.UserCourseSectionItem{ID: id})
		if result.Error != nil {
			return result.Error
		}
		if result.RowsAffected == 0 {
			return fmt.Errorf("no record found with id: %d", id)
		}

		if err := tx.Exec(updateCourseCancelCompletedSectionItemCountSQL, enums.CourseUserStatusInProgress, userId, courseId).Error; err != nil {
			return err
		}

		return nil
	})
}

func (r *UserCourseSectionItemRepository) SetStatus(
	ctx context.Context, userItem *models.UserCourseSectionItem, courseId uint32, courseUserStatus enums.CourseUserStatus,
) error {
	log.Debug().Ctx(ctx).Msg("UserCourseSectionItemRepository.SetStatus")

	return TransactionWithLogging(r.db.WithContext(ctx), func(tx *gorm.DB) error {
		sqlUpdateCourseUser := updateCourseCancelCompletedSectionItemCountSQL

		if userItem.IsCompleted() {
			sqlUpdateCourseUser = updateCourseCompletedSectionItemCountSQL
		}

		if !userItem.NewRecord() || userItem.IsCompleted() {
			if err := tx.Exec(sqlUpdateCourseUser, courseUserStatus, userItem.UserID, courseId).Error; err != nil {
				return err
			}
		}

		if err := tx.Save(&userItem).Error; err != nil {
			return err
		}

		return nil
	})
}
