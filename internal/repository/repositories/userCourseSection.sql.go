package repositories

const updateCourseCompletedSectionCountSQL = `
UPDATE course_users
SET course_user_metadata = jsonb_set(
    COALESCE(course_user_metadata, '{}'::jsonb),
    '{completedSectionCount}',
    (COALESCE((course_user_metadata->>'completedSectionCount')::int, 0) + 1)::text::jsonb
)
FROM course_sections
WHERE course_sections.id = ?
    AND course_users.course_id = course_sections.course_id
    AND course_users.user_id = ?`
