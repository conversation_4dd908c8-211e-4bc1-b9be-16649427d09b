package repositories

import (
	"context"
	"fmt"
	teacherInputs "vibico-education-api/internal/gqls/inputs/teachers"
	"vibico-education-api/internal/models"

	"github.com/BehemothLtd/behemoth-pkg/golang/pagination"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

type TemplateDiagramRepository struct {
	Repository
}

func NewTemplateDiagramRepository(db *gorm.DB) *TemplateDiagramRepository {
	return &TemplateDiagramRepository{
		Repository: Repository{
			db:        db,
			tableName: "template_diagrams",
		},
	}
}

func (r *TemplateDiagramRepository) FindById(ctx context.Context, id uint32, preloads ...CustomPreload) (*models.TemplateDiagram, error) {
	log.Debug().Ctx(ctx).Msg("TemplateDiagramRepository.FindById")

	var template models.TemplateDiagram
	query := r.db.WithContext(ctx).Table(r.tableName)

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	err := query.Take(&template, id).Error

	return &template, err
}

func (r *TemplateDiagramRepository) List(ctx context.Context, args teacherInputs.TemplateDiagramListInput, preloads ...CustomPreload) (
	*pagination.PaginationData, error,
) {
	log.Debug().Ctx(ctx).Msg("TemplateDiagramRepository.List")

	queryInput, paginationData := args.ToPaginationDataAndQuery()
	paginationData.Collection = []*models.TemplateDiagram{}

	query := r.db.WithContext(ctx).Model(&models.TemplateDiagram{})

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	paginationScope, err := pagination.Paginate(
		ctx,
		query.Scopes(r.nameCont(queryInput.NameCont)),
		&paginationData,
		r.tableName,
	)
	if err != nil {
		return nil, err
	}

	err = query.Scopes(paginationScope).
		Order(templateDiagramOrderIdDescSQL).
		Find(&paginationData.Collection).Error

	return &paginationData, err
}

func (r *TemplateDiagramRepository) Create(
	ctx context.Context, template *models.TemplateDiagram, selectFields []string,
) error {
	log.Debug().Ctx(ctx).Msg("TemplateDiagramRepository.Create")

	return r.db.WithContext(ctx).Select(append(selectFields, "Diagrams")).Create(template).Error
}

// Pure update template diagram
func (r *TemplateDiagramRepository) Update(
	ctx context.Context, template *models.TemplateDiagram, selectFields []string,
) error {
	log.Debug().Ctx(ctx).Msg("TemplateDiagramRepository.Update")

	return r.db.WithContext(ctx).Select(selectFields).Updates(template).Error
}

func (r *TemplateDiagramRepository) UpdateWithNestedAttrs(
	ctx context.Context, template *models.TemplateDiagram, selectFields []string,
) error {
	log.Debug().Ctx(ctx).Msg("TemplateDiagramRepository.UpdateWithNestedAttrs")

	err := TransactionWithLogging(r.db.WithContext(ctx), func(tx *gorm.DB) error {
		diagramRepo := NewDiagramRepository(tx)

		if err := diagramRepo.DeleteByParent(ctx, template.ID, "TemplateDiagram"); err != nil {
			return err
		}

		return tx.Select(append(selectFields, "Diagrams")).Updates(template).Error
	})

	return err
}

func (r *TemplateDiagramRepository) DeleteById(ctx context.Context, id uint32) error {
	log.Debug().Ctx(ctx).Msg("TemplateDiagramRepository.DeleteById")

	err := TransactionWithLogging(r.db.WithContext(ctx), func(tx *gorm.DB) error {
		result := tx.Select("Diagrams").Delete(&models.TemplateDiagram{ID: id})
		if result.Error != nil {
			return result.Error
		}
		if result.RowsAffected == 0 {
			return fmt.Errorf("no record found with id: %d", id)
		}

		return nil
	})

	return err
}
