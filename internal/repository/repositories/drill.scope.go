package repositories

import (
	"fmt"
	"strings"
	"vibico-education-api/internal/enums"

	"gorm.io/gorm"
)

func (r *DrillRepository) titleCont(titleCont *string) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if titleCont == nil || *titleCont == "" {
			return db
		}
		sqlTitleCont := fmt.Sprintf("%%%s%%", strings.ToLower(*titleCont))

		return db.Where(gorm.Expr("title ILIKE ?", sqlTitleCont))
	}
}

func (r *DrillRepository) levelIn(levelIn *[]int32) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if levelIn == nil || len(*levelIn) == 0 {
			return db
		}
		return db.Where("drills.level IN ?", *levelIn)
	}
}

func (r *DrillRepository) skillIDIn(skillIDIn *[]int32) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if skillIDIn == nil || len(*skillIDIn) == 0 {
			return db
		}
		return db.Joins(drillLeftJoinDrillSkillSql).Where("drill_skills.skill_id IN ?", *skillIDIn)
	}
}

func (r *DrillRepository) idNotIn(idNotIn *[]int32) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if idNotIn == nil || len(*idNotIn) == 0 {
			return db
		}

		uniqueMap := make(map[int32]struct{})
		uniqueIDs := make([]int32, 0, len(*idNotIn))
		for _, id := range *idNotIn {
			if _, exists := uniqueMap[id]; !exists {
				uniqueMap[id] = struct{}{}
				uniqueIDs = append(uniqueIDs, id)
			}
		}
		return db.Where("drills.id NOT IN (?)", uniqueIDs)
	}
}

func (r *DrillRepository) officialDrill() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		return db.Where("drills.status = ?", enums.DrillStatusPublic.ToInt64())
	}
}

// Scope filter the drill which is public or were bought by current user
func (r *DrillRepository) publicOrBoughtDrill(userId uint32) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		return db.Where(`
		drills.status = ?
    OR (SELECT
            COUNT(id)
        FROM
            user_drills ud
        WHERE
            ud.drill_id = drills.id
            AND ud.user_id = ?) > 0`, enums.DrillStatusPublic.ToInt64(), userId)
	}
}

func (r *DrillRepository) isMaster() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		return db.Where("drills.is_master = TRUE")
	}
}

func (r *DrillRepository) isNotDraft() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		return db.Where("drills.censor != ?", enums.DrillCensorDraft)
	}
}

func (r *DrillRepository) isApproved() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		return db.Where("drills.censor = ?", enums.DrillCensorApproved)
	}
}

func (r *DrillRepository) ownerScope(ownerId uint32, ownerType string) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		return db.Where(gorm.Expr("drills.owner_id = ? AND drills.owner_type = ?", ownerId, ownerType))
	}
}

func (r *DrillRepository) relatedDrills(skillIDIn *[]int32, levelIn *[]string) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if (skillIDIn == nil || len(*skillIDIn) == 0) && (levelIn == nil || len(*levelIn) == 0) {
			return db
		}

		if hasValidSkills := skillIDIn != nil && len(*skillIDIn) > 0; hasValidSkills {
			db = db.Joins(drillLeftJoinDrillSkillSql)
		}

		var levelInts []int64
		if levelIn != nil && len(*levelIn) > 0 {
			levelInts = r.convertAndValidateLevels(*levelIn)
		}

		return r.applyCombinedFilters(db, skillIDIn, levelInts)
	}
}

func (r *DrillRepository) convertAndValidateLevels(levels []string) []int64 {
	var levelInts []int64
	for _, lv := range levels {
		levelEnum := enums.DrillLevel(lv)
		if levelEnum.IsValid() {
			levelInts = append(levelInts, levelEnum.ToInt64())
		}
	}
	return levelInts
}

func (r *DrillRepository) applyCombinedFilters(query *gorm.DB, skillIDIn *[]int32, levelIn []int64) *gorm.DB {
	hasValidSkills := skillIDIn != nil && len(*skillIDIn) > 0
	hasValidLevels := len(levelIn) > 0

	switch {
	case hasValidSkills && hasValidLevels:
		return query.Where("drill_skills.skill_id IN (?) OR drills.level IN (?)", *skillIDIn, levelIn)
	case hasValidSkills:
		return query.Where("drill_skills.skill_id IN (?)", *skillIDIn)
	case hasValidLevels:
		return query.Where("drills.level IN ?", levelIn)
	default:
		return query
	}
}

func (r *DrillRepository) teacherActivated() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if _, loaded := db.Statement.Settings.LoadOrStore("teachers", true); !loaded {
			db.Joins("LEFT JOIN teachers ON drills.owner_type = 'Teacher' AND drills.owner_id = teachers.id")
		}

		return db.Where("drills.owner_type != 'Teacher' OR teachers.active = true")
	}
}
