package repositories

import (
	"context"
	"time"
	"vibico-education-api/internal/enums"
	"vibico-education-api/internal/models"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

type VideoRepository struct {
	Repository
}

func NewVideoRepository(db *gorm.DB) *VideoRepository {
	return &VideoRepository{
		Repository: Repository{db: db, tableName: "videos"},
	}
}

func (r *VideoRepository) FindByID(ctx context.Context, id uint32, preloads ...CustomPreload) (*models.Video, error) {
	log.Debug().Ctx(ctx).Msg("VideoRepository.FindByID")

	var video models.Video

	query := r.db.WithContext(ctx).Table(r.tableName)

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	if err := query.Take(&video, id).Error; err != nil {
		return nil, err
	}

	return &video, nil
}

func (r *VideoRepository) FindSelfVideoByID(ctx context.Context, id uint32, uploaderId uint32, uploaderType string, preloads ...CustomPreload) (*models.Video, error) {
	log.Debug().Ctx(ctx).Msg("VideoRepository.FindSelfVideoByID")
	var video models.Video
	query := r.db.WithContext(ctx).Table(r.tableName).
		Where("videos.id =?", id).
		Where("videos.uploader_id =?", uploaderId).
		Where("videos.uploader_type =?", uploaderType)

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}
	if err := query.Take(&video).Error; err != nil {
		return nil, err
	}
	return &video, nil
}

func (r *VideoRepository) FindByIDAndTeacher(ctx context.Context, videoID, teacherID uint32, preloads ...CustomPreload) (*models.Video, error) {
	log.Debug().Ctx(ctx).Msg("VideoRepository.FindByIDAndTeacher")

	var video models.Video

	query := r.db.WithContext(ctx).Table(r.tableName).
		Joins("JOIN video_uploads ON videos.id = video_uploads.video_id").
		Where("video_uploads.uploader_id = ?", teacherID)

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	if err := query.Take(&video, videoID).Error; err != nil {
		return nil, err
	}

	return &video, nil
}

func (r *VideoRepository) FindOrphans(ctx context.Context, preloads ...CustomPreload) (*[]models.Video, error) {
	log.Debug().Ctx(ctx).Msg("VideoRepository.FindOrphans")
	var videos []models.Video
	query := r.db.WithContext(ctx).Table(r.tableName).
		Where("videos.parent_id = 0").
		Where("videos.created_at < ?", time.Now().Add(-12*time.Hour))

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	err := query.Find(&videos).Error

	return &videos, err
}

func (r *VideoRepository) FindFreeVideo(ctx context.Context, id uint32, preloads ...CustomPreload) (*models.Video, error) {
	log.Debug().Ctx(ctx).Msg("VideoRepository.FindFreeVideo")

	var video models.Video

	query := r.db.WithContext(ctx).Table(r.tableName).
		Where("is_free = ? AND status = ?", true, enums.VideoStatusApproved)

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	if err := query.Take(&video, id).Error; err != nil {
		return nil, err
	}

	return &video, nil
}

func (r *VideoRepository) ListByIDs(ctx context.Context, ids []uint32) (*[]models.Video, error) {
	log.Debug().Ctx(ctx).Msg("VideoRepository.ListByIDs")
	var videos []models.Video

	err := r.db.WithContext(ctx).Table(r.tableName).Where("videos.id in (?)", ids).Find(&videos).Error

	return &videos, err
}

func (r *VideoRepository) ListByParent(
	ctx context.Context, parentId uint32, parentType string, preloads ...CustomPreload,
) (*[]models.Video, error) {
	log.Debug().Ctx(ctx).Msg("VideoRepository.ListByParent")

	var videos []models.Video

	query := r.db.WithContext(ctx).Table(r.tableName).
		Where("videos.parent_id = ? AND parent_type = ?", parentId, parentType)

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	if err := query.Find(&videos).Error; err != nil {
		return nil, err
	}
	return &videos, nil
}

func (r *VideoRepository) Create(ctx context.Context, video *models.Video, selectFields ...string) error {
	log.Debug().Ctx(ctx).Msg("VideoRepository.Create")

	tx := r.db.WithContext(ctx)
	if len(selectFields) > 0 {
		tx = tx.Select(selectFields)
	}

	return tx.Create(&video).Error
}

func (r *VideoRepository) Update(ctx context.Context, video *models.Video, selectFields ...string) error {
	log.Debug().Ctx(ctx).Msg("VideoRepository.Update")

	tx := r.db.WithContext(ctx)
	if len(selectFields) > 0 {
		tx = tx.Select(selectFields)
	}

	return tx.Save(video).Error
}

func (r *VideoRepository) UpdateWithCourse(ctx context.Context, video *models.Video, courseId uint32, selectFields ...string) error {
	log.Debug().Ctx(ctx).Msg("VideoRepository.UpdateWithCourse")

	return TransactionWithLogging(r.db.WithContext(ctx), func(tx *gorm.DB) error {
		if len(selectFields) > 0 {
			tx = tx.Select(selectFields)
		}

		if err := tx.Save(video).Error; err != nil {
			return nil
		}

		return tx.Exec(courseUpdateToDraft, courseId).Error
	})
}

func (r *VideoRepository) UpdateWithDrill(ctx context.Context, video *models.Video, drillId uint32, selectFields ...string) error {
	log.Debug().Ctx(ctx).Msg("VideoRepository.UpdateWithDrill")

	return TransactionWithLogging(r.db.WithContext(ctx), func(tx *gorm.DB) error {
		if len(selectFields) > 0 {
			tx = tx.Select(selectFields)
		}

		if err := tx.Save(video).Error; err != nil {
			return nil
		}

		return tx.Exec(drillUpdateToDraft, drillId).Error
	})
}

func (r *VideoRepository) Delete(ctx context.Context, video *models.Video) error {
	log.Debug().Ctx(ctx).Msg("VideoRepository.Delete")

	return r.db.WithContext(ctx).Delete(video).Error
}

func (r *VideoRepository) DeleteWithCourse(ctx context.Context, video *models.Video, courseId uint32) error {
	log.Debug().Ctx(ctx).Msg("VideoRepository.DeleteWithCourse")

	return TransactionWithLogging(r.db.WithContext(ctx), func(tx *gorm.DB) error {
		if err := tx.Delete(video).Error; err != nil {
			return err
		}

		return tx.Exec(courseUpdateToDraft, courseId).Error
	})
}

func (r *VideoRepository) DeleteDraftByID(ctx context.Context, id uint32) error {
	log.Debug().Ctx(ctx).Msg("VideoRepository.DeleteByID")

	return r.db.WithContext(ctx).Where("status = ?", enums.VideoStatusDraft).Delete(&models.Video{}, id).Error
}
