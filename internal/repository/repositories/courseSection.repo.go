package repositories

import (
	"context"
	"fmt"
	globalPayloads "vibico-education-api/internal/gqls/payloads/global"
	teacherPayloads "vibico-education-api/internal/gqls/payloads/teachers"
	"vibico-education-api/internal/models"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type CourseSectionRepository struct {
	Repository
	TableName string
}

func NewCourseSectionRepository(db *gorm.DB) *CourseSectionRepository {
	return &CourseSectionRepository{
		Repository: Repository{db: db},
		TableName:  "course_sections",
	}
}

func (r *CourseSectionRepository) FindById(ctx context.Context, id uint32) (*models.CourseSection, error) {
	log.Debug().Ctx(ctx).Msg("CourseSectionRepository.FindById")

	var section models.CourseSection
	err := r.db.WithContext(ctx).Table(r.TableName).Take(&section, id).Error
	if err != nil {
		return nil, err
	}
	return &section, nil
}

func (r *CourseSectionRepository) FindBySlugAndCourseId(
	ctx context.Context, sectionSlug string, courseId uint32, preloads ...CustomPreload,
) (*models.CourseSection, error) {
	log.Debug().Ctx(ctx).Msg("CourseSectionRepository.FindBySlugAndCourseId")

	var section models.CourseSection
	query := r.db.WithContext(ctx).Table(r.TableName)

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	err := query.Where(gorm.Expr("course_sections.slug = ?", sectionSlug)).
		Where("course_sections.course_id = ?", courseId).
		Take(&section).Error
	if err != nil {
		return nil, err
	}
	return &section, nil
}

func (r *CourseSectionRepository) FindUserCurrentSection(
	ctx context.Context, courseId, userId uint32, preloads ...CustomPreload,
) (*models.CourseSection, error) {
	log.Debug().Ctx(ctx).Msg("CourseSectionRepository.FindUserCurrentSection")

	var section models.CourseSection
	query := r.db.WithContext(ctx).Table(r.TableName)

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	fetchQuery := query.Session(&gorm.Session{})
	err := fetchQuery.Where("course_sections.course_id = ?", courseId).
		Joins(courseSectionLeftJoinUserCourseSectionSQL).
		Where("user_course_sections.user_id = ?", userId).
		Order("position DESC").
		Limit(1).
		Find(&section).Error

	if err != nil || section.ID == 0 {
		if err = query.
			Where("course_sections.course_id = ?", courseId).
			Not(courseSectionExistUserCourseSectionSQL, userId).
			Order("position").
			Limit(1).
			Find(&section).Error; err != nil {
			return nil, err
		}
	}

	return &section, nil
}

func (r *CourseSectionRepository) FindBySectionIdCourseIdAndTeacher(
	ctx context.Context, id uint32, courseId uint32, teacherId uint32, preloads ...CustomPreload,
) (*models.CourseSection, error) {
	log.Debug().Ctx(ctx).Msg("CourseSectionRepository.FindBySectionIdCourseIdAndTeacher")

	courseSection := &models.CourseSection{}

	query := r.db.WithContext(ctx).Table(r.TableName)
	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	err := query.
		Joins(courseSectionInnerJoinCourseSQL).
		Where("courses.teacher_id =?", teacherId).
		Where("course_sections.course_id = ?", courseId).
		Take(courseSection, id).Error

	return courseSection, err
}

func (r *CourseSectionRepository) FindByTitle(ctx context.Context, title string) (*models.CourseSection, error) {
	log.Debug().Ctx(ctx).Msg("CourseSectionRepository.FindByTitle")

	var courseSection models.CourseSection

	if err := r.db.WithContext(ctx).Table(r.TableName).Where("title = ?", title).First(&courseSection).Error; err != nil {
		return nil, err
	}
	return &courseSection, nil
}

func (r *CourseSectionRepository) FindByIDAndCourseID(ctx context.Context, id uint32, courseId uint32) (*models.CourseSection, error) {
	log.Debug().Ctx(ctx).Msg("CourseSectionRepository.FindByIDAndCourseID")
	var courseSection models.CourseSection
	err := r.db.WithContext(ctx).Table(r.TableName).
		Where("id = ? AND course_id = ?", id, courseId).
		Take(&courseSection).Error
	if err != nil {
		return nil, err
	}
	return &courseSection, nil
}

func (r *CourseSectionRepository) ListByCourseId(
	ctx context.Context, courseID uint32, preloads ...CustomPreload,
) ([]*teacherPayloads.CourseSectionPayload, error) {
	log.Debug().Ctx(ctx).Msg("CourseSectionRepository.ListByCourseId")

	dbTables := r.db.WithContext(ctx).Model(&models.CourseSection{})
	for _, preload := range preloads {
		dbTables = dbTables.Preload(preload.Key, preload.Args...)
	}

	var courseSections []*models.CourseSection

	err := dbTables.Scopes(r.courseIdEq(&courseID)).
		Order(courseSectionsOrderByPositionAscSql).
		Find(&courseSections).Error
	if err != nil {
		return nil, err
	}

	var courseSectionPayloads []*teacherPayloads.CourseSectionPayload
	for _, section := range courseSections {
		courseSectionPayloads = append(courseSectionPayloads, &teacherPayloads.CourseSectionPayload{
			CourseSectionPayload: &globalPayloads.CourseSectionPayload{
				CourseSection: section,
			},
		})
	}

	return courseSectionPayloads, nil
}

// TODO: Relate to update course content after the course has joined user
// update delete condition
func (r *CourseSectionRepository) Delete(ctx context.Context, courseSection *models.CourseSection) error {
	log.Debug().Ctx(ctx).Msgf("CourseSectionRepository.Delete")

	err := TransactionWithLogging(r.db.WithContext(ctx), func(tx *gorm.DB) error {
		var err error
		var sectionItemCount int64
		if err = tx.Table("course_section_items").Select("course_section_items.id").
			Where("course_section_id = ?", courseSection.ID).
			Count(&sectionItemCount).Error; err != nil {
			return err
		}

		if sectionItemCount > 0 {
			err := tx.Exec(courseDecreaseSectionItemCountSQL, sectionItemCount, courseSection.CourseID).Error
			if err != nil {
				return err
			}
		}
		err = tx.Exec(courseDecreaseSectionCountSQL, courseSection.CourseID).Error
		if err != nil {
			return err
		}

		result := tx.Select(clause.Associations).Delete(&courseSection)
		if result.Error != nil {
			return result.Error
		}

		return nil
	})

	return err
}

func (r *CourseSectionRepository) DeleteById(ctx context.Context, id uint32) error {
	log.Debug().Ctx(ctx).Msgf("CourseSectionRepository.DeleteById")

	result := r.db.WithContext(ctx).Table(r.TableName).Where("id = ?", id).Delete(&models.CourseSection{})
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("no record found with id: %d", id)
	}

	return nil
}

func (r *CourseSectionRepository) Create(ctx context.Context, courseSection *models.CourseSection, selectFields ...string) error {
	log.Debug().Ctx(ctx).Msgf("CourseSectionRepository.Create")

	err := TransactionWithLogging(r.db.WithContext(ctx), func(tx *gorm.DB) error {
		var maxPosition int32
		err := tx.Table(r.TableName).
			Where("course_id = ?", courseSection.CourseID).
			Order("position DESC").
			Limit(1).
			Pluck("position", &maxPosition).Error

		if err != nil && err != gorm.ErrRecordNotFound {
			return err
		}

		if maxPosition == 0 {
			courseSection.Position = 1
		} else {
			courseSection.Position = maxPosition + 1
		}

		if len(selectFields) > 0 {
			tx = tx.Select(selectFields)
		}

		err = tx.Create(courseSection).Error
		if err != nil {
			return err
		}

		return tx.Exec(courseIncreaseSectionCountSQL, courseSection.CourseID).Error
	})

	return err
}

func (r *CourseSectionRepository) Update(ctx context.Context, courseSection *models.CourseSection, selectFields ...string) error {
	log.Debug().Ctx(ctx).Msg("CourseSectionRepository.Update")

	return TransactionWithLogging(r.db.WithContext(ctx), func(tx *gorm.DB) error {
		sql := tx.Model(&courseSection)

		if len(selectFields) > 0 {
			sql = sql.Select(selectFields)
		}

		if err := sql.Updates(courseSection).Error; err != nil {
			return err
		}

		return tx.Exec(courseUpdateToDraft, courseSection.CourseID).Error
	})
}

func (r *CourseSectionRepository) SwapPosition(ctx context.Context, courseId uint32, courseSectionId uint32, newIndex int32) error {
	log.Debug().Ctx(ctx).Msg("CourseSectionRepository.SwapPosition")

	section, err := r.FindByIDAndCourseID(ctx, courseSectionId, courseId)
	if err != nil {
		return err
	}

	err = r.switchTo(ctx, section, newIndex)
	if err != nil {
		log.Error().Err(err).Msg("Failed to switch position")
		return err
	}

	return nil
}

func (r *CourseSectionRepository) switchTo(ctx context.Context, courseSection *models.CourseSection, newIndex int32) error {
	log.Debug().Ctx(ctx).Msgf("CourseSectionRepository.switchTo: courseID=%d, sectionID=%d, newIndex=%d", courseSection.CourseID, courseSection.ID, newIndex)

	return TransactionWithLogging(r.db.WithContext(ctx), func(tx *gorm.DB) error {
		var newPosition int32
		if err := tx.Table(r.TableName).
			Where("course_id = ?", courseSection.CourseID).
			Order("position ASC").
			Offset(int(newIndex)).
			Limit(1).
			Pluck("position", &newPosition).Error; err != nil {
			return err
		}

		if newPosition == courseSection.Position {
			return nil
		}

		var condition string
		if courseSection.Position > newPosition {
			condition = "? <= position AND position < ?"
			if err := tx.Table(r.TableName).
				Where(condition, newPosition, courseSection.Position).
				Where("course_id = ?", courseSection.CourseID).
				Update("position", gorm.Expr("position + 1")).Error; err != nil {
				return err
			}
		} else {
			condition = "? < position AND position <= ?"
			if err := tx.Table(r.TableName).
				Where(condition, courseSection.Position, newPosition).
				Where("course_id = ?", courseSection.CourseID).
				Update("position", gorm.Expr("position - 1")).Error; err != nil {
				return err
			}
		}

		courseSection.Position = newPosition
		if err := tx.Table(r.TableName).
			Where("id = ?", courseSection.ID).
			Update("position", newPosition).Error; err != nil {
			return err
		}

		return tx.Exec(courseUpdateToDraft, courseSection.CourseID).Error
	})
}

func (r *CourseSectionRepository) ListWithPractices(ctx context.Context, courseSlug string, teacherID uint32, preloads ...CustomPreload) ([]*models.CourseSection, error) {
	log.Debug().Ctx(ctx).Msg("CourseSectionRepository.ListWithPractices")

	var sections []*models.CourseSection

	query := r.db.WithContext(ctx).Distinct().
		Joins(courseSectionInnerJoinCourseSQL).
		Where("courses.slug = ?", courseSlug).
		Where("courses.teacher_id = ?", teacherID)

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	if err := query.Order(courseSectionsOrderByPositionAscSql).Find(&sections).Error; err != nil {
		return nil, err
	}

	return sections, nil
}
