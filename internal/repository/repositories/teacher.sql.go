package repositories

const teacherInnerJoinCourseSQL = "INNER JOIN courses ON courses.teacher_id = teachers.id"

const teacherIncreaseStudentCountSQL = "UPDATE teachers SET student_count = student_count + 1, updated_at = CURRENT_TIMESTAMP WHERE teachers.id = ?"
const teacherIncreaseCourseCountSQL = "UPDATE teachers SET approved_course_count = approved_course_count + 1, updated_at = CURRENT_TIMESTAMP WHERE teachers.id = ?"
const teacherDecreaseCourseCountSQL = "UPDATE teachers SET approved_course_count = approved_course_count - 1, updated_at = CURRENT_TIMESTAMP WHERE teachers.id = ? AND approved_course_count > 0"

const teacherUpdateAverage = "UPDATE teachers SET average_rating = sub.avg_rating FROM ( SELECT target_id, AVG(rating) AS avg_rating FROM comments WHERE target_type = 'Teacher' GROUP BY target_id ) AS sub WHERE teachers.id = sub.target_id AND teachers.id = ?; "
