package repositories

import (
	"context"
	"vibico-education-api/internal/models"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

type VideoVersionRepository struct {
	Repository
}

func NewVideoVersionRepository(db *gorm.DB) *VideoVersionRepository {
	return &VideoVersionRepository{
		Repository: Repository{db: db, tableName: "video_versions"},
	}
}

func (r *VideoVersionRepository) FindByID(ctx context.Context, id uint32) (*models.VideoVersion, error) {
	log.Debug().Ctx(ctx).Msg("VideoVersionRepository.FindByID")

	var videoVersion models.VideoVersion

	query := r.db.WithContext(ctx).Table(r.tableName)

	if err := query.Take(&videoVersion, id).Error; err != nil {
		return nil, err
	}

	return &videoVersion, nil
}

func (r *VideoVersionRepository) FindByVideoID(ctx context.Context, videoID uint32) ([]*models.VideoVersion, error) {
	log.Debug().Ctx(ctx).Msg("VideoVersionRepository.FindByVideoID")

	var videoVersions []*models.VideoVersion

	if err := r.db.WithContext(ctx).Table(r.tableName).
		Where("video_id = ?", videoID).
		Find(&videoVersions).Error; err != nil {
		return nil, err
	}

	return videoVersions, nil
}

func (r *VideoVersionRepository) Create(ctx context.Context, videoVersion *models.VideoVersion, selectFields ...string) error {
	log.Debug().Ctx(ctx).Msg("VideoVersionRepository.Create")

	tx := r.db.WithContext(ctx)
	if len(selectFields) > 0 {
		tx = tx.Select(selectFields)
	}

	return tx.Create(videoVersion).Error
}

func (r *VideoVersionRepository) Update(ctx context.Context, videoVersion *models.VideoVersion, selectFields ...string) error {
	log.Debug().Ctx(ctx).Msg("VideoVersionRepository.Update")

	tx := r.db.WithContext(ctx)
	if len(selectFields) > 0 {
		tx = tx.Select(selectFields)
	}

	return tx.Save(videoVersion).Error
}

func (r *VideoVersionRepository) Delete(ctx context.Context, videoVersion *models.VideoVersion) error {
	log.Debug().Ctx(ctx).Msg("VideoVersionRepository.Delete")

	return r.db.WithContext(ctx).Delete(videoVersion).Error
}
