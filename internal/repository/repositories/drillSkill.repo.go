package repositories

import (
	"context"
	"vibico-education-api/internal/models"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

type DrillSkillRepository struct {
	Repository
}

func NewDrillSkillRepository(db *gorm.DB) *DrillSkillRepository {
	return &DrillSkillRepository{
		Repository: Repository{
			db:        db,
			tableName: "drill_skills",
		},
	}
}

func (r *DrillSkillRepository) DeleteDrillSkills(ctx context.Context, id uint32) error {
	log.Debug().Ctx(ctx).Msg("DrillSkillRepository.DeleteDrillSkills")

	if err := r.db.WithContext(ctx).
		Where("drill_skills.drill_id = ?", id).
		Delete(&models.DrillSkill{}).Error; err != nil {
		return err
	}

	return nil
}
