package repositories

import (
	"context"
	"vibico-education-api/internal/models"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

type UserDrillRepository struct {
	Repository
}

func NewUserDrillRepository(db *gorm.DB) *UserDrillRepository {
	return &UserDrillRepository{
		Repository{db: db, tableName: "user_drills"},
	}
}

func (r *UserDrillRepository) Create(ctx context.Context, userDrill *models.UserDrill) error {
	log.Debug().Ctx(ctx).Msg("UserDrillRepository.Create")

	return r.db.WithContext(ctx).Table(r.tableName).Create(&userDrill).Error
}

func (r *UserDrillRepository) FindOrInit(ctx context.Context, drillId, userId uint32) *models.UserDrill {
	log.Debug().Ctx(ctx).Msg("UserDrillRepository.FindOrInit")

	userDrill := models.UserDrill{
		DrillId: drillId,
		UserId:  userId,
	}

	r.db.WithContext(ctx).Where("user_drills.drill_id = ? AND user_drills.user_id = ?", drillId, userId).Take(&userDrill)

	return &userDrill
}
