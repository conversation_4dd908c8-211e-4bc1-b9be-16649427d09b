package repositories

import (
	"context"
	publicInputs "vibico-education-api/internal/gqls/inputs/publics"
	"vibico-education-api/internal/models"

	"github.com/BehemothLtd/behemoth-pkg/golang/pagination"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

type CommentRepository struct {
	Repository
}

func NewCommentRepository(db *gorm.DB) *CommentRepository {
	return &CommentRepository{
		Repository: Repository{db: db, tableName: "comments"},
	}
}

func (r *CommentRepository) Create(ctx context.Context, comment *models.Comment, selectFields ...string) error {
	log.Debug().Ctx(ctx).Msg("CommentRepository.Create")

	db := r.db.WithContext(ctx)
	if len(selectFields) > 0 {
		db = db.Select(selectFields)
	}
	return db.<PERSON>reate(comment).Error
}

func (r *CommentRepository) Update(ctx context.Context, comment *models.Comment, selectFields ...string) error {
	log.Debug().Ctx(ctx).Msg("CommentRepository.Update")

	db := r.db.WithContext(ctx).Model(&comment)
	if len(selectFields) > 0 {
		db = db.Select(selectFields)
	}
	return db.Updates(comment).Error
}

func (r *CommentRepository) CreateTeacherCourseReview(ctx context.Context, comment *models.Comment, courseId uint32, selectFields ...string) error {
	log.Debug().Ctx(ctx).Msg("CommentRepository.CreateTeacherCourseReview")

	db := r.db.WithContext(ctx)

	return TransactionWithLogging(db, func(tx *gorm.DB) error {
		if len(selectFields) > 0 {
			tx = tx.Select(selectFields)
		}
		if err := tx.Model(&models.Comment{}).Create(comment).Error; err != nil {
			return err
		}

		if comment.IsValidTargetTypeTeacher() {
			err := r.updateTeacherAverageRating(tx, comment.TargetID)
			if err != nil {
				return err
			}

			if err := r.markCourseUserReviewedTeacher(tx, courseId, comment.AuthorID); err != nil {
				return err
			}
		}

		return nil
	})
}

func (r *CommentRepository) UpdateTeacherCourseReview(ctx context.Context, comment *models.Comment, courseId uint32, selectFields ...string) error {
	log.Debug().Ctx(ctx).Msg("CommentRepository.UpdateTeacherCourseReview")

	db := r.db.WithContext(ctx)

	return TransactionWithLogging(db, func(tx *gorm.DB) error {
		sql := tx.Model(&comment)
		if len(selectFields) > 0 {
			sql = sql.Select(selectFields)
		}
		if err := sql.Updates(comment).Error; err != nil {
			return err
		}

		if comment.IsValidTargetTypeTeacher() {
			if err := r.updateTeacherAverageRating(tx, comment.TargetID); err != nil {
				return err
			}
			if err := r.markCourseUserReviewedTeacher(tx, courseId, comment.AuthorID); err != nil {
				return err
			}
		}

		return nil
	})
}

func (r *CommentRepository) FindByTargetAndAuthor(ctx context.Context,
	targetID, authorID uint32,
	targetType, authorType string,
	preloads ...CustomPreload,
) (*models.Comment, error) {
	log.Debug().Ctx(ctx).Msg("CourseRepository.FindBySlugAndTeacher")

	var comment models.Comment

	query := r.db.WithContext(ctx).
		Where("target_id = ? AND target_type = ?", targetID, targetType).
		Where("author_id = ? AND author_type = ?", authorID, authorType)

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	if err := query.Take(&comment).Error; err != nil {
		return nil, err
	}

	return &comment, nil
}

func (r *CommentRepository) ListReviewsByTarget(
	ctx context.Context,
	args publicInputs.ReviewsInput,
	preloads ...CustomPreload,
) (*pagination.PaginationData, error) {
	log.Debug().Ctx(ctx).Msg("CommentRepository.ListReviewsByTarget")

	paginationData := args.Input.ToPaginationInput()
	paginationData.Collection = []*models.Comment{}

	dbTables := r.db.WithContext(ctx).Model(&models.Comment{}).
		Where("target_id = ? AND target_type = ?", args.TargetID, args.TargetType)

	for _, preload := range preloads {
		dbTables = dbTables.Preload(preload.Key, preload.Args...)
	}

	paginationScope, err := pagination.Paginate(ctx, dbTables, &paginationData, r.tableName)
	if err != nil {
		return nil, err
	}

	err = dbTables.Scopes(paginationScope).Order("id DESC").Find(&paginationData.Collection).Error

	return &paginationData, err
}

func (r *CommentRepository) GetReviewsStatsByTarget(
	ctx context.Context,
	targetID uint32,
	targetType string,
) (*models.ReviewStats, error) {
	var stats models.ReviewStats
	var starCounts []models.StarCount

	err := r.db.WithContext(ctx).
		Model(&models.Comment{}).
		Select("ROUND(rating) as rating, COUNT(*) as count").
		Where("target_id = ? AND target_type = ? AND rating IS NOT NULL", targetID, targetType).
		Group("ROUND(rating)").
		Scan(&starCounts).Error
	if err != nil {
		return nil, err
	}

	for _, sc := range starCounts {
		switch sc.Rating {
		case 1:
			stats.Star1 = sc.Count
		case 2:
			stats.Star2 = sc.Count
		case 3:
			stats.Star3 = sc.Count
		case 4:
			stats.Star4 = sc.Count
		case 5:
			stats.Star5 = sc.Count
		}
		stats.Total += sc.Count
	}

	err = r.db.WithContext(ctx).
		Model(&models.Comment{}).
		Select("COALESCE(ROUND(AVG(rating), 1), 0)").
		Where("target_id = ? AND target_type = ? AND rating IS NOT NULL", targetID, targetType).
		Scan(&stats.AverageRating).Error
	if err != nil {
		return nil, err
	}

	return &stats, nil
}

func (r *CommentRepository) FindByIdAndUserAuthor(ctx context.Context, id uint32, userId uint32) (*models.Comment, error) {
	var comment models.Comment

	if err := r.db.WithContext(ctx).Where("id = ? AND author_id = ? AND author_type = 'User'", id, userId).Take(&comment).Error; err != nil {
		return nil, err
	}

	return &comment, nil
}

func (r *CommentRepository) updateTeacherAverageRating(tx *gorm.DB, targetId uint32) error {
	return tx.Exec(teacherUpdateAverage, targetId).Error
}

func (r *CommentRepository) markCourseUserReviewedTeacher(tx *gorm.DB, courseID uint32, userID uint32) error {
	return tx.Exec(courseUserMarkRatedTeacher, courseID, userID).Error
}
