package repositories

import (
	"context"
	"vibico-education-api/internal/models"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

type YoutubeTokenRepository struct {
	Repository
}

func NewYoutubeTokenRepository(db *gorm.DB) *YoutubeTokenRepository {
	return &YoutubeTokenRepository{
		Repository: Repository{
			db:        db,
			tableName: "youtube_tokens",
		},
	}
}

func (r *YoutubeTokenRepository) GetLeastUsed(ctx context.Context) (*models.YoutubeToken, error) {
	log.Debug().Ctx(ctx).Msg("YoutubeTokenRepository.GetLeastUsed")
	var token models.YoutubeToken

	err := r.db.WithContext(ctx).
		Table(r.tableName).
		Joins("LEFT JOIN video_platforms vp ON vp.token_id = youtube_tokens.id AND vp.created_at >= CURRENT_DATE").
		Group("youtube_tokens.id").
		Order("COUNT(vp.id), RANDOM()").
		First(&token).Error

	return &token, err
}

func (r *YoutubeTokenRepository) FindByID(ctx context.Context, id uint32) (*models.YoutubeToken, error) {
	log.Debug().Ctx(ctx).Msg("YoutubeTokenRepository.FindByID")
	var token models.YoutubeToken

	err := r.db.WithContext(ctx).
		Table(r.tableName).
		Where("id = ?", id).
		Take(&token).Error

	return &token, err
}
