package repositories

import (
	"context"
	"errors"
	"strings"
	"vibico-education-api/internal/enums"
	adminInputs "vibico-education-api/internal/gqls/inputs/admins"
	teacherInputs "vibico-education-api/internal/gqls/inputs/teachers"
	"vibico-education-api/internal/models"
	"vibico-education-api/pkg/helpers"
	translator "vibico-education-api/pkg/translators"
	"vibico-education-api/setup/grpc_clients"

	"github.com/BehemothLtd/behemoth-pkg/golang/pagination"
	pb "github.com/BehemothLtd/vibico-auth/proto/auth"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

type UserRepository struct {
	Repository
}

func NewUserRepository(db *gorm.DB) *UserRepository {
	return &UserRepository{
		Repository: Repository{
			db:        db,
			tableName: "users",
		},
	}
}

func (r *UserRepository) FindById(ctx context.Context, id uint32) (*models.User, error) {
	log.Debug().Ctx(ctx).Msg("UserRepository.FindById")

	var user models.User

	err := r.db.WithContext(ctx).Take(&user, id).Error
	if err != nil {
		return nil, err
	}

	return &user, nil
}

func (r *UserRepository) FindByAuthId(ctx context.Context, authId string) (*models.User, error) {
	log.Debug().Ctx(ctx).Msg("UserRepository.FindByAuthId")

	var user models.User
	err := r.db.WithContext(ctx).Scopes(whereEq(r.field("auth_id"), &authId)).Take(&user).Error
	if err != nil {
		return nil, err
	}

	return &user, nil
}

func (r *UserRepository) FindByActiveAndAuthId(ctx context.Context, authId string) (*models.User, error) {
	log.Debug().Ctx(ctx).Msg("UserRepository.FindByActiveAndAuthId")

	var user models.User
	err := r.db.WithContext(ctx).
		Scopes(
			r.activated(),
			whereEq(r.field("auth_id"), &authId),
		).
		Take(&user).Error
	if err != nil {
		return nil, err
	}

	return &user, nil
}

func (r *UserRepository) ListBySectionItem(
	ctx context.Context,
	sectionItemId uint32,
	args teacherInputs.SectionItemUsersWithPracticesInput,
	preloads ...CustomPreload,
) (*pagination.PaginationData, error) {
	log.Debug().Ctx(ctx).Msg("UserRepository.ListBySectionItem")

	queryInput, paginationData := args.ToPaginationDataAndQuery()
	paginationData.Collection = []*models.User{}

	query := r.db.WithContext(ctx).Model(&models.User{}).
		Joins("JOIN user_course_section_items ucsi ON ucsi.user_id = users.id").
		Where("ucsi.course_section_item_id = ?", sectionItemId)

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	query = query.Scopes(
		r.nameCont(queryInput.NameCont),
		r.practiceSubmissionStatusEq(queryInput.StatusEq, sectionItemId),
	)

	paginationScope, err := pagination.Paginate(ctx, query, &paginationData, r.tableName)
	if err != nil {
		return nil, err
	}

	if err := query.Scopes(paginationScope).Order("ucsi.id desc").Find(&paginationData.Collection).Error; err != nil {
		return nil, err
	}

	return &paginationData, nil
}

func (r *UserRepository) AdminList(
	ctx context.Context,
	args adminInputs.UsersInput,
	preloads ...CustomPreload,
) (*pagination.PaginationData, error) {
	log.Debug().Ctx(ctx).Msg("UserRepository.AdminList")

	queryInput, paginationData := args.ToPaginationDataAndQuery()
	paginationData.Collection = []*models.User{}

	query := r.db.WithContext(ctx).Model(&models.User{})
	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	query = query.Scopes(
		r.nameCont(queryInput.NameCont),
		r.phoneNumberCont(queryInput.PhoneNumberCont),
	)

	paginationScope, err := pagination.Paginate(ctx, query, &paginationData, r.tableName)
	if err != nil {
		return nil, err
	}

	err = query.Scopes(paginationScope).Order("users.id DESC").Find(&paginationData.Collection).Error
	if err != nil {
		return nil, err
	}

	return &paginationData, err
}

func (r *UserRepository) Create(ctx context.Context, user *models.User, selectFields ...string) error {
	log.Debug().Ctx(ctx).Msg("UserRepository.Create")

	sql := r.db.WithContext(ctx)

	if len(selectFields) > 0 {
		sql = sql.Select(selectFields)
	}

	return sql.Create(user).Error
}

func (r *UserRepository) CreateAccounts(ctx context.Context, user *models.User, pbUserParams *pb.UserParams, selectFields ...string) error {
	log.Debug().Ctx(ctx).Msg("UserRepository.CreateAccounts")

	return TransactionWithLogging(r.db.WithContext(ctx), func(tx *gorm.DB) error {
		if err := tx.Select(selectFields).Create(user).Error; err != nil {
			return err
		}

		teacher := &models.Teacher{
			AuthId:       user.AuthId,
			Name:         user.Name,
			ContactEmail: pbUserParams.Email,
			PhoneNumber:  pbUserParams.PhoneNumber,
			Slug:         helpers.GenerateSlug(user.Name),
		}

		if err := tx.Create(teacher).Error; err != nil {
			return err
		}

		return nil
	})
}

func (r *UserRepository) Update(ctx context.Context, user *models.User, selectFields ...string) error {
	log.Debug().Ctx(ctx).Msg("UserRepository.Update")
	sql := r.db.WithContext(ctx)

	if len(selectFields) > 0 {
		sql = sql.Select(selectFields)
	}
	return sql.Updates(user).Error
}

func (r *UserRepository) InviteUser(
	ctx context.Context, courseID, coursePackageID uint32, phoneNumber string, pbUserParams *pb.UserParams,
	teacherID uint32,
) error {
	log.Debug().Ctx(ctx).Msg("UserRepository.InviteUser")

	identityPoolId := grpc_clients.PoolId()
	existingUser, err := grpc_clients.AuthClient().GetUserByPhoneNumber(
		grpc_clients.NewCtx(ctx),
		&pb.UserPhoneNumberRequest{
			IdentityPoolId: identityPoolId,
			PhoneNumber:    phoneNumber,
		},
	)

	if err != nil && !strings.Contains(err.Error(), "record not found") {
		return err
	}

	return TransactionWithLogging(r.db.WithContext(ctx), func(tx *gorm.DB) error {
		var user *models.User
		if err != nil && strings.Contains(err.Error(), "record not found") {
			grpcResponse, err := grpc_clients.AuthClient().CreateUser(
				grpc_clients.NewCtx(ctx),
				&pb.CreateUserRequest{
					IdentityPoolId: identityPoolId,
					UserParams:     pbUserParams,
				},
			)
			if err != nil {
				return err
			}

			newUser := &models.User{
				AuthId:      grpcResponse.User.Id,
				Name:        phoneNumber,
				PhoneNumber: &phoneNumber,
			}
			if err := tx.Create(newUser).Error; err != nil {
				return err
			}

			user = newUser

			teacher := &models.Teacher{
				AuthId:       user.AuthId,
				Name:         user.Name,
				ContactEmail: pbUserParams.Email,
				PhoneNumber:  pbUserParams.PhoneNumber,
				Slug:         helpers.GenerateSlug(user.Name),
			}

			if err := tx.Create(teacher).Error; err != nil {
				return err
			}
		} else {
			err = tx.Where("auth_id = ?", existingUser.User.Id).Take(&user).Error
			if err != nil {
				return err
			}

			var existingCourseUser models.CourseUser
			err = tx.Where("course_id = ? AND user_id = ?", courseID, user.ID).Take(&existingCourseUser).Error
			if err == nil {
				if existingCourseUser.ID != 0 && existingCourseUser.Status != enums.CourseUserStatusInvited {
					return errors.New(translator.Translate(nil, "errDbMsg_UserAlreadyJoinCourse"))
				}
				return nil
			}

			if !errors.Is(err, gorm.ErrRecordNotFound) {
				return err
			}
		}

		coursePackage, err := r.getCoursePackage(ctx, tx, courseID, coursePackageID)
		if err != nil {
			return err
		}

		courseUser := &models.CourseUser{
			CourseID:        courseID,
			UserID:          user.ID,
			Status:          enums.CourseUserStatusInvited,
			CoursePackageID: &coursePackage.ID,
		}

		if err := tx.Create(courseUser).Error; err != nil {
			return err
		}

		senderType := enums.NotificationActorTypeTeacher
		notification := &models.Notification{
			RecipientID:    user.ID,
			RecipientType:  enums.NotificationActorTypeUser,
			SenderID:       &teacherID,
			SenderType:     &senderType,
			NoticeKind:     enums.NoticeKindInviteCourse,
			NotifiableType: enums.NotifiableTypeCourse,
			NotifiableID:   courseID,
		}

		content := models.NotificationContent{
			Title: "{{teacher_invite_course}}",
			Body:  "{{teacher_invite_course_body}}",
		}

		if err := notification.SetContent(content); err != nil {
			return err
		}

		notificationRepo := NewNotificationRepository(tx)
		if err := notificationRepo.Create(ctx, notification); err != nil {
			return err
		}

		return nil

	})
}

func (r *UserRepository) getCoursePackage(ctx context.Context, db *gorm.DB, courseID, coursePackageID uint32) (*models.CoursePackage, error) {
	log.Debug().Ctx(ctx).Msg("UserRepository.getCoursePackage")

	coursePackageRepo := NewCoursePackageRepository(db)

	if coursePackageID == 0 {
		return coursePackageRepo.FindBasicByCourseId(ctx, courseID)
	} else {
		return coursePackageRepo.FindByIdAndCourseId(ctx, coursePackageID, courseID)
	}
}
