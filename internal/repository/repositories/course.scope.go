package repositories

import (
	"fmt"
	"strings"
	"vibico-education-api/internal/enums"

	"gorm.io/gorm"
)

func (r *CourseRepository) statusEq(statusEq *string) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if statusEq == nil || strings.TrimSpace(*statusEq) == "" {
			return db
		}

		statusEnum, err := enums.ParseCourseStatus(*statusEq)
		if err != nil {
			return db
		}

		return db.Where("courses.status = ?", statusEnum)
	}
}

func (r *CourseRepository) isOfficial() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		return db.Where("courses.status = ? AND courses.is_public = ?", enums.CourseStatusApproved, true)
	}
}

func (r *CourseRepository) statusIn(statusIn *[]string) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if statusIn == nil || len(*statusIn) == 0 {
			return db
		}

		var statusEnums []enums.CourseStatus
		for _, s := range *statusIn {
			statusEnum, err := enums.ParseCourseStatus(s)
			if err == nil {
				statusEnums = append(statusEnums, statusEnum)
			}
		}

		if len(statusEnums) == 0 {
			return db
		}

		return db.Where("courses.status IN (?)", statusEnums)
	}
}

func (r *CourseRepository) titleCont(titleCont *string) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if titleCont == nil || strings.TrimSpace(*titleCont) == "" {
			return db
		}
		sqlTitleCont := fmt.Sprintf("%%%s%%", *titleCont)

		return db.Where("courses.title ILIKE ?", sqlTitleCont)
	}
}

func (r *CourseRepository) descriptionCont(descriptionCont *string) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if descriptionCont == nil || strings.TrimSpace(*descriptionCont) == "" {
			return db
		}
		sqlTitleCont := fmt.Sprintf("%%%s%%", *descriptionCont)

		return db.Where("courses.description ILIKE ?", sqlTitleCont)
	}
}

func (r *CourseRepository) teacherActivated() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if _, loaded := db.Statement.Settings.LoadOrStore("teachers", true); !loaded {
			db.Joins("JOIN teachers ON teachers.id = courses.teacher_id")
		}

		return db.Where("teachers.active = true")
	}
}

func (r *CourseRepository) teacherNameCont(teacherNameCont *string) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if teacherNameCont == nil || strings.TrimSpace(*teacherNameCont) == "" {
			return db
		}

		if _, loaded := db.Statement.Settings.LoadOrStore("teachers", true); !loaded {
			db.Joins("JOIN teachers ON teachers.id = courses.teacher_id")
		}

		sqlTitleCont := fmt.Sprintf("%%%s%%", *teacherNameCont)

		return db.Where("teachers.name ILIKE ?", sqlTitleCont)
	}
}

func (c *CourseRepository) categoryCont(categoryCont *string, userId *uint32) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if categoryCont == nil || strings.TrimSpace(*categoryCont) == "" {
			return db
		}

		switch *categoryCont {
		case string(enums.CourseCategoryRecentJoined):
			if userId != nil {
				db = db.Where("course_users.user_id = ? AND course_users.status != ?", *userId, enums.CourseUserStatusInvited)
			}
			return db
		case string(enums.CourseCategoryInProgress):
			if userId != nil {
				db = db.Where("course_users.user_id = ?", *userId).
					Where(`COALESCE(
        		(course_users.course_user_metadata->>'completedSectionItemCount')::float /
          	NULLIF(courses.section_item_count, 0), 0) < 1.0 `)
			}
			return db.Where("course_users.status = ?", enums.CourseUserStatusInProgress)
		case string(enums.CourseCategoryCompleted):
			if userId != nil {
				db = db.Where("course_users.user_id = ?", *userId)
			}

			return db.Where("course_users.status = ?", enums.CourseUserStatusCompleted)
		default:
			return db
		}
	}
}

func (r *CourseRepository) salePriceRange(rangeVals *[]int32) func(db *gorm.DB) *gorm.DB {

	return func(db *gorm.DB) *gorm.DB {
		if rangeVals == nil || len(*rangeVals) != 2 {
			return db
		}

		if rangeVals != nil && len(*rangeVals) == 2 {
			min, max := (*rangeVals)[0], (*rangeVals)[1]
			if min < 0 && max < 0 {
				return db.Where(`COALESCE(courses.sale_price, courses.price, 0) > ?`, 0)
			}
		}

		min := (*rangeVals)[0]
		max := (*rangeVals)[1]

		if min > 0 && max == 0 {
			return db.Where(`COALESCE(courses.sale_price, courses.price, 0) >= ?`, min)
		}

		return db.Where(`COALESCE(courses.sale_price, courses.price, 0) BETWEEN ? AND ?`, min, max)
	}
}

func (c *CourseRepository) orderWithCategory(categoryCont *string) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if categoryCont == nil || strings.TrimSpace(*categoryCont) == "" {
			return db.Group("courses.id").Order(courseOrderByIdDescSql)
		}

		switch *categoryCont {
		case string(enums.CourseCategoryBestSeller):
			return db.Group("courses.id").Order("COUNT(course_users.id) DESC")
		case string(enums.CourseCategoryRecentJoined):
			return db.Group("courses.id, course_users.id").Order(courseUserOrderByIdDescSql)
		case string(enums.CourseCategoryInProgress):
			return db.Order(courseOrderByUserProcessSql)
		case string(enums.CourseCategoryBestRated):
			return db.Group("courses.id").Order("average_rating DESC")
		default:
			return db.Group("courses.id").Order(courseOrderByIdDescSql)
		}
	}
}

func (c *CourseRepository) publicCategoryCont(categoryCont *string) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if categoryCont == nil || strings.TrimSpace(*categoryCont) == "" {
			return db
		}

		// Keep switch case for future categories
		switch *categoryCont {
		case string(enums.CourseCategoryBestSeller):
			return db.Joins("LEFT JOIN course_users ON courses.id = course_users.course_id")
		default:
			return db
		}
	}
}

func (c *CourseRepository) publicOrderWithCategory(categoryCont *string) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if categoryCont == nil || strings.TrimSpace(*categoryCont) == "" {
			return db.Group("courses.id").Order(courseOrderByIdDescSql)
		}

		switch *categoryCont {
		case string(enums.CourseCategoryBestSeller):
			return db.Group("courses.id").Order("COUNT(course_users.id) DESC")
		case string(enums.CourseCategoryBestRated):
			return db.Group("courses.id").Order("average_rating DESC")
		default:
			return db.Group("courses.id").Order(courseOrderByIdDescSql)
		}
	}
}
