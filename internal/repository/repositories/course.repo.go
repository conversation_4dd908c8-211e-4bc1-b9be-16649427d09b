package repositories

import (
	"context"
	"fmt"
	"vibico-education-api/internal/enums"
	adminInputs "vibico-education-api/internal/gqls/inputs/admins"
	publicInputs "vibico-education-api/internal/gqls/inputs/publics"
	teacherInputs "vibico-education-api/internal/gqls/inputs/teachers"
	userInputs "vibico-education-api/internal/gqls/inputs/users"
	"vibico-education-api/internal/models"

	"github.com/BehemothLtd/behemoth-pkg/golang/pagination"
	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

type CourseRepository struct {
	Repository
}

func NewCourseRepository(db *gorm.DB) *CourseRepository {
	return &CourseRepository{
		Repository: Repository{db: db, tableName: "courses"},
	}
}

func (r *CourseRepository) FindByID(ctx context.Context, id uint32, preloads ...CustomPreload) (*models.Course, error) {
	log.Debug().Ctx(ctx).Msg("CourseRepository.FindByID")

	var course models.Course

	query := r.db.WithContext(ctx).Table(r.tableName)

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	if err := query.Take(&course, id).Error; err != nil {
		return nil, err
	}

	return &course, nil
}

func (r *CourseRepository) FindBySlug(ctx context.Context, slug string, preloads ...CustomPreload) (*models.Course, error) {
	log.Debug().Ctx(ctx).Msg("CourseRepository.FindBySlug")

	var course models.Course

	query := r.db.WithContext(ctx).Table(r.tableName)

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	if err := query.Select("courses.*").
		Where(gorm.Expr("courses.slug = ?", slug)).
		Scopes(
			r.teacherActivated(),
		).
		Take(&course).Error; err != nil {
		return nil, err
	}

	return &course, nil
}

func (r *CourseRepository) FindOfficialCourseByID(ctx context.Context, id uint32, preloads ...CustomPreload) (*models.Course, error) {
	log.Debug().Ctx(ctx).Msg("CourseRepository.FindOfficialCourseByID")

	var course models.Course

	query := r.db.WithContext(ctx).Table(r.tableName).Scopes(r.isOfficial())

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	if err := query.Take(&course, id).Error; err != nil {
		return nil, err
	}

	return &course, nil
}

func (r *CourseRepository) FindOfficialCourseBySlug(ctx context.Context, slug string, preloads ...CustomPreload) (*models.Course, error) {
	log.Debug().Ctx(ctx).Msg("CourseRepository.FindOfficialCourseBySlug")

	var course models.Course

	query := r.db.WithContext(ctx).Table(r.tableName).Scopes(r.isOfficial())

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	if err := query.Where(gorm.Expr("courses.slug = ?", slug)).Take(&course).Error; err != nil {
		return nil, fmt.Errorf("failed to find course with slug %s: %w", slug, err)
	}

	return &course, nil
}

func (r *CourseRepository) FindByIDAndUser(ctx context.Context, courseId, userId uint32, preloads ...CustomPreload) (*models.Course, error) {
	log.Debug().Ctx(ctx).Msg("CourseRepository.FindByIDAndUser")

	var course models.Course
	query := r.db.WithContext(ctx).Joins(courseJoinCourseUserSql)

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	if err := query.Select(courseSelectSql).
		Where("course_users.user_id = ?", userId).
		Scopes(
			r.statusEq(utils.PointerString(enums.CourseStatusApproved.String())),
		).
		Take(&course, courseId).Error; err != nil {
		return nil, err
	}

	return &course, nil
}

func (r *CourseRepository) FindByIDAndTeacher(ctx context.Context, courseId, teacherId uint32, preloads ...CustomPreload) (*models.Course, error) {
	log.Debug().Ctx(ctx).Msg("CourseRepository.FindByIDAndTeacher")

	var course models.Course
	query := r.db.WithContext(ctx).Scopes(whereEq(r.field("teacher_id"), &teacherId))

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	if err := query.Take(&course, courseId).Error; err != nil {
		return nil, err
	}

	return &course, nil
}

func (r *CourseRepository) FindBySlugAndUser(ctx context.Context, slug string, userId uint32, preloads ...CustomPreload) (*models.Course, error) {
	log.Debug().Ctx(ctx).Msg("CourseRepository.FindBySlugAndUser")

	var course models.Course
	query := r.db.WithContext(ctx).Joins(courseJoinCourseUserSql)

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	if err := query.Select(courseSelectSql).
		Where("course_users.user_id = ?", userId).
		Where(gorm.Expr("courses.slug = ?", slug)).
		Take(&course).Error; err != nil {
		return nil, err
	}

	return &course, nil
}

func (r *CourseRepository) FindBySlugWithUser(ctx context.Context, slug string, preloads ...CustomPreload) (*models.Course, error) {
	log.Debug().Ctx(ctx).Msg("CourseRepository.FindBySlugWithUser")

	var course models.Course
	query := r.db.WithContext(ctx).Joins(courseLeftJoinCourseUserSql)

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	if err := query.Select(courseSelectSql).
		Where(gorm.Expr("courses.slug = ?", slug)).
		Scopes(
			r.teacherActivated(),
		).
		Take(&course).Error; err != nil {
		return nil, err
	}

	return &course, nil
}

func (r *CourseRepository) FindBySlugAndTeacher(ctx context.Context, slug string, teacherId uint32, preloads ...CustomPreload) (*models.Course, error) {
	log.Debug().Ctx(ctx).Msg("CourseRepository.FindBySlugAndTeacher")

	var course models.Course

	query := r.db.WithContext(ctx).Scopes(whereEq(r.field("teacher_id"), &teacherId))

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	if err := query.Take(&course, "slug = ?", slug).Error; err != nil {
		return nil, err
	}

	return &course, nil
}

func (r *CourseRepository) ListByTeacher(
	ctx context.Context,
	args teacherInputs.CoursesInput,
	teacherId uint32,
	preloads ...CustomPreload,
) (*pagination.PaginationData, error) {
	log.Debug().Ctx(ctx).Msg("CourseRepository.ListByTeacher")

	queryInput, paginationData := args.ToPaginationDataAndQuery()
	paginationData.Collection = []*models.Course{}

	dbTables := r.db.WithContext(ctx).Model(&models.Course{}).Select(courseSelectSql)

	for _, preload := range preloads {
		dbTables = dbTables.Preload(preload.Key, preload.Args...)
	}

	dbTables = dbTables.Scopes(
		r.titleCont(queryInput.TitleCont),
		r.descriptionCont(queryInput.DescriptionCont),
		r.statusEq(queryInput.StatusEq),
		whereEq(r.field("teacher_id"), &teacherId),
	)

	paginationScope, err := pagination.Paginate(ctx, dbTables, &paginationData, r.tableName)
	if err != nil {
		return nil, err
	}

	err = dbTables.Scopes(paginationScope).Order(courseOrderByIdDescSql).Find(&paginationData.Collection).Error

	return &paginationData, err
}

// List user-joined courses
func (r *CourseRepository) ListByUser(
	ctx context.Context,
	args userInputs.CoursesInput,
	userId uint32,
	preloads ...CustomPreload,
) (*pagination.PaginationData, error) {
	log.Debug().Ctx(ctx).Msg("CourseRepository.ListByUser")

	queryInput, paginationData := args.ToPaginationDataAndQuery()
	paginationData.Collection = []*models.Course{}

	dbTables := r.db.WithContext(ctx).Model(&models.Course{}).
		Joins(courseJoinCourseUserSql).
		Joins("LEFT JOIN comments ON courses.id = comments.target_id AND comments.target_type = ?", enums.CommentTargetTypeCourse).
		Where("course_users.user_id = ?", userId).
		Where("course_users.status != ?", enums.CourseUserStatusInvited).
		Select(r.selectWithCategory(queryInput.CategoryCont))

	for _, preload := range preloads {
		dbTables = dbTables.Preload(preload.Key, preload.Args...)
	}

	dbTables = dbTables.Scopes(
		r.categoryCont(queryInput.CategoryCont, nil),
		r.titleCont(queryInput.TitleCont),
		r.descriptionCont(queryInput.DescriptionCont),
	)

	paginationScope, err := pagination.Paginate(ctx, dbTables, &paginationData, r.tableName)
	if err != nil {
		return nil, err
	}

	err = dbTables.Scopes(paginationScope, r.orderWithCategory(queryInput.CategoryCont)).
		Find(&paginationData.Collection).Error

	return &paginationData, err
}

// List Course With User Info
func (r *CourseRepository) ListWithUser(
	ctx context.Context,
	args userInputs.CoursesInput,
	userId uint32,
	preloads ...CustomPreload,
) (*pagination.PaginationData, error) {
	log.Debug().Ctx(ctx).Msg("CourseRepository.ListWithUser")

	queryInput, paginationData := args.ToPaginationDataAndQuery()
	paginationData.Collection = []*models.Course{}

	dbTables := r.db.WithContext(ctx).Model(&models.Course{}).
		Joins(courseLeftJoinCourseUserSql).
		Joins("LEFT JOIN comments ON courses.id = comments.target_id AND comments.target_type = ?", enums.CommentTargetTypeCourse).
		Select(r.selectWithCategory(queryInput.CategoryCont))

	for _, preload := range preloads {
		dbTables = dbTables.Preload(preload.Key, preload.Args...)
	}

	dbTables = dbTables.Scopes(
		r.teacherActivated(),
		r.categoryCont(queryInput.CategoryCont, &userId),
		r.isOfficial(),
		r.titleCont(queryInput.TitleCont),
		r.descriptionCont(queryInput.DescriptionCont),
		r.salePriceRange(queryInput.SalePriceRange),
		r.teacherNameCont(queryInput.TeacherNameCont),
		whereEq(r.field("teacher_id"), queryInput.TeacherIdEq),
	)

	paginationScope, err := pagination.Paginate(ctx, dbTables, &paginationData, r.tableName)
	if err != nil {
		return nil, err
	}

	err = dbTables.Scopes(paginationScope, r.orderWithCategory(queryInput.CategoryCont)).
		Find(&paginationData.Collection).Error

	return &paginationData, err
}

func (r *CourseRepository) AdminList(
	ctx context.Context,
	args adminInputs.CoursesInput,
	preloads ...CustomPreload,
) (*pagination.PaginationData, error) {
	log.Debug().Ctx(ctx).Msg("CourseRepository.AdminList")

	queryInput, paginationData := args.ToPaginationDataAndQuery()
	paginationData.Collection = []*models.Course{}

	dbTables := r.db.WithContext(ctx).Model(&models.Course{}).
		Select(adminCourseSelectSql).Where("courses.status != ?", enums.CourseStatusDraft)

	for _, preload := range preloads {
		dbTables = dbTables.Preload(preload.Key, preload.Args...)
	}

	dbTables = dbTables.Scopes(
		r.titleCont(queryInput.TitleCont),
		whereEq(r.field("teacher_id"), queryInput.TeacherIdEq),
		r.statusIn(queryInput.StatusIn),
	)

	paginationScope, err := pagination.Paginate(ctx, dbTables, &paginationData, r.tableName)
	if err != nil {
		return nil, err
	}

	err = dbTables.Scopes(paginationScope).Order(courseOrderByStatusSql).Find(&paginationData.Collection).Error

	return &paginationData, err
}

func (r *CourseRepository) PublicList(
	ctx context.Context,
	args publicInputs.CoursesInput,
	preloads ...CustomPreload,
) (*pagination.PaginationData, error) {
	log.Debug().Ctx(ctx).Msg("CourseRepository.PublicList")

	queryInput, paginationData := args.ToPaginationDataAndQuery()
	paginationData.Collection = []*models.Course{}

	dbTables := r.db.WithContext(ctx).Model(&models.Course{}).
		Joins("LEFT JOIN comments ON courses.id = comments.target_id AND comments.target_type = ?", enums.CommentTargetTypeCourse).
		Select(r.publicSelectWithCategory(queryInput.CategoryCont))

	for _, preload := range preloads {
		dbTables = dbTables.Preload(preload.Key, preload.Args...)
	}

	dbTables = dbTables.Scopes(
		r.isOfficial(),
		r.teacherActivated(),
		r.publicCategoryCont(queryInput.CategoryCont),
		r.titleCont(queryInput.TitleCont),
		r.descriptionCont(queryInput.DescriptionCont),
		r.salePriceRange(queryInput.SalePriceRange),
		r.teacherNameCont(queryInput.TeacherNameCont),
		whereEq(r.field("teacher_id"), queryInput.TeacherIdEq),
	)

	paginationScope, err := pagination.Paginate(ctx, dbTables, &paginationData, r.tableName)
	if err != nil {
		return nil, err
	}

	err = dbTables.Scopes(paginationScope, r.publicOrderWithCategory(queryInput.CategoryCont)).
		Find(&paginationData.Collection).Error

	return &paginationData, err
}

func (r *CourseRepository) PublicRelatedList(
	ctx context.Context,
	args publicInputs.RelatedCoursesInput,
	currentCourse *models.Course,
	preloads ...CustomPreload,
) (*pagination.PaginationData, error) {
	log.Debug().Ctx(ctx).Msg("CourseRepository.PublicRelatedList")

	paginationData := args.Input.ToPaginationInput()
	paginationData.Collection = []*models.Course{}

	baseQuery := r.db.Model(&models.Course{}).
		Scopes(
			r.isOfficial(),
			r.teacherActivated(),
			whereNotEq(r.field("id"), &currentCourse.ID),
		)

	sql1 := baseQuery.Session(&gorm.Session{}).
		Select("courses.*, 1 AS priority").
		Scopes(whereEq(r.field("instructional_level"), &currentCourse.InstructionalLevel))

	sql2 := baseQuery.Session(&gorm.Session{}).
		Select("courses.*, 2 AS priority").
		Scopes(
			whereEq(r.field("teacher_id"), &currentCourse.TeacherId),
			whereNotEq(r.field("instructional_level"), &currentCourse.InstructionalLevel),
		)

	var totalCount uint32

	if err := r.db.WithContext(ctx).
		Raw(`SELECT COUNT(related.id) FROM (? UNION ALL ?) AS related`, sql1, sql2).
		Scan(&totalCount).Error; err != nil {
		return nil, err
	}

	if err := paginationData.Metadata.Calculate(totalCount); err != nil {
		return nil, err
	}

	dataQuery := r.db.WithContext(ctx).
		Raw("(? UNION ALL ?) ORDER BY priority ASC, id DESC LIMIT ? OFFSET ?",
			sql1, sql2,
			paginationData.Metadata.PerPage,
			paginationData.Metadata.Offset(),
		)

	for _, preload := range preloads {
		dataQuery = dataQuery.Preload(preload.Key, preload.Args...)
	}

	dataQuery.Find(&paginationData.Collection)
	if err := dataQuery.Error; err != nil {
		return nil, err
	}

	return &paginationData, nil
}

func (r *CourseRepository) Create(ctx context.Context, course *models.Course, selectFields ...string) error {
	log.Debug().Ctx(ctx).Msg("CourseRepository.Create")

	sql := r.db.WithContext(ctx)

	if len(selectFields) > 0 {
		sql = sql.Select(selectFields)
	}

	return sql.Create(course).Error
}

func (r *CourseRepository) Update(ctx context.Context, course *models.Course, selectFields ...string) error {
	log.Debug().Ctx(ctx).Msg("CourseRepository.Update")

	sql := r.db.WithContext(ctx).Model(&course)

	if len(selectFields) > 0 {
		sql = sql.Select(selectFields)
	}

	return sql.Updates(course).Error
}

func (r *CourseRepository) Delete(ctx context.Context, course *models.Course) error {
	log.Debug().Ctx(ctx).Msg("CourseRepository.Delete")

	err := TransactionWithLogging(r.db.WithContext(ctx), func(tx *gorm.DB) error {
		if err := tx.Where("course_section_id IN (?)",
			r.db.Model(&models.CourseSection{}).Where("course_id = ?", course.ID).Select("id"),
		).Delete(&models.CourseSectionItem{}).Error; err != nil {
			return err
		}

		result := tx.Select("CourseSections").Delete(&course)
		if result.Error != nil {
			return result.Error
		}

		if result.RowsAffected == 0 {
			return fmt.Errorf("no record found with id: %d", course.ID)
		}

		return nil
	})

	return err
}

func (r *CourseRepository) AssociationCount(ctx context.Context, course *models.Course, association string) (int64, error) {
	log.Debug().Ctx(ctx).Msgf("CourseRepository.AssociationCount %s", association)

	associationHandler := r.db.Model(course).Association(association)
	if err := associationHandler.Error; err != nil {
		return 0, err
	}

	return associationHandler.Count(), nil
}

func (r *CourseRepository) ApprovedCourse(ctx context.Context, course *models.Course) error {
	log.Debug().Ctx(ctx).Msg("CourseRepository.ApprovedCourse")

	return TransactionWithLogging(r.db.WithContext(ctx), func(tx *gorm.DB) error {
		if err := tx.Model(course).Update("status", enums.CourseStatusApproved).Error; err != nil {
			return err
		}

		if err := tx.Exec(teacherIncreaseCourseCountSQL, course.TeacherId).Error; err != nil {
			return err
		}

		return nil
	})
}

func (r *CourseRepository) SubmitCourse(ctx context.Context, course *models.Course) error {
	log.Debug().Ctx(ctx).Msg("CourseRepository.SubmitCourse")

	return TransactionWithLogging(r.db.WithContext(ctx), func(tx *gorm.DB) error {
		if err := tx.Model(models.Course{}).Where("courses.id = ?", course.ID).Update("status", enums.CourseStatusSubmitted).Error; err != nil {
			return err
		}

		if err := tx.Model(&models.CensorHistory{}).
			Where("parent_id = ? AND parent_type = ?", course.ID, enums.CensorHistoryParentCourse).
			Update("status", enums.CensorStatusSubmitted).Error; err != nil {
			return err
		}

		return nil
	})
}

// Get monthly course purchase statistics for a teacher in a given year
func (r *CourseRepository) GetTeacherCourseChart(ctx context.Context, teacherId uint32, year *int32) ([]*models.CourseEarning, error) {
	log.Debug().Ctx(ctx).Msg("CourseRepository.GetTeacherCourseChart")

	var results []*models.CourseEarning
	queryYear := ""

	if year == nil {
		queryYear = "EXTRACT(YEAR FROM CURRENT_DATE)"
	} else {
		queryYear = fmt.Sprintf("%d", *year)
	}
	query := `WITH months AS (SELECT generate_series(1, 12) AS month), monthly_counts AS (
			SELECT
				course_id, EXTRACT(MONTH FROM created_at) AS month, COUNT(id) AS count
			FROM
				course_users
			WHERE
				EXTRACT(YEAR FROM created_at) = %s
			GROUP BY
				course_id, month
		)
		SELECT
			c.title, TO_JSON(ARRAY_AGG(COALESCE(mc.count, 0))) AS data
		FROM
			courses c
		CROSS JOIN
			months m
		LEFT JOIN monthly_counts mc ON c.id = mc.course_id AND m.month = mc.month
		WHERE c.teacher_id = %d
		GROUP BY c.id
		ORDER BY c.id`

	rows, err := r.db.WithContext(ctx).Raw(fmt.Sprintf(query, queryYear, teacherId)).Rows()
	if err != nil {
		return nil, err
	}

	defer rows.Close()

	for rows.Next() {
		var title string
		var data string

		if err := rows.Scan(&title, &data); err != nil {
			return nil, err
		}

		results = append(results, &models.CourseEarning{
			Title: title,
			Data:  data,
		})
	}

	return results, nil
}
