package repositories

import (
	"context"
	"vibico-education-api/internal/enums"
	adminInputs "vibico-education-api/internal/gqls/inputs/admins"
	publicInputs "vibico-education-api/internal/gqls/inputs/publics"
	userInputs "vibico-education-api/internal/gqls/inputs/users"
	"vibico-education-api/internal/models"

	"github.com/BehemothLtd/behemoth-pkg/golang/pagination"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

type TeacherRepository struct {
	Repository
}

func NewTeacherRepository(db *gorm.DB) *TeacherRepository {
	return &TeacherRepository{
		Repository: Repository{
			db:        db,
			tableName: "teachers",
		},
	}
}

func (r *TeacherRepository) FindById(
	ctx context.Context, id uint32, preloads ...CustomPreload,
) (*models.Teacher, error) {
	log.Debug().Ctx(ctx).Msg("TeacherRepository.FindById")

	var teacher models.Teacher
	query := r.db.WithContext(ctx)

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	err := query.Take(&teacher, id).Error

	return &teacher, err
}

func (r *TeacherRepository) FindByContactEmail(
	ctx context.Context, email string, preloads ...CustomPreload,
) (*models.Teacher, error) {
	log.Debug().Ctx(ctx).Msg("TeacherRepository.FindByContactEmail")

	var teacher models.Teacher
	query := r.db.WithContext(ctx)

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	err := query.Where(gorm.Expr("teachers.contact_email = ?", email)).Take(&teacher).Error

	return &teacher, err
}

func (r *TeacherRepository) FindBySlug(
	ctx context.Context, slug string, preloads ...CustomPreload,
) (*models.Teacher, error) {
	log.Debug().Ctx(ctx).Msg("TeacherRepository.FindBySlug")

	var teacher models.Teacher
	query := r.db.WithContext(ctx)

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	err := query.Where(gorm.Expr("teachers.slug = ?", slug)).Take(&teacher).Error

	return &teacher, err
}

func (r *TeacherRepository) FindValidTeacherById(
	ctx context.Context, id uint32, preloads ...CustomPreload,
) (*models.Teacher, error) {
	log.Debug().Ctx(ctx).Msg("TeacherRepository.FindValidTeacherById")

	var teacher models.Teacher
	query := r.db.WithContext(ctx)

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	err := query.Scopes(r.basicEnteredEq(true)).Take(&teacher, id).Error

	return &teacher, err
}

func (r *TeacherRepository) FindByAuthId(
	ctx context.Context, authId string, preloads ...CustomPreload,
) (*models.Teacher, error) {
	log.Debug().Ctx(ctx).Msg("TeacherRepository.FindByAuthId")

	var teacher models.Teacher
	query := r.db.WithContext(ctx)

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	err := query.Scopes(whereEq(r.field("auth_id"), &authId)).Take(&teacher).Error
	if err != nil {
		return nil, err
	}

	return &teacher, nil
}

func (r *TeacherRepository) FindByActiveAndAuthId(
	ctx context.Context, authId string, preloads ...CustomPreload,
) (*models.Teacher, error) {
	log.Debug().Ctx(ctx).Msg("TeacherRepository.FindByActiveAndAuthId")

	var teacher models.Teacher
	query := r.db.WithContext(ctx).Scopes(
		r.activated(),
		whereEq(r.field("auth_id"), &authId),
	)

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	err := query.Take(&teacher).Error
	if err != nil {
		return nil, err
	}

	return &teacher, nil
}

func (r *TeacherRepository) FindByCondition(ctx context.Context, basicEntered bool) (*[]models.Teacher, error) {
	log.Debug().Ctx(ctx).Msg("TeacherRepository.FindByCondition")

	var teachers []models.Teacher
	err := r.db.WithContext(ctx).Where("basic_entered = ?", basicEntered).Order("name ASC").Find(&teachers).Error

	if err != nil {
		return nil, err
	}
	return &teachers, nil
}

func (r *TeacherRepository) Create(ctx context.Context, teacher *models.Teacher, selectFields ...string) error {
	log.Debug().Ctx(ctx).Msg("TeacherRepository.Create")

	sql := r.db.WithContext(ctx)

	if len(selectFields) > 0 {
		sql = sql.Select(selectFields)
	}

	return sql.Create(teacher).Error
}

func (r *TeacherRepository) Update(ctx context.Context, teacher *models.Teacher, selectFields ...string) error {
	log.Debug().Ctx(ctx).Msg("TeacherRepository.Update")
	sql := r.db.WithContext(ctx)

	if len(selectFields) > 0 {
		sql = sql.Select(selectFields)
	}
	return sql.Updates(teacher).Error
}

func (r *TeacherRepository) AdminTeacherList(
	ctx context.Context,
	args adminInputs.TeachersInput,
	preloads ...CustomPreload,
) (*pagination.PaginationData, error) {
	log.Debug().Ctx(ctx).Msg("TeacherRepository.AdminTeacherList")

	queryInput, paginationData := args.ToPaginationDataAndQuery()
	paginationData.Collection = []*models.Teacher{}

	dbTables := r.db.WithContext(ctx).Model(&models.Teacher{})
	for _, preload := range preloads {
		dbTables = dbTables.Preload(preload.Key, preload.Args...)
	}

	paginationScope, err := pagination.Paginate(ctx, dbTables.Scopes(
		r.basicEnteredEq(true),
		r.nameCont(queryInput.NameCont),
		r.phoneNumberCont(queryInput.PhoneNumberCont),
	), &paginationData, r.tableName)
	if err != nil {
		return &paginationData, err
	}

	err = dbTables.Scopes(paginationScope).
		Order("teachers.id DESC").
		Find(&paginationData.Collection).Error

	return &paginationData, err
}

func (r *TeacherRepository) PublicTeacherList(
	ctx context.Context,
	args publicInputs.TeachersInput,
	preloads ...CustomPreload,
) (*pagination.PaginationData, error) {
	log.Debug().Ctx(ctx).Msg("TeacherRepository.PublicTeacherList")

	queryInput, paginationData, orderClause := args.ToPaginationDataAndQuery()
	paginationData.Collection = []*models.Teacher{}

	dbTables := r.db.WithContext(ctx).Model(&models.Teacher{}).Joins(teacherInnerJoinCourseSQL).
		Where("courses.status = ? AND courses.is_public = true", enums.CourseStatusApproved).Having("COUNT(courses.id) > 0")

	for _, preload := range preloads {
		dbTables = dbTables.Preload(preload.Key, preload.Args...)
	}

	paginationScope, err := pagination.Paginate(ctx, dbTables.Scopes(
		r.activated(),
		r.basicEnteredEq(true),
		r.nameCont(queryInput.NameCont),
	), &paginationData, r.tableName)
	if err != nil {
		return &paginationData, err
	}

	err = dbTables.Scopes(paginationScope).
		Group("teachers.id").
		Order(orderClause).
		Find(&paginationData.Collection).Error

	return &paginationData, err
}

func (r *TeacherRepository) UserTeacherList(
	ctx context.Context,
	args userInputs.TeachersInput,
	preloads ...CustomPreload,
) (*pagination.PaginationData, error) {
	log.Debug().Ctx(ctx).Msg("TeacherRepository.UserTeacherList")

	queryInput, paginationData := args.ToPaginationDataAndQuery()
	paginationData.Collection = []*models.Teacher{}

	dbTables := r.db.WithContext(ctx).Model(&models.Teacher{})
	for _, preload := range preloads {
		dbTables = dbTables.Preload(preload.Key, preload.Args...)
	}

	paginationScope, err := pagination.Paginate(ctx, dbTables.Scopes(
		r.basicEnteredEq(true),
		r.nameCont(queryInput.NameCont),
	), &paginationData, r.tableName)
	if err != nil {
		return &paginationData, err
	}

	err = dbTables.Scopes(paginationScope).
		Order("teachers.id DESC").
		Find(&paginationData.Collection).Error

	return &paginationData, err
}
