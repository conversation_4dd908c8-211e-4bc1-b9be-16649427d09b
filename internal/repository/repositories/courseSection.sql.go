package repositories

const courseSectionInnerJoinCourseSQL = "INNER JOIN courses ON course_sections.course_id = courses.id"
const courseSectionLeftJoinUserCourseSectionSQL = "LEFT JOIN user_course_sections ON user_course_sections.course_section_id = course_sections.id"

const courseSectionsOrderByPositionAscSql = "course_sections.position ASC"

const courseSectionExistUserCourseSectionSQL = "EXISTS (SELECT 1 FROM user_course_sections WHERE user_course_sections.course_section_id = course_sections.id AND user_course_sections.user_id = ?)"
