package repositories

import (
	"context"
	"time"
	"vibico-education-api/internal/enums"
	"vibico-education-api/internal/models"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

type VideoModerationRepository struct {
	Repository
}

func NewVideoModerationRepository(db *gorm.DB) *VideoModerationRepository {
	return &VideoModerationRepository{
		Repository: Repository{db: db, tableName: "video_moderations"},
	}
}

func (r *VideoModerationRepository) FindByID(ctx context.Context, id uint32, preloads ...CustomPreload) (*models.VideoModeration, error) {
	log.Debug().Ctx(ctx).Msg("VideoModerationRepository.FindByID")

	var videoModeration models.VideoModeration

	query := r.db.WithContext(ctx).Table(r.tableName)

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	if err := query.Take(&videoModeration, id).Error; err != nil {
		return nil, err
	}

	return &videoModeration, nil
}

func (r *VideoModerationRepository) FindByVideoID(ctx context.Context, videoID uint32, preloads ...CustomPreload) ([]*models.VideoModeration, error) {
	log.Debug().Ctx(ctx).Msg("VideoModerationRepository.FindByVideoID")

	var videoModerations []*models.VideoModeration

	query := r.db.WithContext(ctx).Table(r.tableName).
		Where("video_id = ?", videoID)

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	if err := query.Find(&videoModerations).Error; err != nil {
		return nil, err
	}

	return videoModerations, nil
}

func (r *VideoModerationRepository) FindLatestByVideoID(ctx context.Context, videoID uint32, preloads ...CustomPreload) (*models.VideoModeration, error) {
	log.Debug().Ctx(ctx).Msg("VideoModerationRepository.FindLatestByVideoID")

	var videoModeration models.VideoModeration

	query := r.db.WithContext(ctx).Table(r.tableName).
		Where("video_id = ?", videoID).
		Order("created_at DESC")

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	if err := query.Take(&videoModeration).Error; err != nil {
		return nil, err
	}

	return &videoModeration, nil
}

func (r *VideoModerationRepository) Create(ctx context.Context, videoModeration *models.VideoModeration, selectFields ...string) error {
	log.Debug().Ctx(ctx).Msg("VideoModerationRepository.Create")

	tx := r.db.WithContext(ctx)
	if len(selectFields) > 0 {
		tx = tx.Select(selectFields)
	}

	return tx.Create(videoModeration).Error
}

func (r *VideoModerationRepository) Update(ctx context.Context, videoModeration *models.VideoModeration, selectFields ...string) error {
	log.Debug().Ctx(ctx).Msg("VideoModerationRepository.Update")

	tx := r.db.WithContext(ctx)
	if len(selectFields) > 0 {
		tx = tx.Select(selectFields)
	}

	return tx.Save(videoModeration).Error
}

func (r *VideoModerationRepository) UpdateStatus(ctx context.Context, id uint32, status enums.VideoModerationStatus, comment string, moderatorID uint32) error {
	log.Debug().Ctx(ctx).Msg("VideoModerationRepository.UpdateStatus")

	now := time.Now()

	updates := map[string]interface{}{
		"status":       status,
		"comment":      comment,
		"moderator_id": moderatorID,
		"moderated_at": now,
		"updated_at":   now,
	}

	return r.db.WithContext(ctx).Table(r.tableName).
		Where("id = ?", id).
		Updates(updates).Error
}

func (r *VideoModerationRepository) Delete(ctx context.Context, videoModeration *models.VideoModeration) error {
	log.Debug().Ctx(ctx).Msg("VideoModerationRepository.Delete")

	return r.db.WithContext(ctx).Delete(videoModeration).Error
}
