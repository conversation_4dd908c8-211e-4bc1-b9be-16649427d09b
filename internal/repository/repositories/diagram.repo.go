package repositories

import (
	"context"
	"vibico-education-api/internal/models"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

type DiagramRepository struct {
	Repository
}

func NewDiagramRepository(db *gorm.DB) *DiagramRepository {
	return &DiagramRepository{
		Repository: Repository{
			db:        db,
			tableName: "diagrams",
		},
	}
}

func (r *DiagramRepository) FindById(ctx context.Context, id uint32, preloads ...CustomPreload) (*models.Diagram, error) {
	log.Debug().Ctx(ctx).Msg("DiagramRepository.FindById")

	var diagram models.Diagram

	query := r.db.WithContext(ctx).Table(r.tableName)
	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	err := query.Take(&diagram, id).Error

	return &diagram, err
}

func (r *DiagramRepository) DeleteByParent(ctx context.Context, parentId uint32, parentType string) error {
	log.Debug().Ctx(ctx).Msg("DiagramRepository.DeleteByParent")

	return r.db.Where("diagrams.parent_id = ? AND diagrams.parent_type = ?", parentId, parentType).
		Delete(&models.Diagram{}).Error
}
