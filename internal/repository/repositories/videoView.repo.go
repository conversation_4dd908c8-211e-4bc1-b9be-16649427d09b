package repositories

import (
	"context"
	"vibico-education-api/internal/models"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

type VideoViewRepository struct {
	Repository
}

func NewVideoViewRepository(db *gorm.DB) *VideoViewRepository {
	return &VideoViewRepository{
		Repository: Repository{db: db, tableName: "video_views"},
	}
}

func (r *VideoViewRepository) FindByID(ctx context.Context, id uint32, preloads ...CustomPreload) (*models.VideoView, error) {
	log.Debug().Ctx(ctx).Msg("VideoViewRepository.FindByID")

	var videoView models.VideoView

	query := r.db.WithContext(ctx).Table(r.tableName)

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	if err := query.Take(&videoView, id).Error; err != nil {
		return nil, err
	}

	return &videoView, nil
}

func (r *VideoViewRepository) FindByUserIDAndVideoID(ctx context.Context, userID, videoID uint32, preloads ...CustomPreload) ([]*models.VideoView, error) {
	log.Debug().Ctx(ctx).Msg("VideoViewRepository.FindByUserIDAndVideoID")

	var videoViews []*models.VideoView

	query := r.db.WithContext(ctx).Table(r.tableName).
		Where("user_id = ? AND video_id = ?", userID, videoID)

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	if err := query.Find(&videoViews).Error; err != nil {
		return nil, err
	}

	return videoViews, nil
}

func (r *VideoViewRepository) FindByVideoID(ctx context.Context, videoID uint32, preloads ...CustomPreload) ([]*models.VideoView, error) {
	log.Debug().Ctx(ctx).Msg("VideoViewRepository.FindByVideoID")

	var videoViews []*models.VideoView

	query := r.db.WithContext(ctx).Table(r.tableName).
		Where("video_id = ?", videoID)

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	if err := query.Find(&videoViews).Error; err != nil {
		return nil, err
	}

	return videoViews, nil
}

func (r *VideoViewRepository) CountByVideoID(ctx context.Context, videoID uint32) (int64, error) {
	log.Debug().Ctx(ctx).Msg("VideoViewRepository.CountByVideoID")

	var count int64

	if err := r.db.WithContext(ctx).Table(r.tableName).
		Where("video_id = ?", videoID).
		Count(&count).Error; err != nil {
		return 0, err
	}

	return count, nil
}

func (r *VideoViewRepository) Create(ctx context.Context, videoView *models.VideoView, selectFields ...string) error {
	log.Debug().Ctx(ctx).Msg("VideoViewRepository.Create")

	tx := r.db.WithContext(ctx)
	if len(selectFields) > 0 {
		tx = tx.Select(selectFields)
	}

	return tx.Create(videoView).Error
}

func (r *VideoViewRepository) Delete(ctx context.Context, videoView *models.VideoView) error {
	log.Debug().Ctx(ctx).Msg("VideoViewRepository.Delete")

	return r.db.WithContext(ctx).Delete(videoView).Error
}
