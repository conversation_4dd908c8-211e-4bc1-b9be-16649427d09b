package repositories

import (
	"context"
	"time"
	"vibico-education-api/internal/enums"
	"vibico-education-api/internal/models"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

type VideoPlatformRepository struct {
	Repository
}

func NewVideoPlatformRepository(db *gorm.DB) *VideoPlatformRepository {
	return &VideoPlatformRepository{
		Repository: Repository{db: db, tableName: "video_platforms"},
	}
}

func (r *VideoPlatformRepository) FindByID(ctx context.Context, id uint32, preloads ...CustomPreload) (*models.VideoPlatform, error) {
	log.Debug().Ctx(ctx).Msg("VideoPlatformRepository.FindByID")

	var videoPlatform models.VideoPlatform

	query := r.db.WithContext(ctx).Table(r.tableName)

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	if err := query.Take(&videoPlatform, id).Error; err != nil {
		return nil, err
	}

	return &videoPlatform, nil
}

func (r *VideoPlatformRepository) FindByVideoIDAndPlatform(ctx context.Context, videoID uint32, platform enums.VideoPlatform, preloads ...CustomPreload) (*models.VideoPlatform, error) {
	log.Debug().Ctx(ctx).Msg("VideoPlatformRepository.FindByVideoIDAndPlatform")

	var videoPlatform models.VideoPlatform

	query := r.db.WithContext(ctx).Table(r.tableName).
		Where("video_id = ? AND platform = ?", videoID, platform)

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	if err := query.Take(&videoPlatform).Error; err != nil {
		return nil, err
	}

	return &videoPlatform, nil
}

func (r *VideoPlatformRepository) FindByVideoID(ctx context.Context, videoID uint32, preloads ...CustomPreload) ([]*models.VideoPlatform, error) {
	log.Debug().Ctx(ctx).Msg("VideoPlatformRepository.FindByVideoID")

	var videoPlatforms []*models.VideoPlatform

	query := r.db.WithContext(ctx).Table(r.tableName).
		Where("video_id = ?", videoID)

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	if err := query.Find(&videoPlatforms).Error; err != nil {
		return nil, err
	}

	return videoPlatforms, nil
}

func (r *VideoPlatformRepository) Create(ctx context.Context, videoPlatform *models.VideoPlatform, selectFields ...string) error {
	log.Debug().Ctx(ctx).Msg("VideoPlatformRepository.Create")

	tx := r.db.WithContext(ctx)
	if len(selectFields) > 0 {
		tx = tx.Select(selectFields)
	}

	return tx.Create(videoPlatform).Error
}

func (r *VideoPlatformRepository) Update(ctx context.Context, videoPlatform *models.VideoPlatform, selectFields ...string) error {
	log.Debug().Ctx(ctx).Msg("VideoPlatformRepository.Update")

	tx := r.db.WithContext(ctx)
	if len(selectFields) > 0 {
		tx = tx.Select(selectFields)
	}

	return tx.Save(videoPlatform).Error
}

func (r *VideoPlatformRepository) UpdateStatus(ctx context.Context, id uint32, status enums.VideoPlatformStatus) error {
	log.Debug().Ctx(ctx).Msg("VideoPlatformRepository.UpdateStatus")

	now := time.Now()

	updates := map[string]interface{}{
		"status":     status,
		"updated_at": now,
	}

	if status == enums.VideoPlatformStatusAvailable {
		updates["sync_at"] = now
	}

	return r.db.WithContext(ctx).Table(r.tableName).
		Where("id = ?", id).
		Updates(updates).Error
}

func (r *VideoPlatformRepository) Delete(ctx context.Context, videoPlatform *models.VideoPlatform) error {
	log.Debug().Ctx(ctx).Msg("VideoPlatformRepository.Delete")

	return r.db.WithContext(ctx).Delete(videoPlatform).Error
}
