package repositories

const courseSectionItemInnerJoinSectionSQL = "INNER JOIN course_sections ON course_sections.id = course_section_items.course_section_id"
const courseSectionItemLeftJoinSectionSQL = "LEFT JOIN course_sections ON course_sections.id = course_section_items.course_section_id"

const CSIOrderPositionAscSQL = "course_section_items.position ASC"

const courseSectionItemSelectSQL = `
	DISTINCT ON (course_section_items.id)
	course_section_items.id,
	course_section_items.course_section_id,
	course_section_items.title,
	course_section_items.slug,
	course_section_items.content,
	course_section_items.position,
	course_section_items.type,
	course_section_items.created_at,
	course_section_items.updated_at
	`

const courseSectionItemExistUserSectionItemSQL = "EXISTS (SELECT 1 FROM user_course_section_items WHERE user_course_section_items.course_section_item_id = course_section_items.id and user_course_section_items.user_id = ?)"
