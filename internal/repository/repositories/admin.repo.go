package repositories

import (
	"context"
	"vibico-education-api/internal/models"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

type AdminRepository struct {
	Repository
}

func NewAdminRepository(db *gorm.DB) *AdminRepository {
	return &AdminRepository{
		Repository: Repository{db: db, tableName: "admins"},
	}
}

func (r *AdminRepository) FindByAuthId(ctx context.Context, authId string) (*models.Admin, error) {
	log.Debug().Ctx(ctx).Msg("AdminRepository.FindByAuthId")

	var admin models.Admin
	err := r.db.WithContext(ctx).Where("auth_id = ?", authId).Take(&admin).Error
	if err != nil {
		return nil, err
	}

	return &admin, nil
}
