package repositories

// ORDER BY
const drillsOrderByIdDescSql = "drills.id DESC"
const drillsOrderByCensorAndIdSql = "drills.censor ASC, drills.id DESC"

// JOIN
const drillLeftJoinDrillSkillSql = "LEFT JOIN drill_skills ON drill_skills.drill_id = drills.id"
const drillInnerJoinItemSql = "INNER JOIN course_section_item_drills ON course_section_item_drills.drill_id = drills.id"
const drillLeftJoinUserDrillSql = "LEFT JOIN user_drills ON user_drills.drill_id = drills.id"

// Mutate
const drillUpdateToDraft = "UPDATE drills SET censor = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?"
