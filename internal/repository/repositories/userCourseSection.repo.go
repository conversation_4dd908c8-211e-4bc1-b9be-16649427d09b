package repositories

import (
	"context"
	"vibico-education-api/internal/models"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

type UserCourseSectionRepository struct {
	Repository
}

func NewUserCourseSectionRepository(db *gorm.DB) *UserCourseSectionRepository {
	return &UserCourseSectionRepository{
		Repository: Repository{db: db, tableName: "user_course_sections"},
	}
}

func (r *UserCourseSectionRepository) FindOrInit(
	ctx context.Context, userId, sectionId uint32,
) *models.UserCourseSection {
	log.Debug().Ctx(ctx).Msg("UserCourseSectionRepository.FindOrInit")

	userSection := models.UserCourseSection{
		UserID:          userId,
		CourseSectionId: sectionId,
	}

	r.db.WithContext(ctx).
		Where("user_course_sections.user_id = ? AND user_course_sections.course_section_id = ?", userId, sectionId).
		Take(&userSection)

	return &userSection
}

func (r *UserCourseSectionRepository) Create(
	ctx context.Context, userSection *models.UserCourseSection, currentUserId uint32,
) error {
	log.Debug().Ctx(ctx).Msg("UserCourseSectionRepository.Create")

	return TransactionWithLogging(r.db.WithContext(ctx), func(tx *gorm.DB) error {
		if err := tx.Create(&userSection).Error; err != nil {
			return err
		}

		result := tx.Exec(updateCourseCompletedSectionCountSQL, userSection.CourseSectionId, currentUserId)

		if result.Error != nil {
			return result.Error
		}

		return nil
	})
}
