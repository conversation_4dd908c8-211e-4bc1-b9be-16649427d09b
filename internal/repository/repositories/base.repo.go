package repositories

import (
	"fmt"
	"strings"
	"time"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

type Repository struct {
	db        *gorm.DB
	tableName string
}

func (r *Repository) field(column string) string {
	return fmt.Sprintf("%s.%s", r.tableName, column)
}

type CustomPreload struct {
	Key  string
	Args []any
}

func TransactionWithLogging(db *gorm.DB, fc func(tx *gorm.DB) error) error {
	ctx := db.Statement.Context
	now := time.Now()

	log.Info().Ctx(ctx).Time("startTime", now).Msg("TRANSACTION BEGIN")

	err := db.Transaction(func(tx *gorm.DB) error {
		return fc(tx)
	})

	if err != nil {
		log.Error().Ctx(ctx).Err(err).TimeDiff("Duration", time.Now(), now).Msg("TRANSACTION ROLLBACK")
	} else {
		log.Info().Ctx(ctx).TimeDiff("Duration", time.Now(), now).Msg("TRANSACTION COMMIT")
	}

	return err
}

func whereEq[T comparable](field string, value *T) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if value == nil || isZeroAndString(*value) {
			return db
		}

		return db.Where(fmt.Sprintf("%s = ?", field), *value)
	}
}

func whereNotEq[T comparable](field string, value *T) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if value == nil || isZeroAndString(*value) {
			return db
		}

		return db.Where(fmt.Sprintf("%s <> ?", field), *value)
	}
}

func isZeroAndString[T any](v T) bool {
	switch val := any(v).(type) {
	case string:
		return strings.TrimSpace(val) == ""
	default:
		return false
	}
}
