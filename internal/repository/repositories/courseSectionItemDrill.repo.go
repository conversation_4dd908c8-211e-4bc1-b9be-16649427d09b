package repositories

import (
	"context"
	"errors"
	"fmt"
	"os"
	"strings"
	"time"
	"vibico-education-api/internal/enums"
	"vibico-education-api/internal/models"
	"vibico-education-api/pkg/gcs"
	"vibico-education-api/pkg/helpers"

	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

type CourseSectionItemDrillRepository struct {
	Repository
}

func NewCourseSectionItemDrillRepository(db *gorm.DB) *CourseSectionItemDrillRepository {
	return &CourseSectionItemDrillRepository{
		Repository: Repository{db: db, tableName: "course_section_item_drills"},
	}
}

func (r *CourseSectionItemDrillRepository) DeleteByItemId(ctx context.Context, itemId uint32) error {
	log.Debug().Ctx(ctx).Msg("CourseSectionItemDrillRepository.DeleteByItemId")

	return r.db.WithContext(ctx).
		Where("course_section_item_drills.course_section_item_id = ?", itemId).
		Delete(&models.CourseSectionItemDrill{}).Error
}

func (r *CourseSectionItemDrillRepository) FindByDrillIDAndCourseSectionItemID(ctx context.Context, drillId uint32, courseSectionItemID uint32) (*models.CourseSectionItemDrill, error) {
	log.Debug().Ctx(ctx).Msg("CourseSectionItemDrillRepository.FindByDrillIDAndCourseSectionItemID")

	var courseSectionItemDrill models.CourseSectionItemDrill

	if err := r.db.WithContext(ctx).Table(r.tableName).Where(
		"course_section_item_drills.drill_id = ? AND course_section_item_drills.course_section_item_id = ?",
		drillId,
		courseSectionItemID,
	).Take(&courseSectionItemDrill).Error; err != nil {
		return nil, fmt.Errorf("failed to find CourseSectionItemDrill: %w", err)
	}

	return &courseSectionItemDrill, nil
}

// Create clones the drill if it's a master and associates it with a course section item drill.
// It also duplicates any associated diagrams and updates the course section item type.
// Finally, it updates the course status to draft.
func (r *CourseSectionItemDrillRepository) Create(
	ctx context.Context,
	courseSectionItemDrill *models.CourseSectionItemDrill,
	drill *models.Drill,
	courseId uint32,
) error {
	log.Debug().Ctx(ctx).Msg("CourseSectionItemDrillRepository.Create")

	return TransactionWithLogging(r.db.WithContext(ctx), func(tx *gorm.DB) error {
		drillId := drill.ID

		if drill.IsMaster {
			clonedDrill := *drill
			clonedDrill.ID = 0
			clonedDrill.IsMaster = false
			clonedDrill.Slug = helpers.GenerateSlug(drill.Title)
			level, _ := enums.ParseDrillLevel(drill.Level.String())
			clonedDrill.Level = level
			timeNow := time.Now()
			clonedDrill.CreatedAt = &timeNow
			clonedDrill.UpdatedAt = &timeNow

			if err := tx.Model(&models.Drill{}).Create(&clonedDrill).Error; err != nil {
				return err
			}
			drillId = clonedDrill.ID

			var originalDiagrams []*models.Diagram
			if err := tx.Model(&models.Diagram{}).
				Where("diagrams.parent_id = ? AND parent_type = 'Drill'", drill.ID).Find(&originalDiagrams).Error; err != nil {
				return err
			}

			isLocalStorage := utils.GetEnv("STORAGE_SERVICE", "local") == "local"
			if !isLocalStorage {
				bucketName := os.Getenv("GCS_BUCKET_NAME")
				projectId := os.Getenv("GCS_PROJECT_ID")
				gcsAccountService := os.Getenv("GOOGLE_APPLICATION_CREDENTIALS")

				if bucketName == "" || projectId == "" || gcsAccountService == "" {
					return errors.New("invalid Setting for Upload")
				}
			}

			if len(originalDiagrams) > 0 {
				cloneDiagrams := make([]models.Diagram, len(originalDiagrams))
				for i, diagram := range originalDiagrams {
					clonedDiagram := diagram
					clonedDiagram.ID = 0
					clonedDiagram.ParentID = clonedDrill.ID
					clonedDiagram.CreatedAt = time.Now()
					clonedDiagram.UpdatedAt = time.Now()

					if !isLocalStorage {
						if diagram.ImageUrl != nil && strings.TrimSpace(*diagram.ImageUrl) != "" {
							srcBucket, srcObject := gcs.ParseGCSUrl(*diagram.ImageUrl)

							dstUrl, err := gcs.CopyFileGCS(srcBucket, srcBucket, srcObject)
							if err != nil {
								return err
							}
							clonedDiagram.ImageUrl = dstUrl
						}
					}

					cloneDiagrams[i] = *clonedDiagram
				}
				if err := tx.Model(&models.Diagram{}).Create(&cloneDiagrams).Error; err != nil {
					return err
				}
			}
		}

		courseSectionItemDrill.DrillId = drillId
		if err := tx.Create(&courseSectionItemDrill).Error; err != nil {
			return err
		}

		if err := tx.Model(&models.CourseSectionItem{}).
			Where("id = ?", courseSectionItemDrill.CourseSectionItemId).
			Update("type", enums.CourseSectionItemTypeDrill).Error; err != nil {
			return err
		}

		return tx.Exec(courseUpdateToDraft, courseId).Error
	})
}
