package repositories

import (
	"context"
	"time"
	"vibico-education-api/internal/enums"
	teacherInputs "vibico-education-api/internal/gqls/inputs/teachers"
	"vibico-education-api/internal/models"

	"github.com/BehemothLtd/behemoth-pkg/golang/pagination"
	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

type CourseUserRepository struct {
	Repository
}

func NewCourseUserRepository(db *gorm.DB) *CourseUserRepository {
	return &CourseUserRepository{
		Repository: Repository{db: db, tableName: "course_users"},
	}
}

func (r *CourseUserRepository) Create(ctx context.Context, courseUser *models.CourseUser) error {
	log.Debug().Ctx(ctx).Msg("CourseUserRepository.Create")

	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		if courseUser.Status == enums.CourseUserStatusEnrolled {
			timeNow := time.Now()
			courseUser.JoinedAt = &timeNow
		}

		if err := tx.Create(&courseUser).Error; err != nil {
			return err
		}

		if courseUser.Status == enums.CourseUserStatusEnrolled {
			if err := tx.Exec(courseIncreaseJoinedUseCountSQL, courseUser.CourseID).Error; err != nil {
				return err
			}

			var teacherId int32
			if err := tx.Table("courses").Where("id = ?", courseUser.CourseID).Select("teacher_id").Limit(1).Scan(&teacherId).Error; err != nil {
				return err
			}

			if err := tx.Exec(teacherIncreaseStudentCountSQL, teacherId).Error; err != nil {
				return err
			}
		}

		return nil
	})
}

func (r *CourseUserRepository) Update(ctx context.Context, courseUser *models.CourseUser, isIncrease bool, selectFields ...string) error {
	log.Debug().Ctx(ctx).Msg("CourseUserRepository.Update")

	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		sql := tx.Model(&courseUser)
		if len(selectFields) > 0 {
			sql = sql.Select(selectFields)
		}

		if err := sql.Save(&courseUser).Error; err != nil {
			return err
		}

		if isIncrease {
			if err := tx.Exec(courseIncreaseJoinedUseCountSQL, courseUser.CourseID).Error; err != nil {
				return err
			}

			var teacherId int32
			if err := tx.Table("courses").Where("id = ?", courseUser.CourseID).Select("teacher_id").Scan(&teacherId).Error; err != nil {
				return err
			}

			if err := tx.Exec(teacherIncreaseStudentCountSQL, teacherId).Error; err != nil {
				return err
			}
		}
		return nil
	})
}

func (r *CourseUserRepository) FindOrInit(ctx context.Context, courseId, userId uint32) *models.CourseUser {
	log.Debug().Ctx(ctx).Msg("CourseUserRepository.FindOrInit")

	courseUser := models.CourseUser{
		CourseID: courseId,
		UserID:   userId,
	}

	r.db.WithContext(ctx).
		Where("course_users.course_id = ? AND course_users.user_id = ?", courseId, userId).
		Take(&courseUser)

	return &courseUser
}

func (r *CourseUserRepository) FindByUserAndCourse(
	ctx context.Context, courseId, userId uint32,
) (*models.CourseUser, error) {
	log.Debug().Ctx(ctx).Msg("CourseUserRepository.FindByUserAndCourse")
	var courseUser models.CourseUser

	err := r.db.WithContext(ctx).
		Where("course_users.course_id = ? AND course_users.user_id = ?", courseId, userId).
		Take(&courseUser).Error

	return &courseUser, err
}

func (r *CourseUserRepository) FindByUserEnrolledAndCourse(
	ctx context.Context,
	courseId, userId uint32,
	preloads ...CustomPreload,
) (*models.CourseUser, error) {
	log.Debug().Ctx(ctx).Msg("CourseUserRepository.FindActiveByUserAndCourse")

	var courseUser models.CourseUser
	status := enums.CourseUserStatusInvited

	query := r.db.WithContext(ctx).
		Scopes(
			r.courseIdEq(&courseId),
			whereEq(r.field("user_id"), &userId),
			whereNotEq(r.field("status"), &status),
		)

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	err := query.Take(&courseUser).Error

	return &courseUser, err
}

func (r *CourseUserRepository) ListByCourse(
	ctx context.Context,
	args teacherInputs.CourseUsersInput,
	preloads ...CustomPreload,
) (*pagination.PaginationData, error) {
	log.Debug().Ctx(ctx).Msg("CourseUserRepository.ListByCourse")

	queryInput, paginationData := args.ToPaginationDataAndQuery()
	paginationData.Collection = []*models.CourseUser{}

	dbTables := r.db.WithContext(ctx).Model(&models.CourseUser{})
	for _, preload := range preloads {
		dbTables = dbTables.Preload(preload.Key, preload.Args...)
	}

	courseId := utils.GetGqlUint32OrDefault(&args.CourseId)

	paginationScope, err := pagination.Paginate(ctx, dbTables.Scopes(
		r.courseIdEq(&courseId),
		r.createdAtGteq(queryInput.CreatedAtGteq),
	), &paginationData, r.tableName)
	if err != nil {
		return &paginationData, err
	}

	err = dbTables.Scopes(paginationScope).
		Order("course_users.id DESC").
		Find(&paginationData.Collection).Error

	return &paginationData, err
}

func (r *CourseUserRepository) ListInvitedCourseByUser(
	ctx context.Context, userId uint32, preloads ...CustomPreload) ([]*models.CourseUser, error) {
	log.Debug().Ctx(ctx).Msg("CourseUserRepository.ListInvitedCourseByUser")

	var invitedCourses []*models.CourseUser

	invitedEnum := enums.CourseUserStatusInvited.ToInt64()
	dbTables := r.db.Model(models.CourseUser{}).
		Scopes(whereEq(r.field("user_id"), &userId), whereEq(r.field("status"), &invitedEnum))

	for _, preload := range preloads {
		dbTables = dbTables.Preload(preload.Key, preload.Args...)
	}

	if err := dbTables.Order("course_users.id DESC").Find(&invitedCourses).Error; err != nil {
		return nil, err
	}

	return invitedCourses, nil
}
