package repositories

import (
	"context"
	"fmt"
	"vibico-education-api/internal/models"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

type NotificationRepository struct {
	Repository
}

func NewNotificationRepository(db *gorm.DB) *NotificationRepository {
	return &NotificationRepository{
		Repository: Repository{db: db, tableName: "notifications"},
	}
}

func (r *NotificationRepository) FindByRecipientId(ctx context.Context, recipientId uint32, recipientType string, preloads ...CustomPreload) ([]models.Notification, error) {
	log.Debug().Ctx(ctx).Msg("NotificationRepository.FindByRecipientId")

	var notifications []models.Notification

	query := r.db.WithContext(ctx).Table(r.tableName).
		Where("recipient_id = ?", recipientId).
		Where("recipient_type = ?", recipientType)

	for _, preload := range preloads {
		query = query.Preload(preload.Key, preload.Args...)
	}

	if err := query.Find(&notifications).Error; err != nil {
		return nil, err
	}

	return notifications, nil
}

func (r *NotificationRepository) Create(ctx context.Context, notification *models.Notification) error {
	log.Debug().Ctx(ctx).Msg("NotificationRepository.Create")

	fmt.Println("notificationnotificationnotificationnotification", notification)
	return r.db.WithContext(ctx).Create(notification).Error
}

func (r *NotificationRepository) MakeRead(ctx context.Context, notificationId uint32, recipientId uint32, recipientType string) error {
	log.Debug().Ctx(ctx).Msg("NotificationRepository.MakeRead")

	return r.db.WithContext(ctx).Model(&models.Notification{}).Where("id = ?", notificationId).Where("recipient_id = ?", recipientId).Where("recipient_type = ?", recipientType).Update("is_read", true).Error
}
