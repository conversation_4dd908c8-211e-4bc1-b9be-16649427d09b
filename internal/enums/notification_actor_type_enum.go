// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package enums

import (
	"database/sql/driver"
	"errors"
	"fmt"
	"strconv"
	"strings"
)

const (
	// NotificationActorTypeUser is a NotificationActorType of type User.
	NotificationActorTypeUser NotificationActorType = "User"
	// NotificationActorTypeTeacher is a NotificationActorType of type Teacher.
	NotificationActorTypeTeacher NotificationActorType = "Teacher"
	// NotificationActorTypeAdmin is a NotificationActorType of type Admin.
	NotificationActorTypeAdmin NotificationActorType = "Admin"
)

var ErrInvalidNotificationActorType = fmt.Errorf("not a valid NotificationActorType, try [%s]", strings.Join(_NotificationActorTypeNames, ", "))

var _NotificationActorTypeNames = []string{
	string(NotificationActorTypeUser),
	string(NotificationActorTypeTeacher),
	string(NotificationActorTypeAdmin),
}

// NotificationActorTypeNames returns a list of possible string values of NotificationActorType.
func NotificationActorTypeNames() []string {
	tmp := make([]string, len(_NotificationActorTypeNames))
	copy(tmp, _NotificationActorTypeNames)
	return tmp
}

// NotificationActorTypeValues returns a list of the values for NotificationActorType
func NotificationActorTypeValues() []NotificationActorType {
	return []NotificationActorType{
		NotificationActorTypeUser,
		NotificationActorTypeTeacher,
		NotificationActorTypeAdmin,
	}
}

// String implements the Stringer interface.
func (x NotificationActorType) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x NotificationActorType) IsValid() bool {
	_, err := ParseNotificationActorType(string(x))
	return err == nil
}

var _NotificationActorTypeValue = map[string]NotificationActorType{
	"User":    NotificationActorTypeUser,
	"Teacher": NotificationActorTypeTeacher,
	"Admin":   NotificationActorTypeAdmin,
}

// ParseNotificationActorType attempts to convert a string to a NotificationActorType.
func ParseNotificationActorType(name string) (NotificationActorType, error) {
	if x, ok := _NotificationActorTypeValue[name]; ok {
		return x, nil
	}
	return NotificationActorType(""), fmt.Errorf("%s is %w", name, ErrInvalidNotificationActorType)
}

// MarshalText implements the text marshaller method.
func (x NotificationActorType) MarshalText() ([]byte, error) {
	return []byte(string(x)), nil
}

// UnmarshalText implements the text unmarshaller method.
func (x *NotificationActorType) UnmarshalText(text []byte) error {
	tmp, err := ParseNotificationActorType(string(text))
	if err != nil {
		return err
	}
	*x = tmp
	return nil
}

var errNotificationActorTypeNilPtr = errors.New("value pointer is nil") // one per type for package clashes

var sqlIntNotificationActorTypeMap = map[int64]NotificationActorType{
	0: NotificationActorTypeUser,
	1: NotificationActorTypeTeacher,
	2: NotificationActorTypeAdmin,
}

var sqlIntNotificationActorTypeValue = map[NotificationActorType]int64{
	NotificationActorTypeUser:    0,
	NotificationActorTypeTeacher: 1,
	NotificationActorTypeAdmin:   2,
}

func lookupSqlIntNotificationActorType(val int64) (NotificationActorType, error) {
	x, ok := sqlIntNotificationActorTypeMap[val]
	if !ok {
		return x, fmt.Errorf("%v is not %w", val, ErrInvalidNotificationActorType)
	}
	return x, nil
}

// Scan implements the Scanner interface.
func (x *NotificationActorType) Scan(value interface{}) (err error) {
	if value == nil {
		*x = NotificationActorType("")
		return
	}

	// A wider range of scannable types.
	// driver.Value values at the top of the list for expediency
	switch v := value.(type) {
	case int64:
		*x, err = lookupSqlIntNotificationActorType(v)
	case string:
		*x, err = ParseNotificationActorType(v)
	case []byte:
		if val, verr := strconv.ParseInt(string(v), 10, 64); verr == nil {
			*x, err = lookupSqlIntNotificationActorType(val)
		} else {
			// try parsing the value as a string
			*x, err = ParseNotificationActorType(string(v))
		}
	case NotificationActorType:
		*x = v
	case int:
		*x, err = lookupSqlIntNotificationActorType(int64(v))
	case *NotificationActorType:
		if v == nil {
			return errNotificationActorTypeNilPtr
		}
		*x = *v
	case uint:
		*x, err = lookupSqlIntNotificationActorType(int64(v))
	case uint64:
		*x, err = lookupSqlIntNotificationActorType(int64(v))
	case *int:
		if v == nil {
			return errNotificationActorTypeNilPtr
		}
		*x, err = lookupSqlIntNotificationActorType(int64(*v))
	case *int64:
		if v == nil {
			return errNotificationActorTypeNilPtr
		}
		*x, err = lookupSqlIntNotificationActorType(int64(*v))
	case float64: // json marshals everything as a float64 if it's a number
		*x, err = lookupSqlIntNotificationActorType(int64(v))
	case *float64: // json marshals everything as a float64 if it's a number
		if v == nil {
			return errNotificationActorTypeNilPtr
		}
		*x, err = lookupSqlIntNotificationActorType(int64(*v))
	case *uint:
		if v == nil {
			return errNotificationActorTypeNilPtr
		}
		*x, err = lookupSqlIntNotificationActorType(int64(*v))
	case *uint64:
		if v == nil {
			return errNotificationActorTypeNilPtr
		}
		*x, err = lookupSqlIntNotificationActorType(int64(*v))
	case *string:
		if v == nil {
			return errNotificationActorTypeNilPtr
		}
		*x, err = ParseNotificationActorType(*v)
	default:
		return errors.New("invalid type for NotificationActorType")
	}

	return
}

// Value implements the driver Valuer interface.
func (x NotificationActorType) Value() (driver.Value, error) {
	val, ok := sqlIntNotificationActorTypeValue[x]
	if !ok {
		return nil, ErrInvalidNotificationActorType
	}
	return int64(val), nil
}

// Additional template
func (x NotificationActorType) ToInt64() int64 {
	return sqlIntNotificationActorTypeValue[x]
}
