// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package enums

import (
	"database/sql/driver"
	"errors"
	"fmt"
	"strconv"
	"strings"
)

const (
	// CourseSectionItemTypeText is a CourseSectionItemType of type text.
	CourseSectionItemTypeText CourseSectionItemType = "text"
	// CourseSectionItemTypeDrill is a CourseSectionItemType of type drill.
	CourseSectionItemTypeDrill CourseSectionItemType = "drill"
	// CourseSectionItemTypeVideo is a CourseSectionItemType of type video.
	CourseSectionItemTypeVideo CourseSectionItemType = "video"
)

var ErrInvalidCourseSectionItemType = fmt.Errorf("not a valid CourseSectionItemType, try [%s]", strings.Join(_CourseSectionItemTypeNames, ", "))

var _CourseSectionItemTypeNames = []string{
	string(CourseSectionItemTypeText),
	string(CourseSectionItemTypeDrill),
	string(CourseSectionItemTypeVideo),
}

// CourseSectionItemTypeNames returns a list of possible string values of CourseSectionItemType.
func CourseSectionItemTypeNames() []string {
	tmp := make([]string, len(_CourseSectionItemTypeNames))
	copy(tmp, _CourseSectionItemTypeNames)
	return tmp
}

// CourseSectionItemTypeValues returns a list of the values for CourseSectionItemType
func CourseSectionItemTypeValues() []CourseSectionItemType {
	return []CourseSectionItemType{
		CourseSectionItemTypeText,
		CourseSectionItemTypeDrill,
		CourseSectionItemTypeVideo,
	}
}

// String implements the Stringer interface.
func (x CourseSectionItemType) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x CourseSectionItemType) IsValid() bool {
	_, err := ParseCourseSectionItemType(string(x))
	return err == nil
}

var _CourseSectionItemTypeValue = map[string]CourseSectionItemType{
	"text":  CourseSectionItemTypeText,
	"drill": CourseSectionItemTypeDrill,
	"video": CourseSectionItemTypeVideo,
}

// ParseCourseSectionItemType attempts to convert a string to a CourseSectionItemType.
func ParseCourseSectionItemType(name string) (CourseSectionItemType, error) {
	if x, ok := _CourseSectionItemTypeValue[name]; ok {
		return x, nil
	}
	return CourseSectionItemType(""), fmt.Errorf("%s is %w", name, ErrInvalidCourseSectionItemType)
}

// MarshalText implements the text marshaller method.
func (x CourseSectionItemType) MarshalText() ([]byte, error) {
	return []byte(string(x)), nil
}

// UnmarshalText implements the text unmarshaller method.
func (x *CourseSectionItemType) UnmarshalText(text []byte) error {
	tmp, err := ParseCourseSectionItemType(string(text))
	if err != nil {
		return err
	}
	*x = tmp
	return nil
}

var errCourseSectionItemTypeNilPtr = errors.New("value pointer is nil") // one per type for package clashes

var sqlIntCourseSectionItemTypeMap = map[int64]CourseSectionItemType{
	0: CourseSectionItemTypeText,
	1: CourseSectionItemTypeDrill,
	2: CourseSectionItemTypeVideo,
}

var sqlIntCourseSectionItemTypeValue = map[CourseSectionItemType]int64{
	CourseSectionItemTypeText:  0,
	CourseSectionItemTypeDrill: 1,
	CourseSectionItemTypeVideo: 2,
}

func lookupSqlIntCourseSectionItemType(val int64) (CourseSectionItemType, error) {
	x, ok := sqlIntCourseSectionItemTypeMap[val]
	if !ok {
		return x, fmt.Errorf("%v is not %w", val, ErrInvalidCourseSectionItemType)
	}
	return x, nil
}

// Scan implements the Scanner interface.
func (x *CourseSectionItemType) Scan(value interface{}) (err error) {
	if value == nil {
		*x = CourseSectionItemType("")
		return
	}

	// A wider range of scannable types.
	// driver.Value values at the top of the list for expediency
	switch v := value.(type) {
	case int64:
		*x, err = lookupSqlIntCourseSectionItemType(v)
	case string:
		*x, err = ParseCourseSectionItemType(v)
	case []byte:
		if val, verr := strconv.ParseInt(string(v), 10, 64); verr == nil {
			*x, err = lookupSqlIntCourseSectionItemType(val)
		} else {
			// try parsing the value as a string
			*x, err = ParseCourseSectionItemType(string(v))
		}
	case CourseSectionItemType:
		*x = v
	case int:
		*x, err = lookupSqlIntCourseSectionItemType(int64(v))
	case *CourseSectionItemType:
		if v == nil {
			return errCourseSectionItemTypeNilPtr
		}
		*x = *v
	case uint:
		*x, err = lookupSqlIntCourseSectionItemType(int64(v))
	case uint64:
		*x, err = lookupSqlIntCourseSectionItemType(int64(v))
	case *int:
		if v == nil {
			return errCourseSectionItemTypeNilPtr
		}
		*x, err = lookupSqlIntCourseSectionItemType(int64(*v))
	case *int64:
		if v == nil {
			return errCourseSectionItemTypeNilPtr
		}
		*x, err = lookupSqlIntCourseSectionItemType(int64(*v))
	case float64: // json marshals everything as a float64 if it's a number
		*x, err = lookupSqlIntCourseSectionItemType(int64(v))
	case *float64: // json marshals everything as a float64 if it's a number
		if v == nil {
			return errCourseSectionItemTypeNilPtr
		}
		*x, err = lookupSqlIntCourseSectionItemType(int64(*v))
	case *uint:
		if v == nil {
			return errCourseSectionItemTypeNilPtr
		}
		*x, err = lookupSqlIntCourseSectionItemType(int64(*v))
	case *uint64:
		if v == nil {
			return errCourseSectionItemTypeNilPtr
		}
		*x, err = lookupSqlIntCourseSectionItemType(int64(*v))
	case *string:
		if v == nil {
			return errCourseSectionItemTypeNilPtr
		}
		*x, err = ParseCourseSectionItemType(*v)
	default:
		return errors.New("invalid type for CourseSectionItemType")
	}

	return
}

// Value implements the driver Valuer interface.
func (x CourseSectionItemType) Value() (driver.Value, error) {
	val, ok := sqlIntCourseSectionItemTypeValue[x]
	if !ok {
		return nil, ErrInvalidCourseSectionItemType
	}
	return int64(val), nil
}

// Additional template
func (x CourseSectionItemType) ToInt64() int64 {
	return sqlIntCourseSectionItemTypeValue[x]
}
