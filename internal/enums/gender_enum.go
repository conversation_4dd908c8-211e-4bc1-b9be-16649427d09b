// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package enums

import (
	"errors"
	"fmt"
)

const (
	// GenderMale is a Gender of type male.
	GenderMale Gender = "Male"
	// GenderFemale is a Gender of type female.
	GenderFemale Gender = "Female"
)

var ErrInvalidGender = errors.New("not a valid Gender")

// String implements the Stringer interface.
func (x Gender) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x Gender) IsValid() bool {
	_, err := ParseGender(string(x))
	return err == nil
}

var _GenderValue = map[string]Gender{
	"Male":   GenderMale,
	"Female": GenderFemale,
}

// Parse<PERSON><PERSON> attempts to convert a string to a Gender.
func ParseGender(name string) (Gender, error) {
	if x, ok := _GenderValue[name]; ok {
		return x, nil
	}
	return Gender(""), fmt.Errorf("%s is %w", name, ErrInvalidGender)
}

// MarshalText implements the text marshaller method.
func (x Gender) MarshalText() ([]byte, error) {
	return []byte(string(x)), nil
}

// UnmarshalText implements the text unmarshaller method.
func (x *Gender) UnmarshalText(text []byte) error {
	tmp, err := ParseGender(string(text))
	if err != nil {
		return err
	}
	*x = tmp
	return nil
}
