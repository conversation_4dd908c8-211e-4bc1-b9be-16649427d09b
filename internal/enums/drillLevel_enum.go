// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package enums

import (
	"database/sql/driver"
	"errors"
	"fmt"
	"strconv"
	"strings"
)

const (
	// DrillLevelBeginner is a DrillLevel of type beginner.
	DrillLevelBeginner DrillLevel = "beginner"
	// DrillLevelIntermediate is a DrillLevel of type intermediate.
	DrillLevelIntermediate DrillLevel = "intermediate"
	// DrillLevelAdvanced is a DrillLevel of type advanced.
	DrillLevelAdvanced DrillLevel = "advanced"
	// DrillLevelExpert is a DrillLevel of type expert.
	DrillLevelExpert DrillLevel = "expert"
)

var ErrInvalidDrillLevel = fmt.Errorf("not a valid DrillLevel, try [%s]", strings.Join(_DrillLevelNames, ", "))

var _DrillLevelNames = []string{
	string(DrillLevelBeginner),
	string(DrillLevelIntermediate),
	string(DrillLevelAdvanced),
	string(DrillLevelExpert),
}

// DrillLevelNames returns a list of possible string values of DrillLevel.
func DrillLevelNames() []string {
	tmp := make([]string, len(_DrillLevelNames))
	copy(tmp, _DrillLevelNames)
	return tmp
}

// DrillLevelValues returns a list of the values for DrillLevel
func DrillLevelValues() []DrillLevel {
	return []DrillLevel{
		DrillLevelBeginner,
		DrillLevelIntermediate,
		DrillLevelAdvanced,
		DrillLevelExpert,
	}
}

// String implements the Stringer interface.
func (x DrillLevel) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x DrillLevel) IsValid() bool {
	_, err := ParseDrillLevel(string(x))
	return err == nil
}

var _DrillLevelValue = map[string]DrillLevel{
	"beginner":     DrillLevelBeginner,
	"intermediate": DrillLevelIntermediate,
	"advanced":     DrillLevelAdvanced,
	"expert":       DrillLevelExpert,
}

// ParseDrillLevel attempts to convert a string to a DrillLevel.
func ParseDrillLevel(name string) (DrillLevel, error) {
	if x, ok := _DrillLevelValue[name]; ok {
		return x, nil
	}
	return DrillLevel(""), fmt.Errorf("%s is %w", name, ErrInvalidDrillLevel)
}

// MarshalText implements the text marshaller method.
func (x DrillLevel) MarshalText() ([]byte, error) {
	return []byte(string(x)), nil
}

// UnmarshalText implements the text unmarshaller method.
func (x *DrillLevel) UnmarshalText(text []byte) error {
	tmp, err := ParseDrillLevel(string(text))
	if err != nil {
		return err
	}
	*x = tmp
	return nil
}

var errDrillLevelNilPtr = errors.New("value pointer is nil") // one per type for package clashes

var sqlIntDrillLevelMap = map[int64]DrillLevel{
	1: DrillLevelBeginner,
	2: DrillLevelIntermediate,
	3: DrillLevelAdvanced,
	4: DrillLevelExpert,
}

var sqlIntDrillLevelValue = map[DrillLevel]int64{
	DrillLevelBeginner:     1,
	DrillLevelIntermediate: 2,
	DrillLevelAdvanced:     3,
	DrillLevelExpert:       4,
}

func lookupSqlIntDrillLevel(val int64) (DrillLevel, error) {
	x, ok := sqlIntDrillLevelMap[val]
	if !ok {
		return x, fmt.Errorf("%v is not %w", val, ErrInvalidDrillLevel)
	}
	return x, nil
}

// Scan implements the Scanner interface.
func (x *DrillLevel) Scan(value interface{}) (err error) {
	if value == nil {
		*x = DrillLevel("")
		return
	}

	// A wider range of scannable types.
	// driver.Value values at the top of the list for expediency
	switch v := value.(type) {
	case int64:
		*x, err = lookupSqlIntDrillLevel(v)
	case string:
		*x, err = ParseDrillLevel(v)
	case []byte:
		if val, verr := strconv.ParseInt(string(v), 10, 64); verr == nil {
			*x, err = lookupSqlIntDrillLevel(val)
		} else {
			// try parsing the value as a string
			*x, err = ParseDrillLevel(string(v))
		}
	case DrillLevel:
		*x = v
	case int:
		*x, err = lookupSqlIntDrillLevel(int64(v))
	case *DrillLevel:
		if v == nil {
			return errDrillLevelNilPtr
		}
		*x = *v
	case uint:
		*x, err = lookupSqlIntDrillLevel(int64(v))
	case uint64:
		*x, err = lookupSqlIntDrillLevel(int64(v))
	case *int:
		if v == nil {
			return errDrillLevelNilPtr
		}
		*x, err = lookupSqlIntDrillLevel(int64(*v))
	case *int64:
		if v == nil {
			return errDrillLevelNilPtr
		}
		*x, err = lookupSqlIntDrillLevel(int64(*v))
	case float64: // json marshals everything as a float64 if it's a number
		*x, err = lookupSqlIntDrillLevel(int64(v))
	case *float64: // json marshals everything as a float64 if it's a number
		if v == nil {
			return errDrillLevelNilPtr
		}
		*x, err = lookupSqlIntDrillLevel(int64(*v))
	case *uint:
		if v == nil {
			return errDrillLevelNilPtr
		}
		*x, err = lookupSqlIntDrillLevel(int64(*v))
	case *uint64:
		if v == nil {
			return errDrillLevelNilPtr
		}
		*x, err = lookupSqlIntDrillLevel(int64(*v))
	case *string:
		if v == nil {
			return errDrillLevelNilPtr
		}
		*x, err = ParseDrillLevel(*v)
	default:
		return errors.New("invalid type for DrillLevel")
	}

	return
}

// Value implements the driver Valuer interface.
func (x DrillLevel) Value() (driver.Value, error) {
	val, ok := sqlIntDrillLevelValue[x]
	if !ok {
		return nil, ErrInvalidDrillLevel
	}
	return int64(val), nil
}

// Additional template
func (x DrillLevel) ToInt64() int64 {
	return sqlIntDrillLevelValue[x]
}
