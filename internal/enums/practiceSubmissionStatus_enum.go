// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package enums

import (
	"database/sql/driver"
	"errors"
	"fmt"
	"strconv"
	"strings"
)

const (
	// PracticeSubmissionStatusSubmitted is a PracticeSubmissionStatus of type submitted.
	PracticeSubmissionStatusSubmitted PracticeSubmissionStatus = "submitted"
	// PracticeSubmissionStatusApproved is a PracticeSubmissionStatus of type approved.
	PracticeSubmissionStatusApproved PracticeSubmissionStatus = "approved"
	// PracticeSubmissionStatusRejected is a PracticeSubmissionStatus of type rejected.
	PracticeSubmissionStatusRejected PracticeSubmissionStatus = "rejected"
)

var ErrInvalidPracticeSubmissionStatus = fmt.Errorf("not a valid PracticeSubmissionStatus, try [%s]", strings.Join(_PracticeSubmissionStatusNames, ", "))

var _PracticeSubmissionStatusNames = []string{
	string(PracticeSubmissionStatusSubmitted),
	string(PracticeSubmissionStatusApproved),
	string(PracticeSubmissionStatusRejected),
}

// PracticeSubmissionStatusNames returns a list of possible string values of PracticeSubmissionStatus.
func PracticeSubmissionStatusNames() []string {
	tmp := make([]string, len(_PracticeSubmissionStatusNames))
	copy(tmp, _PracticeSubmissionStatusNames)
	return tmp
}

// PracticeSubmissionStatusValues returns a list of the values for PracticeSubmissionStatus
func PracticeSubmissionStatusValues() []PracticeSubmissionStatus {
	return []PracticeSubmissionStatus{
		PracticeSubmissionStatusSubmitted,
		PracticeSubmissionStatusApproved,
		PracticeSubmissionStatusRejected,
	}
}

// String implements the Stringer interface.
func (x PracticeSubmissionStatus) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x PracticeSubmissionStatus) IsValid() bool {
	_, err := ParsePracticeSubmissionStatus(string(x))
	return err == nil
}

var _PracticeSubmissionStatusValue = map[string]PracticeSubmissionStatus{
	"submitted": PracticeSubmissionStatusSubmitted,
	"approved":  PracticeSubmissionStatusApproved,
	"rejected":  PracticeSubmissionStatusRejected,
}

// ParsePracticeSubmissionStatus attempts to convert a string to a PracticeSubmissionStatus.
func ParsePracticeSubmissionStatus(name string) (PracticeSubmissionStatus, error) {
	if x, ok := _PracticeSubmissionStatusValue[name]; ok {
		return x, nil
	}
	return PracticeSubmissionStatus(""), fmt.Errorf("%s is %w", name, ErrInvalidPracticeSubmissionStatus)
}

// MarshalText implements the text marshaller method.
func (x PracticeSubmissionStatus) MarshalText() ([]byte, error) {
	return []byte(string(x)), nil
}

// UnmarshalText implements the text unmarshaller method.
func (x *PracticeSubmissionStatus) UnmarshalText(text []byte) error {
	tmp, err := ParsePracticeSubmissionStatus(string(text))
	if err != nil {
		return err
	}
	*x = tmp
	return nil
}

var errPracticeSubmissionStatusNilPtr = errors.New("value pointer is nil") // one per type for package clashes

var sqlIntPracticeSubmissionStatusMap = map[int64]PracticeSubmissionStatus{
	0: PracticeSubmissionStatusSubmitted,
	1: PracticeSubmissionStatusApproved,
	2: PracticeSubmissionStatusRejected,
}

var sqlIntPracticeSubmissionStatusValue = map[PracticeSubmissionStatus]int64{
	PracticeSubmissionStatusSubmitted: 0,
	PracticeSubmissionStatusApproved:  1,
	PracticeSubmissionStatusRejected:  2,
}

func lookupSqlIntPracticeSubmissionStatus(val int64) (PracticeSubmissionStatus, error) {
	x, ok := sqlIntPracticeSubmissionStatusMap[val]
	if !ok {
		return x, fmt.Errorf("%v is not %w", val, ErrInvalidPracticeSubmissionStatus)
	}
	return x, nil
}

// Scan implements the Scanner interface.
func (x *PracticeSubmissionStatus) Scan(value interface{}) (err error) {
	if value == nil {
		*x = PracticeSubmissionStatus("")
		return
	}

	// A wider range of scannable types.
	// driver.Value values at the top of the list for expediency
	switch v := value.(type) {
	case int64:
		*x, err = lookupSqlIntPracticeSubmissionStatus(v)
	case string:
		*x, err = ParsePracticeSubmissionStatus(v)
	case []byte:
		if val, verr := strconv.ParseInt(string(v), 10, 64); verr == nil {
			*x, err = lookupSqlIntPracticeSubmissionStatus(val)
		} else {
			// try parsing the value as a string
			*x, err = ParsePracticeSubmissionStatus(string(v))
		}
	case PracticeSubmissionStatus:
		*x = v
	case int:
		*x, err = lookupSqlIntPracticeSubmissionStatus(int64(v))
	case *PracticeSubmissionStatus:
		if v == nil {
			return errPracticeSubmissionStatusNilPtr
		}
		*x = *v
	case uint:
		*x, err = lookupSqlIntPracticeSubmissionStatus(int64(v))
	case uint64:
		*x, err = lookupSqlIntPracticeSubmissionStatus(int64(v))
	case *int:
		if v == nil {
			return errPracticeSubmissionStatusNilPtr
		}
		*x, err = lookupSqlIntPracticeSubmissionStatus(int64(*v))
	case *int64:
		if v == nil {
			return errPracticeSubmissionStatusNilPtr
		}
		*x, err = lookupSqlIntPracticeSubmissionStatus(int64(*v))
	case float64: // json marshals everything as a float64 if it's a number
		*x, err = lookupSqlIntPracticeSubmissionStatus(int64(v))
	case *float64: // json marshals everything as a float64 if it's a number
		if v == nil {
			return errPracticeSubmissionStatusNilPtr
		}
		*x, err = lookupSqlIntPracticeSubmissionStatus(int64(*v))
	case *uint:
		if v == nil {
			return errPracticeSubmissionStatusNilPtr
		}
		*x, err = lookupSqlIntPracticeSubmissionStatus(int64(*v))
	case *uint64:
		if v == nil {
			return errPracticeSubmissionStatusNilPtr
		}
		*x, err = lookupSqlIntPracticeSubmissionStatus(int64(*v))
	case *string:
		if v == nil {
			return errPracticeSubmissionStatusNilPtr
		}
		*x, err = ParsePracticeSubmissionStatus(*v)
	default:
		return errors.New("invalid type for PracticeSubmissionStatus")
	}

	return
}

// Value implements the driver Valuer interface.
func (x PracticeSubmissionStatus) Value() (driver.Value, error) {
	val, ok := sqlIntPracticeSubmissionStatusValue[x]
	if !ok {
		return nil, ErrInvalidPracticeSubmissionStatus
	}
	return int64(val), nil
}

// Additional template
func (x PracticeSubmissionStatus) ToInt64() int64 {
	return sqlIntPracticeSubmissionStatusValue[x]
}
