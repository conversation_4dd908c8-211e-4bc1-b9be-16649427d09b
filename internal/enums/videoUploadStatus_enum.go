// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package enums

import (
	"database/sql/driver"
	"errors"
	"fmt"
	"strconv"
	"strings"
)

const (
	// VideoUploadStatusUploading is a VideoUploadStatus of type uploading.
	VideoUploadStatusUploading VideoUploadStatus = "uploading"
	// VideoUploadStatusProcessing is a VideoUploadStatus of type processing.
	VideoUploadStatusProcessing VideoUploadStatus = "processing"
	// VideoUploadStatusCompleted is a VideoUploadStatus of type completed.
	VideoUploadStatusCompleted VideoUploadStatus = "completed"
	// VideoUploadStatusFailed is a VideoUploadStatus of type failed.
	VideoUploadStatusFailed VideoUploadStatus = "failed"
)

var ErrInvalidVideoUploadStatus = fmt.Errorf("not a valid VideoUploadStatus, try [%s]", strings.Join(_VideoUploadStatusNames, ", "))

var _VideoUploadStatusNames = []string{
	string(VideoUploadStatusUploading),
	string(VideoUploadStatusProcessing),
	string(VideoUploadStatusCompleted),
	string(VideoUploadStatusFailed),
}

// VideoUploadStatusNames returns a list of possible string values of VideoUploadStatus.
func VideoUploadStatusNames() []string {
	tmp := make([]string, len(_VideoUploadStatusNames))
	copy(tmp, _VideoUploadStatusNames)
	return tmp
}

// VideoUploadStatusValues returns a list of the values for VideoUploadStatus
func VideoUploadStatusValues() []VideoUploadStatus {
	return []VideoUploadStatus{
		VideoUploadStatusUploading,
		VideoUploadStatusProcessing,
		VideoUploadStatusCompleted,
		VideoUploadStatusFailed,
	}
}

// String implements the Stringer interface.
func (x VideoUploadStatus) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x VideoUploadStatus) IsValid() bool {
	_, err := ParseVideoUploadStatus(string(x))
	return err == nil
}

var _VideoUploadStatusValue = map[string]VideoUploadStatus{
	"uploading":  VideoUploadStatusUploading,
	"processing": VideoUploadStatusProcessing,
	"completed":  VideoUploadStatusCompleted,
	"failed":     VideoUploadStatusFailed,
}

// ParseVideoUploadStatus attempts to convert a string to a VideoUploadStatus.
func ParseVideoUploadStatus(name string) (VideoUploadStatus, error) {
	if x, ok := _VideoUploadStatusValue[name]; ok {
		return x, nil
	}
	return VideoUploadStatus(""), fmt.Errorf("%s is %w", name, ErrInvalidVideoUploadStatus)
}

// MarshalText implements the text marshaller method.
func (x VideoUploadStatus) MarshalText() ([]byte, error) {
	return []byte(string(x)), nil
}

// UnmarshalText implements the text unmarshaller method.
func (x *VideoUploadStatus) UnmarshalText(text []byte) error {
	tmp, err := ParseVideoUploadStatus(string(text))
	if err != nil {
		return err
	}
	*x = tmp
	return nil
}

var errVideoUploadStatusNilPtr = errors.New("value pointer is nil") // one per type for package clashes

var sqlIntVideoUploadStatusMap = map[int64]VideoUploadStatus{
	1: VideoUploadStatusUploading,
	2: VideoUploadStatusProcessing,
	3: VideoUploadStatusCompleted,
	4: VideoUploadStatusFailed,
}

var sqlIntVideoUploadStatusValue = map[VideoUploadStatus]int64{
	VideoUploadStatusUploading:  1,
	VideoUploadStatusProcessing: 2,
	VideoUploadStatusCompleted:  3,
	VideoUploadStatusFailed:     4,
}

func lookupSqlIntVideoUploadStatus(val int64) (VideoUploadStatus, error) {
	x, ok := sqlIntVideoUploadStatusMap[val]
	if !ok {
		return x, fmt.Errorf("%v is not %w", val, ErrInvalidVideoUploadStatus)
	}
	return x, nil
}

// Scan implements the Scanner interface.
func (x *VideoUploadStatus) Scan(value interface{}) (err error) {
	if value == nil {
		*x = VideoUploadStatus("")
		return
	}

	// A wider range of scannable types.
	// driver.Value values at the top of the list for expediency
	switch v := value.(type) {
	case int64:
		*x, err = lookupSqlIntVideoUploadStatus(v)
	case string:
		*x, err = ParseVideoUploadStatus(v)
	case []byte:
		if val, verr := strconv.ParseInt(string(v), 10, 64); verr == nil {
			*x, err = lookupSqlIntVideoUploadStatus(val)
		} else {
			// try parsing the value as a string
			*x, err = ParseVideoUploadStatus(string(v))
		}
	case VideoUploadStatus:
		*x = v
	case int:
		*x, err = lookupSqlIntVideoUploadStatus(int64(v))
	case *VideoUploadStatus:
		if v == nil {
			return errVideoUploadStatusNilPtr
		}
		*x = *v
	case uint:
		*x, err = lookupSqlIntVideoUploadStatus(int64(v))
	case uint64:
		*x, err = lookupSqlIntVideoUploadStatus(int64(v))
	case *int:
		if v == nil {
			return errVideoUploadStatusNilPtr
		}
		*x, err = lookupSqlIntVideoUploadStatus(int64(*v))
	case *int64:
		if v == nil {
			return errVideoUploadStatusNilPtr
		}
		*x, err = lookupSqlIntVideoUploadStatus(int64(*v))
	case float64: // json marshals everything as a float64 if it's a number
		*x, err = lookupSqlIntVideoUploadStatus(int64(v))
	case *float64: // json marshals everything as a float64 if it's a number
		if v == nil {
			return errVideoUploadStatusNilPtr
		}
		*x, err = lookupSqlIntVideoUploadStatus(int64(*v))
	case *uint:
		if v == nil {
			return errVideoUploadStatusNilPtr
		}
		*x, err = lookupSqlIntVideoUploadStatus(int64(*v))
	case *uint64:
		if v == nil {
			return errVideoUploadStatusNilPtr
		}
		*x, err = lookupSqlIntVideoUploadStatus(int64(*v))
	case *string:
		if v == nil {
			return errVideoUploadStatusNilPtr
		}
		*x, err = ParseVideoUploadStatus(*v)
	default:
		return errors.New("invalid type for VideoUploadStatus")
	}

	return
}

// Value implements the driver Valuer interface.
func (x VideoUploadStatus) Value() (driver.Value, error) {
	val, ok := sqlIntVideoUploadStatusValue[x]
	if !ok {
		return nil, ErrInvalidVideoUploadStatus
	}
	return int64(val), nil
}

// Additional template
func (x VideoUploadStatus) ToInt64() int64 {
	return sqlIntVideoUploadStatusValue[x]
}
