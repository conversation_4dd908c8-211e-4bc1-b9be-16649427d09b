// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package enums

import (
	"database/sql/driver"
	"errors"
	"fmt"
	"strconv"
	"strings"
)

const (
	// VideoStreamingSessionStatusActive is a VideoStreamingSessionStatus of type active.
	VideoStreamingSessionStatusActive VideoStreamingSessionStatus = "active"
	// VideoStreamingSessionStatusEnded is a VideoStreamingSessionStatus of type ended.
	VideoStreamingSessionStatusEnded VideoStreamingSessionStatus = "ended"
	// VideoStreamingSessionStatusExpired is a VideoStreamingSessionStatus of type expired.
	VideoStreamingSessionStatusExpired VideoStreamingSessionStatus = "expired"
)

var ErrInvalidVideoStreamingSessionStatus = fmt.Errorf("not a valid VideoStreamingSessionStatus, try [%s]", strings.Join(_VideoStreamingSessionStatusNames, ", "))

var _VideoStreamingSessionStatusNames = []string{
	string(VideoStreamingSessionStatusActive),
	string(VideoStreamingSessionStatusEnded),
	string(VideoStreamingSessionStatusExpired),
}

// VideoStreamingSessionStatusNames returns a list of possible string values of VideoStreamingSessionStatus.
func VideoStreamingSessionStatusNames() []string {
	tmp := make([]string, len(_VideoStreamingSessionStatusNames))
	copy(tmp, _VideoStreamingSessionStatusNames)
	return tmp
}

// VideoStreamingSessionStatusValues returns a list of the values for VideoStreamingSessionStatus
func VideoStreamingSessionStatusValues() []VideoStreamingSessionStatus {
	return []VideoStreamingSessionStatus{
		VideoStreamingSessionStatusActive,
		VideoStreamingSessionStatusEnded,
		VideoStreamingSessionStatusExpired,
	}
}

// String implements the Stringer interface.
func (x VideoStreamingSessionStatus) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x VideoStreamingSessionStatus) IsValid() bool {
	_, err := ParseVideoStreamingSessionStatus(string(x))
	return err == nil
}

var _VideoStreamingSessionStatusValue = map[string]VideoStreamingSessionStatus{
	"active":  VideoStreamingSessionStatusActive,
	"ended":   VideoStreamingSessionStatusEnded,
	"expired": VideoStreamingSessionStatusExpired,
}

// ParseVideoStreamingSessionStatus attempts to convert a string to a VideoStreamingSessionStatus.
func ParseVideoStreamingSessionStatus(name string) (VideoStreamingSessionStatus, error) {
	if x, ok := _VideoStreamingSessionStatusValue[name]; ok {
		return x, nil
	}
	return VideoStreamingSessionStatus(""), fmt.Errorf("%s is %w", name, ErrInvalidVideoStreamingSessionStatus)
}

// MarshalText implements the text marshaller method.
func (x VideoStreamingSessionStatus) MarshalText() ([]byte, error) {
	return []byte(string(x)), nil
}

// UnmarshalText implements the text unmarshaller method.
func (x *VideoStreamingSessionStatus) UnmarshalText(text []byte) error {
	tmp, err := ParseVideoStreamingSessionStatus(string(text))
	if err != nil {
		return err
	}
	*x = tmp
	return nil
}

var errVideoStreamingSessionStatusNilPtr = errors.New("value pointer is nil") // one per type for package clashes

var sqlIntVideoStreamingSessionStatusMap = map[int64]VideoStreamingSessionStatus{
	1: VideoStreamingSessionStatusActive,
	2: VideoStreamingSessionStatusEnded,
	3: VideoStreamingSessionStatusExpired,
}

var sqlIntVideoStreamingSessionStatusValue = map[VideoStreamingSessionStatus]int64{
	VideoStreamingSessionStatusActive:  1,
	VideoStreamingSessionStatusEnded:   2,
	VideoStreamingSessionStatusExpired: 3,
}

func lookupSqlIntVideoStreamingSessionStatus(val int64) (VideoStreamingSessionStatus, error) {
	x, ok := sqlIntVideoStreamingSessionStatusMap[val]
	if !ok {
		return x, fmt.Errorf("%v is not %w", val, ErrInvalidVideoStreamingSessionStatus)
	}
	return x, nil
}

// Scan implements the Scanner interface.
func (x *VideoStreamingSessionStatus) Scan(value interface{}) (err error) {
	if value == nil {
		*x = VideoStreamingSessionStatus("")
		return
	}

	// A wider range of scannable types.
	// driver.Value values at the top of the list for expediency
	switch v := value.(type) {
	case int64:
		*x, err = lookupSqlIntVideoStreamingSessionStatus(v)
	case string:
		*x, err = ParseVideoStreamingSessionStatus(v)
	case []byte:
		if val, verr := strconv.ParseInt(string(v), 10, 64); verr == nil {
			*x, err = lookupSqlIntVideoStreamingSessionStatus(val)
		} else {
			// try parsing the value as a string
			*x, err = ParseVideoStreamingSessionStatus(string(v))
		}
	case VideoStreamingSessionStatus:
		*x = v
	case int:
		*x, err = lookupSqlIntVideoStreamingSessionStatus(int64(v))
	case *VideoStreamingSessionStatus:
		if v == nil {
			return errVideoStreamingSessionStatusNilPtr
		}
		*x = *v
	case uint:
		*x, err = lookupSqlIntVideoStreamingSessionStatus(int64(v))
	case uint64:
		*x, err = lookupSqlIntVideoStreamingSessionStatus(int64(v))
	case *int:
		if v == nil {
			return errVideoStreamingSessionStatusNilPtr
		}
		*x, err = lookupSqlIntVideoStreamingSessionStatus(int64(*v))
	case *int64:
		if v == nil {
			return errVideoStreamingSessionStatusNilPtr
		}
		*x, err = lookupSqlIntVideoStreamingSessionStatus(int64(*v))
	case float64: // json marshals everything as a float64 if it's a number
		*x, err = lookupSqlIntVideoStreamingSessionStatus(int64(v))
	case *float64: // json marshals everything as a float64 if it's a number
		if v == nil {
			return errVideoStreamingSessionStatusNilPtr
		}
		*x, err = lookupSqlIntVideoStreamingSessionStatus(int64(*v))
	case *uint:
		if v == nil {
			return errVideoStreamingSessionStatusNilPtr
		}
		*x, err = lookupSqlIntVideoStreamingSessionStatus(int64(*v))
	case *uint64:
		if v == nil {
			return errVideoStreamingSessionStatusNilPtr
		}
		*x, err = lookupSqlIntVideoStreamingSessionStatus(int64(*v))
	case *string:
		if v == nil {
			return errVideoStreamingSessionStatusNilPtr
		}
		*x, err = ParseVideoStreamingSessionStatus(*v)
	default:
		return errors.New("invalid type for VideoStreamingSessionStatus")
	}

	return
}

// Value implements the driver Valuer interface.
func (x VideoStreamingSessionStatus) Value() (driver.Value, error) {
	val, ok := sqlIntVideoStreamingSessionStatusValue[x]
	if !ok {
		return nil, ErrInvalidVideoStreamingSessionStatus
	}
	return int64(val), nil
}

// Additional template
func (x VideoStreamingSessionStatus) ToInt64() int64 {
	return sqlIntVideoStreamingSessionStatusValue[x]
}
