// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package enums

import (
	"fmt"
	"strings"
)

const (
	// CommentTargetTypePracticeSubmission is a CommentTargetType of type PracticeSubmission.
	CommentTargetTypePracticeSubmission CommentTargetType = "PracticeSubmission"
	// CommentTargetTypeCourse is a CommentTargetType of type Course.
	CommentTargetTypeCourse CommentTargetType = "Course"
	// CommentTargetTypeTeacher is a CommentTargetType of type Teacher.
	CommentTargetTypeTeacher CommentTargetType = "Teacher"
)

var ErrInvalidCommentTargetType = fmt.Errorf("not a valid CommentTargetType, try [%s]", strings.Join(_CommentTargetTypeNames, ", "))

var _CommentTargetTypeNames = []string{
	string(CommentTargetTypePracticeSubmission),
	string(CommentTargetTypeCourse),
	string(CommentTargetTypeTeacher),
}

// CommentTargetTypeNames returns a list of possible string values of CommentTargetType.
func CommentTargetTypeNames() []string {
	tmp := make([]string, len(_CommentTargetTypeNames))
	copy(tmp, _CommentTargetTypeNames)
	return tmp
}

// CommentTargetTypeValues returns a list of the values for CommentTargetType
func CommentTargetTypeValues() []CommentTargetType {
	return []CommentTargetType{
		CommentTargetTypePracticeSubmission,
		CommentTargetTypeCourse,
		CommentTargetTypeTeacher,
	}
}

// String implements the Stringer interface.
func (x CommentTargetType) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x CommentTargetType) IsValid() bool {
	_, err := ParseCommentTargetType(string(x))
	return err == nil
}

var _CommentTargetTypeValue = map[string]CommentTargetType{
	"PracticeSubmission": CommentTargetTypePracticeSubmission,
	"Course":             CommentTargetTypeCourse,
	"Teacher":            CommentTargetTypeTeacher,
}

// ParseCommentTargetType attempts to convert a string to a CommentTargetType.
func ParseCommentTargetType(name string) (CommentTargetType, error) {
	if x, ok := _CommentTargetTypeValue[name]; ok {
		return x, nil
	}
	return CommentTargetType(""), fmt.Errorf("%s is %w", name, ErrInvalidCommentTargetType)
}

// MarshalText implements the text marshaller method.
func (x CommentTargetType) MarshalText() ([]byte, error) {
	return []byte(string(x)), nil
}

// UnmarshalText implements the text unmarshaller method.
func (x *CommentTargetType) UnmarshalText(text []byte) error {
	tmp, err := ParseCommentTargetType(string(text))
	if err != nil {
		return err
	}
	*x = tmp
	return nil
}
