// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package enums

import (
	"fmt"
	"strings"
)

const (
	// PracticeSubmissionPracticeTypeCourseSectionItem is a PracticeSubmissionPracticeType of type CourseSectionItem.
	PracticeSubmissionPracticeTypeCourseSectionItem PracticeSubmissionPracticeType = "CourseSectionItem"
)

var ErrInvalidPracticeSubmissionPracticeType = fmt.Errorf("not a valid PracticeSubmissionPracticeType, try [%s]", strings.Join(_PracticeSubmissionPracticeTypeNames, ", "))

var _PracticeSubmissionPracticeTypeNames = []string{
	string(PracticeSubmissionPracticeTypeCourseSectionItem),
}

// PracticeSubmissionPracticeTypeNames returns a list of possible string values of PracticeSubmissionPracticeType.
func PracticeSubmissionPracticeTypeNames() []string {
	tmp := make([]string, len(_PracticeSubmissionPracticeTypeNames))
	copy(tmp, _PracticeSubmissionPracticeTypeNames)
	return tmp
}

// PracticeSubmissionPracticeTypeValues returns a list of the values for PracticeSubmissionPracticeType
func PracticeSubmissionPracticeTypeValues() []PracticeSubmissionPracticeType {
	return []PracticeSubmissionPracticeType{
		PracticeSubmissionPracticeTypeCourseSectionItem,
	}
}

// String implements the Stringer interface.
func (x PracticeSubmissionPracticeType) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x PracticeSubmissionPracticeType) IsValid() bool {
	_, err := ParsePracticeSubmissionPracticeType(string(x))
	return err == nil
}

var _PracticeSubmissionPracticeTypeValue = map[string]PracticeSubmissionPracticeType{
	"CourseSectionItem": PracticeSubmissionPracticeTypeCourseSectionItem,
}

// ParsePracticeSubmissionPracticeType attempts to convert a string to a PracticeSubmissionPracticeType.
func ParsePracticeSubmissionPracticeType(name string) (PracticeSubmissionPracticeType, error) {
	if x, ok := _PracticeSubmissionPracticeTypeValue[name]; ok {
		return x, nil
	}
	return PracticeSubmissionPracticeType(""), fmt.Errorf("%s is %w", name, ErrInvalidPracticeSubmissionPracticeType)
}

// MarshalText implements the text marshaller method.
func (x PracticeSubmissionPracticeType) MarshalText() ([]byte, error) {
	return []byte(string(x)), nil
}

// UnmarshalText implements the text unmarshaller method.
func (x *PracticeSubmissionPracticeType) UnmarshalText(text []byte) error {
	tmp, err := ParsePracticeSubmissionPracticeType(string(text))
	if err != nil {
		return err
	}
	*x = tmp
	return nil
}
