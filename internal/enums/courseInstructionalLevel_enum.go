// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package enums

import (
	"database/sql/driver"
	"errors"
	"fmt"
	"strconv"
	"strings"
)

const (
	// CourseInstructionalLevelBeginner is a CourseInstructionalLevel of type beginner.
	CourseInstructionalLevelBeginner CourseInstructionalLevel = "beginner"
	// CourseInstructionalLevelIntermediate is a CourseInstructionalLevel of type intermediate.
	CourseInstructionalLevelIntermediate CourseInstructionalLevel = "intermediate"
	// CourseInstructionalLevelAdvanced is a CourseInstructionalLevel of type advanced.
	CourseInstructionalLevelAdvanced CourseInstructionalLevel = "advanced"
	// CourseInstructionalLevelAllLevels is a CourseInstructionalLevel of type allLevels.
	CourseInstructionalLevelAllLevels CourseInstructionalLevel = "allLevels"
)

var ErrInvalidCourseInstructionalLevel = fmt.Errorf("not a valid CourseInstructionalLevel, try [%s]", strings.Join(_CourseInstructionalLevelNames, ", "))

var _CourseInstructionalLevelNames = []string{
	string(CourseInstructionalLevelBeginner),
	string(CourseInstructionalLevelIntermediate),
	string(CourseInstructionalLevelAdvanced),
	string(CourseInstructionalLevelAllLevels),
}

// CourseInstructionalLevelNames returns a list of possible string values of CourseInstructionalLevel.
func CourseInstructionalLevelNames() []string {
	tmp := make([]string, len(_CourseInstructionalLevelNames))
	copy(tmp, _CourseInstructionalLevelNames)
	return tmp
}

// CourseInstructionalLevelValues returns a list of the values for CourseInstructionalLevel
func CourseInstructionalLevelValues() []CourseInstructionalLevel {
	return []CourseInstructionalLevel{
		CourseInstructionalLevelBeginner,
		CourseInstructionalLevelIntermediate,
		CourseInstructionalLevelAdvanced,
		CourseInstructionalLevelAllLevels,
	}
}

// String implements the Stringer interface.
func (x CourseInstructionalLevel) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x CourseInstructionalLevel) IsValid() bool {
	_, err := ParseCourseInstructionalLevel(string(x))
	return err == nil
}

var _CourseInstructionalLevelValue = map[string]CourseInstructionalLevel{
	"beginner":     CourseInstructionalLevelBeginner,
	"intermediate": CourseInstructionalLevelIntermediate,
	"advanced":     CourseInstructionalLevelAdvanced,
	"allLevels":    CourseInstructionalLevelAllLevels,
}

// ParseCourseInstructionalLevel attempts to convert a string to a CourseInstructionalLevel.
func ParseCourseInstructionalLevel(name string) (CourseInstructionalLevel, error) {
	if x, ok := _CourseInstructionalLevelValue[name]; ok {
		return x, nil
	}
	return CourseInstructionalLevel(""), fmt.Errorf("%s is %w", name, ErrInvalidCourseInstructionalLevel)
}

// MarshalText implements the text marshaller method.
func (x CourseInstructionalLevel) MarshalText() ([]byte, error) {
	return []byte(string(x)), nil
}

// UnmarshalText implements the text unmarshaller method.
func (x *CourseInstructionalLevel) UnmarshalText(text []byte) error {
	tmp, err := ParseCourseInstructionalLevel(string(text))
	if err != nil {
		return err
	}
	*x = tmp
	return nil
}

var errCourseInstructionalLevelNilPtr = errors.New("value pointer is nil") // one per type for package clashes

var sqlIntCourseInstructionalLevelMap = map[int64]CourseInstructionalLevel{
	0: CourseInstructionalLevelBeginner,
	1: CourseInstructionalLevelIntermediate,
	2: CourseInstructionalLevelAdvanced,
	3: CourseInstructionalLevelAllLevels,
}

var sqlIntCourseInstructionalLevelValue = map[CourseInstructionalLevel]int64{
	CourseInstructionalLevelBeginner:     0,
	CourseInstructionalLevelIntermediate: 1,
	CourseInstructionalLevelAdvanced:     2,
	CourseInstructionalLevelAllLevels:    3,
}

func lookupSqlIntCourseInstructionalLevel(val int64) (CourseInstructionalLevel, error) {
	x, ok := sqlIntCourseInstructionalLevelMap[val]
	if !ok {
		return x, fmt.Errorf("%v is not %w", val, ErrInvalidCourseInstructionalLevel)
	}
	return x, nil
}

// Scan implements the Scanner interface.
func (x *CourseInstructionalLevel) Scan(value interface{}) (err error) {
	if value == nil {
		*x = CourseInstructionalLevel("")
		return
	}

	// A wider range of scannable types.
	// driver.Value values at the top of the list for expediency
	switch v := value.(type) {
	case int64:
		*x, err = lookupSqlIntCourseInstructionalLevel(v)
	case string:
		*x, err = ParseCourseInstructionalLevel(v)
	case []byte:
		if val, verr := strconv.ParseInt(string(v), 10, 64); verr == nil {
			*x, err = lookupSqlIntCourseInstructionalLevel(val)
		} else {
			// try parsing the value as a string
			*x, err = ParseCourseInstructionalLevel(string(v))
		}
	case CourseInstructionalLevel:
		*x = v
	case int:
		*x, err = lookupSqlIntCourseInstructionalLevel(int64(v))
	case *CourseInstructionalLevel:
		if v == nil {
			return errCourseInstructionalLevelNilPtr
		}
		*x = *v
	case uint:
		*x, err = lookupSqlIntCourseInstructionalLevel(int64(v))
	case uint64:
		*x, err = lookupSqlIntCourseInstructionalLevel(int64(v))
	case *int:
		if v == nil {
			return errCourseInstructionalLevelNilPtr
		}
		*x, err = lookupSqlIntCourseInstructionalLevel(int64(*v))
	case *int64:
		if v == nil {
			return errCourseInstructionalLevelNilPtr
		}
		*x, err = lookupSqlIntCourseInstructionalLevel(int64(*v))
	case float64: // json marshals everything as a float64 if it's a number
		*x, err = lookupSqlIntCourseInstructionalLevel(int64(v))
	case *float64: // json marshals everything as a float64 if it's a number
		if v == nil {
			return errCourseInstructionalLevelNilPtr
		}
		*x, err = lookupSqlIntCourseInstructionalLevel(int64(*v))
	case *uint:
		if v == nil {
			return errCourseInstructionalLevelNilPtr
		}
		*x, err = lookupSqlIntCourseInstructionalLevel(int64(*v))
	case *uint64:
		if v == nil {
			return errCourseInstructionalLevelNilPtr
		}
		*x, err = lookupSqlIntCourseInstructionalLevel(int64(*v))
	case *string:
		if v == nil {
			return errCourseInstructionalLevelNilPtr
		}
		*x, err = ParseCourseInstructionalLevel(*v)
	default:
		return errors.New("invalid type for CourseInstructionalLevel")
	}

	return
}

// Value implements the driver Valuer interface.
func (x CourseInstructionalLevel) Value() (driver.Value, error) {
	val, ok := sqlIntCourseInstructionalLevelValue[x]
	if !ok {
		return nil, ErrInvalidCourseInstructionalLevel
	}
	return int64(val), nil
}

// Additional template
func (x CourseInstructionalLevel) ToInt64() int64 {
	return sqlIntCourseInstructionalLevelValue[x]
}
