// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package enums

import (
	"database/sql/driver"
	"errors"
	"fmt"
	"strconv"
	"strings"
)

const (
	// DrillCensorDraft is a DrillCensor of type draft.
	DrillCensorDraft DrillCensor = "draft"
	// DrillCensorSubmitted is a DrillCensor of type submitted.
	DrillCensorSubmitted DrillCensor = "submitted"
	// DrillCensorApproved is a DrillCensor of type approved.
	DrillCensorApproved DrillCensor = "approved"
	// DrillCensorRejected is a DrillCensor of type rejected.
	DrillCensorRejected DrillCensor = "rejected"
)

var ErrInvalidDrillCensor = fmt.Errorf("not a valid DrillCensor, try [%s]", strings.Join(_DrillCensorNames, ", "))

var _DrillCensorNames = []string{
	string(DrillCensorDraft),
	string(DrillCensorSubmitted),
	string(DrillCensorApproved),
	string(DrillCensorRejected),
}

// DrillCensorNames returns a list of possible string values of DrillCensor.
func DrillCensorNames() []string {
	tmp := make([]string, len(_DrillCensorNames))
	copy(tmp, _DrillCensorNames)
	return tmp
}

// DrillCensorValues returns a list of the values for DrillCensor
func DrillCensorValues() []DrillCensor {
	return []DrillCensor{
		DrillCensorDraft,
		DrillCensorSubmitted,
		DrillCensorApproved,
		DrillCensorRejected,
	}
}

// String implements the Stringer interface.
func (x DrillCensor) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x DrillCensor) IsValid() bool {
	_, err := ParseDrillCensor(string(x))
	return err == nil
}

var _DrillCensorValue = map[string]DrillCensor{
	"draft":     DrillCensorDraft,
	"submitted": DrillCensorSubmitted,
	"approved":  DrillCensorApproved,
	"rejected":  DrillCensorRejected,
}

// ParseDrillCensor attempts to convert a string to a DrillCensor.
func ParseDrillCensor(name string) (DrillCensor, error) {
	if x, ok := _DrillCensorValue[name]; ok {
		return x, nil
	}
	return DrillCensor(""), fmt.Errorf("%s is %w", name, ErrInvalidDrillCensor)
}

// MarshalText implements the text marshaller method.
func (x DrillCensor) MarshalText() ([]byte, error) {
	return []byte(string(x)), nil
}

// UnmarshalText implements the text unmarshaller method.
func (x *DrillCensor) UnmarshalText(text []byte) error {
	tmp, err := ParseDrillCensor(string(text))
	if err != nil {
		return err
	}
	*x = tmp
	return nil
}

var errDrillCensorNilPtr = errors.New("value pointer is nil") // one per type for package clashes

var sqlIntDrillCensorMap = map[int64]DrillCensor{
	0: DrillCensorDraft,
	1: DrillCensorSubmitted,
	2: DrillCensorApproved,
	3: DrillCensorRejected,
}

var sqlIntDrillCensorValue = map[DrillCensor]int64{
	DrillCensorDraft:     0,
	DrillCensorSubmitted: 1,
	DrillCensorApproved:  2,
	DrillCensorRejected:  3,
}

func lookupSqlIntDrillCensor(val int64) (DrillCensor, error) {
	x, ok := sqlIntDrillCensorMap[val]
	if !ok {
		return x, fmt.Errorf("%v is not %w", val, ErrInvalidDrillCensor)
	}
	return x, nil
}

// Scan implements the Scanner interface.
func (x *DrillCensor) Scan(value interface{}) (err error) {
	if value == nil {
		*x = DrillCensor("")
		return
	}

	// A wider range of scannable types.
	// driver.Value values at the top of the list for expediency
	switch v := value.(type) {
	case int64:
		*x, err = lookupSqlIntDrillCensor(v)
	case string:
		*x, err = ParseDrillCensor(v)
	case []byte:
		if val, verr := strconv.ParseInt(string(v), 10, 64); verr == nil {
			*x, err = lookupSqlIntDrillCensor(val)
		} else {
			// try parsing the value as a string
			*x, err = ParseDrillCensor(string(v))
		}
	case DrillCensor:
		*x = v
	case int:
		*x, err = lookupSqlIntDrillCensor(int64(v))
	case *DrillCensor:
		if v == nil {
			return errDrillCensorNilPtr
		}
		*x = *v
	case uint:
		*x, err = lookupSqlIntDrillCensor(int64(v))
	case uint64:
		*x, err = lookupSqlIntDrillCensor(int64(v))
	case *int:
		if v == nil {
			return errDrillCensorNilPtr
		}
		*x, err = lookupSqlIntDrillCensor(int64(*v))
	case *int64:
		if v == nil {
			return errDrillCensorNilPtr
		}
		*x, err = lookupSqlIntDrillCensor(int64(*v))
	case float64: // json marshals everything as a float64 if it's a number
		*x, err = lookupSqlIntDrillCensor(int64(v))
	case *float64: // json marshals everything as a float64 if it's a number
		if v == nil {
			return errDrillCensorNilPtr
		}
		*x, err = lookupSqlIntDrillCensor(int64(*v))
	case *uint:
		if v == nil {
			return errDrillCensorNilPtr
		}
		*x, err = lookupSqlIntDrillCensor(int64(*v))
	case *uint64:
		if v == nil {
			return errDrillCensorNilPtr
		}
		*x, err = lookupSqlIntDrillCensor(int64(*v))
	case *string:
		if v == nil {
			return errDrillCensorNilPtr
		}
		*x, err = ParseDrillCensor(*v)
	default:
		return errors.New("invalid type for DrillCensor")
	}

	return
}

// Value implements the driver Valuer interface.
func (x DrillCensor) Value() (driver.Value, error) {
	val, ok := sqlIntDrillCensorValue[x]
	if !ok {
		return nil, ErrInvalidDrillCensor
	}
	return int64(val), nil
}

// Additional template
func (x DrillCensor) ToInt64() int64 {
	return sqlIntDrillCensorValue[x]
}
