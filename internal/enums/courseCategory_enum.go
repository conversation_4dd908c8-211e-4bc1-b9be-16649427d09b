// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package enums

import (
	"database/sql/driver"
	"errors"
	"fmt"
	"strconv"
	"strings"
)

const (
	// CourseCategoryRecentUploaded is a CourseCategory of type recentUploaded.
	CourseCategoryRecentUploaded CourseCategory = "recentUploaded"
	// CourseCategoryBestSeller is a CourseCategory of type bestSeller.
	CourseCategoryBestSeller CourseCategory = "bestSeller"
	// CourseCategoryRecentJoined is a CourseCategory of type recentJoined.
	CourseCategoryRecentJoined CourseCategory = "recentJoined"
	// CourseCategoryInProgress is a CourseCategory of type inProgress.
	CourseCategoryInProgress CourseCategory = "inProgress"
	// CourseCategoryCompleted is a CourseCategory of type completed.
	CourseCategoryCompleted CourseCategory = "completed"
	// CourseCategoryBestRated is a CourseCategory of type bestRated.
	CourseCategoryBestRated CourseCategory = "bestRated"
)

var ErrInvalidCourseCategory = fmt.Errorf("not a valid CourseCategory, try [%s]", strings.Join(_CourseCategoryNames, ", "))

var _CourseCategoryNames = []string{
	string(CourseCategoryRecentUploaded),
	string(CourseCategoryBestSeller),
	string(CourseCategoryRecentJoined),
	string(CourseCategoryInProgress),
	string(CourseCategoryCompleted),
	string(CourseCategoryBestRated),
}

// CourseCategoryNames returns a list of possible string values of CourseCategory.
func CourseCategoryNames() []string {
	tmp := make([]string, len(_CourseCategoryNames))
	copy(tmp, _CourseCategoryNames)
	return tmp
}

// CourseCategoryValues returns a list of the values for CourseCategory
func CourseCategoryValues() []CourseCategory {
	return []CourseCategory{
		CourseCategoryRecentUploaded,
		CourseCategoryBestSeller,
		CourseCategoryRecentJoined,
		CourseCategoryInProgress,
		CourseCategoryCompleted,
		CourseCategoryBestRated,
	}
}

// String implements the Stringer interface.
func (x CourseCategory) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x CourseCategory) IsValid() bool {
	_, err := ParseCourseCategory(string(x))
	return err == nil
}

var _CourseCategoryValue = map[string]CourseCategory{
	"recentUploaded": CourseCategoryRecentUploaded,
	"bestSeller":     CourseCategoryBestSeller,
	"recentJoined":   CourseCategoryRecentJoined,
	"inProgress":     CourseCategoryInProgress,
	"completed":      CourseCategoryCompleted,
	"bestRated":      CourseCategoryBestRated,
}

// ParseCourseCategory attempts to convert a string to a CourseCategory.
func ParseCourseCategory(name string) (CourseCategory, error) {
	if x, ok := _CourseCategoryValue[name]; ok {
		return x, nil
	}
	return CourseCategory(""), fmt.Errorf("%s is %w", name, ErrInvalidCourseCategory)
}

// MarshalText implements the text marshaller method.
func (x CourseCategory) MarshalText() ([]byte, error) {
	return []byte(string(x)), nil
}

// UnmarshalText implements the text unmarshaller method.
func (x *CourseCategory) UnmarshalText(text []byte) error {
	tmp, err := ParseCourseCategory(string(text))
	if err != nil {
		return err
	}
	*x = tmp
	return nil
}

var errCourseCategoryNilPtr = errors.New("value pointer is nil") // one per type for package clashes

var sqlIntCourseCategoryMap = map[int64]CourseCategory{
	0: CourseCategoryRecentUploaded,
	1: CourseCategoryBestSeller,
	2: CourseCategoryRecentJoined,
	3: CourseCategoryInProgress,
	4: CourseCategoryCompleted,
	5: CourseCategoryBestRated,
}

var sqlIntCourseCategoryValue = map[CourseCategory]int64{
	CourseCategoryRecentUploaded: 0,
	CourseCategoryBestSeller:     1,
	CourseCategoryRecentJoined:   2,
	CourseCategoryInProgress:     3,
	CourseCategoryCompleted:      4,
	CourseCategoryBestRated:      5,
}

func lookupSqlIntCourseCategory(val int64) (CourseCategory, error) {
	x, ok := sqlIntCourseCategoryMap[val]
	if !ok {
		return x, fmt.Errorf("%v is not %w", val, ErrInvalidCourseCategory)
	}
	return x, nil
}

// Scan implements the Scanner interface.
func (x *CourseCategory) Scan(value interface{}) (err error) {
	if value == nil {
		*x = CourseCategory("")
		return
	}

	// A wider range of scannable types.
	// driver.Value values at the top of the list for expediency
	switch v := value.(type) {
	case int64:
		*x, err = lookupSqlIntCourseCategory(v)
	case string:
		*x, err = ParseCourseCategory(v)
	case []byte:
		if val, verr := strconv.ParseInt(string(v), 10, 64); verr == nil {
			*x, err = lookupSqlIntCourseCategory(val)
		} else {
			// try parsing the value as a string
			*x, err = ParseCourseCategory(string(v))
		}
	case CourseCategory:
		*x = v
	case int:
		*x, err = lookupSqlIntCourseCategory(int64(v))
	case *CourseCategory:
		if v == nil {
			return errCourseCategoryNilPtr
		}
		*x = *v
	case uint:
		*x, err = lookupSqlIntCourseCategory(int64(v))
	case uint64:
		*x, err = lookupSqlIntCourseCategory(int64(v))
	case *int:
		if v == nil {
			return errCourseCategoryNilPtr
		}
		*x, err = lookupSqlIntCourseCategory(int64(*v))
	case *int64:
		if v == nil {
			return errCourseCategoryNilPtr
		}
		*x, err = lookupSqlIntCourseCategory(int64(*v))
	case float64: // json marshals everything as a float64 if it's a number
		*x, err = lookupSqlIntCourseCategory(int64(v))
	case *float64: // json marshals everything as a float64 if it's a number
		if v == nil {
			return errCourseCategoryNilPtr
		}
		*x, err = lookupSqlIntCourseCategory(int64(*v))
	case *uint:
		if v == nil {
			return errCourseCategoryNilPtr
		}
		*x, err = lookupSqlIntCourseCategory(int64(*v))
	case *uint64:
		if v == nil {
			return errCourseCategoryNilPtr
		}
		*x, err = lookupSqlIntCourseCategory(int64(*v))
	case *string:
		if v == nil {
			return errCourseCategoryNilPtr
		}
		*x, err = ParseCourseCategory(*v)
	default:
		return errors.New("invalid type for CourseCategory")
	}

	return
}

// Value implements the driver Valuer interface.
func (x CourseCategory) Value() (driver.Value, error) {
	val, ok := sqlIntCourseCategoryValue[x]
	if !ok {
		return nil, ErrInvalidCourseCategory
	}
	return int64(val), nil
}

// Additional template
func (x CourseCategory) ToInt64() int64 {
	return sqlIntCourseCategoryValue[x]
}
