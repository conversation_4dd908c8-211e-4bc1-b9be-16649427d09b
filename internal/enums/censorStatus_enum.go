// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package enums

import (
	"database/sql/driver"
	"errors"
	"fmt"
	"strconv"
	"strings"
)

const (
	// CensorStatusFeedback is a CensorStatus of type feedback.
	CensorStatusFeedback CensorStatus = "feedback"
	// CensorStatusSubmitted is a CensorStatus of type submitted.
	CensorStatusSubmitted CensorStatus = "submitted"
)

var ErrInvalidCensorStatus = fmt.Errorf("not a valid CensorStatus, try [%s]", strings.Join(_CensorStatusNames, ", "))

var _CensorStatusNames = []string{
	string(CensorStatusFeedback),
	string(CensorStatusSubmitted),
}

// CensorStatusNames returns a list of possible string values of CensorStatus.
func CensorStatusNames() []string {
	tmp := make([]string, len(_CensorStatusNames))
	copy(tmp, _CensorStatusNames)
	return tmp
}

// CensorStatusValues returns a list of the values for CensorStatus
func CensorStatusValues() []CensorStatus {
	return []CensorStatus{
		CensorStatusFeedback,
		CensorStatusSubmitted,
	}
}

// String implements the Stringer interface.
func (x CensorStatus) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x CensorStatus) IsValid() bool {
	_, err := ParseCensorStatus(string(x))
	return err == nil
}

var _CensorStatusValue = map[string]CensorStatus{
	"feedback":  CensorStatusFeedback,
	"submitted": CensorStatusSubmitted,
}

// ParseCensorStatus attempts to convert a string to a CensorStatus.
func ParseCensorStatus(name string) (CensorStatus, error) {
	if x, ok := _CensorStatusValue[name]; ok {
		return x, nil
	}
	return CensorStatus(""), fmt.Errorf("%s is %w", name, ErrInvalidCensorStatus)
}

// MarshalText implements the text marshaller method.
func (x CensorStatus) MarshalText() ([]byte, error) {
	return []byte(string(x)), nil
}

// UnmarshalText implements the text unmarshaller method.
func (x *CensorStatus) UnmarshalText(text []byte) error {
	tmp, err := ParseCensorStatus(string(text))
	if err != nil {
		return err
	}
	*x = tmp
	return nil
}

var errCensorStatusNilPtr = errors.New("value pointer is nil") // one per type for package clashes

var sqlIntCensorStatusMap = map[int64]CensorStatus{
	0: CensorStatusFeedback,
	1: CensorStatusSubmitted,
}

var sqlIntCensorStatusValue = map[CensorStatus]int64{
	CensorStatusFeedback:  0,
	CensorStatusSubmitted: 1,
}

func lookupSqlIntCensorStatus(val int64) (CensorStatus, error) {
	x, ok := sqlIntCensorStatusMap[val]
	if !ok {
		return x, fmt.Errorf("%v is not %w", val, ErrInvalidCensorStatus)
	}
	return x, nil
}

// Scan implements the Scanner interface.
func (x *CensorStatus) Scan(value interface{}) (err error) {
	if value == nil {
		*x = CensorStatus("")
		return
	}

	// A wider range of scannable types.
	// driver.Value values at the top of the list for expediency
	switch v := value.(type) {
	case int64:
		*x, err = lookupSqlIntCensorStatus(v)
	case string:
		*x, err = ParseCensorStatus(v)
	case []byte:
		if val, verr := strconv.ParseInt(string(v), 10, 64); verr == nil {
			*x, err = lookupSqlIntCensorStatus(val)
		} else {
			// try parsing the value as a string
			*x, err = ParseCensorStatus(string(v))
		}
	case CensorStatus:
		*x = v
	case int:
		*x, err = lookupSqlIntCensorStatus(int64(v))
	case *CensorStatus:
		if v == nil {
			return errCensorStatusNilPtr
		}
		*x = *v
	case uint:
		*x, err = lookupSqlIntCensorStatus(int64(v))
	case uint64:
		*x, err = lookupSqlIntCensorStatus(int64(v))
	case *int:
		if v == nil {
			return errCensorStatusNilPtr
		}
		*x, err = lookupSqlIntCensorStatus(int64(*v))
	case *int64:
		if v == nil {
			return errCensorStatusNilPtr
		}
		*x, err = lookupSqlIntCensorStatus(int64(*v))
	case float64: // json marshals everything as a float64 if it's a number
		*x, err = lookupSqlIntCensorStatus(int64(v))
	case *float64: // json marshals everything as a float64 if it's a number
		if v == nil {
			return errCensorStatusNilPtr
		}
		*x, err = lookupSqlIntCensorStatus(int64(*v))
	case *uint:
		if v == nil {
			return errCensorStatusNilPtr
		}
		*x, err = lookupSqlIntCensorStatus(int64(*v))
	case *uint64:
		if v == nil {
			return errCensorStatusNilPtr
		}
		*x, err = lookupSqlIntCensorStatus(int64(*v))
	case *string:
		if v == nil {
			return errCensorStatusNilPtr
		}
		*x, err = ParseCensorStatus(*v)
	default:
		return errors.New("invalid type for CensorStatus")
	}

	return
}

// Value implements the driver Valuer interface.
func (x CensorStatus) Value() (driver.Value, error) {
	val, ok := sqlIntCensorStatusValue[x]
	if !ok {
		return nil, ErrInvalidCensorStatus
	}
	return int64(val), nil
}

// Additional template
func (x CensorStatus) ToInt64() int64 {
	return sqlIntCensorStatusValue[x]
}
