// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package enums

import (
	"database/sql/driver"
	"errors"
	"fmt"
	"strconv"
	"strings"
)

const (
	// NoticeKindInviteCourse is a NoticeKind of type invite_course.
	NoticeKindInviteCourse NoticeKind = "invite_course"
)

var ErrInvalidNoticeKind = fmt.Errorf("not a valid NoticeKind, try [%s]", strings.Join(_NoticeKindNames, ", "))

var _NoticeKindNames = []string{
	string(NoticeKindInviteCourse),
}

// NoticeKindNames returns a list of possible string values of NoticeKind.
func NoticeKindNames() []string {
	tmp := make([]string, len(_NoticeKindNames))
	copy(tmp, _NoticeKindNames)
	return tmp
}

// NoticeKindValues returns a list of the values for NoticeKind
func NoticeKindValues() []NoticeKind {
	return []NoticeKind{
		NoticeKindInviteCourse,
	}
}

// String implements the Stringer interface.
func (x NoticeKind) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x NoticeKind) IsValid() bool {
	_, err := ParseNoticeKind(string(x))
	return err == nil
}

var _NoticeKindValue = map[string]NoticeKind{
	"invite_course": NoticeKindInviteCourse,
}

// ParseNoticeKind attempts to convert a string to a NoticeKind.
func ParseNoticeKind(name string) (NoticeKind, error) {
	if x, ok := _NoticeKindValue[name]; ok {
		return x, nil
	}
	return NoticeKind(""), fmt.Errorf("%s is %w", name, ErrInvalidNoticeKind)
}

// MarshalText implements the text marshaller method.
func (x NoticeKind) MarshalText() ([]byte, error) {
	return []byte(string(x)), nil
}

// UnmarshalText implements the text unmarshaller method.
func (x *NoticeKind) UnmarshalText(text []byte) error {
	tmp, err := ParseNoticeKind(string(text))
	if err != nil {
		return err
	}
	*x = tmp
	return nil
}

var errNoticeKindNilPtr = errors.New("value pointer is nil") // one per type for package clashes

var sqlIntNoticeKindMap = map[int64]NoticeKind{
	1: NoticeKindInviteCourse,
}

var sqlIntNoticeKindValue = map[NoticeKind]int64{
	NoticeKindInviteCourse: 1,
}

func lookupSqlIntNoticeKind(val int64) (NoticeKind, error) {
	x, ok := sqlIntNoticeKindMap[val]
	if !ok {
		return x, fmt.Errorf("%v is not %w", val, ErrInvalidNoticeKind)
	}
	return x, nil
}

// Scan implements the Scanner interface.
func (x *NoticeKind) Scan(value interface{}) (err error) {
	if value == nil {
		*x = NoticeKind("")
		return
	}

	// A wider range of scannable types.
	// driver.Value values at the top of the list for expediency
	switch v := value.(type) {
	case int64:
		*x, err = lookupSqlIntNoticeKind(v)
	case string:
		*x, err = ParseNoticeKind(v)
	case []byte:
		if val, verr := strconv.ParseInt(string(v), 10, 64); verr == nil {
			*x, err = lookupSqlIntNoticeKind(val)
		} else {
			// try parsing the value as a string
			*x, err = ParseNoticeKind(string(v))
		}
	case NoticeKind:
		*x = v
	case int:
		*x, err = lookupSqlIntNoticeKind(int64(v))
	case *NoticeKind:
		if v == nil {
			return errNoticeKindNilPtr
		}
		*x = *v
	case uint:
		*x, err = lookupSqlIntNoticeKind(int64(v))
	case uint64:
		*x, err = lookupSqlIntNoticeKind(int64(v))
	case *int:
		if v == nil {
			return errNoticeKindNilPtr
		}
		*x, err = lookupSqlIntNoticeKind(int64(*v))
	case *int64:
		if v == nil {
			return errNoticeKindNilPtr
		}
		*x, err = lookupSqlIntNoticeKind(int64(*v))
	case float64: // json marshals everything as a float64 if it's a number
		*x, err = lookupSqlIntNoticeKind(int64(v))
	case *float64: // json marshals everything as a float64 if it's a number
		if v == nil {
			return errNoticeKindNilPtr
		}
		*x, err = lookupSqlIntNoticeKind(int64(*v))
	case *uint:
		if v == nil {
			return errNoticeKindNilPtr
		}
		*x, err = lookupSqlIntNoticeKind(int64(*v))
	case *uint64:
		if v == nil {
			return errNoticeKindNilPtr
		}
		*x, err = lookupSqlIntNoticeKind(int64(*v))
	case *string:
		if v == nil {
			return errNoticeKindNilPtr
		}
		*x, err = ParseNoticeKind(*v)
	default:
		return errors.New("invalid type for NoticeKind")
	}

	return
}

// Value implements the driver Valuer interface.
func (x NoticeKind) Value() (driver.Value, error) {
	val, ok := sqlIntNoticeKindValue[x]
	if !ok {
		return nil, ErrInvalidNoticeKind
	}
	return int64(val), nil
}

// Additional template
func (x NoticeKind) ToInt64() int64 {
	return sqlIntNoticeKindValue[x]
}
