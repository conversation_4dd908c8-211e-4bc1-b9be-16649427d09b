// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package enums

import (
	"database/sql/driver"
	"errors"
	"fmt"
	"strconv"
	"strings"
)

const (
	// VideoAccessTypeFree is a VideoAccessType of type free.
	VideoAccessTypeFree VideoAccessType = "free"
	// VideoAccessTypePremium is a VideoAccessType of type premium.
	VideoAccessTypePremium VideoAccessType = "premium"
	// VideoAccessTypeSpecificPlan is a VideoAccessType of type specificPlan.
	VideoAccessTypeSpecificPlan VideoAccessType = "specificPlan"
)

var ErrInvalidVideoAccessType = fmt.Errorf("not a valid VideoAccessType, try [%s]", strings.Join(_VideoAccessTypeNames, ", "))

var _VideoAccessTypeNames = []string{
	string(VideoAccessTypeFree),
	string(VideoAccessTypePremium),
	string(VideoAccessTypeSpecificPlan),
}

// VideoAccessTypeNames returns a list of possible string values of VideoAccessType.
func VideoAccessTypeNames() []string {
	tmp := make([]string, len(_VideoAccessTypeNames))
	copy(tmp, _VideoAccessTypeNames)
	return tmp
}

// VideoAccessTypeValues returns a list of the values for VideoAccessType
func VideoAccessTypeValues() []VideoAccessType {
	return []VideoAccessType{
		VideoAccessTypeFree,
		VideoAccessTypePremium,
		VideoAccessTypeSpecificPlan,
	}
}

// String implements the Stringer interface.
func (x VideoAccessType) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x VideoAccessType) IsValid() bool {
	_, err := ParseVideoAccessType(string(x))
	return err == nil
}

var _VideoAccessTypeValue = map[string]VideoAccessType{
	"free":         VideoAccessTypeFree,
	"premium":      VideoAccessTypePremium,
	"specificPlan": VideoAccessTypeSpecificPlan,
}

// ParseVideoAccessType attempts to convert a string to a VideoAccessType.
func ParseVideoAccessType(name string) (VideoAccessType, error) {
	if x, ok := _VideoAccessTypeValue[name]; ok {
		return x, nil
	}
	return VideoAccessType(""), fmt.Errorf("%s is %w", name, ErrInvalidVideoAccessType)
}

// MarshalText implements the text marshaller method.
func (x VideoAccessType) MarshalText() ([]byte, error) {
	return []byte(string(x)), nil
}

// UnmarshalText implements the text unmarshaller method.
func (x *VideoAccessType) UnmarshalText(text []byte) error {
	tmp, err := ParseVideoAccessType(string(text))
	if err != nil {
		return err
	}
	*x = tmp
	return nil
}

var errVideoAccessTypeNilPtr = errors.New("value pointer is nil") // one per type for package clashes

var sqlIntVideoAccessTypeMap = map[int64]VideoAccessType{
	1: VideoAccessTypeFree,
	2: VideoAccessTypePremium,
	3: VideoAccessTypeSpecificPlan,
}

var sqlIntVideoAccessTypeValue = map[VideoAccessType]int64{
	VideoAccessTypeFree:         1,
	VideoAccessTypePremium:      2,
	VideoAccessTypeSpecificPlan: 3,
}

func lookupSqlIntVideoAccessType(val int64) (VideoAccessType, error) {
	x, ok := sqlIntVideoAccessTypeMap[val]
	if !ok {
		return x, fmt.Errorf("%v is not %w", val, ErrInvalidVideoAccessType)
	}
	return x, nil
}

// Scan implements the Scanner interface.
func (x *VideoAccessType) Scan(value interface{}) (err error) {
	if value == nil {
		*x = VideoAccessType("")
		return
	}

	// A wider range of scannable types.
	// driver.Value values at the top of the list for expediency
	switch v := value.(type) {
	case int64:
		*x, err = lookupSqlIntVideoAccessType(v)
	case string:
		*x, err = ParseVideoAccessType(v)
	case []byte:
		if val, verr := strconv.ParseInt(string(v), 10, 64); verr == nil {
			*x, err = lookupSqlIntVideoAccessType(val)
		} else {
			// try parsing the value as a string
			*x, err = ParseVideoAccessType(string(v))
		}
	case VideoAccessType:
		*x = v
	case int:
		*x, err = lookupSqlIntVideoAccessType(int64(v))
	case *VideoAccessType:
		if v == nil {
			return errVideoAccessTypeNilPtr
		}
		*x = *v
	case uint:
		*x, err = lookupSqlIntVideoAccessType(int64(v))
	case uint64:
		*x, err = lookupSqlIntVideoAccessType(int64(v))
	case *int:
		if v == nil {
			return errVideoAccessTypeNilPtr
		}
		*x, err = lookupSqlIntVideoAccessType(int64(*v))
	case *int64:
		if v == nil {
			return errVideoAccessTypeNilPtr
		}
		*x, err = lookupSqlIntVideoAccessType(int64(*v))
	case float64: // json marshals everything as a float64 if it's a number
		*x, err = lookupSqlIntVideoAccessType(int64(v))
	case *float64: // json marshals everything as a float64 if it's a number
		if v == nil {
			return errVideoAccessTypeNilPtr
		}
		*x, err = lookupSqlIntVideoAccessType(int64(*v))
	case *uint:
		if v == nil {
			return errVideoAccessTypeNilPtr
		}
		*x, err = lookupSqlIntVideoAccessType(int64(*v))
	case *uint64:
		if v == nil {
			return errVideoAccessTypeNilPtr
		}
		*x, err = lookupSqlIntVideoAccessType(int64(*v))
	case *string:
		if v == nil {
			return errVideoAccessTypeNilPtr
		}
		*x, err = ParseVideoAccessType(*v)
	default:
		return errors.New("invalid type for VideoAccessType")
	}

	return
}

// Value implements the driver Valuer interface.
func (x VideoAccessType) Value() (driver.Value, error) {
	val, ok := sqlIntVideoAccessTypeValue[x]
	if !ok {
		return nil, ErrInvalidVideoAccessType
	}
	return int64(val), nil
}

// Additional template
func (x VideoAccessType) ToInt64() int64 {
	return sqlIntVideoAccessTypeValue[x]
}
