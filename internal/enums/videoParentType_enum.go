// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package enums

import (
	"fmt"
	"strings"
)

const (
	// VideoParentTypePracticeSubmission is a VideoParentType of type PracticeSubmission.
	VideoParentTypePracticeSubmission VideoParentType = "PracticeSubmission"
	// VideoParentTypeCourseSectionItem is a VideoParentType of type CourseSectionItem.
	VideoParentTypeCourseSectionItem VideoParentType = "CourseSectionItem"
	// VideoParentTypeDrill is a VideoParentType of type Drill.
	VideoParentTypeDrill VideoParentType = "Drill"
)

var ErrInvalidVideoParentType = fmt.Errorf("not a valid VideoParentType, try [%s]", strings.Join(_VideoParentTypeNames, ", "))

var _VideoParentTypeNames = []string{
	string(VideoParentTypePracticeSubmission),
	string(VideoParentTypeCourseSectionItem),
	string(VideoParentTypeDrill),
}

// VideoParentTypeNames returns a list of possible string values of VideoParentType.
func VideoParentTypeNames() []string {
	tmp := make([]string, len(_VideoParentTypeNames))
	copy(tmp, _VideoParentTypeNames)
	return tmp
}

// VideoParentTypeValues returns a list of the values for VideoParentType
func VideoParentTypeValues() []VideoParentType {
	return []VideoParentType{
		VideoParentTypePracticeSubmission,
		VideoParentTypeCourseSectionItem,
		VideoParentTypeDrill,
	}
}

// String implements the Stringer interface.
func (x VideoParentType) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x VideoParentType) IsValid() bool {
	_, err := ParseVideoParentType(string(x))
	return err == nil
}

var _VideoParentTypeValue = map[string]VideoParentType{
	"PracticeSubmission": VideoParentTypePracticeSubmission,
	"CourseSectionItem":  VideoParentTypeCourseSectionItem,
	"Drill":              VideoParentTypeDrill,
}

// ParseVideoParentType attempts to convert a string to a VideoParentType.
func ParseVideoParentType(name string) (VideoParentType, error) {
	if x, ok := _VideoParentTypeValue[name]; ok {
		return x, nil
	}
	return VideoParentType(""), fmt.Errorf("%s is %w", name, ErrInvalidVideoParentType)
}

// MarshalText implements the text marshaller method.
func (x VideoParentType) MarshalText() ([]byte, error) {
	return []byte(string(x)), nil
}

// UnmarshalText implements the text unmarshaller method.
func (x *VideoParentType) UnmarshalText(text []byte) error {
	tmp, err := ParseVideoParentType(string(text))
	if err != nil {
		return err
	}
	*x = tmp
	return nil
}
