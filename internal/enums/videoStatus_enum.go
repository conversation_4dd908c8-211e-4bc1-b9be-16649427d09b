// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package enums

import (
	"database/sql/driver"
	"errors"
	"fmt"
	"strconv"
	"strings"
)

const (
	// VideoStatusDraft is a VideoStatus of type draft.
	VideoStatusDraft VideoStatus = "draft"
	// VideoStatusPending is a VideoStatus of type pending.
	VideoStatusPending VideoStatus = "pending"
	// VideoStatusApproved is a VideoStatus of type approved.
	VideoStatusApproved VideoStatus = "approved"
	// VideoStatusRejected is a VideoStatus of type rejected.
	VideoStatusRejected VideoStatus = "rejected"
)

var ErrInvalidVideoStatus = fmt.Errorf("not a valid VideoStatus, try [%s]", strings.Join(_VideoStatusNames, ", "))

var _VideoStatusNames = []string{
	string(VideoStatusDraft),
	string(VideoStatusPending),
	string(VideoStatusApproved),
	string(VideoStatusRejected),
}

// VideoStatusNames returns a list of possible string values of VideoStatus.
func VideoStatusNames() []string {
	tmp := make([]string, len(_VideoStatusNames))
	copy(tmp, _VideoStatusNames)
	return tmp
}

// VideoStatusValues returns a list of the values for VideoStatus
func VideoStatusValues() []VideoStatus {
	return []VideoStatus{
		VideoStatusDraft,
		VideoStatusPending,
		VideoStatusApproved,
		VideoStatusRejected,
	}
}

// String implements the Stringer interface.
func (x VideoStatus) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x VideoStatus) IsValid() bool {
	_, err := ParseVideoStatus(string(x))
	return err == nil
}

var _VideoStatusValue = map[string]VideoStatus{
	"draft":    VideoStatusDraft,
	"pending":  VideoStatusPending,
	"approved": VideoStatusApproved,
	"rejected": VideoStatusRejected,
}

// ParseVideoStatus attempts to convert a string to a VideoStatus.
func ParseVideoStatus(name string) (VideoStatus, error) {
	if x, ok := _VideoStatusValue[name]; ok {
		return x, nil
	}
	return VideoStatus(""), fmt.Errorf("%s is %w", name, ErrInvalidVideoStatus)
}

// MarshalText implements the text marshaller method.
func (x VideoStatus) MarshalText() ([]byte, error) {
	return []byte(string(x)), nil
}

// UnmarshalText implements the text unmarshaller method.
func (x *VideoStatus) UnmarshalText(text []byte) error {
	tmp, err := ParseVideoStatus(string(text))
	if err != nil {
		return err
	}
	*x = tmp
	return nil
}

var errVideoStatusNilPtr = errors.New("value pointer is nil") // one per type for package clashes

var sqlIntVideoStatusMap = map[int64]VideoStatus{
	1: VideoStatusDraft,
	2: VideoStatusPending,
	3: VideoStatusApproved,
	4: VideoStatusRejected,
}

var sqlIntVideoStatusValue = map[VideoStatus]int64{
	VideoStatusDraft:    1,
	VideoStatusPending:  2,
	VideoStatusApproved: 3,
	VideoStatusRejected: 4,
}

func lookupSqlIntVideoStatus(val int64) (VideoStatus, error) {
	x, ok := sqlIntVideoStatusMap[val]
	if !ok {
		return x, fmt.Errorf("%v is not %w", val, ErrInvalidVideoStatus)
	}
	return x, nil
}

// Scan implements the Scanner interface.
func (x *VideoStatus) Scan(value interface{}) (err error) {
	if value == nil {
		*x = VideoStatus("")
		return
	}

	// A wider range of scannable types.
	// driver.Value values at the top of the list for expediency
	switch v := value.(type) {
	case int64:
		*x, err = lookupSqlIntVideoStatus(v)
	case string:
		*x, err = ParseVideoStatus(v)
	case []byte:
		if val, verr := strconv.ParseInt(string(v), 10, 64); verr == nil {
			*x, err = lookupSqlIntVideoStatus(val)
		} else {
			// try parsing the value as a string
			*x, err = ParseVideoStatus(string(v))
		}
	case VideoStatus:
		*x = v
	case int:
		*x, err = lookupSqlIntVideoStatus(int64(v))
	case *VideoStatus:
		if v == nil {
			return errVideoStatusNilPtr
		}
		*x = *v
	case uint:
		*x, err = lookupSqlIntVideoStatus(int64(v))
	case uint64:
		*x, err = lookupSqlIntVideoStatus(int64(v))
	case *int:
		if v == nil {
			return errVideoStatusNilPtr
		}
		*x, err = lookupSqlIntVideoStatus(int64(*v))
	case *int64:
		if v == nil {
			return errVideoStatusNilPtr
		}
		*x, err = lookupSqlIntVideoStatus(int64(*v))
	case float64: // json marshals everything as a float64 if it's a number
		*x, err = lookupSqlIntVideoStatus(int64(v))
	case *float64: // json marshals everything as a float64 if it's a number
		if v == nil {
			return errVideoStatusNilPtr
		}
		*x, err = lookupSqlIntVideoStatus(int64(*v))
	case *uint:
		if v == nil {
			return errVideoStatusNilPtr
		}
		*x, err = lookupSqlIntVideoStatus(int64(*v))
	case *uint64:
		if v == nil {
			return errVideoStatusNilPtr
		}
		*x, err = lookupSqlIntVideoStatus(int64(*v))
	case *string:
		if v == nil {
			return errVideoStatusNilPtr
		}
		*x, err = ParseVideoStatus(*v)
	default:
		return errors.New("invalid type for VideoStatus")
	}

	return
}

// Value implements the driver Valuer interface.
func (x VideoStatus) Value() (driver.Value, error) {
	val, ok := sqlIntVideoStatusValue[x]
	if !ok {
		return nil, ErrInvalidVideoStatus
	}
	return int64(val), nil
}

// Additional template
func (x VideoStatus) ToInt64() int64 {
	return sqlIntVideoStatusValue[x]
}
