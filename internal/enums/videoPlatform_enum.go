// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package enums

import (
	"database/sql/driver"
	"errors"
	"fmt"
	"strconv"
	"strings"
)

const (
	// VideoPlatformMux is a VideoPlatform of type mux.
	VideoPlatformMux VideoPlatform = "mux"
	// VideoPlatformYoutube is a VideoPlatform of type youtube.
	VideoPlatformYoutube VideoPlatform = "youtube"
)

var ErrInvalidVideoPlatform = fmt.Errorf("not a valid VideoPlatform, try [%s]", strings.Join(_VideoPlatformNames, ", "))

var _VideoPlatformNames = []string{
	string(VideoPlatformMux),
	string(VideoPlatformYoutube),
}

// VideoPlatformNames returns a list of possible string values of VideoPlatform.
func VideoPlatformNames() []string {
	tmp := make([]string, len(_VideoPlatformNames))
	copy(tmp, _VideoPlatformNames)
	return tmp
}

// VideoPlatformValues returns a list of the values for VideoPlatform
func VideoPlatformValues() []VideoPlatform {
	return []VideoPlatform{
		VideoPlatformMux,
		VideoPlatformYoutube,
	}
}

// String implements the Stringer interface.
func (x VideoPlatform) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x VideoPlatform) IsValid() bool {
	_, err := ParseVideoPlatform(string(x))
	return err == nil
}

var _VideoPlatformValue = map[string]VideoPlatform{
	"mux":     VideoPlatformMux,
	"youtube": VideoPlatformYoutube,
}

// ParseVideoPlatform attempts to convert a string to a VideoPlatform.
func ParseVideoPlatform(name string) (VideoPlatform, error) {
	if x, ok := _VideoPlatformValue[name]; ok {
		return x, nil
	}
	return VideoPlatform(""), fmt.Errorf("%s is %w", name, ErrInvalidVideoPlatform)
}

// MarshalText implements the text marshaller method.
func (x VideoPlatform) MarshalText() ([]byte, error) {
	return []byte(string(x)), nil
}

// UnmarshalText implements the text unmarshaller method.
func (x *VideoPlatform) UnmarshalText(text []byte) error {
	tmp, err := ParseVideoPlatform(string(text))
	if err != nil {
		return err
	}
	*x = tmp
	return nil
}

var errVideoPlatformNilPtr = errors.New("value pointer is nil") // one per type for package clashes

var sqlIntVideoPlatformMap = map[int64]VideoPlatform{
	1: VideoPlatformMux,
	2: VideoPlatformYoutube,
}

var sqlIntVideoPlatformValue = map[VideoPlatform]int64{
	VideoPlatformMux:     1,
	VideoPlatformYoutube: 2,
}

func lookupSqlIntVideoPlatform(val int64) (VideoPlatform, error) {
	x, ok := sqlIntVideoPlatformMap[val]
	if !ok {
		return x, fmt.Errorf("%v is not %w", val, ErrInvalidVideoPlatform)
	}
	return x, nil
}

// Scan implements the Scanner interface.
func (x *VideoPlatform) Scan(value interface{}) (err error) {
	if value == nil {
		*x = VideoPlatform("")
		return
	}

	// A wider range of scannable types.
	// driver.Value values at the top of the list for expediency
	switch v := value.(type) {
	case int64:
		*x, err = lookupSqlIntVideoPlatform(v)
	case string:
		*x, err = ParseVideoPlatform(v)
	case []byte:
		if val, verr := strconv.ParseInt(string(v), 10, 64); verr == nil {
			*x, err = lookupSqlIntVideoPlatform(val)
		} else {
			// try parsing the value as a string
			*x, err = ParseVideoPlatform(string(v))
		}
	case VideoPlatform:
		*x = v
	case int:
		*x, err = lookupSqlIntVideoPlatform(int64(v))
	case *VideoPlatform:
		if v == nil {
			return errVideoPlatformNilPtr
		}
		*x = *v
	case uint:
		*x, err = lookupSqlIntVideoPlatform(int64(v))
	case uint64:
		*x, err = lookupSqlIntVideoPlatform(int64(v))
	case *int:
		if v == nil {
			return errVideoPlatformNilPtr
		}
		*x, err = lookupSqlIntVideoPlatform(int64(*v))
	case *int64:
		if v == nil {
			return errVideoPlatformNilPtr
		}
		*x, err = lookupSqlIntVideoPlatform(int64(*v))
	case float64: // json marshals everything as a float64 if it's a number
		*x, err = lookupSqlIntVideoPlatform(int64(v))
	case *float64: // json marshals everything as a float64 if it's a number
		if v == nil {
			return errVideoPlatformNilPtr
		}
		*x, err = lookupSqlIntVideoPlatform(int64(*v))
	case *uint:
		if v == nil {
			return errVideoPlatformNilPtr
		}
		*x, err = lookupSqlIntVideoPlatform(int64(*v))
	case *uint64:
		if v == nil {
			return errVideoPlatformNilPtr
		}
		*x, err = lookupSqlIntVideoPlatform(int64(*v))
	case *string:
		if v == nil {
			return errVideoPlatformNilPtr
		}
		*x, err = ParseVideoPlatform(*v)
	default:
		return errors.New("invalid type for VideoPlatform")
	}

	return
}

// Value implements the driver Valuer interface.
func (x VideoPlatform) Value() (driver.Value, error) {
	val, ok := sqlIntVideoPlatformValue[x]
	if !ok {
		return nil, ErrInvalidVideoPlatform
	}
	return int64(val), nil
}

// Additional template
func (x VideoPlatform) ToInt64() int64 {
	return sqlIntVideoPlatformValue[x]
}
