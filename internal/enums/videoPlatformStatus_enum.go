// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package enums

import (
	"database/sql/driver"
	"errors"
	"fmt"
	"strconv"
	"strings"
)

const (
	// VideoPlatformStatusSyncing is a VideoPlatformStatus of type syncing.
	VideoPlatformStatusSyncing VideoPlatformStatus = "syncing"
	// VideoPlatformStatusAvailable is a VideoPlatformStatus of type available.
	VideoPlatformStatusAvailable VideoPlatformStatus = "available"
	// VideoPlatformStatusError is a VideoPlatformStatus of type error.
	VideoPlatformStatusError VideoPlatformStatus = "error"
)

var ErrInvalidVideoPlatformStatus = fmt.Errorf("not a valid VideoPlatformStatus, try [%s]", strings.Join(_VideoPlatformStatusNames, ", "))

var _VideoPlatformStatusNames = []string{
	string(VideoPlatformStatusSyncing),
	string(VideoPlatformStatusAvailable),
	string(VideoPlatformStatusError),
}

// VideoPlatformStatusNames returns a list of possible string values of VideoPlatformStatus.
func VideoPlatformStatusNames() []string {
	tmp := make([]string, len(_VideoPlatformStatusNames))
	copy(tmp, _VideoPlatformStatusNames)
	return tmp
}

// VideoPlatformStatusValues returns a list of the values for VideoPlatformStatus
func VideoPlatformStatusValues() []VideoPlatformStatus {
	return []VideoPlatformStatus{
		VideoPlatformStatusSyncing,
		VideoPlatformStatusAvailable,
		VideoPlatformStatusError,
	}
}

// String implements the Stringer interface.
func (x VideoPlatformStatus) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x VideoPlatformStatus) IsValid() bool {
	_, err := ParseVideoPlatformStatus(string(x))
	return err == nil
}

var _VideoPlatformStatusValue = map[string]VideoPlatformStatus{
	"syncing":   VideoPlatformStatusSyncing,
	"available": VideoPlatformStatusAvailable,
	"error":     VideoPlatformStatusError,
}

// ParseVideoPlatformStatus attempts to convert a string to a VideoPlatformStatus.
func ParseVideoPlatformStatus(name string) (VideoPlatformStatus, error) {
	if x, ok := _VideoPlatformStatusValue[name]; ok {
		return x, nil
	}
	return VideoPlatformStatus(""), fmt.Errorf("%s is %w", name, ErrInvalidVideoPlatformStatus)
}

// MarshalText implements the text marshaller method.
func (x VideoPlatformStatus) MarshalText() ([]byte, error) {
	return []byte(string(x)), nil
}

// UnmarshalText implements the text unmarshaller method.
func (x *VideoPlatformStatus) UnmarshalText(text []byte) error {
	tmp, err := ParseVideoPlatformStatus(string(text))
	if err != nil {
		return err
	}
	*x = tmp
	return nil
}

var errVideoPlatformStatusNilPtr = errors.New("value pointer is nil") // one per type for package clashes

var sqlIntVideoPlatformStatusMap = map[int64]VideoPlatformStatus{
	1: VideoPlatformStatusSyncing,
	2: VideoPlatformStatusAvailable,
	3: VideoPlatformStatusError,
}

var sqlIntVideoPlatformStatusValue = map[VideoPlatformStatus]int64{
	VideoPlatformStatusSyncing:   1,
	VideoPlatformStatusAvailable: 2,
	VideoPlatformStatusError:     3,
}

func lookupSqlIntVideoPlatformStatus(val int64) (VideoPlatformStatus, error) {
	x, ok := sqlIntVideoPlatformStatusMap[val]
	if !ok {
		return x, fmt.Errorf("%v is not %w", val, ErrInvalidVideoPlatformStatus)
	}
	return x, nil
}

// Scan implements the Scanner interface.
func (x *VideoPlatformStatus) Scan(value interface{}) (err error) {
	if value == nil {
		*x = VideoPlatformStatus("")
		return
	}

	// A wider range of scannable types.
	// driver.Value values at the top of the list for expediency
	switch v := value.(type) {
	case int64:
		*x, err = lookupSqlIntVideoPlatformStatus(v)
	case string:
		*x, err = ParseVideoPlatformStatus(v)
	case []byte:
		if val, verr := strconv.ParseInt(string(v), 10, 64); verr == nil {
			*x, err = lookupSqlIntVideoPlatformStatus(val)
		} else {
			// try parsing the value as a string
			*x, err = ParseVideoPlatformStatus(string(v))
		}
	case VideoPlatformStatus:
		*x = v
	case int:
		*x, err = lookupSqlIntVideoPlatformStatus(int64(v))
	case *VideoPlatformStatus:
		if v == nil {
			return errVideoPlatformStatusNilPtr
		}
		*x = *v
	case uint:
		*x, err = lookupSqlIntVideoPlatformStatus(int64(v))
	case uint64:
		*x, err = lookupSqlIntVideoPlatformStatus(int64(v))
	case *int:
		if v == nil {
			return errVideoPlatformStatusNilPtr
		}
		*x, err = lookupSqlIntVideoPlatformStatus(int64(*v))
	case *int64:
		if v == nil {
			return errVideoPlatformStatusNilPtr
		}
		*x, err = lookupSqlIntVideoPlatformStatus(int64(*v))
	case float64: // json marshals everything as a float64 if it's a number
		*x, err = lookupSqlIntVideoPlatformStatus(int64(v))
	case *float64: // json marshals everything as a float64 if it's a number
		if v == nil {
			return errVideoPlatformStatusNilPtr
		}
		*x, err = lookupSqlIntVideoPlatformStatus(int64(*v))
	case *uint:
		if v == nil {
			return errVideoPlatformStatusNilPtr
		}
		*x, err = lookupSqlIntVideoPlatformStatus(int64(*v))
	case *uint64:
		if v == nil {
			return errVideoPlatformStatusNilPtr
		}
		*x, err = lookupSqlIntVideoPlatformStatus(int64(*v))
	case *string:
		if v == nil {
			return errVideoPlatformStatusNilPtr
		}
		*x, err = ParseVideoPlatformStatus(*v)
	default:
		return errors.New("invalid type for VideoPlatformStatus")
	}

	return
}

// Value implements the driver Valuer interface.
func (x VideoPlatformStatus) Value() (driver.Value, error) {
	val, ok := sqlIntVideoPlatformStatusValue[x]
	if !ok {
		return nil, ErrInvalidVideoPlatformStatus
	}
	return int64(val), nil
}

// Additional template
func (x VideoPlatformStatus) ToInt64() int64 {
	return sqlIntVideoPlatformStatusValue[x]
}
