// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package enums

import (
	"database/sql/driver"
	"errors"
	"fmt"
	"strconv"
	"strings"
)

const (
	// NotifiableTypeCourse is a NotifiableType of type Course.
	NotifiableTypeCourse NotifiableType = "Course"
)

var ErrInvalidNotifiableType = fmt.Errorf("not a valid NotifiableType, try [%s]", strings.Join(_NotifiableTypeNames, ", "))

var _NotifiableTypeNames = []string{
	string(NotifiableTypeCourse),
}

// NotifiableTypeNames returns a list of possible string values of NotifiableType.
func NotifiableTypeNames() []string {
	tmp := make([]string, len(_NotifiableTypeNames))
	copy(tmp, _NotifiableTypeNames)
	return tmp
}

// NotifiableTypeValues returns a list of the values for NotifiableType
func NotifiableTypeValues() []NotifiableType {
	return []NotifiableType{
		NotifiableTypeCourse,
	}
}

// String implements the Stringer interface.
func (x NotifiableType) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x NotifiableType) IsValid() bool {
	_, err := ParseNotifiableType(string(x))
	return err == nil
}

var _NotifiableTypeValue = map[string]NotifiableType{
	"Course": NotifiableTypeCourse,
}

// ParseNotifiableType attempts to convert a string to a NotifiableType.
func ParseNotifiableType(name string) (NotifiableType, error) {
	if x, ok := _NotifiableTypeValue[name]; ok {
		return x, nil
	}
	return NotifiableType(""), fmt.Errorf("%s is %w", name, ErrInvalidNotifiableType)
}

// MarshalText implements the text marshaller method.
func (x NotifiableType) MarshalText() ([]byte, error) {
	return []byte(string(x)), nil
}

// UnmarshalText implements the text unmarshaller method.
func (x *NotifiableType) UnmarshalText(text []byte) error {
	tmp, err := ParseNotifiableType(string(text))
	if err != nil {
		return err
	}
	*x = tmp
	return nil
}

var errNotifiableTypeNilPtr = errors.New("value pointer is nil") // one per type for package clashes

var sqlIntNotifiableTypeMap = map[int64]NotifiableType{
	0: NotifiableTypeCourse,
}

var sqlIntNotifiableTypeValue = map[NotifiableType]int64{
	NotifiableTypeCourse: 0,
}

func lookupSqlIntNotifiableType(val int64) (NotifiableType, error) {
	x, ok := sqlIntNotifiableTypeMap[val]
	if !ok {
		return x, fmt.Errorf("%v is not %w", val, ErrInvalidNotifiableType)
	}
	return x, nil
}

// Scan implements the Scanner interface.
func (x *NotifiableType) Scan(value interface{}) (err error) {
	if value == nil {
		*x = NotifiableType("")
		return
	}

	// A wider range of scannable types.
	// driver.Value values at the top of the list for expediency
	switch v := value.(type) {
	case int64:
		*x, err = lookupSqlIntNotifiableType(v)
	case string:
		*x, err = ParseNotifiableType(v)
	case []byte:
		if val, verr := strconv.ParseInt(string(v), 10, 64); verr == nil {
			*x, err = lookupSqlIntNotifiableType(val)
		} else {
			// try parsing the value as a string
			*x, err = ParseNotifiableType(string(v))
		}
	case NotifiableType:
		*x = v
	case int:
		*x, err = lookupSqlIntNotifiableType(int64(v))
	case *NotifiableType:
		if v == nil {
			return errNotifiableTypeNilPtr
		}
		*x = *v
	case uint:
		*x, err = lookupSqlIntNotifiableType(int64(v))
	case uint64:
		*x, err = lookupSqlIntNotifiableType(int64(v))
	case *int:
		if v == nil {
			return errNotifiableTypeNilPtr
		}
		*x, err = lookupSqlIntNotifiableType(int64(*v))
	case *int64:
		if v == nil {
			return errNotifiableTypeNilPtr
		}
		*x, err = lookupSqlIntNotifiableType(int64(*v))
	case float64: // json marshals everything as a float64 if it's a number
		*x, err = lookupSqlIntNotifiableType(int64(v))
	case *float64: // json marshals everything as a float64 if it's a number
		if v == nil {
			return errNotifiableTypeNilPtr
		}
		*x, err = lookupSqlIntNotifiableType(int64(*v))
	case *uint:
		if v == nil {
			return errNotifiableTypeNilPtr
		}
		*x, err = lookupSqlIntNotifiableType(int64(*v))
	case *uint64:
		if v == nil {
			return errNotifiableTypeNilPtr
		}
		*x, err = lookupSqlIntNotifiableType(int64(*v))
	case *string:
		if v == nil {
			return errNotifiableTypeNilPtr
		}
		*x, err = ParseNotifiableType(*v)
	default:
		return errors.New("invalid type for NotifiableType")
	}

	return
}

// Value implements the driver Valuer interface.
func (x NotifiableType) Value() (driver.Value, error) {
	val, ok := sqlIntNotifiableTypeValue[x]
	if !ok {
		return nil, ErrInvalidNotifiableType
	}
	return int64(val), nil
}

// Additional template
func (x NotifiableType) ToInt64() int64 {
	return sqlIntNotifiableTypeValue[x]
}
