// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package enums

import (
	"database/sql/driver"
	"errors"
	"fmt"
	"strconv"
	"strings"
)

const (
	// CourseUserStatusInvited is a CourseUserStatus of type invited.
	CourseUserStatusInvited CourseUserStatus = "invited"
	// CourseUserStatusEnrolled is a CourseUserStatus of type enrolled.
	CourseUserStatusEnrolled CourseUserStatus = "enrolled"
	// CourseUserStatusInProgress is a CourseUserStatus of type in_progress.
	CourseUserStatusInProgress CourseUserStatus = "in_progress"
	// CourseUserStatusCompleted is a CourseUserStatus of type completed.
	CourseUserStatusCompleted CourseUserStatus = "completed"
)

var ErrInvalidCourseUserStatus = fmt.Errorf("not a valid CourseUserStatus, try [%s]", strings.Join(_CourseUserStatusNames, ", "))

var _CourseUserStatusNames = []string{
	string(CourseUserStatusInvited),
	string(CourseUserStatusEnrolled),
	string(CourseUserStatusInProgress),
	string(CourseUserStatusCompleted),
}

// CourseUserStatusNames returns a list of possible string values of CourseUserStatus.
func CourseUserStatusNames() []string {
	tmp := make([]string, len(_CourseUserStatusNames))
	copy(tmp, _CourseUserStatusNames)
	return tmp
}

// CourseUserStatusValues returns a list of the values for CourseUserStatus
func CourseUserStatusValues() []CourseUserStatus {
	return []CourseUserStatus{
		CourseUserStatusInvited,
		CourseUserStatusEnrolled,
		CourseUserStatusInProgress,
		CourseUserStatusCompleted,
	}
}

// String implements the Stringer interface.
func (x CourseUserStatus) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x CourseUserStatus) IsValid() bool {
	_, err := ParseCourseUserStatus(string(x))
	return err == nil
}

var _CourseUserStatusValue = map[string]CourseUserStatus{
	"invited":     CourseUserStatusInvited,
	"enrolled":    CourseUserStatusEnrolled,
	"in_progress": CourseUserStatusInProgress,
	"completed":   CourseUserStatusCompleted,
}

// ParseCourseUserStatus attempts to convert a string to a CourseUserStatus.
func ParseCourseUserStatus(name string) (CourseUserStatus, error) {
	if x, ok := _CourseUserStatusValue[name]; ok {
		return x, nil
	}
	return CourseUserStatus(""), fmt.Errorf("%s is %w", name, ErrInvalidCourseUserStatus)
}

// MarshalText implements the text marshaller method.
func (x CourseUserStatus) MarshalText() ([]byte, error) {
	return []byte(string(x)), nil
}

// UnmarshalText implements the text unmarshaller method.
func (x *CourseUserStatus) UnmarshalText(text []byte) error {
	tmp, err := ParseCourseUserStatus(string(text))
	if err != nil {
		return err
	}
	*x = tmp
	return nil
}

var errCourseUserStatusNilPtr = errors.New("value pointer is nil") // one per type for package clashes

var sqlIntCourseUserStatusMap = map[int64]CourseUserStatus{
	0: CourseUserStatusInvited,
	1: CourseUserStatusEnrolled,
	2: CourseUserStatusInProgress,
	3: CourseUserStatusCompleted,
}

var sqlIntCourseUserStatusValue = map[CourseUserStatus]int64{
	CourseUserStatusInvited:    0,
	CourseUserStatusEnrolled:   1,
	CourseUserStatusInProgress: 2,
	CourseUserStatusCompleted:  3,
}

func lookupSqlIntCourseUserStatus(val int64) (CourseUserStatus, error) {
	x, ok := sqlIntCourseUserStatusMap[val]
	if !ok {
		return x, fmt.Errorf("%v is not %w", val, ErrInvalidCourseUserStatus)
	}
	return x, nil
}

// Scan implements the Scanner interface.
func (x *CourseUserStatus) Scan(value interface{}) (err error) {
	if value == nil {
		*x = CourseUserStatus("")
		return
	}

	// A wider range of scannable types.
	// driver.Value values at the top of the list for expediency
	switch v := value.(type) {
	case int64:
		*x, err = lookupSqlIntCourseUserStatus(v)
	case string:
		*x, err = ParseCourseUserStatus(v)
	case []byte:
		if val, verr := strconv.ParseInt(string(v), 10, 64); verr == nil {
			*x, err = lookupSqlIntCourseUserStatus(val)
		} else {
			// try parsing the value as a string
			*x, err = ParseCourseUserStatus(string(v))
		}
	case CourseUserStatus:
		*x = v
	case int:
		*x, err = lookupSqlIntCourseUserStatus(int64(v))
	case *CourseUserStatus:
		if v == nil {
			return errCourseUserStatusNilPtr
		}
		*x = *v
	case uint:
		*x, err = lookupSqlIntCourseUserStatus(int64(v))
	case uint64:
		*x, err = lookupSqlIntCourseUserStatus(int64(v))
	case *int:
		if v == nil {
			return errCourseUserStatusNilPtr
		}
		*x, err = lookupSqlIntCourseUserStatus(int64(*v))
	case *int64:
		if v == nil {
			return errCourseUserStatusNilPtr
		}
		*x, err = lookupSqlIntCourseUserStatus(int64(*v))
	case float64: // json marshals everything as a float64 if it's a number
		*x, err = lookupSqlIntCourseUserStatus(int64(v))
	case *float64: // json marshals everything as a float64 if it's a number
		if v == nil {
			return errCourseUserStatusNilPtr
		}
		*x, err = lookupSqlIntCourseUserStatus(int64(*v))
	case *uint:
		if v == nil {
			return errCourseUserStatusNilPtr
		}
		*x, err = lookupSqlIntCourseUserStatus(int64(*v))
	case *uint64:
		if v == nil {
			return errCourseUserStatusNilPtr
		}
		*x, err = lookupSqlIntCourseUserStatus(int64(*v))
	case *string:
		if v == nil {
			return errCourseUserStatusNilPtr
		}
		*x, err = ParseCourseUserStatus(*v)
	default:
		return errors.New("invalid type for CourseUserStatus")
	}

	return
}

// Value implements the driver Valuer interface.
func (x CourseUserStatus) Value() (driver.Value, error) {
	val, ok := sqlIntCourseUserStatusValue[x]
	if !ok {
		return nil, ErrInvalidCourseUserStatus
	}
	return int64(val), nil
}

// Additional template
func (x CourseUserStatus) ToInt64() int64 {
	return sqlIntCourseUserStatusValue[x]
}
