// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package enums

import (
	"database/sql/driver"
	"errors"
	"fmt"
	"strconv"
	"strings"
)

const (
	// VideoModerationStatusPending is a VideoModerationStatus of type pending.
	VideoModerationStatusPending VideoModerationStatus = "pending"
	// VideoModerationStatusApproved is a VideoModerationStatus of type approved.
	VideoModerationStatusApproved VideoModerationStatus = "approved"
	// VideoModerationStatusRejected is a VideoModerationStatus of type rejected.
	VideoModerationStatusRejected VideoModerationStatus = "rejected"
)

var ErrInvalidVideoModerationStatus = fmt.Errorf("not a valid VideoModerationStatus, try [%s]", strings.Join(_VideoModerationStatusNames, ", "))

var _VideoModerationStatusNames = []string{
	string(VideoModerationStatusPending),
	string(VideoModerationStatusApproved),
	string(VideoModerationStatusRejected),
}

// VideoModerationStatusNames returns a list of possible string values of VideoModerationStatus.
func VideoModerationStatusNames() []string {
	tmp := make([]string, len(_VideoModerationStatusNames))
	copy(tmp, _VideoModerationStatusNames)
	return tmp
}

// VideoModerationStatusValues returns a list of the values for VideoModerationStatus
func VideoModerationStatusValues() []VideoModerationStatus {
	return []VideoModerationStatus{
		VideoModerationStatusPending,
		VideoModerationStatusApproved,
		VideoModerationStatusRejected,
	}
}

// String implements the Stringer interface.
func (x VideoModerationStatus) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x VideoModerationStatus) IsValid() bool {
	_, err := ParseVideoModerationStatus(string(x))
	return err == nil
}

var _VideoModerationStatusValue = map[string]VideoModerationStatus{
	"pending":  VideoModerationStatusPending,
	"approved": VideoModerationStatusApproved,
	"rejected": VideoModerationStatusRejected,
}

// ParseVideoModerationStatus attempts to convert a string to a VideoModerationStatus.
func ParseVideoModerationStatus(name string) (VideoModerationStatus, error) {
	if x, ok := _VideoModerationStatusValue[name]; ok {
		return x, nil
	}
	return VideoModerationStatus(""), fmt.Errorf("%s is %w", name, ErrInvalidVideoModerationStatus)
}

// MarshalText implements the text marshaller method.
func (x VideoModerationStatus) MarshalText() ([]byte, error) {
	return []byte(string(x)), nil
}

// UnmarshalText implements the text unmarshaller method.
func (x *VideoModerationStatus) UnmarshalText(text []byte) error {
	tmp, err := ParseVideoModerationStatus(string(text))
	if err != nil {
		return err
	}
	*x = tmp
	return nil
}

var errVideoModerationStatusNilPtr = errors.New("value pointer is nil") // one per type for package clashes

var sqlIntVideoModerationStatusMap = map[int64]VideoModerationStatus{
	1: VideoModerationStatusPending,
	2: VideoModerationStatusApproved,
	3: VideoModerationStatusRejected,
}

var sqlIntVideoModerationStatusValue = map[VideoModerationStatus]int64{
	VideoModerationStatusPending:  1,
	VideoModerationStatusApproved: 2,
	VideoModerationStatusRejected: 3,
}

func lookupSqlIntVideoModerationStatus(val int64) (VideoModerationStatus, error) {
	x, ok := sqlIntVideoModerationStatusMap[val]
	if !ok {
		return x, fmt.Errorf("%v is not %w", val, ErrInvalidVideoModerationStatus)
	}
	return x, nil
}

// Scan implements the Scanner interface.
func (x *VideoModerationStatus) Scan(value interface{}) (err error) {
	if value == nil {
		*x = VideoModerationStatus("")
		return
	}

	// A wider range of scannable types.
	// driver.Value values at the top of the list for expediency
	switch v := value.(type) {
	case int64:
		*x, err = lookupSqlIntVideoModerationStatus(v)
	case string:
		*x, err = ParseVideoModerationStatus(v)
	case []byte:
		if val, verr := strconv.ParseInt(string(v), 10, 64); verr == nil {
			*x, err = lookupSqlIntVideoModerationStatus(val)
		} else {
			// try parsing the value as a string
			*x, err = ParseVideoModerationStatus(string(v))
		}
	case VideoModerationStatus:
		*x = v
	case int:
		*x, err = lookupSqlIntVideoModerationStatus(int64(v))
	case *VideoModerationStatus:
		if v == nil {
			return errVideoModerationStatusNilPtr
		}
		*x = *v
	case uint:
		*x, err = lookupSqlIntVideoModerationStatus(int64(v))
	case uint64:
		*x, err = lookupSqlIntVideoModerationStatus(int64(v))
	case *int:
		if v == nil {
			return errVideoModerationStatusNilPtr
		}
		*x, err = lookupSqlIntVideoModerationStatus(int64(*v))
	case *int64:
		if v == nil {
			return errVideoModerationStatusNilPtr
		}
		*x, err = lookupSqlIntVideoModerationStatus(int64(*v))
	case float64: // json marshals everything as a float64 if it's a number
		*x, err = lookupSqlIntVideoModerationStatus(int64(v))
	case *float64: // json marshals everything as a float64 if it's a number
		if v == nil {
			return errVideoModerationStatusNilPtr
		}
		*x, err = lookupSqlIntVideoModerationStatus(int64(*v))
	case *uint:
		if v == nil {
			return errVideoModerationStatusNilPtr
		}
		*x, err = lookupSqlIntVideoModerationStatus(int64(*v))
	case *uint64:
		if v == nil {
			return errVideoModerationStatusNilPtr
		}
		*x, err = lookupSqlIntVideoModerationStatus(int64(*v))
	case *string:
		if v == nil {
			return errVideoModerationStatusNilPtr
		}
		*x, err = ParseVideoModerationStatus(*v)
	default:
		return errors.New("invalid type for VideoModerationStatus")
	}

	return
}

// Value implements the driver Valuer interface.
func (x VideoModerationStatus) Value() (driver.Value, error) {
	val, ok := sqlIntVideoModerationStatusValue[x]
	if !ok {
		return nil, ErrInvalidVideoModerationStatus
	}
	return int64(val), nil
}

// Additional template
func (x VideoModerationStatus) ToInt64() int64 {
	return sqlIntVideoModerationStatusValue[x]
}
