// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package enums

import (
	"database/sql/driver"
	"errors"
	"fmt"
	"strconv"
	"strings"
)

const (
	// CourseStatusDraft is a CourseStatus of type draft.
	CourseStatusDraft CourseStatus = "draft"
	// CourseStatusSubmitted is a CourseStatus of type submitted.
	CourseStatusSubmitted CourseStatus = "submitted"
	// CourseStatusApproved is a CourseStatus of type approved.
	CourseStatusApproved CourseStatus = "approved"
	// CourseStatusRejected is a CourseStatus of type rejected.
	CourseStatusRejected CourseStatus = "rejected"
)

var ErrInvalidCourseStatus = fmt.Errorf("not a valid CourseStatus, try [%s]", strings.Join(_CourseStatusNames, ", "))

var _CourseStatusNames = []string{
	string(CourseStatusDraft),
	string(CourseStatusSubmitted),
	string(CourseStatusApproved),
	string(CourseStatusRejected),
}

// CourseStatusNames returns a list of possible string values of CourseStatus.
func CourseStatusNames() []string {
	tmp := make([]string, len(_CourseStatusNames))
	copy(tmp, _CourseStatusNames)
	return tmp
}

// CourseStatusValues returns a list of the values for CourseStatus
func CourseStatusValues() []CourseStatus {
	return []CourseStatus{
		CourseStatusDraft,
		CourseStatusSubmitted,
		CourseStatusApproved,
		CourseStatusRejected,
	}
}

// String implements the Stringer interface.
func (x CourseStatus) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x CourseStatus) IsValid() bool {
	_, err := ParseCourseStatus(string(x))
	return err == nil
}

var _CourseStatusValue = map[string]CourseStatus{
	"draft":     CourseStatusDraft,
	"submitted": CourseStatusSubmitted,
	"approved":  CourseStatusApproved,
	"rejected":  CourseStatusRejected,
}

// ParseCourseStatus attempts to convert a string to a CourseStatus.
func ParseCourseStatus(name string) (CourseStatus, error) {
	if x, ok := _CourseStatusValue[name]; ok {
		return x, nil
	}
	return CourseStatus(""), fmt.Errorf("%s is %w", name, ErrInvalidCourseStatus)
}

// MarshalText implements the text marshaller method.
func (x CourseStatus) MarshalText() ([]byte, error) {
	return []byte(string(x)), nil
}

// UnmarshalText implements the text unmarshaller method.
func (x *CourseStatus) UnmarshalText(text []byte) error {
	tmp, err := ParseCourseStatus(string(text))
	if err != nil {
		return err
	}
	*x = tmp
	return nil
}

var errCourseStatusNilPtr = errors.New("value pointer is nil") // one per type for package clashes

var sqlIntCourseStatusMap = map[int64]CourseStatus{
	0: CourseStatusDraft,
	1: CourseStatusSubmitted,
	2: CourseStatusApproved,
	3: CourseStatusRejected,
}

var sqlIntCourseStatusValue = map[CourseStatus]int64{
	CourseStatusDraft:     0,
	CourseStatusSubmitted: 1,
	CourseStatusApproved:  2,
	CourseStatusRejected:  3,
}

func lookupSqlIntCourseStatus(val int64) (CourseStatus, error) {
	x, ok := sqlIntCourseStatusMap[val]
	if !ok {
		return x, fmt.Errorf("%v is not %w", val, ErrInvalidCourseStatus)
	}
	return x, nil
}

// Scan implements the Scanner interface.
func (x *CourseStatus) Scan(value interface{}) (err error) {
	if value == nil {
		*x = CourseStatus("")
		return
	}

	// A wider range of scannable types.
	// driver.Value values at the top of the list for expediency
	switch v := value.(type) {
	case int64:
		*x, err = lookupSqlIntCourseStatus(v)
	case string:
		*x, err = ParseCourseStatus(v)
	case []byte:
		if val, verr := strconv.ParseInt(string(v), 10, 64); verr == nil {
			*x, err = lookupSqlIntCourseStatus(val)
		} else {
			// try parsing the value as a string
			*x, err = ParseCourseStatus(string(v))
		}
	case CourseStatus:
		*x = v
	case int:
		*x, err = lookupSqlIntCourseStatus(int64(v))
	case *CourseStatus:
		if v == nil {
			return errCourseStatusNilPtr
		}
		*x = *v
	case uint:
		*x, err = lookupSqlIntCourseStatus(int64(v))
	case uint64:
		*x, err = lookupSqlIntCourseStatus(int64(v))
	case *int:
		if v == nil {
			return errCourseStatusNilPtr
		}
		*x, err = lookupSqlIntCourseStatus(int64(*v))
	case *int64:
		if v == nil {
			return errCourseStatusNilPtr
		}
		*x, err = lookupSqlIntCourseStatus(int64(*v))
	case float64: // json marshals everything as a float64 if it's a number
		*x, err = lookupSqlIntCourseStatus(int64(v))
	case *float64: // json marshals everything as a float64 if it's a number
		if v == nil {
			return errCourseStatusNilPtr
		}
		*x, err = lookupSqlIntCourseStatus(int64(*v))
	case *uint:
		if v == nil {
			return errCourseStatusNilPtr
		}
		*x, err = lookupSqlIntCourseStatus(int64(*v))
	case *uint64:
		if v == nil {
			return errCourseStatusNilPtr
		}
		*x, err = lookupSqlIntCourseStatus(int64(*v))
	case *string:
		if v == nil {
			return errCourseStatusNilPtr
		}
		*x, err = ParseCourseStatus(*v)
	default:
		return errors.New("invalid type for CourseStatus")
	}

	return
}

// Value implements the driver Valuer interface.
func (x CourseStatus) Value() (driver.Value, error) {
	val, ok := sqlIntCourseStatusValue[x]
	if !ok {
		return nil, ErrInvalidCourseStatus
	}
	return int64(val), nil
}

// Additional template
func (x CourseStatus) ToInt64() int64 {
	return sqlIntCourseStatusValue[x]
}
