// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package enums

import (
	"database/sql/driver"
	"errors"
	"fmt"
	"strconv"
	"strings"
)

const (
	// UserCourseSectionItemStatusStudying is a UserCourseSectionItemStatus of type studying.
	UserCourseSectionItemStatusStudying UserCourseSectionItemStatus = "studying"
	// UserCourseSectionItemStatusCompleted is a UserCourseSectionItemStatus of type completed.
	UserCourseSectionItemStatusCompleted UserCourseSectionItemStatus = "completed"
)

var ErrInvalidUserCourseSectionItemStatus = fmt.Errorf("not a valid UserCourseSectionItemStatus, try [%s]", strings.Join(_UserCourseSectionItemStatusNames, ", "))

var _UserCourseSectionItemStatusNames = []string{
	string(UserCourseSectionItemStatusStudying),
	string(UserCourseSectionItemStatusCompleted),
}

// UserCourseSectionItemStatusNames returns a list of possible string values of UserCourseSectionItemStatus.
func UserCourseSectionItemStatusNames() []string {
	tmp := make([]string, len(_UserCourseSectionItemStatusNames))
	copy(tmp, _UserCourseSectionItemStatusNames)
	return tmp
}

// UserCourseSectionItemStatusValues returns a list of the values for UserCourseSectionItemStatus
func UserCourseSectionItemStatusValues() []UserCourseSectionItemStatus {
	return []UserCourseSectionItemStatus{
		UserCourseSectionItemStatusStudying,
		UserCourseSectionItemStatusCompleted,
	}
}

// String implements the Stringer interface.
func (x UserCourseSectionItemStatus) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x UserCourseSectionItemStatus) IsValid() bool {
	_, err := ParseUserCourseSectionItemStatus(string(x))
	return err == nil
}

var _UserCourseSectionItemStatusValue = map[string]UserCourseSectionItemStatus{
	"studying":  UserCourseSectionItemStatusStudying,
	"completed": UserCourseSectionItemStatusCompleted,
}

// ParseUserCourseSectionItemStatus attempts to convert a string to a UserCourseSectionItemStatus.
func ParseUserCourseSectionItemStatus(name string) (UserCourseSectionItemStatus, error) {
	if x, ok := _UserCourseSectionItemStatusValue[name]; ok {
		return x, nil
	}
	return UserCourseSectionItemStatus(""), fmt.Errorf("%s is %w", name, ErrInvalidUserCourseSectionItemStatus)
}

// MarshalText implements the text marshaller method.
func (x UserCourseSectionItemStatus) MarshalText() ([]byte, error) {
	return []byte(string(x)), nil
}

// UnmarshalText implements the text unmarshaller method.
func (x *UserCourseSectionItemStatus) UnmarshalText(text []byte) error {
	tmp, err := ParseUserCourseSectionItemStatus(string(text))
	if err != nil {
		return err
	}
	*x = tmp
	return nil
}

var errUserCourseSectionItemStatusNilPtr = errors.New("value pointer is nil") // one per type for package clashes

var sqlIntUserCourseSectionItemStatusMap = map[int64]UserCourseSectionItemStatus{
	0: UserCourseSectionItemStatusStudying,
	1: UserCourseSectionItemStatusCompleted,
}

var sqlIntUserCourseSectionItemStatusValue = map[UserCourseSectionItemStatus]int64{
	UserCourseSectionItemStatusStudying:  0,
	UserCourseSectionItemStatusCompleted: 1,
}

func lookupSqlIntUserCourseSectionItemStatus(val int64) (UserCourseSectionItemStatus, error) {
	x, ok := sqlIntUserCourseSectionItemStatusMap[val]
	if !ok {
		return x, fmt.Errorf("%v is not %w", val, ErrInvalidUserCourseSectionItemStatus)
	}
	return x, nil
}

// Scan implements the Scanner interface.
func (x *UserCourseSectionItemStatus) Scan(value interface{}) (err error) {
	if value == nil {
		*x = UserCourseSectionItemStatus("")
		return
	}

	// A wider range of scannable types.
	// driver.Value values at the top of the list for expediency
	switch v := value.(type) {
	case int64:
		*x, err = lookupSqlIntUserCourseSectionItemStatus(v)
	case string:
		*x, err = ParseUserCourseSectionItemStatus(v)
	case []byte:
		if val, verr := strconv.ParseInt(string(v), 10, 64); verr == nil {
			*x, err = lookupSqlIntUserCourseSectionItemStatus(val)
		} else {
			// try parsing the value as a string
			*x, err = ParseUserCourseSectionItemStatus(string(v))
		}
	case UserCourseSectionItemStatus:
		*x = v
	case int:
		*x, err = lookupSqlIntUserCourseSectionItemStatus(int64(v))
	case *UserCourseSectionItemStatus:
		if v == nil {
			return errUserCourseSectionItemStatusNilPtr
		}
		*x = *v
	case uint:
		*x, err = lookupSqlIntUserCourseSectionItemStatus(int64(v))
	case uint64:
		*x, err = lookupSqlIntUserCourseSectionItemStatus(int64(v))
	case *int:
		if v == nil {
			return errUserCourseSectionItemStatusNilPtr
		}
		*x, err = lookupSqlIntUserCourseSectionItemStatus(int64(*v))
	case *int64:
		if v == nil {
			return errUserCourseSectionItemStatusNilPtr
		}
		*x, err = lookupSqlIntUserCourseSectionItemStatus(int64(*v))
	case float64: // json marshals everything as a float64 if it's a number
		*x, err = lookupSqlIntUserCourseSectionItemStatus(int64(v))
	case *float64: // json marshals everything as a float64 if it's a number
		if v == nil {
			return errUserCourseSectionItemStatusNilPtr
		}
		*x, err = lookupSqlIntUserCourseSectionItemStatus(int64(*v))
	case *uint:
		if v == nil {
			return errUserCourseSectionItemStatusNilPtr
		}
		*x, err = lookupSqlIntUserCourseSectionItemStatus(int64(*v))
	case *uint64:
		if v == nil {
			return errUserCourseSectionItemStatusNilPtr
		}
		*x, err = lookupSqlIntUserCourseSectionItemStatus(int64(*v))
	case *string:
		if v == nil {
			return errUserCourseSectionItemStatusNilPtr
		}
		*x, err = ParseUserCourseSectionItemStatus(*v)
	default:
		return errors.New("invalid type for UserCourseSectionItemStatus")
	}

	return
}

// Value implements the driver Valuer interface.
func (x UserCourseSectionItemStatus) Value() (driver.Value, error) {
	val, ok := sqlIntUserCourseSectionItemStatusValue[x]
	if !ok {
		return nil, ErrInvalidUserCourseSectionItemStatus
	}
	return int64(val), nil
}

// Additional template
func (x UserCourseSectionItemStatus) ToInt64() int64 {
	return sqlIntUserCourseSectionItemStatusValue[x]
}
