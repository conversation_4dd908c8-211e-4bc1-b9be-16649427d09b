// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package enums

import (
	"database/sql/driver"
	"errors"
	"fmt"
	"strconv"
	"strings"
)

const (
	// DrillStatusPrivate is a DrillStatus of type private.
	DrillStatusPrivate DrillStatus = "private"
	// DrillStatusPublic is a DrillStatus of type public.
	DrillStatusPublic DrillStatus = "public"
)

var ErrInvalidDrillStatus = fmt.Errorf("not a valid DrillStatus, try [%s]", strings.Join(_DrillStatusNames, ", "))

var _DrillStatusNames = []string{
	string(DrillStatusPrivate),
	string(DrillStatusPublic),
}

// DrillStatusNames returns a list of possible string values of DrillStatus.
func DrillStatusNames() []string {
	tmp := make([]string, len(_DrillStatusNames))
	copy(tmp, _DrillStatusNames)
	return tmp
}

// DrillStatusValues returns a list of the values for DrillStatus
func DrillStatusValues() []DrillStatus {
	return []DrillStatus{
		DrillStatusPrivate,
		DrillStatusPublic,
	}
}

// String implements the Stringer interface.
func (x DrillStatus) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x DrillStatus) IsValid() bool {
	_, err := ParseDrillStatus(string(x))
	return err == nil
}

var _DrillStatusValue = map[string]DrillStatus{
	"private": DrillStatusPrivate,
	"public":  DrillStatusPublic,
}

// ParseDrillStatus attempts to convert a string to a DrillStatus.
func ParseDrillStatus(name string) (DrillStatus, error) {
	if x, ok := _DrillStatusValue[name]; ok {
		return x, nil
	}
	return DrillStatus(""), fmt.Errorf("%s is %w", name, ErrInvalidDrillStatus)
}

// MarshalText implements the text marshaller method.
func (x DrillStatus) MarshalText() ([]byte, error) {
	return []byte(string(x)), nil
}

// UnmarshalText implements the text unmarshaller method.
func (x *DrillStatus) UnmarshalText(text []byte) error {
	tmp, err := ParseDrillStatus(string(text))
	if err != nil {
		return err
	}
	*x = tmp
	return nil
}

var errDrillStatusNilPtr = errors.New("value pointer is nil") // one per type for package clashes

var sqlIntDrillStatusMap = map[int64]DrillStatus{
	0: DrillStatusPrivate,
	1: DrillStatusPublic,
}

var sqlIntDrillStatusValue = map[DrillStatus]int64{
	DrillStatusPrivate: 0,
	DrillStatusPublic:  1,
}

func lookupSqlIntDrillStatus(val int64) (DrillStatus, error) {
	x, ok := sqlIntDrillStatusMap[val]
	if !ok {
		return x, fmt.Errorf("%v is not %w", val, ErrInvalidDrillStatus)
	}
	return x, nil
}

// Scan implements the Scanner interface.
func (x *DrillStatus) Scan(value interface{}) (err error) {
	if value == nil {
		*x = DrillStatus("")
		return
	}

	// A wider range of scannable types.
	// driver.Value values at the top of the list for expediency
	switch v := value.(type) {
	case int64:
		*x, err = lookupSqlIntDrillStatus(v)
	case string:
		*x, err = ParseDrillStatus(v)
	case []byte:
		if val, verr := strconv.ParseInt(string(v), 10, 64); verr == nil {
			*x, err = lookupSqlIntDrillStatus(val)
		} else {
			// try parsing the value as a string
			*x, err = ParseDrillStatus(string(v))
		}
	case DrillStatus:
		*x = v
	case int:
		*x, err = lookupSqlIntDrillStatus(int64(v))
	case *DrillStatus:
		if v == nil {
			return errDrillStatusNilPtr
		}
		*x = *v
	case uint:
		*x, err = lookupSqlIntDrillStatus(int64(v))
	case uint64:
		*x, err = lookupSqlIntDrillStatus(int64(v))
	case *int:
		if v == nil {
			return errDrillStatusNilPtr
		}
		*x, err = lookupSqlIntDrillStatus(int64(*v))
	case *int64:
		if v == nil {
			return errDrillStatusNilPtr
		}
		*x, err = lookupSqlIntDrillStatus(int64(*v))
	case float64: // json marshals everything as a float64 if it's a number
		*x, err = lookupSqlIntDrillStatus(int64(v))
	case *float64: // json marshals everything as a float64 if it's a number
		if v == nil {
			return errDrillStatusNilPtr
		}
		*x, err = lookupSqlIntDrillStatus(int64(*v))
	case *uint:
		if v == nil {
			return errDrillStatusNilPtr
		}
		*x, err = lookupSqlIntDrillStatus(int64(*v))
	case *uint64:
		if v == nil {
			return errDrillStatusNilPtr
		}
		*x, err = lookupSqlIntDrillStatus(int64(*v))
	case *string:
		if v == nil {
			return errDrillStatusNilPtr
		}
		*x, err = ParseDrillStatus(*v)
	default:
		return errors.New("invalid type for DrillStatus")
	}

	return
}

// Value implements the driver Valuer interface.
func (x DrillStatus) Value() (driver.Value, error) {
	val, ok := sqlIntDrillStatusValue[x]
	if !ok {
		return nil, ErrInvalidDrillStatus
	}
	return int64(val), nil
}

// Additional template
func (x DrillStatus) ToInt64() int64 {
	return sqlIntDrillStatusValue[x]
}
