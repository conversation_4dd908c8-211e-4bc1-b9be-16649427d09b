// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package enums

import (
	"fmt"
	"strings"
)

const (
	// CensorHistoryParentDrill is a CensorHistoryParent of type drill.
	CensorHistoryParentDrill CensorHistoryParent = "Drill"
	// CensorHistoryParentCourse is a CensorHistoryParent of type course.
	CensorHistoryParentCourse CensorHistoryParent = "Course"
)

var ErrInvalidCensorHistoryParent = fmt.Errorf("not a valid CensorHistoryParent, try [%s]", strings.Join(_CensorHistoryParentNames, ", "))

var _CensorHistoryParentNames = []string{
	string(CensorHistoryParentDrill),
	string(CensorHistoryParentCourse),
}

// CensorHistoryParentNames returns a list of possible string values of CensorHistoryParent.
func CensorHistoryParentNames() []string {
	tmp := make([]string, len(_CensorHistoryParentNames))
	copy(tmp, _CensorHistoryParentNames)
	return tmp
}

// CensorHistoryParentValues returns a list of the values for CensorHistoryParent
func CensorHistoryParentValues() []CensorHistoryParent {
	return []CensorHistoryParent{
		CensorHistoryParentDrill,
		CensorHistoryParentCourse,
	}
}

// String implements the Stringer interface.
func (x CensorHistoryParent) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x CensorHistoryParent) IsValid() bool {
	_, err := ParseCensorHistoryParent(string(x))
	return err == nil
}

var _CensorHistoryParentValue = map[string]CensorHistoryParent{
	"Drill":  CensorHistoryParentDrill,
	"Course": CensorHistoryParentCourse,
}

// ParseCensorHistoryParent attempts to convert a string to a CensorHistoryParent.
func ParseCensorHistoryParent(name string) (CensorHistoryParent, error) {
	if x, ok := _CensorHistoryParentValue[name]; ok {
		return x, nil
	}
	return CensorHistoryParent(""), fmt.Errorf("%s is %w", name, ErrInvalidCensorHistoryParent)
}

// MarshalText implements the text marshaller method.
func (x CensorHistoryParent) MarshalText() ([]byte, error) {
	return []byte(string(x)), nil
}

// UnmarshalText implements the text unmarshaller method.
func (x *CensorHistoryParent) UnmarshalText(text []byte) error {
	tmp, err := ParseCensorHistoryParent(string(text))
	if err != nil {
		return err
	}
	*x = tmp
	return nil
}
