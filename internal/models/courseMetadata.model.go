package models

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
)

type CourseUserMetadata struct {
	CompletedSectionCount     *int32
	CompletedSectionItemCount *int32
	LastAccessSectionSlug     *string
	IsReviewTeacherInCourse   *bool
}

func (CourseUserMetadata) ImplementsGraphQLType(name string) bool {
	return name == "CourseUserMetadata"
}

func (cm *CourseUserMetadata) UnmarshalGraphQL(input interface{}) error {
	switch input := input.(type) {
	case map[string]interface{}:
		data, err := json.Marshal(input)
		if err != nil {
			return err
		}
		return json.Unmarshal(data, cm)
	case string:
		return json.Unmarshal([]byte(input), cm)
	case nil:
		*cm = CourseUserMetadata{}
		return nil
	default:
		return fmt.Errorf("invalid CourseUserMetadata input type: %T", input)
	}
}

func (cp *CourseUserMetadata) MarshalJSON() ([]byte, error) {
	if cp == nil {
		return []byte("null"), nil
	}
	return json.Marshal(*cp)
}

func (cp *CourseUserMetadata) Scan(value interface{}) error {
	if value == nil {
		*cp = CourseUserMetadata{}
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("failed to unmarshal CourseUserMetadata value: %v", value)
	}

	return json.Unmarshal(bytes, cp)
}

func (cp CourseUserMetadata) Value() (driver.Value, error) {
	if cp == (CourseUserMetadata{}) {
		return nil, nil
	}
	return json.Marshal(cp)
}
