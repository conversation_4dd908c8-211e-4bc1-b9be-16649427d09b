package models

import (
	"time"
	"vibico-education-api/internal/enums"
)

type VideoPlatform struct {
	ID              uint32                    `gorm:"primaryKey"`
	VideoID         uint32                    `gorm:"column:video_id;not null;index"`
	Platform        enums.VideoPlatform       `gorm:"type:smallint;not null"`
	PlatformVideoID string                    `gorm:"column:platform_video_id;type:varchar(255);not null"`
	URL             string                    `gorm:"column:url;type:varchar(255);not null"`
	Status          enums.VideoPlatformStatus `gorm:"type:smallint;not null;default:1"`
	SyncAt          *time.Time                `gorm:"type:timestamptz(6)"`
	CreatedAt       time.Time                 `gorm:"type:timestamptz(6);not null;default:current_timestamp"`
	UpdatedAt       time.Time                 `gorm:"type:timestamptz(6);not null;default:current_timestamp"`
	Video           *Video                    `gorm:"foreignKey:video_id"`
	TokenID         *uint32                   `gorm:"column:token_id"`
	Token           *YoutubeToken             `gorm:"foreignKey:id"`
}

func (platform *VideoPlatform) IsSyncing() bool {
	return platform.Status == enums.VideoPlatformStatusSyncing
}

func (platform *VideoPlatform) IsAvailable() bool {
	return platform.Status == enums.VideoPlatformStatusAvailable
}

func (platform *VideoPlatform) IsError() bool {
	return platform.Status == enums.VideoPlatformStatusError
}
