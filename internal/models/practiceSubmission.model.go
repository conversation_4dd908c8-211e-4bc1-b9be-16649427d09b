package models

import (
	"time"
	"vibico-education-api/internal/enums"
)

type PracticeSubmission struct {
	ID           uint32                               `gorm:"primaryKey"`
	PracticeID   uint32                               `gorm:"column:practice_id;not null"`
	PracticeType enums.PracticeSubmissionPracticeType `gorm:"column:practice_type;type:varchar(50);not null"`
	TeacherID    uint32                               `gorm:"column:teacher_id;not null"`
	UserID       uint32                               `gorm:"column:user_id;not null"`
	Content      *string                              `gorm:"column:content;type:text"`
	Status       enums.PracticeSubmissionStatus       `gorm:"column:status;type:smallint;not null;default:0"`
	CreatedAt    time.Time                            `gorm:"type:timestamptz(6);not null;default:current_timestamp"`
	UpdatedAt    time.Time                            `gorm:"type:timestamptz(6);not null;default:current_timestamp"`

	// Relationships
	User     *User       `gorm:"foreignKey:user_id"`
	Teacher  *Teacher    `gorm:"foreignKey:teacher_id"`
	Comments *[]*Comment `gorm:"polymorphic:Target;polymorphicValue:PracticeSubmission"`
	Videos   *[]*Video   `gorm:"polymorphic:Parent;polymorphicValue:PracticeSubmission"`
}

func (ps *PracticeSubmission) IsSubmitted() bool {
	return ps.Status == enums.PracticeSubmissionStatusSubmitted
}

func (ps *PracticeSubmission) IsNotSubmitted() bool {
	return !ps.IsSubmitted()
}
