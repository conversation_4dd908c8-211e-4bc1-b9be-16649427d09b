package models

import (
	"time"
	"vibico-education-api/internal/enums"

	"gorm.io/datatypes"
)

type UserCourseSectionItem struct {
	ID                  uint32                            `gorm:"primaryKey"`
	UserID              uint32                            `gorm:"not null;index:idx_ucsi_user_id"`
	CourseSectionItemId uint32                            `gorm:"not null;index:idx_ucsi_course_section_item_id"`
	Status              enums.UserCourseSectionItemStatus `gorm:"type:smallint;default:0;not null"`
	Data                *datatypes.JSON
	CreatedAt           time.Time `gorm:"type:timestamptz(6);not null;default:current_timestamp"`
	UpdatedAt           time.Time `gorm:"type:timestamptz(6);not null;default:current_timestamp"`

	CourseSectionItem *CourseSectionItem
}

func (ucsi *UserCourseSectionItem) NewRecord() bool {
	return ucsi.ID == 0
}

func (ucsi *UserCourseSectionItem) IsStudying() bool {
	return ucsi.Status == enums.UserCourseSectionItemStatusStudying
}

func (ucsi *UserCourseSectionItem) IsCompleted() bool {
	return ucsi.Status == enums.UserCourseSectionItemStatusCompleted
}
