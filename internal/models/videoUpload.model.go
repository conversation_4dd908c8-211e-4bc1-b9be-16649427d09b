package models

import (
	"time"
	"vibico-education-api/internal/enums"
)

type VideoUpload struct {
	ID           uint32                  `gorm:"primaryKey"`
	VideoID      uint32                  `gorm:"column:video_id;not null;index"`
	Filename     string                  `gorm:"column:filename;type:varchar(255);not null"`
	Status       enums.VideoUploadStatus `gorm:"column:status;type:integer;default:1"`
	Progress     float64                 `gorm:"column:progress;type:float;default:0"`
	ErrorMessage *string                 `gorm:"column:error_message;type:text"`
	StartedAt    *time.Time              `gorm:"column:started_at;type:timestamp"`
	CompletedAt  *time.Time              `gorm:"column:completed_at;type:timestamp"`
	CreatedAt    time.Time               `gorm:"column:created_at;type:timestamp;default:CURRENT_TIMESTAMP"`
	UpdatedAt    time.Time               `gorm:"column:updated_at;type:timestamp;default:CURRENT_TIMESTAMP"`
	Video        Video                   `gorm:"foreignKey:VideoID"`
}

func (upload *VideoUpload) IsUploading() bool {
	return upload.Status == enums.VideoUploadStatusUploading
}

func (upload *VideoUpload) IsProcessing() bool {
	return upload.Status == enums.VideoUploadStatusProcessing
}

func (upload *VideoUpload) IsCompleted() bool {
	return upload.Status == enums.VideoUploadStatusCompleted
}

func (upload *VideoUpload) IsFailed() bool {
	return upload.Status == enums.VideoUploadStatusFailed
}
