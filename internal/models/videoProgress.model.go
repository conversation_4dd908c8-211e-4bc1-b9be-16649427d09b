package models

import (
	"time"
)

type VideoProgress struct {
	ID           uint32     `gorm:"primaryKey"`
	VideoID      uint32     `gorm:"column:video_id;not null;index"`
	UserID       uint32     `gorm:"column:user_id;not null;index"`
	LastPosition int32      `gorm:"type:integer;not null;default:0"`
	Completed    bool       `gorm:"type:boolean;not null;default:false"`
	CompletedAt  *time.Time `gorm:"type:timestamptz(6)"`
	CreatedAt    time.Time  `gorm:"type:timestamptz(6);not null;default:current_timestamp"`
	UpdatedAt    time.Time  `gorm:"type:timestamptz(6);not null;default:current_timestamp"`
	Video        *Video     `gorm:"foreignKey:video_id"`
	User         *User      `gorm:"foreignKey:user_id"`
}
