package models

import (
	"encoding/json"
	"time"
	"vibico-education-api/internal/enums"

	"gorm.io/datatypes"
)

type NotificationContent struct {
	Title string `json:"title"`
	Body  string `json:"body"`
}

type Notification struct {
	ID             uint32                       `gorm:"type:bigserial;primaryKey"`
	RecipientID    uint32                       `gorm:"type:bigint;not null"`
	RecipientType  enums.NotificationActorType  `gorm:"type:varchar(30);not null"`
	SenderID       *uint32                      `gorm:"type:bigint"`
	SenderType     *enums.NotificationActorType `gorm:"type:varchar(30)"`
	NoticeKind     enums.NoticeKind             `gorm:"type:varchar(30);not null"`
	NotifiableType enums.NotifiableType         `gorm:"type:varchar(30);not null"`
	NotifiableID   uint32                       `gorm:"type:bigint;not null"`
	IsRead         bool                         `gorm:"default:false;not null"`
	ReadAt         *time.Time                   `gorm:"type:timestamptz"`
	Content        datatypes.JSON
	CreatedAt      time.Time `gorm:"type:timestamptz;not null;default:CURRENT_TIMESTAMP"`
	UpdatedAt      time.Time `gorm:"type:timestamptz;not null;default:CURRENT_TIMESTAMP"`

	RecipientUser    *User    `gorm:"foreignKey:RecipientID;references:ID"`
	RecipientTeacher *Teacher `gorm:"foreignKey:RecipientID;references:ID"`
	RecipientAdmin   *Admin   `gorm:"foreignKey:RecipientID;references:ID"`
	SenderUser       *User    `gorm:"foreignKey:SenderID;references:ID"`
	SenderTeacher    *Teacher `gorm:"foreignKey:SenderID;references:ID"`
	SenderAdmin      *Admin   `gorm:"foreignKey:SenderID;references:ID"`

	NotifiableCourse *Course `gorm:"foreignKey:NotifiableID;references:ID"`
}

func (n *Notification) GetContent() (NotificationContent, error) {
	var content NotificationContent
	if err := json.Unmarshal(n.Content, &content); err != nil {
		return NotificationContent{}, err
	}
	return content, nil
}

func (n *Notification) SetContent(content NotificationContent) error {
	data, err := json.Marshal(content)
	if err != nil {
		return err
	}
	n.Content = data
	return nil
}
