package models

import (
	"time"
)

type VideoView struct {
	ID                uint32    `gorm:"primaryKey"`
	VideoID           uint32    `gorm:"column:video_id;not null;index"`
	UserID            uint32    `gorm:"column:user_id;not null;index"`
	Platform          string    `gorm:"type:varchar(50);not null"`
	ViewDuration      int       `gorm:"type:integer;not null;default:0"`
	WatchedPercentage float64   `gorm:"type:float;not null;default:0"`
	IPAddress         string    `gorm:"column:ip_address;type:varchar(50)"`
	ViewedAt          time.Time `gorm:"type:timestamptz(6);not null"`
	CreatedAt         time.Time `gorm:"type:timestamptz(6);not null;default:current_timestamp"`
	Video             *Video    `gorm:"foreignKey:video_id"`
	User              *User     `gorm:"foreignKey:user_id"`
}
