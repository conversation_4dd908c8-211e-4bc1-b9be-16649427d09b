package models

import "time"

type Teacher struct {
	ID                uint32  `gorm:"primaryKey"`
	AuthId            string  `gorm:"type:varchar(255);not null;uniqueIndex"`
	Name              string  `gorm:"type:varchar(255);not null"`
	PhoneNumber       *string `gorm:"type:varchar(255);uniqueIndex"`
	ContactEmail      *string `gorm:"type:varchar(255);uniqueIndex"`
	Slug              string  `gorm:"type:varchar(255);not null;uniqueIndex"`
	Description       *string `gorm:"type:varchar(255)"`
	Award             *string `gorm:"type:varchar(255)"`
	Address           *string `gorm:"type:varchar(255)"`
	BasicEntered      bool    `gorm:"type:boolean;not null;default:false"`
	CanInviteStudents bool    `gorm:"type:boolean;not null;default:false"`
	Active            bool    `gorm:"type:boolean;not null;default:true"`
	ImageUrl          *string `gorm:"type:varchar(255)"`

	ApprovedCourseCount uint32 `gorm:"type:int;not null;default:0"`
	StudentCount        uint32 `gorm:"type:int;not null;default:0"`

	AverageRating *float32  `gorm:"type:decimal(2,1)"`
	CreatedAt     time.Time `gorm:"type:timestamptz(6);not null;default:current_timestamp"`
	UpdatedAt     time.Time `gorm:"type:timestamptz(6);not null;default:current_timestamp"`

	Courses                *[]*Course       `gorm:"foreignKey:teacher_id"`
	Drills                 *[]*Drill        `gorm:"polymorphic:Owner;polymorphicValue:Teacher"`
	Comments               *[]*Comment      `gorm:"polymorphic:Author;polymorphicValue:Teacher"`
	Ratings                *[]*Comment      `gorm:"polymorphic:Target;polymorphicValue:Teacher"`
	NotificationRecipients *[]*Notification `gorm:"polymorphic:Recipient;polymorphicValue:Teacher"`
	NotificationSenders    *[]*Notification `gorm:"polymorphic:Sender;polymorphicValue:Teacher"`
}
