package models

import (
	"time"
	"vibico-education-api/internal/enums"
)

type VideoStreamingSession struct {
	ID           uint32                            `gorm:"primaryKey"`
	VideoID      uint32                            `gorm:"column:video_id;not null;index"`
	UserID       uint32                            `gorm:"column:user_id;not null;index"`
	SessionToken string                            `gorm:"type:varchar(255);not null"`
	IPAddress    string                            `gorm:"column:ip_address;type:varchar(50)"`
	UserAgent    string                            `gorm:"column:user_agent;type:varchar(255)"`
	StartedAt    time.Time                         `gorm:"type:timestamptz(6);not null"`
	EndedAt      *time.Time                        `gorm:"type:timestamptz(6)"`
	Status       enums.VideoStreamingSessionStatus `gorm:"type:smallint;not null;default:1"`
	CreatedAt    time.Time                         `gorm:"type:timestamptz(6);not null;default:current_timestamp"`
	UpdatedAt    time.Time                         `gorm:"type:timestamptz(6);not null;default:current_timestamp"`
	Video        *Video                            `gorm:"foreignKey:video_id"`
	User         *User                             `gorm:"foreignKey:user_id"`
}

func (session *VideoStreamingSession) IsActive() bool {
	return session.Status == enums.VideoStreamingSessionStatusActive
}

func (session *VideoStreamingSession) IsEnded() bool {
	return session.Status == enums.VideoStreamingSessionStatusEnded
}

func (session *VideoStreamingSession) IsExpired() bool {
	return session.Status == enums.VideoStreamingSessionStatusExpired
}
