package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"time"
	"vibico-education-api/internal/enums"
)

type VideoOptions struct {
	Tags      []string `json:"tags,omitempty"`
	Keywords  []string `json:"keywords,omitempty"`
	Language  string   `json:"language,omitempty"`
	Copyright string   `json:"copyright,omitempty"`
}

// <PERSON>an implements the driver.Valuer interface
func (o *VideoOptions) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	v, ok := value.([]byte)
	if !ok {
		return errors.New("invalid scan source for VideoOptions")
	}
	return json.Unmarshal(v, o)
}

// Value implements the driver.Valuer interface
func (o VideoOptions) Value() (driver.Value, error) {
	return json.Marshal(o)
}

type Video struct {
	ID             uint32                `gorm:"primaryKey"`
	ParentID       uint32                `gorm:"index:idx_videos_parent;not null"`
	ParentType     enums.VideoParentType `gorm:"index:idx_videos_parent;type:varchar(50);not null"`
	UploaderID     uint32                `gorm:"column:uploader_id;not null;index"`
	UploaderType   string                `gorm:"column:uploader_type;type:varchar(50);not null"`
	Title          string                `gorm:"column:title;type:varchar(255);not null"`
	Description    *string               `gorm:"column:description;type:text"`
	Duration       int32                 `gorm:"column:duration"`
	ThumbnailURL   *string               `gorm:"column:thumbnail_url;type:varchar(255)"`
	IsFree         bool                  `gorm:"column:is_free;default:false"`
	Status         enums.VideoStatus     `gorm:"column:status;default:1"`
	CreatedAt      time.Time             `gorm:"column:created_at;type:timestamp;default:CURRENT_TIMESTAMP"`
	UpdatedAt      time.Time             `gorm:"column:updated_at;type:timestamp;default:CURRENT_TIMESTAMP"`
	Options        *VideoOptions         `gorm:"column:options;type:jsonb"`
	VideoPlatforms []*VideoPlatform      `gorm:"foreignKey:video_id"`
	VideoUpload    *VideoUpload          `gorm:"foreignKey:video_id"`
	VideoProgress  *VideoProgress        `gorm:"foreignKey:video_id"`
}

func (video *Video) IsDraft() bool {
	return video.Status == enums.VideoStatusDraft
}

func (video *Video) IsPending() bool {
	return video.Status == enums.VideoStatusPending
}

func (video *Video) IsApproved() bool {
	return video.Status == enums.VideoStatusApproved
}

func (video *Video) IsRejected() bool {
	return video.Status == enums.VideoStatusRejected
}

func (video *Video) IsPlayable() bool {
	if len(video.VideoPlatforms) == 0 {
		return false
	}

	for _, platform := range video.VideoPlatforms {
		if platform.IsAvailable() {

			if video.IsFree && platform.Platform == enums.VideoPlatformYoutube {
				return true
			}
			if !video.IsFree && platform.Platform == enums.VideoPlatformMux {
				return true
			}
		}
	}

	return false
}
