package models

import (
	"time"
	"vibico-education-api/internal/enums"

	"gorm.io/datatypes"
)

type Drill struct {
	ID          uint32  `gorm:"primaryKey"`
	Title       string  `gorm:"type:varchar(255);not null"`
	Slug        string  `gorm:"type:varchar(255);not null;index"`
	Description *string `gorm:"type:text"`

	DrillSkills            *[]*DrillSkill             `gorm:"foreignKey:drill_id"`
	UserDrills             *[]*UserDrill              `gorm:"foreignKey:drill_id"`
	Skills                 *[]*Skill                  `gorm:"many2many:drill_skills;"`
	Diagrams               *[]*Diagram                `gorm:"polymorphic:Parent;polymorphicValue:Drill"`
	CensorHistories        *[]*CensorHistory          `gorm:"polymorphic:Parent;polymorphicValue:Drill"`
	CourseSectionItemDrill *[]*CourseSectionItemDrill `gorm:"foreignKey:drill_id"`
	CourseSectionItems     *[]*CourseSectionItem      `gorm:"many2many:course_section_item_drills"`
	Videos                 *[]Video                   `gorm:"polymorphic:Parent;polymorphicValue:Drill"`

	OwnerID   uint32 `gorm:"index:idx_drills_owner;not null"`
	OwnerType string `gorm:"index:idx_drills_owner;type:varchar(50);not null"`

	Level  enums.DrillLevel  `gorm:"type:smallint;not null;default:1"`
	Status enums.DrillStatus `gorm:"type:smallint;not null;default:0"`
	Censor enums.DrillCensor `gorm:"type:smallint;not null;default:0"`

	IsMaster bool `gorm:"type:boolean;not null"`

	SalePrice *int32 `gorm:"type:int;"`
	Price     *int32 `gorm:"type:int;"`
	Step      *datatypes.JSON

	CreatedAt *time.Time `gorm:"type:timestamptz(6);not null;default:current_timestamp"`
	UpdatedAt *time.Time `gorm:"type:timestamptz(6);not null;default:current_timestamp"`
}

func (d *Drill) StoreSkill(skillIds []uint32) *[]*DrillSkill {
	drillSkills := make([]*DrillSkill, 0)

	for _, skillId := range skillIds {
		drillSkills = append(drillSkills, &DrillSkill{
			SkillId: skillId,
			DrillId: d.ID,
		})
	}

	return &drillSkills
}

func (drill *Drill) IsDraft() bool {
	return drill.Censor == enums.DrillCensorDraft
}

func (drill *Drill) IsApproved() bool {
	return drill.Censor == enums.DrillCensorApproved
}

func (drill *Drill) IsSubmitted() bool {
	return drill.Censor == enums.DrillCensorSubmitted
}

func (drill *Drill) IsFree() bool {
	if drill.SalePrice == nil {
		return drill.Price == nil || *drill.Price == 0
	}
	return *drill.SalePrice == 0
}

func (drill *Drill) IsBoughtByUser(userId uint32) bool {
	if drill.UserDrills == nil || len(*drill.UserDrills) == 0 {
		return false
	}

	for _, ud := range *drill.UserDrills {
		if ud.UserId == userId {
			return true
		}
	}

	return false
}
