package models

import (
	"time"

	"gorm.io/datatypes"
)

type UserCourseSection struct {
	ID              uint32 `gorm:"primaryKey"`
	UserID          uint32 `gorm:"not null;index:idx_ucs_user_id"`
	CourseSectionId uint32 `gorm:"not null;index:idx_ucs_course_section_id"`
	CourseSection   *CourseSection
	Data            *datatypes.JSON
	CreatedAt       time.Time `gorm:"type:timestamptz(6);not null;default:current_timestamp"`
	UpdatedAt       time.Time `gorm:"type:timestamptz(6);not null;default:current_timestamp"`
}
