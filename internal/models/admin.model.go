package models

import "time"

type Admin struct {
	ID        uint32    `gorm:"primaryKey"`
	AuthId    string    `gorm:"type:varchar(255);not null;uniqueIndex"`
	Name      string    `gorm:"type:varchar(255);not null"`
	Drills    *[]*Drill `gorm:"polymorphic:Owner;polymorphicValue:Admin"`
	CreatedAt time.Time `gorm:"type:timestamptz(6);not null;default:current_timestamp"`
	UpdatedAt time.Time `gorm:"type:timestamptz(6);not null;default:current_timestamp"`

	NotificationRecipients *[]*Notification `gorm:"polymorphic:Recipient;polymorphicValue:Admin"`
	NotificationSenders    *[]*Notification `gorm:"polymorphic:Sender;polymorphicValue:Admin"`
}
