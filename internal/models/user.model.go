package models

import "time"

type User struct {
	ID          uint32     `gorm:"primaryKey"`
	AuthId      string     `gorm:"type:varchar(255);not null;uniqueIndex"`
	Name        string     `gorm:"type:varchar(255);not null"`
	PhoneNumber *string    `gorm:"type:varchar(255);uniqueIndex"`
	Gender      *string    `gorm:"type:varchar(255)"`
	BirthDate   *time.Time `gorm:"type:date"`
	ImageUrl    *string    `gorm:"type:varchar(255)"`
	Active      bool       `gorm:"type:boolean;not null;default:true"`
	CreatedAt   time.Time  `gorm:"type:timestamptz(6);not null;default:current_timestamp"`
	UpdatedAt   time.Time  `gorm:"type:timestamptz(6);not null;default:current_timestamp"`

	UserCourseSection      *[]*UserCourseSection     `gorm:"foreignKey:user_id"`
	UserCourseSectionItems *[]*UserCourseSectionItem `gorm:"foreignKey:user_id"`
	Comments               *[]*Comment               `gorm:"polymorphic:Author;polymorphicValue:User"`
	PracticeSubmissions    *[]*PracticeSubmission    `gorm:"foreignKey:user_id"`
	CourseUsers            *[]*CourseUser            `gorm:"foreignKey:user_id"`
	NotificationRecipients *[]*Notification          `gorm:"polymorphic:Recipient;polymorphicValue:User"`
	NotificationSenders    *[]*Notification          `gorm:"polymorphic:Sender;polymorphicValue:User"`
}
