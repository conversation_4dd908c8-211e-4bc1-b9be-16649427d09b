package models

import (
	"time"
	"vibico-education-api/internal/enums"
)

type CensorHistory struct {
	ID         uint32 `gorm:"primaryKey"`
	ParentID   uint32 `gorm:"index:idx_censor_histories_parent;not null"`
	ParentType string `gorm:"index:idx_censor_histories_parent;type:varchar(50);not null"`
	Creator    *Admin `gorm:"foreignKey:created_by"`
	CreatedBy  uint32 `gorm:"column:created_by;not null"`

	Feedback  string             `gorm:"type:text;not null"`
	Status    enums.CensorStatus `gorm:"type:smallint;default:0"`
	CreatedAt time.Time          `gorm:"type:timestamptz(6);not null;default:current_timestamp"`
	UpdatedAt time.Time          `gorm:"type:timestamptz(6);not null;default:current_timestamp"`
}
