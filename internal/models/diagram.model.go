package models

import (
	"time"

	"gorm.io/datatypes"
)

type Diagram struct {
	ID         uint32 `gorm:"primaryKey"`
	ParentID   uint32 `gorm:"index:idx_diagrams_parent;not null"`
	ParentType string `gorm:"index:idx_diagrams_parent;type:varchar(50);not null"`
	ImageUrl   *string
	Setting    *datatypes.JSON
	Position   uint32
	CreatedAt  time.Time `gorm:"type:timestamptz(6);not null;default:current_timestamp"`
	UpdatedAt  time.Time `gorm:"type:timestamptz(6);not null;default:current_timestamp"`
}
