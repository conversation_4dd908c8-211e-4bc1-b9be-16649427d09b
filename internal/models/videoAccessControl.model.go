package models

import (
	"time"
	"vibico-education-api/internal/enums"
)

type VideoAccessControl struct {
	ID            uint32                `gorm:"primaryKey"`
	VideoID       uint32                `gorm:"column:video_id;not null;index"`
	PlanID        *uint32               `gorm:"column:plan_id;index"`
	AccessType    enums.VideoAccessType `gorm:"type:smallint;not null;default:1"`
	StreamingOnly bool                  `gorm:"type:boolean;not null;default:false"`
	CreatedAt     time.Time             `gorm:"type:timestamptz(6);not null;default:current_timestamp"`
	UpdatedAt     time.Time             `gorm:"type:timestamptz(6);not null;default:current_timestamp"`
	Video         *Video                `gorm:"foreignKey:video_id"`
}

func (control *VideoAccessControl) IsFree() bool {
	return control.AccessType == enums.VideoAccessTypeFree
}

func (control *VideoAccessControl) IsPremium() bool {
	return control.AccessType == enums.VideoAccessTypePremium
}

func (control *VideoAccessControl) IsSpecificPlan() bool {
	return control.AccessType == enums.VideoAccessTypeSpecificPlan
}
