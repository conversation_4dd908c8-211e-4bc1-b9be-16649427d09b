package models

import (
	"time"
	"vibico-education-api/internal/enums"
)

type VideoModeration struct {
	ID          uint32                      `gorm:"primaryKey"`
	VideoID     uint32                      `gorm:"column:video_id;not null;index"`
	ModeratorID uint32                      `gorm:"column:moderator_id;not null;index"`
	Status      enums.VideoModerationStatus `gorm:"type:smallint;not null;default:1"`
	Comment     *string                     `gorm:"type:text"`
	ModeratedAt *time.Time                  `gorm:"type:timestamptz(6)"`
	CreatedAt   time.Time                   `gorm:"type:timestamptz(6);not null;default:current_timestamp"`
	UpdatedAt   time.Time                   `gorm:"type:timestamptz(6);not null;default:current_timestamp"`
	Video       *Video                      `gorm:"foreignKey:video_id"`
}

func (moderation *VideoModeration) IsPending() bool {
	return moderation.Status == enums.VideoModerationStatusPending
}

func (moderation *VideoModeration) IsApproved() bool {
	return moderation.Status == enums.VideoModerationStatusApproved
}

func (moderation *VideoModeration) IsRejected() bool {
	return moderation.Status == enums.VideoModerationStatusRejected
}
