package models

import (
	"time"
	"vibico-education-api/internal/enums"
)

type CourseUser struct {
	ID              uint32                 `gorm:"primaryKey"`
	UserID          uint32                 `gorm:"not null;index:idx_course_users_user_id"`
	CourseID        uint32                 `gorm:"not null;index:idx_course_users_course_id"`
	CoursePackageID *uint32                `gorm:"not null;index:idx_course_users_course_package_id"`
	Status          enums.CourseUserStatus `gorm:"type:smallint;default:1"`

	CourseUserMetadata *CourseUserMetadata
	User               *User
	Course             *Course

	JoinedAt  *time.Time `gorm:"type:timestamptz(6);"`
	CreatedAt time.Time  `gorm:"type:timestamptz(6);not null;default:current_timestamp"`
	UpdatedAt time.Time  `gorm:"type:timestamptz(6);not null;default:current_timestamp"`
}

func (cu *CourseUser) IsInvited() bool {
	return cu.Status == enums.CourseUserStatusInvited
}

func (cu *CourseUser) IsEnrolled() bool {
	return cu.Status == enums.CourseUserStatusEnrolled
}

func (cu *CourseUser) IsInProgress() bool {
	return cu.Status == enums.CourseUserStatusInProgress
}

func (cu *CourseUser) IsCompleted() bool {
	return cu.Status == enums.CourseUserStatusCompleted
}
