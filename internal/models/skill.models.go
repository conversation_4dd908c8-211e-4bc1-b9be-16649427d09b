package models

import "time"

type Skill struct {
	ID          uint32         `gorm:"primaryKey"`
	Name        string         `gorm:"type:varchar(255);not null"`
	Description *string        `gorm:"type:text"`
	DrillSkills *[]*DrillSkill `gorm:"foreignKey:skill_id"`
	Drills      *[]*Drill      `gorm:"many2many:drill_skills;"`
	CreatedAt   *time.Time     `gorm:"type:timestamptz(6);not null;default:current_timestamp"`
	UpdatedAt   *time.Time     `gorm:"type:timestamptz(6);not null;default:current_timestamp"`
}
