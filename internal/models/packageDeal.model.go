package models

import "time"

type PackageDeal struct {
	ID uint32 `gorm:"primaryKey"`

	Name        string `gorm:"type:varchar(255);not null"`
	Description string `gorm:"type:text"`

	ApprovalSubmissionRequired bool `gorm:"type:boolean;not null"`
	Active                     bool `gorm:"type:boolean;not null"`

	CreatedAt *time.Time `gorm:"type:timestamptz(6);not null;default:current_timestamp"`
	UpdatedAt *time.Time `gorm:"type:timestamptz(6);not null;default:current_timestamp"`
}
