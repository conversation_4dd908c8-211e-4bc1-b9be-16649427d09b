package models

import (
	"time"
)

type VideoVersion struct {
	ID               uint32    `gorm:"primaryKey"`
	VideoID          uint32    `gorm:"column:video_id;not null;index"`
	Quality          string    `gorm:"type:varchar(20);not null"`
	Resolution       string    `gorm:"type:varchar(20);not null"`
	FileSize         int64     `gorm:"type:bigint"`
	FileFormat       string    `gorm:"type:varchar(10);not null"`
	S3Path           string    `gorm:"column:s3_path;type:varchar(255);not null"`
	StreamingProfile string    `gorm:"type:varchar(20)"`
	ManifestURL      string    `gorm:"column:manifest_url;type:varchar(255)"`
	DRMKeyID         *string   `gorm:"column:drm_key_id;type:uuid"`
	CacheStatus      int       `gorm:"type:int;not null;default:1"`
	StorageClass     string    `gorm:"type:varchar(20);not null;default:'STANDARD'"`
	AccessTier       int       `gorm:"type:int;not null;default:1"`
	CreatedAt        time.Time `gorm:"type:timestamptz(6);not null;default:current_timestamp"`
	UpdatedAt        time.Time `gorm:"type:timestamptz(6);not null;default:current_timestamp"`
	Video            *Video    `gorm:"foreignKey:video_id"`
}
