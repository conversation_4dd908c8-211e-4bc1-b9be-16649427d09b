package models

import "time"

type CoursePackage struct {
	ID uint32 `gorm:"primaryKey"`

	SalePrice *int32 `gorm:"type:int"`
	Price     *int32 `gorm:"type:int"`

	CourseID      uint32 `gorm:"not null;index:idx_course_packages_course_id"`
	PackageDealID uint32 `gorm:"not null;index:idx_course_packages_package_deal_id "`

	Course      *Course      `gorm:"foreignKey:course_id;"`
	PackageDeal *PackageDeal `gorm:"foreignKey:package_deal_id;"`

	CreatedAt *time.Time `gorm:"type:timestamptz(6);not null;default:current_timestamp"`
	UpdatedAt *time.Time `gorm:"type:timestamptz(6);not null;default:current_timestamp"`
}
