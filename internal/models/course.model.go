package models

import (
	"time"
	"vibico-education-api/internal/enums"
)

type Course struct {
	ID                 uint32                         `gorm:"primaryKey"`
	TeacherId          uint32                         `gorm:"index;not null"`
	Title              string                         `gorm:"type:varchar(255);not null"`
	Slug               string                         `gorm:"type:varchar(255);not null;index"`
	Description        *string                        `gorm:"type:text"`
	Banner             *string                        `gorm:"type:varchar(255)"`
	Status             enums.CourseStatus             `gorm:"type:smallint;not null;default:0"`
	InstructionalLevel enums.CourseInstructionalLevel `gorm:"type:smallint;not null;default:0"`
	IsPublic           bool                           `gorm:"type:boolean;not null;default:false"`
	IsSettingPackage   bool                           `gorm:"type:boolean;not null;default:false"`
	SalePrice          *int32                         `gorm:"type:int"`
	Price              *int32                         `gorm:"type:int"`
	BonusPoint         *int32                         `gorm:"type:int"`
	BonusPointPercent  *int32                         `gorm:"type:int"`
	SectionCount       uint32                         `gorm:"type:int;not null;default:0"`
	SectionItemCount   uint32                         `gorm:"type:int;not null;default:0"`
	JoinedUserCount    uint32                         `gorm:"type:int;not null;default:0"`
	CreatedAt          time.Time                      `gorm:"type:timestamptz(6);not null;default:current_timestamp"`
	UpdatedAt          time.Time                      `gorm:"type:timestamptz(6);not null;default:current_timestamp"`

	Teacher               *Teacher          `gorm:"foreignKey:teacher_id"`
	CourseUsers           []*CourseUser     `gorm:"foreignKey:course_id"`
	Users                 *[]*User          `gorm:"many2many:course_users;"`
	CourseSections        *[]*CourseSection `gorm:"foreignKey:course_id"`
	CourseCensorHistories *[]*CensorHistory `gorm:"polymorphic:Parent;polymorphicValue:Course"`
	Comments              *[]*Comment       `gorm:"polymorphic:Target;polymorphicValue:Course"`
	CoursePackages        *[]*CoursePackage `gorm:"foreignKey:course_id;"`

	AverageRating *float32 `gorm:"->"`
}

func (course *Course) IsSubmitted() bool {
	return course.Status == enums.CourseStatusSubmitted
}

func (course *Course) IsDraft() bool {
	return course.Status == enums.CourseStatusDraft
}

func (course *Course) IsApproved() bool {
	return course.Status == enums.CourseStatusApproved
}

func (course *Course) GetUserComment(userID uint32) *Comment {
	if course.Comments == nil || len(*course.Comments) == 0 {
		return nil
	}

	for _, c := range *course.Comments {
		if c.AuthorID == userID && c.AuthorType == "User" {
			return c
		}
	}

	return nil
}

func (course *Course) GetCourseUser(userID uint32) *CourseUser {
	if len(course.CourseUsers) == 0 {
		return nil
	}

	for _, cu := range course.CourseUsers {
		if cu.UserID == userID {
			return cu
		}
	}

	return nil
}
