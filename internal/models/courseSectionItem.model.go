package models

import (
	"time"
	"vibico-education-api/internal/enums"
)

type SectionItemUsersStats struct {
	TotalEnrolledUsers      uint32
	TotalSubmittedUsers     uint32
	TotalViewedSectionUsers uint32
}

type CourseSectionItem struct {
	ID                      uint32 `gorm:"primaryKey"`
	CourseSectionId         uint32 `gorm:"index;not null"`
	CourseId                uint32 `gorm:"index;not null"`
	CourseSection           *CourseSection
	Diagrams                *[]*Diagram                `gorm:"polymorphic:Parent;polymorphicValue:CourseSectionItem"`
	CourseSectionItemDrills *[]*CourseSectionItemDrill `gorm:"foreignKey:course_section_item_id"`
	Drills                  *[]*Drill                  `gorm:"many2many:course_section_item_drills"`
	UserCourseSectionItems  *[]*UserCourseSectionItem  `gorm:"foreignKey:course_section_item_id"`
	Videos                  *[]*Video                  `gorm:"polymorphic:Parent;polymorphicValue:CourseSectionItem"`
	PracticeSubmissions     *[]*PracticeSubmission     `gorm:"polymorphic:Practice;polymorphicValue:CourseSectionItem"`

	Title     string                      `gorm:"type:varchar(255);not null"`
	Slug      string                      `gorm:"type:varchar(255);not null;index"`
	Content   *string                     `gorm:"type:text"`
	Type      enums.CourseSectionItemType `gorm:"type:smallint;default:0"`
	Position  uint32                      `gorm:"type:integer;not null;default:0"`
	CreatedAt time.Time                   `gorm:"type:timestamptz(6);not null;default:current_timestamp"`
	UpdatedAt time.Time                   `gorm:"type:timestamptz(6);not null;default:current_timestamp"`
}

func (csi *CourseSectionItem) GetUserSectionItemByID(userID uint32) *UserCourseSectionItem {
	if csi.UserCourseSectionItems == nil || len(*csi.UserCourseSectionItems) == 0 {
		return nil
	}

	for _, ucsi := range *csi.UserCourseSectionItems {
		if ucsi.UserID == userID {
			return ucsi
		}
	}

	return nil
}
