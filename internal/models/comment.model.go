package models

import (
	"time"
	"vibico-education-api/internal/enums"
)

type ReviewStats struct {
	Star1         int
	Star2         int
	Star3         int
	Star4         int
	Star5         int
	AverageRating float64
	Total         int
}

type StarCount struct {
	Rating int
	Count  int
}

type Comment struct {
	ID         uint32                  `gorm:"primaryKey"`
	ParentID   *uint32                 `gorm:"column:parent_id"`
	AuthorID   uint32                  `gorm:"column:author_id"`
	AuthorType string                  `gorm:"column:author_type"`
	TargetID   uint32                  `gorm:"column:target_id"`
	TargetType enums.CommentTargetType `gorm:"column:target_type"`
	Content    string                  `gorm:"column:content;type:text"`
	Rating     *float32                `gorm:"column:rating;type:decimal(2,1)"`
	CreatedAt  time.Time               `gorm:"type:timestamptz(6);not null;default:current_timestamp"`
	UpdatedAt  time.Time               `gorm:"type:timestamptz(6);not null;default:current_timestamp"`

	// Relationships
	Parent  *Comment    `gorm:"foreignKey:parent_id"`
	Replies *[]*Comment `gorm:"foreignKey:parent_id"`
	// TODO: find a way to preload based on AuthorType
	AuthorTeacher *Teacher `gorm:"foreignKey:ID;references:AuthorID"`
	AuthorUser    *User    `gorm:"foreignKey:ID;references:AuthorID"`
}

func (r *Comment) IsValidTargetTypeTeacher() bool {
	return r.TargetType == enums.CommentTargetTypeTeacher
}
