package models

import (
	"time"
)

type CourseSection struct {
	ID                 uint32 `gorm:"primaryKey"`
	CourseID           uint32 `gorm:"index;not null"`
	Course             *Course
	UserCourseSection  *UserCourseSection `gorm:"foreignKey:course_section_id"`
	Slug               string             `gorm:"type:varchar(255);not null"`
	Position           int32
	Title              string                `gorm:"type:varchar(255);not null"`
	CourseSectionItems *[]*CourseSectionItem `gorm:"foreignKey:course_section_id"`
	CreatedAt          time.Time             `gorm:"type:timestamptz(6);not null;default:current_timestamp"`
	UpdatedAt          time.Time             `gorm:"type:timestamptz(6);not null;default:current_timestamp"`
}
