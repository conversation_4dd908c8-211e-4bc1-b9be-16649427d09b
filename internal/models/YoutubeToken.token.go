package models

import (
	"time"
)

type YoutubeToken struct {
	ID             uint32          `gorm:"primaryKey"`
	Token          string          `gorm:"not null"`
	CreatedAt      time.Time       `gorm:"type:timestamptz(6);not null;default:current_timestamp"`
	UpdatedAt      time.Time       `gorm:"type:timestamptz(6);not null;default:current_timestamp"`
	VideoPlatforms []VideoPlatform `gorm:"foreignKey:token_id"` // slice instead of pointer
}
