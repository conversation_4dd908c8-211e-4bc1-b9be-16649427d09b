package models

import "time"

type CourseSectionItemDrill struct {
	ID                  uint32             `gorm:"primaryKey"`
	CourseSectionItemId uint32             `gorm:"index;not null"`
	DrillId             uint32             `gorm:"index;not null"`
	CourseSectionItem   *CourseSectionItem `gorm:"constraint:OnDelete:CASCADE;"`
	Drill               *Drill

	CreatedAt time.Time `gorm:"type:timestamptz(6);not null;default:current_timestamp"`
	UpdatedAt time.Time `gorm:"type:timestamptz(6);not null;default:current_timestamp"`
}
