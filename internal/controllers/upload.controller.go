package controllers

import (
	"errors"
	"net/http"
	"vibico-education-api/internal/repository"
	"vibico-education-api/pkg/helpers"
	"vibico-education-api/pkg/uploaders"

	"github.com/BehemothLtd/behemoth-pkg/golang/exceptions"
	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
)

func HandleUpload(c *gin.Context) {
	log.Info().Ctx(c).Msg("Controller: Handle Upload")
	uploader := createUploader(c)

	uploaded, err := uploader.Upload()

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"errors": handleErrors(err),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": uploaded,
	})
}

func HandleDrillUpload(c *gin.Context) {
	log.Info().Ctx(c).Msg("Controller: Handle Drill Upload")
	uploader := createUploader(c)

	replaced, err := uploader.DrillUpload()

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"errors": handleErrors(err),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": replaced,
	})
}

func HandleChunksUpload(c *gin.Context, repos repository.IRepositories) {

	log.Info().Ctx(c).Msg("Controller: Handle Video Upload")
	uploader := createUploader(c)

	chunk, err := c.FormFile("chunk")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"errors": handleErrors(err),
		})
		return
	}

	uploaded, err := uploader.UploadChunkFile(chunk, repos)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"errors": handleErrors(err),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": uploaded,
	})
}

func HandleFinishUpload(c *gin.Context, repos repository.IRepositories) {
	log.Info().Ctx(c).Msg("Controller: Handle Finish Upload")

	var payload struct {
		FileId     string `json:"fileId"`
		ParentType string `json:"parentType"`
		ParentId   int    `json:"parentId"`
	}

	if err := c.ShouldBindJSON(&payload); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"errors": "invalid request payload",
		})
		return
	}

	if payload.FileId == "" || payload.ParentType == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"errors": "please provide fileId, parentType and parentId in the request payload",
		})
		return
	}

	redisClient := helpers.GetRedisClient()

	chunkUploader, err := uploaders.NewChunkUploader(c, redisClient, repos)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"errors": handleErrors(err),
		})
		return
	}

	videoID, err := chunkUploader.FinishUpload(payload.FileId, payload.ParentType, payload.ParentId)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"errors": handleErrors(err),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": map[string]any{
			"videoID": videoID,
		},
	})
}

func createUploader(c *gin.Context) *uploaders.Uploader {
	return &uploaders.Uploader{
		Ctx:            c,
		StorageService: utils.GetEnv("STORAGE_SERVICE", "local"),
		UploadPath:     "", // Update with your desired upload path
	}
}

func handleErrors(err error) []map[string]interface{} {
	errorList := []map[string]interface{}{
		{"error": err.Error(),
			"message": err.Error(),
		},
	}

	var customErr exceptions.BadRequestError
	if errors.As(err, &customErr) {
		errorList[0]["message"] = customErr.Message
	}

	return errorList
}
