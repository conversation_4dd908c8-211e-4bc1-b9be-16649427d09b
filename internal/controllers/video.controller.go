package controllers

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"
	"vibico-education-api/internal/repository"
	"vibico-education-api/internal/services/mux"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
)

// HandleVideoStream proxies HLS streaming from Mux for better security
func HandleVideoStream(c *gin.Context, repos repository.IRepositories) {
	assetID := c.Param("playbackId")
	path := c.Param("path")

	// Strip .m3u8 extension if present
	if strings.HasSuffix(assetID, ".m3u8") {
		assetID = strings.TrimSuffix(assetID, ".m3u8")
	}

	if assetID == "" {
		c.JSO<PERSON>(http.StatusBadRequest, gin.H{
			"error": "asset ID is required",
		})
		return
	}

	// Get the actual playback ID from the asset
	muxService, err := mux.NewVideoService(context.Background())
	if err != nil {
		log.Error().Err(err).Msg("Failed to create Mux service")
		c.<PERSON>(http.StatusInternalServerError, gin.H{
			"error": "failed to initialize video service",
		})
		return
	}

	// Get asset details to find the playback ID
	asset, err := muxService.GetAsset(assetID)
	if err != nil {
		log.Error().Err(err).Str("assetID", assetID).Msg("Failed to get asset")
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "invalid asset ID",
		})
		return
	}

	// Find the public playback ID
	var playbackID string
	for _, pid := range asset.PlaybackIDs {
		if pid.Policy == "public" {
			playbackID = pid.ID
			break
		}
	}

	if playbackID == "" {
		log.Error().Str("assetID", assetID).Msg("No public playback ID found")
		c.JSON(http.StatusNotFound, gin.H{
			"error": "no playback ID available",
		})
		return
	}

	// Construct the Mux URL with the correct playback ID
	muxURL := fmt.Sprintf("https://stream.mux.com/%s.m3u8", playbackID)
	if path != "" {
		muxURL = fmt.Sprintf("https://stream.mux.com/%s/%s", playbackID, path)
	}

	// Create HTTP client with timeout
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	req, err := http.NewRequestWithContext(c.Request.Context(), "GET", muxURL, nil)
	if err != nil {
		log.Error().Err(err).Str("url", muxURL).Msg("Failed to create request")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "failed to create request",
		})
		return
	}

	// Forward relevant headers
	if userAgent := c.GetHeader("User-Agent"); userAgent != "" {
		req.Header.Set("User-Agent", userAgent)
	}
	if acceptEncoding := c.GetHeader("Accept-Encoding"); acceptEncoding != "" {
		req.Header.Set("Accept-Encoding", acceptEncoding)
	}
	if rangeHeader := c.GetHeader("Range"); rangeHeader != "" {
		req.Header.Set("Range", rangeHeader)
	}
	// Forward referer for better compatibility
	if referer := c.GetHeader("Referer"); referer != "" {
		req.Header.Set("Referer", referer)
	}

	// Make request to Mux
	resp, err := client.Do(req)
	if err != nil {
		log.Error().Err(err).Str("url", muxURL).Msg("Failed to fetch from Mux")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "failed to fetch video stream",
		})
		return
	}
	defer resp.Body.Close()

	c.Header("Access-Control-Allow-Origin", "*")
	c.Header("Access-Control-Allow-Methods", "GET, OPTIONS")
	c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization, Range, Education-Authorization")
	c.Header("Access-Control-Expose-Headers", "Content-Length, Content-Range, Accept-Ranges")

	for key, values := range resp.Header {
		if strings.HasPrefix(strings.ToLower(key), "access-control-") {
			continue
		}
		for _, value := range values {
			c.Header(key, value)
		}
	}

	if strings.Contains(path, ".ts") {
		c.Header("Cache-Control", "public, max-age=31536000")
	} else if strings.Contains(path, ".m3u8") {
		c.Header("Cache-Control", "no-cache, no-store, must-revalidate")
		c.Header("Pragma", "no-cache")
		c.Header("Expires", "0")
	}

	// Forward status code
	c.Status(resp.StatusCode)

	// Stream the response body
	_, err = io.Copy(c.Writer, resp.Body)
	if err != nil {
		log.Error().Err(err).Msg("Failed to stream response")
		return
	}
}
