# Config file for [Air](https://github.com/cosmtrek/air) in TOML format

# Working directory
# . or absolute path, please note that the directories following must be under root
root = "."
tmp_dir = "tmp_app"

[build]
# Just plain old shell command. You could use `make` as well.
cmd = "go build -o ./tmp_app/app/serve ./cmd/apiserver"
# Binary file yields from `cmd`.
bin = "tmp_app/app"
# Customize binary.
full_bin = "./tmp_app/app/serve"
# This log file places in your tmp_dir.
log = "air_errors.log"
# Watch these filename extensions.
include_ext = ["go", "yaml", "toml"]
# Exclude specific regular expressions.
exclude_regex = ["_test\\.go"]
# Ignore these filename extensions or directories.
exclude_dir = ["tmp_app", "tmp"]
# It's not necessary to trigger build each time file changes if it's too frequent.
delay = 1000 # ms

[log]
# Show log time
time = true

[color]
# Customize each part's color. If no color found, use the raw app log.
main = "magenta"
watcher = "cyan"
build = "yellow"
runner = "green"

[misc]
# Delete tmp directory on exit
clean_on_exit = true
