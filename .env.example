# APP CONFIG
APP_NAME=vibico-academy-api
APP_ENV=development
APP_DEBUG=true
APP_PORT=3000
LOGGER_LEVEL=debug
LOGGER_SQL_PARAMETERIZED_QUERIES=true
WEB_URL=http://localhost:8080
DOMAIN=localhost
CLIENT_LANGUAGE=vi

# DB
DB_HOST=localhost
DB_USER=postgres
DB_PASSWORD=postgres
DB_SCHEMA=vibico_academy

DB_NAME=billiard-community-dev
DB_PORT=5432
DB_SSL_MODE=disable
DB_MAX_OPEN_CONNS=20
DB_MAX_IDLE_CONNS=10
DB_CONN_MAX_LIFETIME_HOURS=1

ENCRYPTION_AES_SECRET_KEY=a123456789a123456789a123456789@@

AUTH_GRPC_SERVER_ADDR=localhost:50055
AUTH_POOL_ID=db1badb2-db60-4c4d-b53c-206cf191e559

STORAGE_SERVICE=
GCS_BUCKET_NAME=
GCS_PROJECT_ID=
GOOGLE_APPLICATION_CREDENTIALS=

MUX_TOKEN_ID=
MUX_TOKEN_SECRET=
MUX_SIGNING_KEY_ID=
MUX_BASE64_ENCODED_PRIVATE_KEY=

YOUTUBE_API_CREDENTIALS_FILE=credentials/credentials_file.json
YOUTUBE_API_TOKEN_FILE=credentials/token.json

# Redis
REDIS_ADDR=localhost:6379
REDIS_PASSWORD=
REDIS_DB_ASYNC=5

# TWILIO
TWILIO_AUTH_TOKEN=
TWILIO_ACCOUNT_SID=
TWILIO_SERVICE_SID=
USER_SMS_VERIFY_CODE=000000
TWILIO_SMS_ENABLED=false
