ifneq (,$(wildcard ./.env))
    include .env
    export
endif

DB_USER ?= postgres
DB_PASSWORD ?=
DB_HOST ?= localhost
DB_PORT ?= 5432
DB_NAME ?= billiard-community-dev
DB_SSL_MODE ?= disable
DB_SCHEMA ?= vibico_education

export PATH   := $(PWD)/bin:$(PATH)
export SHELL  := bash
export OSTYPE := $(shell uname -s | tr A-Z a-z)
export ARCH := $(shell uname -m)

# --- Tooling & Variables ----------------------------------------------------------------
include ./misc/make/tools.Makefile
include ./misc/make/help.Makefile

MIGRATIONS_FOLDER = $(PWD)/setup/migrations

POSTGRESQL_URL=postgres://$(DB_USER):$(DB_PASSWORD)@$(DB_HOST):$(DB_PORT)/$(DB_NAME)?sslmode=$(DB_SSL_MODE)

cmd-exists-%:
	@hash $(*) > /dev/null 2>&1 || \
		(echo "ERROR: '$(*)' must be installed and available on your PATH."; exit 1)

db.migrate.create: $(MIGRATE)
	@ read -p "Please provide name for the migration: " Name; \
	migrate create -ext sql -dir $(MIGRATIONS_FOLDER) -seq $${Name}

db.migrate: $(MIGRATE)
	migrate -path $(MIGRATIONS_FOLDER) -database "$(POSTGRESQL_URL)" --verbose up

db.rollback: $(MIGRATE)
	migrate -path $(MIGRATIONS_FOLDER) -database "$(POSTGRESQL_URL)" --verbose down ${STEP}

db.migrate.force: $(MIGRATE)
	migrate -path $(MIGRATIONS_FOLDER) -database "$(POSTGRESQL_URL)" --verbose force $(VERSION)

db.create:
	createdb --username=$(DB_USER) --owner=$(DB_USER) $(DB_NAME)

db.drop:
	dropdb --username=$(DB_USER) ${DB_NAME}

db.reset: drop.schema db.migrate
	@echo "Database has been reset"

db.seed: cmd-exists-go
	go run cmd/seed/main.go

db.migrate.rerun: $(MIGRATE)
ifndef VERSION
	$(error VERSION is not set)
endif
	migrate -path $(MIGRATIONS_FOLDER) -database "$(POSTGRESQL_URL)" --verbose down $(VERSION)
	migrate -path $(MIGRATIONS_FOLDER) -database "$(POSTGRESQL_URL)" --verbose up $(VERSION)

db.rake: cmd-exists-go
	@if [ -z "$$TASK" ]; then \
	  echo "Please provide a rake task name using TASK variable, e.g. make db.rake TASK=my_task"; \
	  exit 1; \
	fi
	go run cmd/rake/main.go $$TASK

# ----------- SCHEMA MIGRATION ----------------------------------------------------
SCHEMA_URL=postgres://$(DB_USER):$(DB_PASSWORD)@$(DB_HOST):$(DB_PORT)/$(DB_NAME)?sslmode=$(DB_SSL_MODE)&search_path=$(DB_SCHEMA)
schema.create: cmd-exists-psql
	psql $(POSTGRESQL_URL) -c "CREATE SCHEMA IF NOT EXISTS $(DB_SCHEMA);"

schema.rollback-all: $(MIGRATE)
	migrate -path $(MIGRATIONS_FOLDER) -database "$(SCHEMA_URL)" --verbose down -all

schema.reset-migrate: schema.rollback-all schema.migrate db.seed

schema.drop: cmd-exists-psql
	psql $(POSTGRESQL_URL) -c "DROP SCHEMA IF EXISTS $(DB_SCHEMA) CASCADE; CREATE SCHEMA IF NOT EXISTS $(DB_SCHEMA);"

schema.migrate: $(MIGRATE)
	migrate -path $(MIGRATIONS_FOLDER) -database "$(SCHEMA_URL)" --verbose up

schema.reset: schema.drop schema.create schema.migrate db.seed
	@echo "Schema has been reset"

schema.migrate-dev:
	@echo "==> Running dev-only migrations"
	go run cmd/devMigrate/main.go

schema.rollback-dev:
	@echo "==> Rolling back dev-only migrations"
	go run cmd/devMigrate/main.go --rollback

# ----------- GQL ----------------------------------------------------
gql.make: cmd-exists-go
	go run cmd/gql/makeSchema.cmd.go

# ----------- DEVELOPMENT ENVIRONMENT ----------------------------------------------------
up: dev-air             ## Run Hot Reload Development Environment, run the application and watch for changes.
update-dependencies:
	export GOPRIVATE="github.com/kero-chan/*,github.com/BehemothLtd/*"
	git config --global url."**************:".insteadOf "https://github.com/"
	go get github.com/BehemothLtd/behemoth-pkg
	go get github.com/BehemothLtd/vibico-auth/proto@latest

install-deps: migrate air go-enum install-ffmpeg
deps: $(MIGRATE) $(AIR) $(GO_ENUM) install-ffmpeg
deps:
	@echo "Required Tools Are Available"

dev-air: $(AIR) gql.make ## Starts AIR ( Continuous Development app).
	air

async.worker: cmd-exists-go
	go run cmd/asynq/main.go

cronjob.run:
	go run cmd/cronjob/main.go -job=$(job)

enum-generate: $(GO_ENUM) ## Go enum generate file
	@ read -p "Please provide filepath: " Filepath; \
	go-enum --marshal --names --values -f $${Filepath} --template=internal/enums/additional_template.tmpl

enum-int-generate: $(GO_ENUM) ## Go enum generate file
	@ read -p "Please provide filepath: " Filepath; \
	go-enum --sqlint --marshal --names --values -f $${Filepath} --template=internal/enums/additional_template.tmpl

go-generate:
	go generate ./...

check:
	staticcheck ./...

# Generate a consolidated SQL file from the migration files
generate-sql-doc:
	@echo "Generating combined SQL documentation..."
	@mkdir -p documents
	@echo "-- Vibico Education Database Schema V1\n" > documents/vibico_v1.sql
	@echo "-- Generated at: $$(date)\n" >> documents/vibico_v1.sql
	@for file in $$(ls setup/migrations/*.up.sql); do \
		echo "-- File: $$file\n" >> documents/vibico_v1.sql; \
		cat $$file >> documents/vibico_v1.sql; \
		echo "" >> documents/vibico_v1.sql; \
	done
	@echo "SQL documentation generated at documents/vibico_v1.sql"

# ------------ Docker Build & Deploy ----------------------------------------------------
REGISTRY=us-west1-docker.pkg.dev/behemothvn/library/vibico
IMAGE_NAME=academy-api
IMAGE_BUILDER_NAME=academy-api-builder
COMMIT_SHA=$(shell git rev-parse HEAD)
NAMESPACE_DEV=vibico-dev
NAMESPACE_SANDBOX=vibico-sandbox
CONTEXT=gke_behemothvn_us-west1_insight-prod

.PHONY: docker-builder-build docker-builder-push docker-build docker-push k8s-release-dev k8s-release-sandbox release-dev release-sandbox

docker-builder-build: ## Build docker builder stage image
	docker build --platform=linux/amd64 --target=builder -t $(IMAGE_BUILDER_NAME):latest .
	docker tag $(IMAGE_BUILDER_NAME):latest $(REGISTRY)/$(IMAGE_BUILDER_NAME):latest
	docker tag $(IMAGE_BUILDER_NAME):latest $(REGISTRY)/$(IMAGE_BUILDER_NAME):$(COMMIT_SHA)

docker-builder-push: ## Push docker builder image to registry
	docker push $(REGISTRY)/$(IMAGE_BUILDER_NAME):latest
	docker push $(REGISTRY)/$(IMAGE_BUILDER_NAME):$(COMMIT_SHA)

docker-build: ## Build docker image
	docker build --platform=linux/amd64 -t $(IMAGE_NAME):latest .
	docker tag $(IMAGE_NAME):latest $(REGISTRY)/$(IMAGE_NAME):latest
	docker tag $(IMAGE_NAME):latest $(REGISTRY)/$(IMAGE_NAME):$(COMMIT_SHA)

docker-push: ## Push docker image to registry
	docker push $(REGISTRY)/$(IMAGE_NAME):latest
	docker push $(REGISTRY)/$(IMAGE_NAME):$(COMMIT_SHA)

k8s-release-dev: ## Update deployment image for dev environment
	kubectl --context=$(CONTEXT) set image deployment/$(IMAGE_NAME) $(IMAGE_NAME)=$(REGISTRY)/$(IMAGE_NAME):$(COMMIT_SHA) -n $(NAMESPACE_DEV)
	kubectl --context=$(CONTEXT) set image deployment/asynq-video-transcoder asynq-video-transcoder=$(REGISTRY)/$(IMAGE_NAME):$(COMMIT_SHA) -n $(NAMESPACE_DEV)
	kubectl --context=$(CONTEXT) set image cronjob/cleanup-orphan-videos cleanup-orphan-videos=$(REGISTRY)/$(IMAGE_NAME):$(COMMIT_SHA) -n $(NAMESPACE_DEV)

k8s-release-sandbox: ## Update deployment image for sandbox environment
	@echo "About to update $(IMAGE_NAME), asynq-video-transcoder deployments and cleanup-orphan-videos cronjob in $(NAMESPACE_SANDBOX) environment with image $(REGISTRY)/$(IMAGE_NAME):$(COMMIT_SHA)"
	@read -p "Are you sure you want to continue? (Y/n): " -n 1 -r; \
	echo; \
	if [[ $$REPLY =~ ^[Yy]$$ ]]; then \
		kubectl --context=$(CONTEXT) set image deployment/$(IMAGE_NAME) $(IMAGE_NAME)=$(REGISTRY)/$(IMAGE_NAME):$(COMMIT_SHA) -n $(NAMESPACE_SANDBOX); \
		kubectl --context=$(CONTEXT) set image deployment/asynq-video-transcoder asynq-video-transcoder=$(REGISTRY)/$(IMAGE_NAME):$(COMMIT_SHA) -n $(NAMESPACE_SANDBOX); \
		kubectl --context=$(CONTEXT) set image cronjob/cleanup-orphan-videos cleanup-orphan-videos=$(REGISTRY)/$(IMAGE_NAME):$(COMMIT_SHA) -n $(NAMESPACE_SANDBOX); \
		echo "Checking deployment status..."; \
		kubectl --context=$(CONTEXT) rollout status deployment/$(IMAGE_NAME) -n $(NAMESPACE_SANDBOX); \
		kubectl --context=$(CONTEXT) rollout status deployment/asynq-video-transcoder -n $(NAMESPACE_SANDBOX); \
		echo "Cronjob cleanup-orphan-videos updated successfully."; \
	else \
		echo "Deployment cancelled."; \
		exit 1; \
	fi

release-dev: docker-build docker-push k8s-release-dev

release-sandbox: docker-build docker-push k8s-release-sandbox

# --------------- Builder Deployment ----------------------------------------------------
BUILDER_IMAGE := us-west1-docker.pkg.dev/behemothvn/library/billiard-community/api-builder
BUILDER_POD_NAME := builder
BUILDER_NAMESPACE := billiard-community-dev

.PHONY: builder-deploy
builder-deploy: ## Deploy builder pod to k8s cluster
	@ echo "Cleaning up existing builder pod..."
	@ kubectl delete pod $(BUILDER_POD_NAME) -n $(BUILDER_NAMESPACE) --ignore-not-found=true

	@ echo "Building builder image..."
	@ docker build --platform=linux/amd64 --target builder -t $(BUILDER_IMAGE):$$(git rev-parse HEAD) .
	@ docker push $(BUILDER_IMAGE):$$(git rev-parse HEAD)

	@ echo "Updating builder pod manifest..."
	@ sed "s|$(BUILDER_IMAGE):[[:alnum:]]*|$(BUILDER_IMAGE):$$(git rev-parse HEAD)|g" misc/make/builder.yaml > bin/builder_generated.yaml

	@ echo "Deploying builder pod..."
	@ kubectl apply -f bin/builder_generated.yaml

	@ echo "Cleaning up generated manifest..."
	@ rm bin/builder_generated.yaml

	@ echo "Waiting for builder pod to be ready..."
	@ kubectl wait --for=condition=ready pod/$(BUILDER_POD_NAME) -n $(BUILDER_NAMESPACE) --timeout=120s

	@ echo "Builder pod deployed successfully!"
