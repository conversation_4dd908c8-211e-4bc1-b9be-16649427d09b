FROM golang:1.24.3-bookworm as dev

WORKDIR /app

COPY id_rsa /root/.ssh/id_rsa
RUN mkdir -p /root/.ssh && \
    chmod 700 /root/.ssh && \
    chmod 400 /root/.ssh/id_rsa && \
    ssh-keyscan github.com >> /root/.ssh/known_hosts && \
    git config --global url."ssh://**************/".insteadOf "https://github.com/"

COPY go.mod go.sum ./
RUN go mod download && rm -f /root/.ssh/id_rsa

RUN apt-get update && \
    apt-get install -y \
    redis \
    redis-tools \
    postgresql \
    postgresql-client \
    make \
    tar \
    gzip \
    ffmpeg \
    vim

RUN go install -tags 'postgres' github.com/golang-migrate/migrate/v4/cmd/migrate@latest

COPY Makefile ./
COPY misc/make/tools.Makefile ./misc/make/
COPY misc/make/help.Makefile ./misc/make/

RUN make install-deps
RUN go install github.com/air-verse/air@latest

COPY . .

CMD ["air", "-c", ".air.toml"]
