CREATE TABLE IF NOT EXISTS course_censor_histories (
  id BIGSERIAL PRIMARY KEY,
  course_id BIGINT NOT NULL,
  created_by BIGINT NOT NULL,
  feedback TEXT NOT NULL,
  status SMALLINT NOT NULL DEFAULT 0,
  created_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_course_censor_histories_course_id ON course_censor_histories (course_id);

CREATE INDEX idx_course_censor_histories_created_by ON course_censor_histories (created_by);
