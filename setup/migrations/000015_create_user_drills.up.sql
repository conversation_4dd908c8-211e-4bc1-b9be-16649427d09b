CREATE TABLE IF NOT EXISTS user_drills (
  id BIGSERIAL PRIMARY KEY,
  drill_id BIGINT NOT NULL,
  user_id BIGINT NOT NULL,
  created_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_user_drills_drill_id ON user_drills (drill_id);

CREATE INDEX idx_user_drills_user_id ON user_drills (user_id);

CREATE UNIQUE INDEX idx_user_drills_user_drill ON user_drills (user_id, drill_id);
