CREATE TABLE IF NOT EXISTS authens (
  id BIGSERIAL PRIMARY KEY,
  username VA<PERSON><PERSON><PERSON>(255) NOT NULL,
  phone VARCHAR(20),
  email VARCHAR(255),
  encrypted_password VARCHAR(255) NOT NULL,
  created_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE UNIQUE INDEX idx_authens_username ON authens (username);

CREATE UNIQUE INDEX idx_authens_email ON authens (email)
WHERE
  email IS NOT NULL;

CREATE UNIQUE INDEX idx_authens_phone ON authens (phone)
WHERE
  phone IS NOT NULL;
