BEGIN;

ALTER TABLE
  course_section_items
ADD
  COLUMN IF NOT EXISTS course_id BIGINT;

update
  vibico_education.course_section_items
set
  course_id = (
    select
      course_id
    from
      course_sections
    where
      course_sections.id = course_section_items.course_section_id
  )
where
  true;

ALTER TABLE
  course_section_items
ALTER COLUMN
  course_id
SET
  NOT NULL;

CREATE INDEX IF NOT EXISTS idx_course_section_items_course_id ON course_section_items (course_id);

COMMIT;
