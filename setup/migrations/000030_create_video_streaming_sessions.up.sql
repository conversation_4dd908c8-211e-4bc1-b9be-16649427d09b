CREATE TABLE IF NOT EXISTS video_streaming_sessions (
    id BIGSERIAL PRIMARY KEY,
    video_id BIGINT NOT NULL REFERENCES videos(id) ON DELETE CASCADE,
    user_id BIGINT NOT NULL, -- reference to students
    session_token VARCHAR(255) NOT NULL, -- authentication token
    ip_address VA<PERSON>HAR(50),
    user_agent <PERSON><PERSON><PERSON><PERSON>(255),
    started_at TIMESTAMP,
    ended_at TIMESTAMP,
    status INTEGER DEFAULT 1, -- 1: active, 2: ended, 3: expired
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX video_streaming_sessions_video_id_idx ON video_streaming_sessions (video_id);
CREATE INDEX video_streaming_sessions_user_id_idx ON video_streaming_sessions (user_id);
CREATE INDEX video_streaming_sessions_session_token_idx ON video_streaming_sessions (session_token);
CREATE INDEX video_streaming_sessions_status_idx ON video_streaming_sessions (status);
