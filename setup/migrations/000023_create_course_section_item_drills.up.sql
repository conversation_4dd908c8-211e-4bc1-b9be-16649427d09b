CREATE TABLE IF NOT EXISTS course_section_item_drills (
  id BIGSERIAL PRIMARY KEY,
  course_section_item_id BIGINT NOT NULL,
  drill_id BIGINT NOT NULL,
  created_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_csids_drill_id ON course_section_item_drills (drill_id);

CREATE INDEX idx_csids_course_section_item_id ON course_section_item_drills (course_section_item_id);

CREATE UNIQUE INDEX idx_csids_course_section_item_id_drill_id ON course_section_item_drills (course_section_item_id, drill_id);
