CREATE TABLE IF NOT EXISTS notifications (
  id BIGSERIAL PRIMARY KEY,
  recipient_id BIGINT NOT NULL,
  recipient_type VARCHAR(30) NOT NULL,
  sender_id BIGINT,
  sender_type VARCHAR(30),
  notice_kind SMALLINT NOT NULL DEFAULT 1,
  notifiable_type VARCHAR(30),
  notifiable_id BIGINT,
  is_read BOOLEAN DEFAULT FALSE,
  read_at TIMESTAMPTZ,
  content JSONB,
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_notifications_recipient ON notifications (recipient_id, recipient_type);