CREATE TABLE IF NOT EXISTS user_course_sections (
  id BIGSERIAL PRIMARY KEY,
  user_id BIGINT NOT NULL,
  course_section_id BIGINT NOT NULL,
  data JSONB,
  created_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_ucs_user_id ON user_course_sections (user_id);

CREATE INDEX idx_ucs_course_section_id ON user_course_sections (course_section_id);

CREATE UNIQUE INDEX idx_ucs_user_course ON user_course_sections (user_id, course_section_id);
