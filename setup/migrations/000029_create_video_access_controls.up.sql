CREATE TABLE IF NOT EXISTS video_access_controls (
    id BIGSERIAL PRIMARY KEY,
    video_id BIGINT NOT NULL REFERENCES videos(id) ON DELETE CASCADE,
    plan_id BIGINT, -- reference to plans (optional)
    access_type VARCHAR(50) NOT NULL, -- free, premium, specific_plan
    streaming_only BOOLEAN DEFAULT FALSE, -- must stream
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX video_access_controls_video_id_idx ON video_access_controls (video_id);
CREATE INDEX video_access_controls_plan_id_idx ON video_access_controls (plan_id);
CREATE INDEX video_access_controls_access_type_idx ON video_access_controls (access_type);
