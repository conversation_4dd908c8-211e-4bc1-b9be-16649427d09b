CREATE TABLE IF NOT EXISTS video_moderations (
    id BIGSERIAL PRIMARY KEY,
    video_id BIGINT NOT NULL REFERENCES videos(id) ON DELETE CASCADE,
    moderator_id BIGINT NOT NULL, -- ID of moderator
    status INTEGER DEFAULT 1, -- 1: pending, 2: approved, 3: rejected
    comment TEXT,
    moderated_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX video_moderations_video_id_idx ON video_moderations (video_id);
CREATE INDEX video_moderations_moderator_id_idx ON video_moderations (moderator_id);
CREATE INDEX video_moderations_status_idx ON video_moderations (status);
