CREATE TABLE IF NOT EXISTS drills (
  id BIGSERIAL PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  slug VARCHAR(255) NOT NULL,
  description TEXT,
  level SMALLINT NOT NULL DEFAULT 1,
  status SMALLINT NOT NULL DEFAULT 0,
  censor SMALLINT NOT NULL DEFAULT 0,
  tags JSONB,
  step JSONB,
  sale_price INTEGER,
  price INTEGER,
  is_master BOOLEAN NOT NULL,
  owner_id BIGINT NOT NULL,
  owner_type VARCHAR(50) NOT NULL,
  created_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_drills_owner ON drills (owner_id, owner_type);

CREATE INDEX idx_drills_slug ON drills (slug);

CREATE INDEX idx_drills_created_at ON drills (created_at);
