CREATE TABLE IF NOT EXISTS user_course_section_items (
  id BIGSERIAL PRIMARY KEY,
  user_id BIGINT NOT NULL,
  course_section_item_id BIGINT NOT NULL,
  status SMALLINT NOT NULL DEFAULT 0,
  data JSONB,
  created_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_ucsi_user_id ON user_course_section_items (user_id);

CREATE INDEX idx_ucsi_course_section_item_id ON user_course_section_items (course_section_item_id);

CREATE UNIQUE INDEX idx_ucsi_user_course ON user_course_section_items (user_id, course_section_item_id);
