CREATE TABLE IF NOT EXISTS diagrams (
  id BIGSERIAL PRIMARY KEY,
  parent_id BIGINT NOT NULL,
  parent_type VARCHAR(50) NOT NULL,
  image_url VARCHAR(255),
  position INTEGER NOT NULL DEFAULT 1,
  setting <PERSON><PERSON><PERSON><PERSON>,
  created_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_diagrams_parent ON diagrams (parent_type, parent_id);
