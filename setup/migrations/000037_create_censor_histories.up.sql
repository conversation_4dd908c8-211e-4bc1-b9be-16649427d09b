CREATE TABLE IF NOT EXISTS censor_histories (
  id BIGSERIAL PRIMARY KEY,
  parent_id BIGINT NOT NULL,
  parent_type VARCHAR(255) NOT NULL,
  created_by BIGINT NOT NULL,
  feedback TEXT NOT NULL,
  status SMALLINT NOT NULL DEFAULT 0,
  created_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_censor_histories_created_by ON censor_histories (created_by);

CREATE INDEX idx_censor_histories_parent ON censor_histories (parent_type, parent_id);
