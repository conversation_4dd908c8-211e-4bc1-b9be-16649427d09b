CREATE TABLE IF NOT EXISTS video_progresses (
    id BIGSERIAL PRIMARY KEY,
    video_id BIGINT NOT NULL REFERENCES videos(id) ON DELETE CASCADE,
    user_id BIGINT NOT NULL,
    -- reference to students
    last_position INTEGER DEFAULT 0,
    -- last position (seconds)
    completed BOOLEAN DEFAULT FALSE,
    -- completed
    completed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX video_progresses_video_id_idx ON video_progresses (video_id);

CREATE INDEX video_progresses_user_id_idx ON video_progresses (user_id);

CREATE UNIQUE INDEX video_progresses_video_id_user_id_idx ON video_progresses (video_id, user_id);
