CREATE TABLE IF NOT EXISTS teachers (
  id BIGSERIAL PRIMARY KEY,
  auth_id VARCHAR(255) NOT NULL,
  name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
  slug VARCHAR(255) NOT NULL,
  phone_number VARCHAR(255),
  contact_email VARCHAR(255),
  description VARCHAR(255),
  award VARCHAR(255),
  address VARCHAR(255),
  image_url VARCHAR(255),
  can_invite_students BOOLEAN NOT NULL DEFAULT FALSE,
  basic_entered BOOLEAN NOT NULL DEFAULT FALSE,
  active BOOLEAN NOT NULL DEFAULT TRUE,
  approved_course_count INTEGER NOT NULL DEFAULT 0,
  student_count INTEGER NOT NULL DEFAULT 0,
  average_rating DECIMAL(2, 1),
  created_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE UNIQUE INDEX idx_teachers_auth_id ON teachers (auth_id);

CREATE UNIQUE INDEX idx_teachers_phone_number ON teachers (phone_number);

CREATE UNIQUE INDEX idx_teachers_contact_email ON teachers (contact_email);

CREATE UNIQUE INDEX idx_teachers_slug ON teachers (slug);
