CREATE TABLE IF NOT EXISTS video_platforms (
    id BIGSERIAL PRIMARY KEY,
    video_id BIGINT NOT NULL REFERENCES videos(id) ON DELETE CASCADE,
    platform INTEGER NOT NULL DEFAULT 1,
    -- Mux, YouTube
    platform_video_id VARCHAR(100),
    -- ID của video trên nền tảng (YouTube ID)
    url VARCHAR(255),
    -- URL đến video trên nền tảng
    status INTEGER DEFAULT 1,
    -- 1: syncing, 2: available, 3: error
    sync_at TIMESTAMP,
    token_id BIGINT,
    -- ID của token dùng để sync
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX video_platforms_video_id_idx ON video_platforms (video_id);

CREATE INDEX video_platforms_platform_idx ON video_platforms (platform);

CREATE INDEX video_platforms_status_idx ON video_platforms (status);
