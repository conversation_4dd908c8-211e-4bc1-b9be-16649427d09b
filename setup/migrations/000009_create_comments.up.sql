CREATE TABLE IF NOT EXISTS comments (
  id BIGSERIAL PRIMARY KEY,
  parent_id BIGINT REFERENCES comments (id) ON DELETE CASCADE,
  author_id BIGINT NOT NULL,
  author_type VARCHAR(50) NOT NULL,
  target_id BIGINT NOT NULL,
  target_type VARCHAR(50) NOT NULL,
  content TEXT NOT NULL,
  rating DECIMAL(2, 1),
  created_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_comments_author ON comments (author_type, author_id);

CREATE INDEX idx_comments_target ON comments (target_type, target_id);

CREATE INDEX idx_comments_parent ON comments (parent_id);
