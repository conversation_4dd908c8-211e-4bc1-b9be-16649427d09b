CREATE TABLE IF NOT EXISTS course_packages (
  id BIGSERIAL PRIMARY KEY,
  course_id BIGINT,
  package_deal_id BIGINT,
  sale_price INTEGER,
  price INTEGER,
  metadata JSONB,
  created_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_course_packages_course_id ON course_packages (course_id);
CREATE INDEX idx_course_packages_package_deal_id ON course_packages (package_deal_id);
CREATE UNIQUE INDEX idx_course_packages_course_id_package_deal_id ON course_packages (course_id, package_deal_id);
