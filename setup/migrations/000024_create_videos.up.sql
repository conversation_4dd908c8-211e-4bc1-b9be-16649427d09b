CREATE TABLE IF NOT EXISTS videos (
    id BIGSERIAL PRIMARY KEY,
    parent_id BIGINT NOT NULL,
    parent_type VARCHAR(50) NOT NULL,
    uploader_type VARCHAR(50) NOT NULL,
    uploader_id BIGINT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    duration INTEGER,
    thumbnail_url VARCHAR(255),
    is_free BOOLEAN DEFAULT FALSE,
    status INTEGER DEFAULT 1, -- 1: draft, 2: pending, 3: approved, 4: rejected
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    options JSONB
);

CREATE INDEX videos_status_idx ON videos (status);
CREATE INDEX videos_is_free_idx ON videos (is_free);
CREATE INDEX idx_videos_parent ON videos (parent_type, parent_id);
CREATE INDEX idx_videos_uploader ON videos (uploader_type, uploader_id);
