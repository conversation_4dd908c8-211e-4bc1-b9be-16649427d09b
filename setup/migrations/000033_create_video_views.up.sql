CREATE TABLE IF NOT EXISTS video_views (
    id BIGSERIAL PRIMARY KEY,
    video_id BIGINT NOT NULL REFERENCES videos(id) ON DELETE CASCADE,
    user_id BIGINT, -- reference to students, can be NULL for anonymous views
    platform VARCHAR(50) NOT NULL, -- YouTube, Mux
    view_duration INTEGER DEFAULT 0, -- view duration (seconds)
    watched_percentage FLOAT DEFAULT 0, -- watched percentage
    ip_address VARCHAR(50),
    viewed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX video_views_video_id_idx ON video_views (video_id);
CREATE INDEX video_views_user_id_idx ON video_views (user_id) WHERE user_id IS NOT NULL;
CREATE INDEX video_views_platform_idx ON video_views (platform);
CREATE INDEX video_views_viewed_at_idx ON video_views (viewed_at);
