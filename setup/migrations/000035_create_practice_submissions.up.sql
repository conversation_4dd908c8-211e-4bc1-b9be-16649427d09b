CREATE TABLE IF NOT EXISTS practice_submissions (
  id BIGSERIAL PRIMARY KEY,
  practice_id BIGINT NOT NULL,
  practice_type VARCHAR(50) NOT NULL,
  teacher_id BIGINT NOT NULL,
  user_id BIGINT NOT NULL,
  content TEXT,
  status SMALLINT NOT NULL DEFAULT 0,
  created_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_practice_submissions_practice ON practice_submissions (practice_id, practice_type);

CREATE INDEX idx_practice_submissions_user_id ON practice_submissions (user_id);

CREATE INDEX idx_practice_submissions_teacher_id ON practice_submissions (teacher_id);

CREATE INDEX idx_practice_submissions_status ON practice_submissions (status);
