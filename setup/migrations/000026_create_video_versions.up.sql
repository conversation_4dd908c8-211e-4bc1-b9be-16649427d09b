CREATE TABLE IF NOT EXISTS video_versions (
    id BIGSERIAL PRIMARY KEY,
    video_id BIGINT NOT NULL REFERENCES videos(id) ON DELETE CASCADE,
    quality VARCHAR(50), -- SD, HD, Full HD, 4K
    resolution VARCHAR(50), -- 720p, 1080p, etc.
    file_size BIGINT,
    file_format VARCHAR(10), -- MP4, MKV
    s3_path VARCHAR(255),
    streaming_profile VARCHAR(20), -- basic, hd, 4k
    manifest_url VARCHAR(255), -- HLS/DASH manifest URL
    drm_key_id UUID,
    cache_status INTEGER DEFAULT 1, -- 1: not cached, 2: cached
    storage_class VARCHAR(20), -- STANDARD, NEARLINE
    access_tier INTEGER DEFAULT 1, -- 1: free, 2: premium, 3: ultimate
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX video_versions_video_id_idx ON video_versions (video_id);
CREATE INDEX video_versions_access_tier_idx ON video_versions (access_tier);
