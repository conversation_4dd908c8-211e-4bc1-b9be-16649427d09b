CREATE TABLE IF NOT EXISTS course_users (
  id BIGSERIAL PRIMARY KEY,
  user_id BIGINT NOT NULL,
  course_id BIGINT NOT NULL,
  course_package_id BIGINT,
  status SMALLINT NOT NULL DEFAULT 1,
  course_user_metadata JSONB,
  joined_at TIMESTAMPTZ (6),
  created_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_course_users_user_id ON course_users (user_id);

CREATE INDEX idx_course_users_course_id ON course_users (course_id);

CREATE INDEX idx_course_users_course_package_id ON course_users (course_package_id);

CREATE UNIQUE INDEX idx_course_users_user_course ON course_users (user_id, course_id);
