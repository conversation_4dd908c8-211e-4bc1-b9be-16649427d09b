CREATE TABLE IF NOT EXISTS video_uploads (
    id BIGSERIAL PRIMARY KEY,
    video_id BIGINT NOT NULL REFERENCES videos(id) ON DELETE CASCADE,
    filename VARCHAR(255) NOT NULL,
    status INTEGER DEFAULT 1, -- 1: uploading, 2: processing, 3: completed, 4: failed
    progress FLOAT DEFAULT 0, -- 0-100%
    error_message TEXT,
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX video_uploads_video_id_idx ON video_uploads (video_id);
CREATE INDEX video_uploads_status_idx ON video_uploads (status);
