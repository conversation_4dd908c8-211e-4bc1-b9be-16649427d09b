CREATE TABLE IF NOT EXISTS course_section_items (
  id BIGSERIAL PRIMARY KEY,
  course_id BIGINT NOT NULL,
  course_section_id BIGINT NOT NULL,
  title VARCHAR(255) NOT NULL,
  slug VARCHAR(255) NOT NULL,
  type SMALLINT NOT NULL DEFAULT 0,
  position INTEGER DEFAULT 1 NOT NULL,
  content TEXT,
  created_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_course_section_items_section_id ON course_section_items (course_section_id);

CREATE INDEX idx_course_section_items_course_id ON course_section_items (course_id);

CREATE INDEX idx_course_section_items_slug ON course_section_items (slug);
