CREATE TABLE IF NOT EXISTS courses (
  id BIGSERIAL PRIMARY KEY,
  teacher_id BIGINT NOT NULL,
  title VARCHAR(255) NOT NULL,
  slug VARCHAR(255) NOT NULL,
  description TEXT,
  status SMALLINT NOT NULL DEFAULT 0,
  instructional_level SMALLINT NOT NULL DEFAULT 0,
  is_public BOOLEAN DEFAULT FALSE,
  is_setting_package BOOLEAN DEFAULT FALSE,
  sale_price INTEGER,
  price INTEGER,
  bonus_point INTEGER,
  bonus_point_percent INTEGER,
  section_count INTEGER NOT NULL DEFAULT 0,
  section_item_count INTEGER NOT NULL DEFAULT 0,
  joined_user_count INTEGER NOT NULL DEFAULT 0,
  banner VARCHAR(255),
  created_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_courses_teacher_id ON courses (teacher_id);

CREATE INDEX idx_courses_slug ON courses (slug);
