CREATE TABLE users (
  id BIGSERIAL PRIMARY KEY,
  auth_id VARCHAR(255) NOT NULL,
  name VA<PERSON>HA<PERSON>(255) NOT NULL,
  phone_number VARCHAR(255),
  birth_date DATE,
  gender VARCHAR(255),
  image_url VARCHAR(255),
  active BOOLEAN NOT NULL DEFAULT TRUE,
  created_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ (6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE UNIQUE INDEX idx_users_auth_id ON users (auth_id);

CREATE UNIQUE INDEX idx_users_phone_number ON users (phone_number);
