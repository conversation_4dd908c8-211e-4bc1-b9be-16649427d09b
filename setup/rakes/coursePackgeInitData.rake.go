package rakeTasks

import (
	"vibico-education-api/constants"
	"vibico-education-api/internal/models"

	"gorm.io/gorm"
)

func CoursePackageInitData(db *gorm.DB) error {
	packages := []*models.PackageDeal{}
	if err := db.Table("package_deals").
		Where("name IN (?)", []string{constants.CourseBasicPackage, constants.CourseAdvancePackage}).
		Find(&packages).Error; err != nil {
		return err
	}

	courses := []*models.Course{}
	if err := db.Table("courses").Where("true").
		Preload("CoursePackages").Find(&courses).Error; err != nil {
		return err
	}

	var coursePackages []*models.CoursePackage

	for _, course := range courses {
		existingPackageIDs := make(map[uint32]bool)

		if course.CoursePackages != nil && len(*course.CoursePackages) > 0 {
			for _, coursePackage := range *course.CoursePackages {
				existingPackageIDs[coursePackage.PackageDealID] = true
			}
		}

		for _, packageDeal := range packages {
			if !existingPackageIDs[packageDeal.ID] {
				coursePackages = append(coursePackages, &models.CoursePackage{
					CourseID:      course.ID,
					PackageDealID: packageDeal.ID,
					Price:         course.Price,
					SalePrice:     course.SalePrice,
				})
			}
		}
	}

	if len(coursePackages) > 0 {
		if err := db.Table("course_packages").Create(&coursePackages).Error; err != nil {
			return err
		}
	}

	return nil
}
