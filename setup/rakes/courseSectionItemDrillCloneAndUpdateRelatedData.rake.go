package rakeTasks

import (
	"errors"
	"os"
	"strings"
	"vibico-education-api/internal/models"
	"vibico-education-api/pkg/gcs"
	"vibico-education-api/pkg/helpers"

	"gorm.io/gorm"
)

func cloneDrillAndDiagrams(tx *gorm.DB, origDrill *models.Drill) (*models.Drill, error) {
	newDrill := *origDrill
	newDrill.ID = 0
	newDrill.IsMaster = false
	newDrill.Slug = helpers.GenerateSlug(origDrill.Title)
	if err := tx.Create(&newDrill).Error; err != nil {
		return nil, err
	}
	var diagrams []models.Diagram
	if err := tx.Where("parent_id = ? AND parent_type = ?", origDrill.ID, "Drill").Find(&diagrams).Error; err != nil {
		return nil, err
	}

	bucketName := os.Getenv("GCS_BUCKET_NAME")
	projectId := os.Getenv("GCS_PROJECT_ID")
	gcsAccountService := os.Getenv("GOOGLE_APPLICATION_CREDENTIALS")

	if bucketName == "" || projectId == "" || gcsAccountService == "" {
		return nil, errors.New("invalid Setting for Upload")
	}

	for _, d := range diagrams {
		newDiagram := d
		newDiagram.ID = 0
		newDiagram.ParentID = newDrill.ID

		if d.ImageUrl != nil && strings.TrimSpace(*d.ImageUrl) != "" {
			srcBucket, srcObject := gcs.ParseGCSUrl(*d.ImageUrl)

			dstUrl, err := gcs.CopyFileGCS(srcBucket, srcBucket, srcObject)
			if err != nil {
				return nil, err
			}
			newDiagram.ImageUrl = dstUrl
		}

		if err := tx.Create(&newDiagram).Error; err != nil {
			return nil, err
		}
	}
	return &newDrill, nil
}

func CourseSectionItemDrillCloneAndUpdateRelatedData(db *gorm.DB) error {
	return db.Transaction(func(tx *gorm.DB) error {
		if err := tx.Model(&models.Drill{}).Where("1=1").Update("is_master", true).Error; err != nil {
			return err
		}

		var csidrs []models.CourseSectionItemDrill
		if err := tx.Find(&csidrs).Error; err != nil {
			return err
		}

		for _, csidr := range csidrs {
			var origDrill models.Drill
			if err := tx.First(&origDrill, csidr.DrillId).Error; err != nil {
				return err
			}
			newDrill, err := cloneDrillAndDiagrams(tx, &origDrill)
			if err != nil {
				return err
			}
			if err := tx.Model(&models.CourseSectionItemDrill{}).Where("id = ?", csidr.ID).Update("drill_id", newDrill.ID).Error; err != nil {
				return err
			}
		}
		return nil
	})
}
