package rakeTasks

import "gorm.io/gorm"

func TeacherApprovedCourseAndStudentCount(db *gorm.DB) error {
	return db.Exec(
		`UPDATE teachers t
		SET
		    approved_course_count = subquery.course_count,
		    student_count = subquery.student_count
		FROM (
		    SELECT
		        teacher_id,
		        COUNT(DISTINCT c.id) AS course_count,
		        SUM(c.joined_user_count) AS student_count
		    FROM
		        courses c
		        where c.status = 2
		    GROUP BY
		        teacher_id
		) AS subquery
		WHERE t.id = subquery.teacher_id`,
	).Error
}
