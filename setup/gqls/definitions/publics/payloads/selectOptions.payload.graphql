type generalOption {
  value: Uint32!
  label: String!
}

type stringOption {
  value: String!
  label: String!
}

type enumOptions {
  value: Uint32!
  label: String!
  description: String!
}

type selectOptionsPayload {
  skillOptions: [generalOption!]!
  levelOptions: [enumOptions!]!
  courseStatusOptions: [stringOption!]!
  teacherOptions: [generalOption!]!
  courseInstructionalLevelOptions: [stringOption!]!
}
