scalar Time
scalar Uint32
scalar Float32
scalar JSON

schema {
  query: Query
  mutation: Mutation
}

type Query {
  # Course
  course(slug: String!): CoursePayload
  courses(input: PagyInput, query: CourseQueryInput): CoursesCollectionPayload
  relatedCourses(
    input: PagyInput
    courseSlug: String!
  ): CoursesCollectionPayload!

  videoPlayback(videoId: ID!): VideoPlaybackPayload!

  # Drill
  drill(slug: String!): Drill!
  drills(
    input: PagyInput
    query: DrillQueryInput
    orderBy: String
  ): DrillsCollectionPayload!
  relatedDrills(
    input: PagyInput
    query: RelatedDrillInput
    orderBy: String
    drillSlug: String!
  ): DrillsCollectionPayload!

  selectOptions(input: selectOptionsInput!): selectOptionsPayload

  # Teacher
  teachers(
    input: PagyInput
    query: TeacherQueryInput
    orderBy: String
  ): TeacherCollectionPayload
  teacher(slug: String!): TeacherPayload

  reviews(
    input: PagyInput
    targetID: ID!
    targetType: String!
  ): ReviewsCollectionPayload
}

type Mutation {
  muxVideoUpload(videoId: ID!): MessageInfo!
  youTubeVideoUpload(videoId: ID!): MessageInfo!

  signIn(input: SignInInput!): SignInPayload!
  signOut: MessageInfo

  signUp(input: SignUpInput!): MessageInfo
  signUpVerify(phoneNumber: String, code: String): MessageInfo
  signUpVerifyResend(phoneNumber: String): MessageInfo

  refreshToken(refreshToken: String!): TokenPayload!

  resetPasswordRequest(identifier: String): MessageInfo!
  resetPasswordVerify(
    input: resetPasswordVerifyInput!
  ): ResetPasswordVerifyPayload
  resetPassword(input: passwordResetInput!): MessageInfo
}
