extend type CoursePayload {
  teacher: TeacherPayload
  courseSections: [CourseSectionPayload]
  courseUserMetadata: CourseUserMetadata
  currentUserJoining: CourseUserPayload
  joined: Boolean!
  processPercent: Float32!
}

type CoursesCollectionPayload {
  collection: [CoursePayload!]
  metadata: Metadata!
}

type MyCoursePayload {
  id: Uint32!
  teacherId: Uint32!
  teacher: TeacherPayload
  title: String!
  slug: String!
  description: String
  salePrice: Int
  price: Int
  bonusPoint: Int
  bonusPointPercent: Int
  sectionCount: Uint32!
  sectionItemCount: Uint32!
  joinedUserCount: Uint32!
  courseSections: [MyCourseSectionPayload]
  courseUserMetadata: CourseUserMetadata
  currentUserJoining: CourseUserPayload
  myReview: CommentPayload
  joined: Boolean!
  status: String!
  processPercent: Float32!
  banner: String
  isPublic: Boolean!
  createdAt: Time
  updatedAt: Time
}

type MyCoursesCollectionPayload {
  collection: [CoursePayload!]
  metadata: Metadata!
}
