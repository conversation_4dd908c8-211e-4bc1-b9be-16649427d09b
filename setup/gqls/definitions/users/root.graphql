scalar Time
scalar Uint32
scalar Float32
scalar JSON

schema {
  query: Query
  mutation: Mutation
}

type Query {
  # self info
  selfInfo: UserPayload!

  # Course - the APIs that are called at academy page when user has logged in
  userCourse(slug: String!): CoursePayload
  userCourses(
    input: PagyInput
    query: CourseQueryInput
  ): CoursesCollectionPayload

  # Course Section Item
  # userCourseSectionItem(
  #   itemSlug: String!
  #   courseSlug: String!
  # ): CourseSectionItemPayload

  # userCourseSectionItems(
  #   courseSlug: String!
  #   courseSectionSlug: String!
  # ): [CourseSectionItemPayload]

  userCourseSection(
    sectionSlug: String
    courseSlug: String!
  ): CourseSectionPayload

  ################################################################################
  # My Course - the APIs that are called on course detail for joined user
  myCourse(slug: String!): MyCoursePayload
  myCourses(
    input: PagyInput
    query: CourseQueryInput
  ): MyCoursesCollectionPayload
  myCourseSectionItems(
    courseSlug: String!
    courseSectionSlug: String!
  ): [MyCourseSectionItemPayload]

  myCourseSectionItem(
    itemSlug: String
    courseSlug: String!
  ): MyCourseSectionItemPayload

  drill(slug: String!): Drill!
  drills(
    input: PagyInput
    query: DrillQueryInput
    orderBy: String
  ): DrillsCollectionPayload!
  myDrill(courseId: ID!, itemId: ID!, drillId: ID!): Drill!

  # Practice Submission
  practiceSubmission(id: ID!): PracticeSubmissionPayload!
  practiceSubmissions(
    input: PagyInput
    query: PracticeSubmissionsQueryInput
  ): PracticeSubmissionsCollectionPayload!

  # Teacher
  teachers(input: PagyInput, query: TeacherQueryInput): TeacherCollectionPayload
  teacher(slug: String!): TeacherPayload

  # Review
  review(targetID: ID, targetType: String): CommentPayload
}

type Mutation {
  updateSelfInfo(input: SelfInfoInput!): MessageInfo!
  changePassword(input: ChangePasswordInput!): MessageInfo!
  joinCourse(id: ID!, verifyCode: String, coursePackageID: ID): MessageInfo!

  # Drill
  drillBuy(id: ID!): MessageInfo!

  completeSection(id: ID!, courseId: ID!): MessageInfo!
  setSectionItemStatus(id: ID!, courseId: ID!, status: String!): MessageInfo!

  practiceSubmissionCreate(
    input: PracticeSubmissionCreateInput!
  ): PracticeSubmissionCreatePayload!
  practiceSubmissionUpdate(
    id: ID!
    input: PracticeSubmissionUpdateInput!
  ): PracticeSubmissionUpdatePayload!

  commentCreate(input: CommentCreateInput!): CommentCreatePayload!
  reviewCreate(input: ReviewCreateInput!): CommentCreatePayload!
  reviewUpdate(reviewID: ID, input: ReviewUpdateInput!): CommentUpdatePayload!

  selfVideoDelete(videoId: ID!): MessageInfo!
  markVideoProgress(videoId: ID!, progress: Int!): MessageInfo!
}
