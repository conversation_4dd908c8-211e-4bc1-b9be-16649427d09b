scalar Time
scalar Uint32
scalar Float32
scalar JSON

schema {
  query: Query
  mutation: Mutation
}

type Query {
  # self info
  selfInfo: TeacherPayload!

  # Course
  course(slug: String!): CoursePayload
  courses(input: PagyInput, query: CourseQueryInput): CoursesCollectionPayload

  teacherCourseChart(year: Int): [CourseEarningPayload]

  courseUsers(
    courseId: ID!
    input: PagyInput
    query: CourseUserQueryInput
  ): CourseUsersCollectionPayload

  # Course Section
  courseSection(id: ID!, courseID: ID!): CourseSectionPayload
  courseSections(courseId: ID!): [CourseSectionPayload]!

  courseSectionsWithPractices(courseSlug: String!): [CourseSectionPayload]
  sectionItemUsersWithPractices(
    courseSlug: String!
    sectionItemSLug: String!
    input: PagyInput
    query: SectionItemUsersWithPracticesQueryInput
  ): SectionItemUsersCollectionPayload

  # Course Section Item
  courseSectionItems(
    courseId: ID!
    courseSectionId: ID!
  ): [CourseSectionItemPayload]

  courseSectionItem(
    id: ID!
    courseId: ID!
    courseSectionId: ID!
  ): CourseSectionItemPayload

  # Diagram
  diagram(id: ID!): DiagramPayload

  # Template Diagram
  templateDiagram(id: ID!): TemplateDiagramPayload
  templateDiagrams(
    input: PagyInput
    query: TemplateDiagramQueryInput
  ): TemplateDiagramCollectionPayload

  # Drill
  drills(input: PagyInput, query: DrillQueryInput): DrillsCollectionPayload!
  drill(slug: String!): Drill!
  practiceSubmission(id: ID!): PracticeSubmissionPayload!
  practiceSubmissions(
    input: PagyInput
    query: PracticeSubmissionsQueryInput
  ): PracticeSubmissionsCollectionPayload!
}

type Mutation {
  # self info
  setupInfo(input: BasicSetupInput!): MessageInfo!
  updateSelfInfo(input: BasicSetupInput!): MessageInfo!
  inviteUser(
    courseId: ID!
    phoneNumber: String
    coursePackageID: ID
  ): MessageInfo!

  courseCreate(input: CourseCreateInput!): CourseCreatePayload
  courseUpdate(id: ID!, input: CourseUpdateInput!): CourseUpdatePayload
  courseDelete(id: ID!): MessageInfo!
  courseSubmit(id: ID!): MessageInfo!
  coursePublic(id: ID!): MessageInfo!

  courseSectionItemCreate(
    courseId: ID!
    courseSectionId: ID!
    input: CourseSectionItemInput
  ): CourseSectionItemMutatePayload!

  courseSectionItemUpdate(
    id: ID!
    courseId: ID!
    courseSectionId: ID!
    input: CourseSectionItemInput
  ): CourseSectionItemMutatePayload!

  courseSectionItemDestroy(
    id: ID!
    courseId: ID!
    courseSectionId: ID!
  ): MessageInfo!

  courseSectionItemSwapPosition(
    courseId: ID!
    courseSectionId: ID!
    courseSectionItemId: ID!
    newIndex: Int!
  ): MessageInfo!

  courseSectionItemAddDrill(
    itemId: ID!
    courseId: ID!
    courseSectionId: ID!
    drillId: ID!
  ): MessageInfo!

  courseSectionItemRemoveDrill(
    itemId: ID!
    courseId: ID!
    courseSectionId: ID!
    drillId: ID!
  ): MessageInfo!

  courseSectionItemAddVideo(
    itemId: ID!
    courseId: ID!
    videoId: ID!
  ): MessageInfo!

  # Course Section
  courseSectionDelete(id: ID!, courseID: ID!): MessageInfo!
  courseSectionCreate(
    courseId: ID!
    input: CourseSectionInput!
  ): CourseSectionMutatePayload!
  courseSectionUpdate(
    id: ID!
    courseId: ID!
    input: CourseSectionInput!
  ): CourseSectionMutatePayload!

  courseSectionSwapPosition(
    courseId: ID!
    courseSectionId: ID!
    newIndex: Int!
  ): MessageInfo!

  # Template Diagram
  templateDiagramCreate(
    input: TemplateDiagramFormInput
  ): TemplateDiagramMutatePayload

  templateDiagramUpdate(
    id: ID!
    input: TemplateDiagramFormInput
  ): TemplateDiagramMutatePayload

  templateDiagramDelete(id: ID!): MessageInfo

  # Drill
  drillDelete(id: ID!): MessageInfo!
  drillCreate(input: DrillInput!): DrillModifyPayload!
  drillUpdate(input: DrillInput!, id: ID!): DrillModifyPayload!
  drillPublish(id: ID!, status: String!): MessageInfo!
  drillSubmit(id: ID!): MessageInfo!
  saveAndUploadDrillVideo(videoId: ID!, drillId: ID!): MessageInfo!

  commentCreate(input: CommentCreateInput!): CommentCreatePayload!

  selfVideoDelete(videoId: ID!): MessageInfo!
  courseSectionItemDeleteVideo(videoId: ID!, courseId: ID!): MessageInfo!

  practiceSubmissionChangeStatus(
    id: ID!
    status: String!
  ): PracticeSubmissionPayload
}
