input CourseCreateInput {
  title: String
  description: String
  salePrice: Int
  price: Int
  bonusPoint: Int
  bonusPointPercent: Int
}

input CourseUpdateInput {
  title: String
  description: String
  salePrice: Int
  price: Int
  bonusPoint: Int
  bonusPointPercent: Int
  banner: String
  isPublic: Boolean
  instructionalLevel: String
}

input CourseQueryInput {
  titleCont: String
  descriptionCont: String
  statusEq: String
}

input CourseUserQueryInput {
  createdAtGteq: String
}
