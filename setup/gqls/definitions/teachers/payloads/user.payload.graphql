type SectionItemUsersCollectionPayload {
  Stats: SectionItemUsersStatsPayload
  collection: [SectionItemUserPayload!]
  metadata: Metadata!
}

type SectionItemUserPayload {
  id: Uint32!
  name: String!
  imageUrl: String
  phoneNumber: String
  gender: String
  birthDate: String
  createdAt: Time
  updatedAt: Time

  LatestSubmissionTime: Time
  LatestSubmissionStatus: String!
  LatestSubmissionStatusI18n: String!
  practiceSubmissions: [PracticeSubmissionPayload]
}

type SectionItemUsersStatsPayload {
  TotalEnrolledUsers: Uint32!
  TotalSubmittedUsers: Uint32!
  TotalViewedSectionUsers: Uint32!
}
