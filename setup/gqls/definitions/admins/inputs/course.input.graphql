input CourseQueryInput {
  titleCont: String
  teacherIdEq: String
  statusIn: [String!]
}

input CourseMutateInput {
  title: String
  teacherId: Int
  description: String
  salePrice: Int
  price: Int
  bonusPoint: Int
  bonusPointPercent: Int
}

input CourseUpdateInput {
  title: String
  description: String
  status: String
  salePrice: Int
  price: Int
  bonusPoint: Int
  bonusPointPercent: Int
}
