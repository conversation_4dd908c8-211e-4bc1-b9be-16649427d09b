scalar Time
scalar Uint32
scalar Float32
scalar JSON

schema {
  query: Query
  mutation: Mutation
}

type Query {
  test: String!

  # Drill
  drills(input: PagyInput, query: DrillQueryInput): DrillsCollectionPayload!
  drill(id: ID!): Drill!

  # Course
  course(id: ID!): CoursePayload
  courses(input: PagyInput, query: CourseQueryInput): CoursesCollectionPayload

  # Teacher
  teacher(id: ID!): TeacherPayload
  teachers(input: PagyInput, query: TeacherQueryInput): TeacherCollectionPayload

  users(input: PagyInput, query: UserQueryInput): UserCollectionPayload

  courseSectionItem(id: ID!): CourseSectionItemPayload
}

type Mutation {
  # Auth
  signIn(input: SignInInput!): SignInPayload!

  # Drill
  drillDelete(id: ID!): MessageInfo!
  drillCreate(input: DrillInput!): DrillModifyPayload!
  drillUpdate(input: DrillInput!, id: ID!): DrillModifyPayload!
  drillPublish(id: ID!, status: String!): MessageInfo!
  drillCensor(id: ID!, censor: String!, feedback: String): MessageInfo!

  # Course
  courseCreate(input: CourseMutateInput!): CourseMutatePayload
  courseUpdate(id: ID!, input: CourseMutateInput!): CourseMutatePayload
  courseDelete(id: ID!): MessageInfo!
  courseCensor(id: ID!, censor: String!, feedback: String): MessageInfo!

  # Course Section
  courseSectionDelete(id: ID!, courseID: ID!): MessageInfo!
  courseSectionCreate(
    courseId: ID!
    input: CourseSectionInput!
  ): CourseSectionMutatePayload!
  courseSectionUpdate(
    id: ID!
    courseId: ID!
    input: CourseSectionInput!
  ): CourseSectionMutatePayload!
  courseSectionSwapPosition(
    courseId: ID!
    courseSectionId: ID!
    newIndex: Int!
  ): MessageInfo!

  # Course Section Item
  courseSectionItemCreate(
    courseId: ID!
    courseSectionId: ID!
    input: CourseSectionItemInput
  ): CourseSectionItemMutatePayload!

  courseSectionItemUpdate(
    id: ID!
    courseId: ID!
    courseSectionId: ID!
    input: CourseSectionItemInput
  ): CourseSectionItemMutatePayload!

  courseSectionItemDestroy(id: ID!): MessageInfo!

  permitTeacherInviteStudent(id: ID!): MessageInfo!

  selfVideoDelete(videoId: ID!): MessageInfo!
  saveAndUploadDrillVideo(videoId: ID!, drillId: ID!): MessageInfo!

  toggleTeacherActive(id: ID!): MessageInfo!
  toggleUserActive(id: ID!): MessageInfo!
}
