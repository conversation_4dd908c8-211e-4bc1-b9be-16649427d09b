type TeacherPayload {
  id: Uint32!
  name: String!
  active: Boolean!
  award: String
  address: String
  phoneNumber: String
  slug: String!
  contactEmail: String
  description: String
  basicEntered: Boolean!
  canInviteStudents: Boolean!
  imageUrl: String
  averageRating: Float
  approvedCourseCount: Uint32!
  studentCount: Uint32!
  createdAt: Time
  updatedAt: Time
}

type TeacherCollectionPayload {
  collection: [TeacherPayload!]
  metadata: Metadata!
}
