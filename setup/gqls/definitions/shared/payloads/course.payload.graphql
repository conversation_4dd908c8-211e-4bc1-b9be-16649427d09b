type CoursePayload {
  id: Uint32!
  teacherId: Uint32!
  title: String!
  slug: String!
  description: String
  salePrice: Int
  price: Int
  bonusPoint: Int
  status: String!
  statusI18n: String!
  instructionalLevel: String!
  instructionalLevelI18n: String!
  bonusPointPercent: Int
  sectionCount: Uint32!
  sectionItemCount: Uint32!
  joinedUserCount: Uint32!
  banner: String
  isPublic: Boolean!
  isSettingPackage: Boolean!
  averageRating: Float32
  createdAt: Time
  updatedAt: Time
}

type CoursesCollectionPayload {
  collection: [CoursePayload!]
  metadata: Metadata!
}
