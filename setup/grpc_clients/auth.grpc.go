package grpc_clients

import (
	"context"
	"os"
	"sync"

	"github.com/BehemothLtd/behemoth-pkg/golang/constants"
	pb "github.com/BehemothLtd/vibico-auth/proto/auth"
	"github.com/rs/zerolog/log"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/metadata"
)

var (
	client pb.AuthServiceClient
	Conn   *grpc.ClientConn
	once   sync.Once
)

func InitAuthGrpcClient() {
	if os.Getenv("AUTH_GRPC_SERVER_ADDR") == "" {
		log.Logger.Info().Msg("AUTH_GRPC_SERVER_ADDR is not set. Skip init auth grpc client")
		return
	}

	once.Do(func() {
		dialOps := []grpc.DialOption{
			grpc.WithTransportCredentials(insecure.NewCredentials()),
		}

		var err error
		Conn, err = grpc.NewClient(os.Getenv("AUTH_GRPC_SERVER_ADDR"), dialOps...)
		if err != nil {
			log.Fatal().Err(err).Msg("New GRPC Client Error")
		}
		client = pb.NewAuthServiceClient(Conn)
	})
}

func AuthClient() pb.AuthServiceClient {
	return client
}

func PoolId() string {
	return os.Getenv("AUTH_POOL_ID")
}

func NewCtx(ctx context.Context) context.Context {
	if requestID, ok := ctx.Value(constants.CtxRequestID).(string); ok {
		md := metadata.New(map[string]string{"x-request-id": requestID})
		return metadata.NewOutgoingContext(ctx, md)
	}
	return ctx
}
