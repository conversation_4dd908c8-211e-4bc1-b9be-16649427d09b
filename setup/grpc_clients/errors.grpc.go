package grpc_clients

import translator "vibico-education-api/pkg/translators"

const (
	ErrorCodeSqlError            = "SQL_ERROR"
	ErrorCodeAPIValidationError  = "API_VALIDATION_ERROR"
	ErrorCodeInvalidAPIKey       = "INVALID_API_KEY"
	ErrorCodeRequestTimeout      = "REQUEST_TIMEOUT"
	ErrorCodeDataNotFound        = "NOT_FOUND"
	ErrorCodeInternalServerError = "SERVER_ERROR"
	ErrorCodeServiceUnavailable  = "SERVICE_UNAVAILABLE"
	ErrorCodeNotImplemented      = "NOT_IMPLEMENTED"
	ErrorCodeInsufficientBalance = "INSUFFICIENT_BALANCE"
	ErrorCodeDataAlreadyExist    = "ALREADY_EXISTS"
	ErrorCodeInvalidCredentials  = "INVALID_CREDENTIAL"
	ErrorCodeFieldRequired       = "REQUIRED"
)

var mappingErrorCodeToTranslate = map[string]string{
	ErrorCodeDataAlreadyExist: "errValidation_uniq",
	ErrorCodeFieldRequired:    "errValidationMsg_required",
}

func MapErrorCodeToTranslate(code string) string {
	var key = "errValidationMsg_general"
	if translate, ok := mappingErrorCodeToTranslate[code]; ok {
		key = translate
	}

	return translator.Translate(nil, key)
}
