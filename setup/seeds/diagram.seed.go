package seeds

import (
	"vibico-education-api/internal/models"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

func DiagramSeed(db *gorm.DB) {
	if !db.Migrator().HasTable(&models.TemplateDiagram{}) {
		return
	}

	var count int64
	db.Table("diagrams").Count(&count)

	if count > 0 {
		return
	}

	var templateIds []int
	if err := db.Table("template_diagrams").Select("template_diagrams.id").Scan(&templateIds).Error; err != nil {
		return
	}

	diagrams := make([]models.Diagram, 0)

	for _, id := range templateIds {
		for i := 1; i <= 5; i++ {
			settingData := datatypes.JSON([]byte(`[
                {
                    "id": "ball_30kyguqxi2w",
                    "number": 6,
                    "color": "#006515",
                    "x": 435,
                    "y": 511,
                    "radius": 10.5,
                    "isInTable": true,
                    "isCue": false,
                    "paths": []
                },
                {
                    "id": "ball_oaq7kehridt",
                    "number": 10,
                    "color": "#3B82F6",
                    "x": 557,
                    "y": 278,
                    "radius": 10.5,
                    "isInTable": true,
                    "isCue": false,
                    "paths": []
                },
                {
                    "id": "ball_bzfhum6bpx",
                    "number": 3,
                    "color": "#EF4444",
                    "x": 317,
                    "y": 278,
                    "radius": 10.5,
                    "isInTable": true,
                    "isCue": false,
                    "paths": []
                }
            ]`))

			diagrams = append(diagrams, models.Diagram{
				ParentID:   uint32(id),
				ParentType: "TemplateDiagram",
				Setting:    &settingData,
				Position:   uint32(i),
			})
		}
	}

	db.Session(&gorm.Session{SkipHooks: true}).Create(&diagrams)
}
