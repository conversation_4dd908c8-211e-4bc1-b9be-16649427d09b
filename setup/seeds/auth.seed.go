package seeds

import (
	"fmt"
	"os"
	"time"
	"vibico-education-api/setup/grpc_clients"

	"github.com/BehemothLtd/behemoth-pkg/golang/databases"
	"github.com/BehemothLtd/behemoth-pkg/golang/utils"
	"github.com/rs/zerolog/log"
	"github.com/truongkma/gormzerolog"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

var authDb *gorm.DB

// var authIds *[]string
var authUsers *[]UserAuth

type UserAuth struct {
	ID          string
	PhoneNumber string
}

// ConnectDatabase establishes a connection to PostgreSQL using GORM.
func ConnectAuthDatabase() *gorm.DB {
	logger := gormzerolog.NewLogger(gormzerolog.Config{
		SlowThreshold:        time.Second,
		ParameterizedQueries: utils.GetEnv("LOGGER_SQL_PARAMETERIZED_QUERIES", "false") == "true",
	})

	var err error
	authDb, err = gorm.Open(
		postgres.Open(fmt.Sprintf(
			"host=%s user=%s dbname=%s search_path=%s port=%s sslmode=%s TimeZone=UTC password=%s",
			utils.GetEnv("DB_HOST", "localhost"),
			utils.GetEnv("DB_USER", "postgres"),
			utils.GetEnv("DB_NAME", "database-dev"),
			"vibico_auth",
			utils.GetEnv("DB_PORT", "5432"),
			getSSLMode(),
			utils.GetEnv("DB_PASSWORD", ""),
		)), // ✅ Now calling from `database` package, avoiding circular import
		&gorm.Config{Logger: logger},
	)

	if err != nil {
		log.Fatal().Err(err).Msg("❌ Failed to connect to database")
		os.Exit(1)
	}

	log.Info().Msg("✅ Connected to PostgreSQL")

	databases.ConfigureDatabase(authDb)

	return authDb
}

func getSSLMode() string {
	sslModes := map[string]string{
		"require":     "require",
		"verify-full": "verify-full",
		"verify-ca":   "verify-ca",
		"prefer":      "prefer",
		"allow":       "allow",
		"disable":     "disable",
	}

	sslMode := sslModes[utils.GetEnv("DB_SSL_MODE", "disable")]
	if sslMode == "" {
		sslMode = "disable"
	}

	return sslMode
}

func GetAutUsers() []UserAuth {
	if authDb == nil {
		authDb = ConnectAuthDatabase()
	}

	if authUsers == nil {
		authDb.Table("users").
			Where("identity_pool_id = ?", grpc_clients.PoolId()).
			Where("username NOT ILIKE '%%%admin%%'").
			Find(&authUsers)
	}

	return *authUsers
}

func GetAdminAuthIds() []string {
	if authDb == nil {
		authDb = ConnectAuthDatabase()
	}

	adminAuthIds := []string{}
	authDb.Table("users").
		Where("identity_pool_id = ?", grpc_clients.PoolId()).
		Where("username ILIKE '%%%admin%%'").
		Pluck("id", &adminAuthIds)

	return adminAuthIds
}
