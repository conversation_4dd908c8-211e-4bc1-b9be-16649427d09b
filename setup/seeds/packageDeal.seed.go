package seeds

import (
	"vibico-education-api/constants"
	"vibico-education-api/internal/models"

	"gorm.io/gorm"
)

func PackageDealSeed(db *gorm.DB) {
	if !db.Migrator().HasTable(&models.PackageDeal{}) {
		return
	}

	var count int64
	db.Table("package_deals").Count(&count)

	if count > 0 {
		return
	}

	packageDeals := []models.PackageDeal{
		{
			Name:                       constants.CourseBasicPackage,
			Description:                "Basic package course for students",
			ApprovalSubmissionRequired: false,
			Active:                     true,
		},
		{
			Name:                       constants.CourseAdvancePackage,
			Description:                "Advanced package course for students",
			ApprovalSubmissionRequired: true,
			Active:                     true,
		},
		{
			Name:                       constants.CourseOfflinePackage,
			Description:                "Offline package course for students",
			ApprovalSubmissionRequired: true,
			Active:                     true,
		},
	}

	db.Session(&gorm.Session{SkipHooks: true}).Create(&packageDeals)
}
