package seeds

import (
	"fmt"
	"vibico-education-api/internal/models"
	"vibico-education-api/pkg/helpers"

	"gorm.io/gorm"
)

func TeacherSeed(db *gorm.DB) {
	if !db.Migrator().HasTable(&models.Teacher{}) {
		return
	}

	var count int64
	db.Table("teachers").Count(&count)

	if count > 0 {
		return
	}

	// Connect to the auth database to get the list of user IDs
	if authUsers == nil {
		GetAutUsers()
	}

	teachers := make([]models.Teacher, 0)
	for index, u := range *authUsers {
		teacherName := fmt.Sprintf("Teacher %d", index+1)

		teachers = append(teachers, models.Teacher{
			AuthId:      u.ID,
			Name:        teacherName,
			PhoneNumber: &u.PhoneNumber,
			Slug:        helpers.GenerateSlug(teacherName),
		})
	}

	db.Session(&gorm.Session{SkipHooks: true}).Create(&teachers)
}
