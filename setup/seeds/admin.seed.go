package seeds

import (
	"fmt"
	"vibico-education-api/internal/models"

	"gorm.io/gorm"
)

func AdminSeed(db *gorm.DB) {
	if !db.Migrator().HasTable(&models.Admin{}) {
		return
	}

	var count int64
	db.Table("admins").Count(&count)

	if count > 0 {
		return
	}

	// Connect to the auth database to get the list of user IDs
	adminAuthIds := GetAdminAuthIds()

	admins := make([]models.Admin, 0)
	for index, id := range adminAuthIds {
		admins = append(admins, models.Admin{
			AuthId: id,
			Name:   fmt.Sprintf("Admin %d", index+1),
		})
	}

	db.Session(&gorm.Session{SkipHooks: true}).Create(&admins)
}
