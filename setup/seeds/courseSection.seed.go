package seeds

import (
	"fmt"
	"vibico-education-api/internal/enums"
	"vibico-education-api/internal/models"
	"vibico-education-api/pkg/helpers"

	"gorm.io/gorm"
)

func CourseSectionSeed(db *gorm.DB) {
	if !db.Migrator().HasTable(&models.CourseSection{}) {
		return
	}

	var count int64
	db.Table("course_sections").Count(&count)

	if count > 0 {
		return
	}

	var courseIds []int32
	if err := db.Table("courses").Select("courses.id").Scan(&courseIds).Error; err != nil {
		return
	}

	coursesSections := make([]models.CourseSection, 0)

	for _, id := range courseIds {
		for i := 0; i < 10; i++ {

			itemTitle := fmt.Sprintf("Course Section Item Title %d", i)
			itemContent := fmt.Sprintf("Course Section Item Content %d", i)
			items := []*models.CourseSectionItem{
				{
					Title:   itemTitle,
					Slug:    helpers.GenerateSlug(itemTitle),
					Type:    enums.CourseSectionItemTypeText,
					Content: &itemContent,
				},
			}

			sectionTitle := fmt.Sprintf("Course Section Title %d", i)
			coursesSections = append(coursesSections, models.CourseSection{
				Title:              sectionTitle,
				Slug:               helpers.GenerateSlug(sectionTitle),
				Position:           int32(i + 1),
				CourseID:           uint32(id),
				CourseSectionItems: &items,
			})
		}
	}

	db.Session(&gorm.Session{SkipHooks: true}).Where("courses.id IN (?)", courseIds).Updates(models.Course{SectionCount: uint32(10), SectionItemCount: uint32(10)})
	db.Session(&gorm.Session{SkipHooks: true}).Create(&coursesSections)
}
