package seeds

import (
	"math/rand"
	"vibico-education-api/internal/models"

	"gorm.io/gorm"
)

func CourseUserSeed(db *gorm.DB) {
	if !db.Migrator().HasTable(&models.CourseUser{}) {
		return
	}

	var count int64
	db.Table("course_users").Count(&count)

	if count > 0 {
		return
	}

	var courseIds []int
	if err := db.Table("courses").Select("courses.id").Where("courses.status = 2").Scan(&courseIds).Error; err != nil {
		return
	}

	var userIds []int
	if err := db.Table("users").Select("users.id").Scan(&userIds).Error; err != nil {
		return
	}

	courseUsers := make([]models.CourseUser, 0)

	for _, userId := range userIds {
		assignedCourses := make(map[int]bool)
		attempts := 0

		for i := 0; i < 10 && attempts < len(courseIds); i++ {
			randomIndex := rand.Intn(len(courseIds))
			courseId := courseIds[randomIndex]
			if !assignedCourses[courseId] {
				courseUsers = append(courseUsers, models.CourseUser{
					UserID:   uint32(userId),
					CourseID: uint32(courseIds[randomIndex]),
				})
				assignedCourses[courseId] = true
				i++
			}
			attempts++
		}
	}

	db.Session(&gorm.Session{SkipHooks: true}).Create(&courseUsers)
}
