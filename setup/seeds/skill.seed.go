package seeds

import (
	"vibico-education-api/internal/models"

	"gorm.io/gorm"
)

func SkillSeed(db *gorm.DB) {
	if !db.Migrator().HasTable(&models.Teacher{}) {
		return
	}

	var count int64
	db.Table("skills").Count(&count)

	if count > 0 {
		return
	}

	skillNames := []string{"Position Play", "Shotmaking", "Kicking", "Banking", "Jumping", "Safety Play", "Masse Shots", "Breaking", "Straight Stroke"}

	skills := make([]models.Skill, len(skillNames))
	for i, name := range skillNames {
		skills[i] = models.Skill{
			Name: name,
		}
	}

	db.Session(&gorm.Session{SkipHooks: true}).Create(&skills)
}
