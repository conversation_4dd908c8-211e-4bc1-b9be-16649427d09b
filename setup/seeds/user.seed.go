package seeds

import (
	"fmt"
	"vibico-education-api/internal/models"

	"gorm.io/gorm"
)

func UserSeed(db *gorm.DB) {
	if !db.Migrator().HasTable(&models.User{}) {
		return
	}

	var count int64
	db.Table("users").Count(&count)

	if count > 0 {
		return
	}

	// Connect to the auth database to get the list of user
	if authUsers == nil {
		GetAutUsers()
	}

	users := make([]models.User, 0)
	for index, u := range *authUsers {
		users = append(users, models.User{
			AuthId:      u.ID,
			Name:        fmt.Sprintf("Tên người dùng %d", index+1),
			PhoneNumber: &u.PhoneNumber,
		})
	}

	db.Session(&gorm.Session{SkipHooks: true}).Create(&users)
}
