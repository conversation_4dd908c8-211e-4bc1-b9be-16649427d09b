package seeds

import (
	"fmt"
	"math/rand"
	"vibico-education-api/constants"
	"vibico-education-api/internal/enums"
	"vibico-education-api/internal/models"
	"vibico-education-api/pkg/helpers"

	"gorm.io/gorm"
)

func CourseSeed(db *gorm.DB) {
	if !db.Migrator().HasTable(&models.Course{}) {
		return
	}

	var count int64
	db.Table("courses").Count(&count)

	if count > 0 {
		return
	}

	var teachers []models.Teacher
	db.Limit(10).Find(&teachers)

	var fundamentalPackages []*models.PackageDeal
	err := db.Model(models.PackageDeal{}).
		Where("name IN (?)", []string{constants.CourseBasicPackage, constants.CourseAdvancePackage}).
		Find(&fundamentalPackages).Error

	if err != nil {
		return
	}

	courses := make([]models.Course, 0)
	statuses := []enums.CourseStatus{
		enums.CourseStatusDraft,
		enums.CourseStatusSubmitted,
		enums.CourseStatusApproved,
		enums.CourseStatusRejected}

	for _, teacher := range teachers {
		for i := 0; i < 50; i++ {
			randomBonusPoint := rand.Intn(100000000)

			coursePackages := []*models.CoursePackage{}
			if fundamentalPackages != nil && len(fundamentalPackages) > 0 {
				for _, packageDeal := range fundamentalPackages {
					coursePackage := &models.CoursePackage{
						PackageDealID: packageDeal.ID,
						Price:         func(i int32) *int32 { return &i }(int32(randomBonusPoint)),
						SalePrice:     func(i int32) *int32 { return &i }(int32(randomBonusPoint)),
					}
					coursePackages = append(coursePackages, coursePackage)
				}
			}

			title := fmt.Sprintf("Course Title %d", i)
			courses = append(courses, models.Course{
				Title:             title,
				Slug:              helpers.GenerateSlug(title),
				Description:       func(s string) *string { return &s }(fmt.Sprintf("Course Description %d", i)),
				TeacherId:         teacher.ID,
				SalePrice:         func(i int32) *int32 { return &i }(int32(randomBonusPoint)),
				Price:             func(i int32) *int32 { return &i }(int32(randomBonusPoint)),
				BonusPoint:        func(i int32) *int32 { return &i }(int32(randomBonusPoint)),
				BonusPointPercent: func(i int32) *int32 { return &i }(int32(rand.Intn(100))),
				Status:            statuses[rand.Intn(3)],
				IsPublic:          rand.Intn(2) == 1,
				CoursePackages:    &coursePackages,
			})
		}
	}

	db.Session(&gorm.Session{SkipHooks: true}).Create(&courses)
}
