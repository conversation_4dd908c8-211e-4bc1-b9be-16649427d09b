package seeds

import (
	"fmt"
	"math/rand"
	"vibico-education-api/internal/enums"
	"vibico-education-api/internal/models"
	"vibico-education-api/pkg/helpers"

	"gorm.io/gorm"
)

func DrillSeed(db *gorm.DB) {
	if !db.Migrator().HasTable(&models.Drill{}) {
		return
	}

	var count int64
	db.Table("drills").Count(&count)

	if count > 0 {
		return
	}

	var admins []models.Admin
	db.Limit(10).Find(&admins)

	drills := make([]models.Drill, 0)
	statuses := []enums.DrillStatus{enums.DrillStatusPublic, enums.DrillStatusPrivate}

	for _, admin := range admins {
		for i := 0; i < 50; i++ {
			title := fmt.Sprintf("Drill Title %d", i+1)
			drills = append(drills, models.Drill{
				Title:     title,
				Slug:      helpers.GenerateSlug(title),
				Level:     enums.DrillLevelBeginner,
				Status:    statuses[rand.Intn(2)],
				OwnerID:   admin.ID,
				OwnerType: "Admin",
				IsMaster:  true,
			})
		}
	}

	db.Session(&gorm.Session{SkipHooks: true}).Create(&drills)
}
