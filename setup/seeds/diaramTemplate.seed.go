package seeds

import (
	"fmt"
	"vibico-education-api/internal/models"

	"gorm.io/gorm"
)

func TemplateDiagramSeed(db *gorm.DB) {
	if !db.Migrator().HasTable(&models.TemplateDiagram{}) {
		return
	}

	var count int64
	db.Table("template_diagrams").Count(&count)

	if count > 0 {
		return
	}

	templates := make([]models.TemplateDiagram, 0)

	for i := 0; i < 50; i++ {
		templates = append(templates, models.TemplateDiagram{
			Name: fmt.Sprintf("Template name %d", i+1),
		})
	}

	db.Session(&gorm.Session{SkipHooks: true}).Create(&templates)
}
